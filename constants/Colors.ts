/**
 * Application Colors
 * 
 * This file centralizes all color definitions for the app, making it easy to maintain
 * and update the color scheme. Colors are defined for both light and dark modes.
 */

// Primary brand colors
const primaryLight = '#0a7ea4';
const primaryDark = '#38b6ff';
const secondaryLight = '#6c47ff';
const secondaryDark = '#8b6fff';

// Accent colors
const accentLight = '#ff6b6b';
const accentDark = '#ff8585';

// Core neutrals
const white = '#ffffff';
const black = '#000000';

// Gray scale
const gray = {
  50: '#f9fafb',
  100: '#f3f4f6',
  200: '#e5e7eb',
  300: '#d1d5db',
  400: '#9ca3af',
  500: '#6b7280',
  600: '#4b5563',
  700: '#374151',
  800: '#1f2937',
  900: '#111827',
};

// Semantic colors
const success = {
  light: '#10b981',
  dark: '#34d399',
};

const warning = {
  light: '#f59e0b',
  dark: '#fbbf24',
};

const error = {
  light: '#ef4444',
  dark: '#f87171',
};

const info = {
  light: '#3b82f6',
  dark: '#60a5fa',
};

export const Colors = {
  light: {
    // Base
    primary: primaryLight,
    secondary: secondaryLight,
    accent: accentLight,
    
    // Text
    text: {
      primary: gray[900],
      secondary: gray[700],
      tertiary: gray[500],
      disabled: gray[400],
      inverse: white,
    },
    
    // UI Elements
    button: {
      primary: {
        background: primaryLight,
        text: white,
        border: primaryLight,
      },
      secondary: {
        background: 'transparent',
        text: primaryLight,
        border: primaryLight,
      },
      disabled: {
        background: gray[200],
        text: gray[500],
        border: gray[200],
      },
    },
    
    // Form elements
    input: {
      background: white,
      text: gray[900],
      placeholder: gray[500],
      border: gray[300],
      focusBorder: primaryLight,
      error: error.light,
    },
    
    // General backgrounds
    background: {
      primary: white,
      secondary: gray[50],
      tertiary: gray[100],
      transparent: 'transparent',
    },
    
    // Components
    card: {
      background: white,
      border: gray[200],
    },
    
    // States and feedback
    status: {
      success: success.light,
      warning: warning.light,
      error: error.light,
      info: info.light,
    },
    
    // Legacy (for backward compatibility)
    legacyText: gray[900],
    legacyBackground: white,
    tint: primaryLight,
    icon: gray[600],
    tabIconDefault: gray[500],
    tabIconSelected: primaryLight,
  },
  
  dark: {
    // Base
    primary: primaryDark,
    secondary: secondaryDark,
    accent: accentDark,
    
    // Text
    text: {
      primary: gray[50],
      secondary: gray[300],
      tertiary: gray[400],
      disabled: gray[600],
      inverse: gray[900],
    },
    
    // UI Elements
    button: {
      primary: {
        background: primaryDark,
        text: black,
        border: primaryDark,
      },
      secondary: {
        background: 'transparent',
        text: primaryDark,
        border: primaryDark,
      },
      disabled: {
        background: gray[800],
        text: gray[600],
        border: gray[800],
      },
    },
    
    // Form elements
    input: {
      background: gray[800],
      text: gray[100],
      placeholder: gray[500],
      border: gray[700],
      focusBorder: primaryDark,
      error: error.dark,
    },
    
    // General backgrounds
    background: {
      primary: gray[900],
      secondary: gray[800],
      tertiary: gray[700],
      transparent: 'transparent',
    },
    
    // Components
    card: {
      background: gray[800],
      border: gray[700],
    },
    
    // States and feedback
    status: {
      success: success.dark,
      warning: warning.dark,
      error: error.dark,
      info: info.dark,
    },
    
    // Legacy (for backward compatibility)
    legacyText: gray[50],
    legacyBackground: gray[900],
    tint: primaryDark,
    icon: gray[400],
    tabIconDefault: gray[500],
    tabIconSelected: primaryDark,
  },
};

// Utility function to get colors based on theme
export const getColors = (colorScheme: 'light' | 'dark') => Colors[colorScheme];
