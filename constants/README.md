# Application Constants

This directory contains constant values used throughout the application.

## Colors

The `Colors.ts` file contains a comprehensive color system for both light and dark themes. It provides a centralized location for all color management in the application, making it easy to maintain a consistent look and feel, as well as to make app-wide color changes when needed.

### How to Use Colors in Your Components

```typescript
import { useColorScheme } from 'react-native';
import { Colors, getColors } from '../constants/Colors';

function MyComponent() {
  // Method 1: Using the getColors utility function
  const colorScheme = useColorScheme() ?? 'light';
  const colors = getColors(colorScheme);
  
  // Method 2: Directly accessing the Colors object
  const primaryColor = Colors[colorScheme].primary;
  
  return (
    <View style={{ backgroundColor: colors.background.primary }}>
      <Text style={{ color: colors.text.primary }}>
        Primary text color
      </Text>
      
      <TouchableOpacity 
        style={{ 
          backgroundColor: colors.button.primary.background,
          borderColor: colors.button.primary.border,
        }}
      >
        <Text style={{ color: colors.button.primary.text }}>
          Primary Button
        </Text>
      </TouchableOpacity>
      
      <TextInput 
        style={{ 
          backgroundColor: colors.input.background,
          color: colors.input.text,
          borderColor: colors.input.border,
        }}
        placeholderTextColor={colors.input.placeholder}
      />
    </View>
  );
}
```

### Available Color Categories

The color system includes:

1. **Base Colors**
   - `primary`: Main brand color
   - `secondary`: Secondary brand color
   - `accent`: Accent color for highlights

2. **Text Colors**
   - `text.primary`: Main text color
   - `text.secondary`: Secondary text for less emphasis
   - `text.tertiary`: For lower importance text
   - `text.disabled`: For disabled text
   - `text.inverse`: Contrasting text color

3. **Button Colors**
   - `button.primary`: Colors for primary buttons
   - `button.secondary`: Colors for secondary buttons
   - `button.disabled`: Colors for disabled buttons

4. **Input Colors**
   - `input.background`: Background color for input fields
   - `input.text`: Text color in input fields
   - `input.placeholder`: Color for placeholder text
   - `input.border`: Border color for inputs
   - `input.focusBorder`: Border color when focused
   - `input.error`: Color for error states

5. **Background Colors**
   - `background.primary`: Main background color
   - `background.secondary`: Secondary background color
   - `background.tertiary`: Tertiary background color

6. **Component Colors**
   - `card.background`: Background color for cards
   - `card.border`: Border color for cards

7. **Status Colors**
   - `status.success`: For success states
   - `status.warning`: For warning states
   - `status.error`: For error states
   - `status.info`: For informational states

8. **Legacy Colors** (for backward compatibility)
   - `legacyText`: Old text color
   - `legacyBackground`: Old background color
   - `tint`: Tint color
   - `icon`: Icon color
   - `tabIconDefault`: Default tab icon color
   - `tabIconSelected`: Selected tab icon color

### Tips for Using Colors

- Always use the color system rather than hard-coding color values
- Use semantic color names (like `colors.status.error`) rather than direct color values
- Consider accessibility when choosing colors - ensure sufficient contrast
- Test your UI in both light and dark modes 