# Color System Migration Guide

This guide helps you migrate from hardcoded color values to our centralized color system. 

## Why Use the Centralized Color System?

1. **Consistency**: Maintain a uniform look and feel across the app
2. **Dark Mode Support**: All colors automatically work with both light and dark themes
3. **Maintainability**: Change colors in one place instead of throughout the codebase
4. **Semantic Naming**: Colors are named by their purpose, not their value

## How to Migrate

### Step 1: Import the useColors hook

```typescript
import { useColors } from '@/hooks/useThemeColor';
```

### Step 2: Use the hook to get colors in your component

```typescript
function MyComponent() {
  const colors = useColors();
  
  return (
    <View style={styles.container}>
      <Text style={styles.text}>Hello World</Text>
    </View>
  );
  
  const styles = StyleSheet.create({
    container: {
      backgroundColor: colors.background.primary,
    },
    text: {
      color: colors.text.primary,
    },
  });
}
```

### Step 3: Replace hardcoded colors with semantic color values

#### Common Replacements

Below are common hardcoded colors and their recommended replacements:

| Hardcoded Color | Replacement |
|-----------------|-------------|
| #FFFFFF, #fff | colors.background.primary |
| #F5F7FA, #f5f5f5, #f9f9f9 | colors.background.secondary |
| #F0F0F0, #f0f0f0, #EFEFEF | colors.background.tertiary |
| #333333, #333 | colors.text.primary |
| #666666, #666, #777777, #777 | colors.text.secondary |
| #999999, #999 | colors.text.tertiary |
| #0F96BB, #0F6EBB, #0a7ea4, #3068E1 | colors.primary |
| #e1e1e1, #E0E0E0, #ddd | colors.input.border |

### Examples

#### Before:

```typescript
<View style={{ backgroundColor: '#FFFFFF' }}>
  <Text style={{ color: '#333333' }}>Title</Text>
  <Text style={{ color: '#666666' }}>Description</Text>
  <TouchableOpacity 
    style={{ backgroundColor: '#0F96BB' }}
  >
    <Text style={{ color: '#FFFFFF' }}>Button</Text>
  </TouchableOpacity>
</View>
```

#### After:

```typescript
const colors = useColors();

<View style={{ backgroundColor: colors.background.primary }}>
  <Text style={{ color: colors.text.primary }}>Title</Text>
  <Text style={{ color: colors.text.secondary }}>Description</Text>
  <TouchableOpacity 
    style={{ backgroundColor: colors.button.primary.background }}
  >
    <Text style={{ color: colors.button.primary.text }}>Button</Text>
  </TouchableOpacity>
</View>
```

## Status Colors

Use semantic status colors for alerts, indicators, etc.:

- Success: `colors.status.success`
- Warning: `colors.status.warning`
- Error: `colors.status.error`
- Info: `colors.status.info`

## Form Elements

For form inputs and controls:

- Input background: `colors.input.background`
- Input text: `colors.input.text`
- Input placeholder: `colors.input.placeholder`
- Input border: `colors.input.border`
- Input focus border: `colors.input.focusBorder`

## Migration Strategy

1. Start with one component at a time
2. Focus on the most frequently used components first
3. Update shared components for maximum impact
4. Use search and replace for common color values
5. Test in both light and dark modes after each change

## Need Help?

Refer to the `constants/README.md` file for a complete listing of all available colors in the system. 