{"name": "salescrm", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start --dev-client", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/cli-platform-ios": "^17.0.0", "@react-native-community/datetimepicker": "8.2.0", "@react-native-firebase/app": "^21.14.0", "@react-native-firebase/messaging": "^21.14.0", "@react-native-picker/picker": "2.9.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "axios": "^1.8.1", "date-fns": "^4.1.0", "expo": "~52.0.41", "expo-blur": "~14.0.3", "expo-checkbox": "~4.0.1", "expo-constants": "~17.0.7", "expo-dev-client": "~5.0.15", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-image": "~2.0.7", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-location": "~18.0.8", "expo-media-library": "~17.0.6", "expo-notifications": "~0.29.14", "expo-router": "~4.0.19", "expo-speech": "~13.0.1", "expo-splash-screen": "~0.29.22", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.8", "expo-web-browser": "~14.0.2", "moment": "^2.30.1", "pod-install": "^0.3.4", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.7", "react-native-audio-recorder-player": "^3.6.12", "react-native-datepicker": "^1.7.2", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.20.2", "react-native-image-viewing": "^0.2.2", "react-native-linear-gradient": "^2.8.3", "react-native-maps": "^1.20.1", "react-native-marquee": "^0.5.0", "react-native-marquee-view": "^1.0.5", "react-native-modal": "^14.0.0-rc.1", "react-native-paper": "^5.13.1", "react-native-permissions": "^5.3.0", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-snackbar": "^2.8.0", "react-native-text-ticker": "^1.14.0", "react-native-toast-message": "^2.2.1", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@react-native-community/cli": "^17.0.0", "@types/jest": "^29.5.12", "@types/mime": "^3.0.4", "@types/react": "~18.3.12", "@types/react-native": "^0.72.8", "@types/react-test-renderer": "^18.3.0", "babel-plugin-module-resolver": "^5.0.2", "jest": "^29.2.1", "jest-expo": "~52.0.6", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true}