# Sales CRM Mobile App

A React Native mobile application for sales teams to manage contacts, accounts, deals, and track activities.

## Features

- **Authentication**: Secure login system
- **Dashboard**: Calendar view with meetings and tasks
- **Contacts Management**: View, add, edit, and delete contacts
- **Distributors/Accounts**: Manage business accounts and distributors
- **Orders**: Track and manage customer orders
- **Attendance Tracking**: Record daily attendance with check-in/check-out
- **Conveyance Claims**: Submit and track travel expense claims
- **Side Menu**: Quick access to important features

## Tech Stack

- React Native
- Expo
- TypeScript
- React Navigation
- Expo Router
- AsyncStorage for local data persistence

## Getting Started

### Prerequisites

- Node.js (v14 or newer)
- npm or yarn
- Expo CLI

### Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/salescrm.git
cd salescrm
```

2. Install dependencies:
```bash
npm install
# or
yarn install
```

3. Start the development server:
```bash
npm start
# or
yarn start
```

4. Run on a device or emulator:
```bash
npm run android
# or
npm run ios
```

## Project Structure

```
salescrm/
├── app/                  # Main application screens
│   ├── (tabs)/           # Tab-based screens
│   │   ├── index.tsx     # Home/Dashboard screen
│   │   ├── contacts.tsx  # Contacts screen
│   │   ├── distributors.tsx # Distributors screen
│   │   ├── orders.tsx    # Orders screen
│   │   └── _layout.tsx   # Tab navigation layout
│   ├── login.tsx         # Login screen
│   └── _layout.tsx       # Root navigation layout
├── assets/               # Static assets (images, fonts)
├── components/           # Reusable UI components
├── context/              # React Context for state management
├── hooks/                # Custom React hooks
└── constants/            # App constants and configuration
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [Expo](https://expo.dev/)
- [React Native](https://reactnative.dev/)
- [React Navigation](https://reactnavigation.org/)
