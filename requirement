Sales CRM  Application Requirements Document
1. Login Form
Fields: Email/Username and Password.
2. Bottom Navigation Tabs
Home
Contacts
Plus
Accounts
Deals

3. Home Screen
Toggle Button for Attendance at Top Right Corner.
Notification Icon.
Calendar with Task List Based on Selected Date.
Upcoming Task List with 'View All' Option.
Task List Sections: Overdue, Completed, Upcoming with Filter.
Hamburger Menu with Options:
Attendance History
Conveyance History
About Us
Contact Us
Privacy Policy
Terms and Conditions
Account Delete
Logout
4. Contacts
List All Contacts from API with Search, Filter, Sort Options.
Create New Contact (Mandatory Fields Required).
Contact Information Page with:
Contact Details
Activities (Call, Voice Note, Tasks, Meetings, Call Log, Notes)
More (Accounts, Deals, Files)
Edit and Delete Contact Options
Add Activities Option
5. Deals
List All Deals from API with Search, Filter, Sort Options.
Create New Deal (Mandatory Fields Required).
Deal Information Page with:
Deal Details
Activities (Call, Voice Note, Tasks, Meetings, Call Log, Notes)
More (Contacts, Files)
Edit and Delete Deal Options
Add Activities Option
6. Accounts
List All Accounts from API with Search, Filter, Sort Options.
Create New Account (Mandatory Fields Required).
Account Information Page with:
Account Details
Activities (Call, Voice Note, Tasks, Meetings, Call Log, Notes)
More (Contacts, Deals, Files)
Edit and Delete Account Options
Add Activities Option
7. Attendance History
Displays Current Date's Punch-in and Punch-out Details.
Complete View of Day’s Attendance.
Filter Icon for Viewing Daily, Weekly, or Monthly Attendance History.
8. Conveyance History
Displays Current Date’s Conveyance Details.
Complete View of Day’s Conveyance.
Filter Icon for Viewing Today, Weekly, and Monthly Conveyance History.
9. Conveyance Details
Displays Site Visit Tasks for the Day (Check-in, Check-out, Code, Amount Details).
Clicking on Site Visit Opens Popup with Task Details, Claim Details, and Status.
Default Transport Mode: Bike (Non-editable).
Edit Icon to Change Mode, Update Amount with Remarks, and Upload Mandatory File Before Saving.
10. Claim Module
Lists Eligible Claims Available Every 15 Days:
1st-15th Claims on the 16th
16th-31st Claims on the 1st of the Next Month
Apply Claim Button to Submit All Listed Claims at Once.
Filter Icon for Viewing Last Two Months' Claim Cycles.
11. Claim History
After Applying for a Claim, Users Can Click Highlighted Icon to View Status.
Date Filter and Submit Button for Filtering Claims.
Pending Section:
Claims Show "Waiting for Manager Approval" Until Approved.
After Manager Approval, Claims Show "Waiting for Finance Approval".
Completed Section Lists Fully Approved Claims.
Rejected Section Lists Denied Claims by Manager or Finance.
Rejected Claims Can Be Reapplied by Updating Details, Uploading a Mandatory File, and Resubmitting for Approval.
