/**
 * Forced FCM token retrieval utility module
 * This module bypasses standard FCM initialization and provides
 * a more aggressive approach to token retrieval with better handling
 * of the SERVICE_NOT_AVAILABLE error.
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { getApp, getApps, initializeApp } from '@react-native-firebase/app';
import { getMessaging, getToken } from '@react-native-firebase/messaging';
import { Platform, AppState, NativeModules, Linking } from 'react-native';
import * as Notifications from 'expo-notifications';

// Firebase config with explicit values
const FIREBASE_CONFIG = {
  apiKey: "AIzaSyCh5gE623XX2PvCGIBOxJpqzKBicZNHVek",
  projectId: "salescrm-f2511",
  appId: "1:880620206085:android:d6697f8f77f46b66b3c46c",
  messagingSenderId: "880620206085",
  databaseURL: "https://salescrm-f2511.firebaseio.com",
  storageBucket: "salescrm-f2511.appspot.com"
};

const FCM_TOKEN_KEY = 'forced_fcm_token';
const DEVICE_ID_KEY = 'device_fallback_id';

// Configure Expo notifications as fallback
const configureFallbackNotifications = async () => {
  try {
    console.log('[ForcedFCM] Setting up fallback notification handler');
    
    await Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: true,
      }),
    });
    
    // Request permissions for local notifications
    const { status } = await Notifications.requestPermissionsAsync();
    console.log(`[ForcedFCM] Expo notifications permission status: ${status}`);
    
    return status === 'granted';
  } catch (error) {
    console.error('[ForcedFCM] Error setting up fallback notifications:', error);
    return false;
  }
};

// Generate a unique device ID for fallback token
const generateFallbackDeviceId = async () => {
  try {
    // Check for existing ID
    let deviceId = await AsyncStorage.getItem(DEVICE_ID_KEY);
    
    if (deviceId) {
      console.log('[ForcedFCM] Using existing fallback device ID');
      return deviceId;
    }
    
    // Generate a new unique ID
    deviceId = `fallback-${Platform.OS}-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;
    await AsyncStorage.setItem(DEVICE_ID_KEY, deviceId);
    
    console.log('[ForcedFCM] Generated new fallback device ID');
    return deviceId;
  } catch (error) {
    console.error('[ForcedFCM] Error generating fallback ID:', error);
    return `fallback-error-${Date.now()}`;
  }
};

/**
 * Force initialize Firebase with explicit config
 * This bypasses any existing Firebase instances
 */
export const forcedFirebaseInit = async () => {
  try {
    console.log('[ForcedFCM] Starting Firebase forced initialization');
    
    // Clean up existing apps using getApps()
    if (getApps().length > 0) {
      try {
        await Promise.all(getApps().map(app => app.delete()));
        console.log('[ForcedFCM] Successfully deleted existing Firebase apps');
      } catch (cleanupError) {
        console.error('[ForcedFCM] Error cleaning up existing Firebase apps:', cleanupError);
      }
    }
    
    // Initialize using modular API
    console.log('[ForcedFCM] Initializing Firebase with explicit config');
    const app = initializeApp(FIREBASE_CONFIG);
    
    console.log(`[ForcedFCM] Firebase app initialized: ${app.name}`);
    return true;
  } catch (error) {
    console.error('[ForcedFCM] Firebase forced initialization failed:', error);
    return false;
  }
};

/**
 * Request permissions for notifications with platform-specific handling
 */
export const forcedRequestPermissions = async () => {
  try {
    const app = getApp();
    const messaging = getMessaging(app);
    
    if (Platform.OS === 'ios') {
      await messaging.registerDeviceForRemoteMessages().catch(console.warn);
      const authStatus = await messaging.requestPermission().catch(console.warn);
      console.log(`[ForcedFCM] iOS authorization status: ${authStatus || 'unknown'}`);
      return true;
    } else {
      try {
        const authStatus = await messaging.requestPermission();
        console.log(`[ForcedFCM] Android authorization status: ${authStatus}`);
        return true;
      } catch (permError) {
        console.log('[ForcedFCM] Android permission error:', permError);
        return true;
      }
    }
  } catch (error) {
    console.error('[ForcedFCM] Permission request error:', error);
    return false;
  }
};

/**
 * Check Google Play Services status on Android
 * Helps diagnose SERVICE_NOT_AVAILABLE errors
 */
export const checkGooglePlayServices = async () => {
  if (Platform.OS !== 'android') {
    return { available: true, message: 'Not Android platform' };
  }

  try {
    console.log('[ForcedFCM] Checking Google Play Services status...');
    
    // In a real app, you would use native modules to check GPS status
    // For now, we'll use a simple connection test approach with network check
    
    // Check if device has internet connection
    try {
      // Simple connectivity test
      const timeout = 10000;
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);
      
      const response = await fetch('https://firebase.google.com/favicon.ico', {
        method: 'HEAD',
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (response.ok) {
        console.log('[ForcedFCM] Network connectivity test passed');
      } else {
        console.log('[ForcedFCM] Network connectivity test failed with status:', response.status);
        return { 
          available: false, 
          message: `Network connectivity issue: HTTP ${response.status}` 
        };
      }
    } catch (netError) {
      console.log('[ForcedFCM] Network connectivity test failed:', netError);
      return { 
        available: false, 
        message: `Network error: ${netError.message}`,
        error: netError
      };
    }
    
    // Check if app is in foreground - Google Play Services connection often fails in background
    if (AppState.currentState !== 'active') {
      console.log('[ForcedFCM] App is in background, waiting for foreground state');
      try {
        await new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            subscription.remove();
            reject(new Error('Timeout waiting for app to enter foreground'));
          }, 30000);
          
          const subscription = AppState.addEventListener('change', (nextAppState) => {
            if (nextAppState === 'active') {
              clearTimeout(timeout);
              subscription.remove();
              resolve();
            }
          });
        });
      } catch (stateError) {
        console.log('[ForcedFCM] Error waiting for app state change:', stateError);
        // Continue anyway
      }
    }
    
    // Add a delay to allow Google Play Services to connect
          await new Promise(resolve => setTimeout(resolve, 5000));
          
    return { available: true, message: 'Connection test passed' };
  } catch (error) {
    console.error('[ForcedFCM] Google Play Services check failed:', error);
    return {
      available: false,
      message: error.message || 'Unknown Google Play Services error',
      error
    };
  }
};

/**
 * Check if Google Play Services is installed and up to date
 * @returns {Promise<Object>} - Object with availability status and additional information
 */
export const checkPlayServicesAvailability = async () => {
  if (Platform.OS !== 'android') {
    return { available: true, message: 'Not Android platform' };
  }

  try {
    console.log('[ForcedFCM] Checking Google Play Services availability');
    
    // Try to open Google Play Services app via deeplink
    const canOpen = await Linking.canOpenURL('market://details?id=com.google.android.gms');
    
    if (!canOpen) {
      console.log('[ForcedFCM] Google Play Services app might not be installed');
      return { 
        available: false, 
        message: 'Google Play Services app might not be installed',
        actionRequired: true,
        actionUrl: 'https://play.google.com/store/apps/details?id=com.google.android.gms'
      };
    }
    
    // Check for network connectivity to Google
    try {
      const timeout = 5000;
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);
      
      const response = await fetch('https://www.google.com/favicon.ico', {
        method: 'HEAD',
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        console.log('[ForcedFCM] Network connectivity issues with Google services');
        return { 
          available: false, 
          message: 'Network connectivity issues with Google services',
          networkIssue: true 
        };
      }
    } catch (netError) {
      console.log('[ForcedFCM] Network connectivity test failed:', netError);
      return { 
        available: false, 
        message: 'Network connectivity issue',
        networkIssue: true,
        error: netError
      };
    }
    
    return { available: true, message: 'Google Play Services appears to be installed' };
  } catch (error) {
    console.error('[ForcedFCM] Error checking Google Play Services:', error);
    return { 
      available: false, 
      message: error.message || 'Unknown error checking Google Play Services',
      error
    };
  }
};

/**
 * Extremely aggressive FCM token retrieval with multiple fallback strategies
 * This function will make multiple attempts with increasing delays
 */
export const forcedGetFCMToken = async () => {
  try {
    const app = getApp();
    const messaging = getMessaging(app);
    
    const playServices = await checkPlayServicesAvailability();
    if (!playServices.available) {
      throw new Error('GOOGLE_PLAY_SERVICES_UNAVAILABLE');
    }

    const token = await getToken(messaging, {
      vapidKey: 'BLMqUxJ3Hq9Vv-9YHk2M7eBzT7DmW4s1Xy9bN6Z7J7o',
      serviceWorkerRegistration: null
    });
      
      if (token) {
        await AsyncStorage.setItem(FCM_TOKEN_KEY, token);
        return token;
      }
    } catch (error) {
    console.error('[ForcedFCM] Error:', error);
    
    if (error.code === 'messaging/service-unavailable') {
      console.log('[ForcedFCM] SERVICE_NOT_AVAILABLE - initiating recovery');
      const networkStatus = await checkNetworkConnection();
      
      if (networkStatus.isConnected) {
        await forcedFirebaseInit();
        await new Promise(resolve => setTimeout(resolve, 10000));
      }
      
      return forcedGetFCMToken();
    }
    
    throw error;
  }
};

/**
 * Last resort fallback when FCM can't be initialized
 * This enables the app to continue without Firebase Messaging
 */
export const createFallbackToken = async () => {
  try {
    console.log('[ForcedFCM] Creating fallback identification...');
    
    // Generate a device ID for fallback
    const deviceId = await generateFallbackDeviceId();
    
    // Create a token-like string that can be recognized as a fallback 
    const fallbackToken = `FALLBACK:${Platform.OS}:${deviceId}:${Date.now()}`;
    
    // Store it just like a real token
    await AsyncStorage.setItem(FCM_TOKEN_KEY, fallbackToken);
    
    console.log('[ForcedFCM] Fallback token created successfully');
    return fallbackToken;
  } catch (error) {
    console.error('[ForcedFCM] Error creating fallback token:', error);
  return null;
  }
};

/**
 * Complete initialization of FCM with forced approach and fallback option
 * Returns token if successful, fallback ID if FCM fails, null if all fails
 */
export const setupForcedFCM = async () => {
  try {
    console.log('[ForcedFCM] Starting forced FCM setup');
    
    // For Android, check Google Play Services first
    if (Platform.OS === 'android') {
      // First check if Google Play Services is installed
      const playServicesAvailability = await checkPlayServicesAvailability();
      console.log(`[ForcedFCM] Google Play Services availability: ${JSON.stringify(playServicesAvailability)}`);
      
      if (!playServicesAvailability.available) {
        console.warn(`[ForcedFCM] Google Play Services issue: ${playServicesAvailability.message}`);
        
        // If Play Services is not installed or has network issues
        if (playServicesAvailability.actionRequired || playServicesAvailability.networkIssue) {
          console.log('[ForcedFCM] FCM may not work properly due to Google Play Services issues');
          // Return fallback token immediately since FCM won't work without Play Services
          return await createFallbackToken();
        }
      }
      
      // Then also perform general Google service connectivity check
      const playServicesStatus = await checkGooglePlayServices();
      console.log(`[ForcedFCM] Google Play Services status: ${playServicesStatus.available ? 'Available' : 'Unavailable'}`);
      
      if (!playServicesStatus.available) {
        console.warn(`[ForcedFCM] Google Play Services issue: ${playServicesStatus.message}`);
        // Continue anyway - our aggressive retry logic might still succeed
      }
    }
    
    // Set up fallback notifications first
    const fallbackReady = await configureFallbackNotifications();
    console.log(`[ForcedFCM] Fallback notifications ${fallbackReady ? 'ready' : 'not available'}`);
    
    // Make sure Firebase is initialized first - with retries
    const maxFirebaseRetries = 3;
    let retryCount = 0;
    let initError = null;
    
    let firebaseInitialized = false;
    
    while (!firebaseInitialized && retryCount < maxFirebaseRetries) {
      try {
        if (!isFirebaseReady()) {
          console.log(`[ForcedFCM] Firebase not initialized yet, initializing now (attempt ${retryCount + 1})...`);
          await initializeFirebase();
        }
        
        // Verify Firebase is initialized
        if (getApp().length > 0) {
          firebaseInitialized = true;
          console.log('[ForcedFCM] Firebase app initialized successfully');
        } else {
          throw new Error("Firebase apps array is empty after initialization");
        }
      } catch (error) {
        initError = error;
        console.error(`[ForcedFCM] Firebase initialization attempt ${retryCount} failed:`, initError);
        retryCount++;
        
        if (retryCount < maxFirebaseRetries) {
          // Wait before retry
          const delay = 2000 * retryCount;
          console.log(`[ForcedFCM] Waiting ${delay}ms before Firebase init retry...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    // If Firebase initialization failed after all retries, we can't proceed with FCM token
    if (!firebaseInitialized) {
      console.error("[ForcedFCM] Firebase initialization failed after multiple attempts");
      return await createFallbackToken();
    }
    
    console.log('[ForcedFCM] Firebase app initialized, setting up notifications');
    
    // Request permissions for notifications
    const permissionResult = await forcedRequestPermissions();
    console.log(`[ForcedFCM] Permission request result: ${permissionResult}`);
    
    // Get FCM token with retries
    const maxTokenRetries = 8;
    let tokenRetries = 0;
    let token = null;
    let tokenError = null;
    let serviceNotAvailableCount = 0;
    
    while (!token && tokenRetries < maxTokenRetries) {
      try {
        console.log(`[ForcedFCM] Attempting to get FCM token (try ${tokenRetries + 1}/${maxTokenRetries})`);
        token = await getFCMToken(tokenRetries);
        console.log('FCM token obtained:', token ? 'yes' : 'no');
      } catch (error) {
        tokenError = error;
        console.error(`[ForcedFCM] Error getting FCM token (attempt ${tokenRetries}):`, tokenError);
        
        // Track SERVICE_NOT_AVAILABLE errors
        if (error.message && error.message.includes('SERVICE_NOT_AVAILABLE')) {
          serviceNotAvailableCount++;
          
          // If we have multiple SERVICE_NOT_AVAILABLE errors and we're on Android,
          // it's likely a persistent Google Play Services issue - use fallback
          if (serviceNotAvailableCount >= 2 && Platform.OS === 'android') {
            console.log('[ForcedFCM] Multiple SERVICE_NOT_AVAILABLE errors detected - Google Play Services likely unavailable');
            
            // Check Play Services again to confirm
            const playServicesCheck = await checkPlayServicesAvailability();
            if (!playServicesCheck.available) {
              console.log('[ForcedFCM] Confirmed Google Play Services issue - switching to fallback token');
              return await createFallbackToken();
            }
          }
        }
        
        tokenRetries++;
        
        if (tokenRetries < maxTokenRetries) {
          // Exponential backoff with capped delay
          const delay = Math.min(60000, 2000 * Math.pow(2, tokenRetries));
          console.log(`[ForcedFCM] Waiting ${delay}ms before token retry...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    // If we couldn't get a token after all retries
    if (!token) {
      console.warn('[ForcedFCM] Failed to get FCM token after all retries');
      return await createFallbackToken();
    }
    
    return token;
  } catch (setupError) {
    console.error('[ForcedFCM] Setup error:', setupError);
    return await createFallbackToken();
  }
};

/**
 * Test function to show a local notification
 * This can be used to verify notifications work without FCM
 */
export const showTestNotification = async () => {
  try {
    console.log('[ForcedFCM] Showing test notification');
    
    await Notifications.scheduleNotificationAsync({
      content: {
        title: 'Test Notification',
        body: 'This is a test notification from the app',
        data: { testData: 'Sample notification data' },
      },
      trigger: null, // Show immediately
    });
    
    console.log('[ForcedFCM] Test notification sent successfully');
    return true;
  } catch (error) {
    console.error('[ForcedFCM] Error showing test notification:', error);
    return false;
  }
}; 