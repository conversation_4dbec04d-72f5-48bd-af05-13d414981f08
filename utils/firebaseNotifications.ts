import messaging from '@react-native-firebase/messaging';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Notifications from 'expo-notifications';
import { Platform, PermissionsAndroid, NativeModules } from 'react-native';
import { initializeFirebase, isFirebaseReady } from './firebaseConfig';
import firebase from '@react-native-firebase/app';

const FCM_TOKEN_KEY = 'fcm_token';

// Check if Google Play Services is available on Android
const checkPlayServicesAvailability = async (): Promise<boolean> => {
  if (Platform.OS !== 'android') return true; // Always true for non-Android
  
  try {
    // First check if app can communicate with Firebase
    if (firebase.app() && messaging()) {
      console.log('Firebase and Messaging modules are available');
      return true;
    }
    
    console.log('Unable to verify Google Play Services availability');
    return false;
  } catch (error) {
    console.error('Error checking Play Services:', error);
    return false;
  }
};

export const requestUserPermission = async () => {
  try {
    // For iOS, we need to register for remote notifications first
    if (Platform.OS === 'ios') {
      const authStatus = await messaging().requestPermission({
        sound: true,
        alert: true,
        badge: true,
        provisional: false,
      });
      
      // iOS-specific: register for remote notifications
      await messaging().registerDeviceForRemoteMessages();
      
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (enabled) {
        console.log('iOS Authorization status:', authStatus);
      } else {
        console.log('iOS authorization denied:', authStatus);
      }

      return enabled;
    } 
    // Android permissions
    else if (Platform.OS === 'android') {
      try {
        // For Android 13+ (API level 33+), we need to request POST_NOTIFICATIONS
        if (Platform.Version >= 33) {
          const androidStatus = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
          );
          console.log('Android POST_NOTIFICATIONS permission status:', androidStatus);
          
          // Make sure permission was granted
          if (androidStatus !== PermissionsAndroid.RESULTS.GRANTED) {
            console.log('POST_NOTIFICATIONS permission denied, but continuing as FCM may still work');
          }
        }
        
        // Always request Firebase messaging permission
        const authStatus = await messaging().requestPermission();
        
        const enabled =
          authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
          authStatus === messaging.AuthorizationStatus.PROVISIONAL;

        if (enabled) {
          console.log('Android Authorization status:', authStatus);
          
          // Add a small delay after permission is granted for Google Play Services
          await new Promise(resolve => setTimeout(resolve, 1000));
          console.log('Added delay after permission grant to ensure Google Play Services connection');
        } else {
          console.log('Android authorization not granted:', authStatus);
        }

        return enabled;
      } catch (error) {
        console.error('Error requesting Android notification permissions:', error);
        
        // Special handling for older Android versions without explicit permission requirements
        console.log('Continuing without explicit permissions due to error - may still work on older Android versions');
        return true;
      }
    }
    
    return false;
  } catch (error) {
    console.error('Error in requestUserPermission:', error);
    return false;
  }
};

export const getFCMToken = async (retryCount = 0): Promise<string | null> => {
  try {
    console.log('Starting FCM token retrieval...');
    
    // Double check Firebase is initialized properly
    if (!isFirebaseReady()) {
      console.log('Initializing Firebase first...');
      await initializeFirebase();
      
      // Wait a moment to ensure initialization is complete
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // For Android, check if Google Play Services is available
    if (Platform.OS === 'android') {
      const playServicesAvailable = await checkPlayServicesAvailability();
      if (!playServicesAvailable) {
        console.log('Warning: Google Play Services may not be available');
      }
    }
    
    // For iOS, make sure we're registered for remote notifications
    if (Platform.OS === 'ios') {
      const isRegistered = await messaging().isDeviceRegisteredForRemoteMessages;
      if (!isRegistered) {
        console.log('Registering iOS device for remote messages');
        await messaging().registerDeviceForRemoteMessages();
      }
    }
    
    // Check for existing token
    const existingToken = await AsyncStorage.getItem(FCM_TOKEN_KEY);
    if (existingToken) {
      console.log('Using existing FCM token:', existingToken);
      return existingToken;
    }
    
    // For Android, check if we need to wait before requesting token
    // This helps with the SERVICE_NOT_AVAILABLE error
    if (Platform.OS === 'android' && retryCount > 0) {
      const delayTime = retryCount * 2000; // Exponential backoff
      console.log(`Adding delay before retry: ${delayTime}ms`);
      await new Promise(resolve => setTimeout(resolve, delayTime));
    }
    
    // Request a new FCM token directly
    console.log(`Requesting new FCM token on ${Platform.OS}...`);
    const fcmToken = await messaging().getToken();
    
    if (fcmToken) {
      console.log('New FCM token generated:', fcmToken.substring(0, 10) + '...');
      await AsyncStorage.setItem(FCM_TOKEN_KEY, fcmToken);
      return fcmToken;
    } else {
      console.error('Failed to get FCM token - empty result');
      
      // Retry logic
      if (retryCount < 3) { // Increased max retries
        console.log(`Retrying FCM token retrieval (attempt ${retryCount + 1})...`);
        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
        return getFCMToken(retryCount + 1);
      }
      
      return null;
    }
  } catch (error) {
    console.error(`FCM token error (${Platform.OS}):`, error);
    
    // Check for specific SERVICE_NOT_AVAILABLE error
    const errorMessage = String(error);
    const isServiceUnavailable = errorMessage.includes('SERVICE_NOT_AVAILABLE');
    
    if (isServiceUnavailable) {
      console.log('Detected SERVICE_NOT_AVAILABLE error - this usually means Google Play Services connectivity issue');
      
      // Use longer delay for this specific error
      const serviceUnavailableDelay = 5000 + (retryCount * 2000);
      console.log(`Waiting ${serviceUnavailableDelay}ms before retrying...`);
      await new Promise(resolve => setTimeout(resolve, serviceUnavailableDelay));
      
      // Retry with increased backoff for SERVICE_NOT_AVAILABLE
      if (retryCount < 5) { // More retries for this specific error
        console.log(`Special SERVICE_NOT_AVAILABLE retry (attempt ${retryCount + 1})...`);
        return getFCMToken(retryCount + 1);
      }
    } else {
      // Regular retry logic for other errors
      if (retryCount < 3) {
        console.log(`Retrying FCM token retrieval after error (attempt ${retryCount + 1})...`);
        await new Promise(resolve => setTimeout(resolve, 2000 * (retryCount + 1)));
        return getFCMToken(retryCount + 1);
      }
    }
    
    return null;
  }
};

export const notificationListener = async () => {
  try {
    // Listen for notification clicks in background
    messaging().onNotificationOpenedApp(remoteMessage => {
      console.log(
        'Notification caused app to open from background state:',
        remoteMessage.notification,
      );
    });

    // Check whether an initial notification is available
    messaging()
      .getInitialNotification()
      .then(remoteMessage => {
        if (remoteMessage) {
          console.log(
            'Notification caused app to open from quit state:',
            remoteMessage.notification,
          );
        }
      });

    // Foreground message handling
    messaging().onMessage(async remoteMessage => {
      console.log('Received foreground message:', remoteMessage);

      // For both platforms, use Expo Notifications for foreground notifications
      await Notifications.scheduleNotificationAsync({
        content: {
          title: remoteMessage.notification?.title || '',
          body: remoteMessage.notification?.body || '',
          data: remoteMessage.data,
        },
        trigger: null,
      });
    });
  } catch (error) {
    console.error('Error setting up notification listeners:', error);
  }
};

// Configure Expo notifications
const configureExpoNotifications = async () => {
  await Notifications.setNotificationHandler({
    handleNotification: async () => ({
      shouldShowAlert: true,
      shouldPlaySound: true,
      shouldSetBadge: true,
    }),
  });
};

export const setupNotifications = async () => {
  try {
    console.log(`Setting up notifications on ${Platform.OS}`);
    
    // Make sure Firebase is initialized first - with retries
    let maxRetries = 3;
    let retryCount = 0;
    let firebaseInitialized = false;
    
    while (!firebaseInitialized && retryCount < maxRetries) {
      try {
        if (!isFirebaseReady()) {
          console.log(`Firebase not initialized yet, initializing now (attempt ${retryCount + 1})...`);
          await initializeFirebase();
          
          // Short delay to ensure initialization completes
          await new Promise(resolve => setTimeout(resolve, 500));
        }
        
        // Verify Firebase is initialized
        if (firebase.apps.length > 0) {
          firebaseInitialized = true;
          console.log('Firebase app initialized successfully');
        } else {
          throw new Error("Firebase apps array is empty after initialization");
        }
      } catch (initError) {
        retryCount++;
        console.error(`Firebase initialization attempt ${retryCount} failed:`, initError);
        
        // Wait longer between retries
        if (retryCount < maxRetries) {
          console.log(`Waiting before retry ${retryCount + 1}...`);
          await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
        }
      }
    }

    // If all retries failed, throw error
    if (!firebaseInitialized) {
      throw new Error("Firebase initialization failed after multiple attempts");
    }

    console.log('Firebase app initialized, setting up notifications');

    // Configure Expo notifications
    await configureExpoNotifications();

    // Request permissions and set up listeners
    const hasPermission = await requestUserPermission();
    if (hasPermission) {
      // Get FCM token with retries
      let token = null;
      let tokenRetries = 0;
      let serviceNotAvailable = false;
      
      while (!token && tokenRetries < 5) { // Increased from 3 to 5 retries
        try {
          token = await getFCMToken(tokenRetries);
          console.log('FCM token obtained:', token ? 'yes' : 'no');
          
          if (!token) {
            // Add increasing delay between retries
            const delayMs = 2000 * (tokenRetries + 1);
            console.log(`No token yet, waiting ${delayMs}ms before retry...`);
            await new Promise(resolve => setTimeout(resolve, delayMs));
          }
        } catch (tokenError) {
          tokenRetries++;
          console.error(`Error getting FCM token (attempt ${tokenRetries}):`, tokenError);
          
          // Check if it's SERVICE_NOT_AVAILABLE error
          if (String(tokenError).includes('SERVICE_NOT_AVAILABLE')) {
            serviceNotAvailable = true;
            console.log('SERVICE_NOT_AVAILABLE detected - may be Google Play Services issue');
            
            // Add longer delay for this specific error type
            const serviceErrorDelay = 5000 + (tokenRetries * 3000);
            console.log(`Waiting ${serviceErrorDelay}ms before retry...`);
            await new Promise(resolve => setTimeout(resolve, serviceErrorDelay));
          } else {
            // Normal delay for other errors
            if (tokenRetries < 5) {
              const normalErrorDelay = 2000 * tokenRetries;
              console.log(`Waiting ${normalErrorDelay}ms before retry...`);
              await new Promise(resolve => setTimeout(resolve, normalErrorDelay));
            }
          }
        }
      }
      
      // Even if token retrieval failed, continue with notification listeners
      // because notifications might still work with server-side registration
      if (!token && serviceNotAvailable) {
        console.log('WARNING: Could not get FCM token due to SERVICE_NOT_AVAILABLE error');
        console.log('Notifications may still work when app is in foreground');
      }
      
      // Set up notification listeners
      await notificationListener();
      console.log('Notification setup complete');
      
      return hasPermission;
    } else {
      console.log('Notification permissions not granted');
      return false;
    }
  } catch (error) {
    console.error('Failed to initialize notifications:', error);
    throw error; // Re-throw to allow proper error handling
  }
};