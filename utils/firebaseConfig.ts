import { Platform } from 'react-native';
import firebase from '@react-native-firebase/app';

// Track initialization status
let isFirebaseInitialized = false;

// Define Firebase config with explicit values
const FIREBASE_CONFIG = {
  apiKey: "AIzaSyCh5gE623XX2PvCGIBOxJpqzKBicZNHVek",
  projectId: "salescrm-f2511",
  appId: "1:880620206085:android:d6697f8f77f46b66b3c46c",
  messagingSenderId: "880620206085",
  databaseURL: "https://salescrm-f2511.firebaseio.com",
  storageBucket: "salescrm-f2511.appspot.com"
};

// Initialize Firebase if it hasn't been initialized yet
export const initializeFirebase = async () => {
  // If already initialized, return existing app
  if (isFirebaseInitialized && firebase.apps.length > 0) {
    console.log('Firebase already initialized, returning existing app');
    return firebase.app();
  }

  // Clear existing apps if there are any issues
  try {
    // Clean up any existing app instances first
    if (firebase.apps.length > 0) {
      await Promise.all(firebase.apps.map(app => app.delete()));
      console.log('Deleted existing Firebase apps');
    }
    
    console.log(`Initializing Firebase app with direct config on ${Platform.OS}`);
    
    // Direct initialization with explicit config
    const app = firebase.initializeApp(FIREBASE_CONFIG);
    isFirebaseInitialized = true;
    console.log('Firebase initialized successfully with explicit config');
    return app;
  } catch (error) {
    console.error('Error initializing Firebase:', error);
    throw error;
  }
};

// Check if Firebase is initialized
export const isFirebaseReady = () => {
  const ready = isFirebaseInitialized || firebase.apps.length > 0;
  console.log(`Firebase ready status: ${ready}`);
  return ready;
};

export default firebase;
