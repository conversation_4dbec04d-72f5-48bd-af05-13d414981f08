/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/CreateAccounts`; params?: Router.UnknownInputParams; } | { pathname: `/CreateDeals`; params?: Router.UnknownInputParams; } | { pathname: `/DealsAddProduct`; params?: Router.UnknownInputParams; } | { pathname: `/DealsAddQuote`; params?: Router.UnknownInputParams; } | { pathname: `/DealsProductList`; params?: Router.UnknownInputParams; } | { pathname: `/DealsQuoteAddProduct`; params?: Router.UnknownInputParams; } | { pathname: `/DealsQuoteEdit`; params?: Router.UnknownInputParams; } | { pathname: `/DealsQuoteList`; params?: Router.UnknownInputParams; } | { pathname: `/DealsQuotesDetail`; params?: Router.UnknownInputParams; } | { pathname: `/EditDeal`; params?: Router.UnknownInputParams; } | { pathname: `/MonthlyCalendarView`; params?: Router.UnknownInputParams; } | { pathname: `/RelatedAccountsList`; params?: Router.UnknownInputParams; } | { pathname: `/RelatedContactsList`; params?: Router.UnknownInputParams; } | { pathname: `/RelatedDealsList`; params?: Router.UnknownInputParams; } | { pathname: `/SelectAccountForContact`; params?: Router.UnknownInputParams; } | { pathname: `/SelectContactForAccount`; params?: Router.UnknownInputParams; } | { pathname: `/SelectDealForContact`; params?: Router.UnknownInputParams; } | { pathname: `/TaskDetailScreen`; params?: Router.UnknownInputParams; } | { pathname: `/TaskListScreen`; params?: Router.UnknownInputParams; } | { pathname: `/UpdateAccounts`; params?: Router.UnknownInputParams; } | { pathname: `/UpdateDeals`; params?: Router.UnknownInputParams; } | { pathname: `/about`; params?: Router.UnknownInputParams; } | { pathname: `/accountDetail`; params?: Router.UnknownInputParams; } | { pathname: `/attendance-empty-state-demo`; params?: Router.UnknownInputParams; } | { pathname: `/attendance-storage-demo`; params?: Router.UnknownInputParams; } | { pathname: `/attendance-toggle-demo`; params?: Router.UnknownInputParams; } | { pathname: `/attendance-validation-demo`; params?: Router.UnknownInputParams; } | { pathname: `/attendance`; params?: Router.UnknownInputParams; } | { pathname: `/claim`; params?: Router.UnknownInputParams; } | { pathname: `/claimHistory`; params?: Router.UnknownInputParams; } | { pathname: `/contact`; params?: Router.UnknownInputParams; } | { pathname: `/contactDetail`; params?: Router.UnknownInputParams; } | { pathname: `/contacts_from_details`; params?: Router.UnknownInputParams; } | { pathname: `/contactus`; params?: Router.UnknownInputParams; } | { pathname: `/conveyance-empty-state-demo`; params?: Router.UnknownInputParams; } | { pathname: `/conveyance`; params?: Router.UnknownInputParams; } | { pathname: `/conveyanceDetails`; params?: Router.UnknownInputParams; } | { pathname: `/dealDetails`; params?: Router.UnknownInputParams; } | { pathname: `/deals`; params?: Router.UnknownInputParams; } | { pathname: `/fcm-test`; params?: Router.UnknownInputParams; } | { pathname: `/fcm-token-direct`; params?: Router.UnknownInputParams; } | { pathname: `/files`; params?: Router.UnknownInputParams; } | { pathname: `/forced-fcm`; params?: Router.UnknownInputParams; } | { pathname: `/imageViewer`; params?: Router.UnknownInputParams; } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/loading`; params?: Router.UnknownInputParams; } | { pathname: `/login`; params?: Router.UnknownInputParams; } | { pathname: `/map`; params?: Router.UnknownInputParams; } | { pathname: `/notes-add`; params?: Router.UnknownInputParams; } | { pathname: `/notifications`; params?: Router.UnknownInputParams; } | { pathname: `/privacy`; params?: Router.UnknownInputParams; } | { pathname: `/terms`; params?: Router.UnknownInputParams; } | { pathname: `/testNavigation`; params?: Router.UnknownInputParams; } | { pathname: `/updateClaim`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/contacts` | `/contacts`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/distributors` | `/distributors`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/orders` | `/orders`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/plus` | `/plus`; params?: Router.UnknownInputParams; } | { pathname: `/components/Calendar`; params?: Router.UnknownInputParams; } | { pathname: `/components/InAppNotification`; params?: Router.UnknownInputParams; } | { pathname: `/components/MarqueeText`; params?: Router.UnknownInputParams; } | { pathname: `/components/RecordsPopup`; params?: Router.UnknownInputParams; } | { pathname: `/components/TransportModeModal`; params?: Router.UnknownInputParams; } | { pathname: `/config/api`; params?: Router.UnknownInputParams; } | { pathname: `/conveyanceMap/view`; params?: Router.UnknownInputParams; } | { pathname: `/dynamic_fields/DynamicForm`; params?: Router.UnknownInputParams; } | { pathname: `/dynamic_fields/DynamicFormField`; params?: Router.UnknownInputParams; } | { pathname: `/hooks/useConveyance`; params?: Router.UnknownInputParams; } | { pathname: `/meetings/createMeeting`; params?: Router.UnknownInputParams; } | { pathname: `/meetings/detailedMeeting`; params?: Router.UnknownInputParams; } | { pathname: `/meetings/editMeeting`; params?: Router.UnknownInputParams; } | { pathname: `/meetings/meetingList`; params?: Router.UnknownInputParams; } | { pathname: `/models/Account`; params?: Router.UnknownInputParams; } | { pathname: `/models/AccountField`; params?: Router.UnknownInputParams; } | { pathname: `/models/Attendance`; params?: Router.UnknownInputParams; } | { pathname: `/models/Claim`; params?: Router.UnknownInputParams; } | { pathname: `/models/ClaimHistory`; params?: Router.UnknownInputParams; } | { pathname: `/models/Contact`; params?: Router.UnknownInputParams; } | { pathname: `/models/Conveyance`; params?: Router.UnknownInputParams; } | { pathname: `/models/Deal`; params?: Router.UnknownInputParams; } | { pathname: `/models/DealField`; params?: Router.UnknownInputParams; } | { pathname: `/models/DealsProductsListModel`; params?: Router.UnknownInputParams; } | { pathname: `/models/DynamicField`; params?: Router.UnknownInputParams; } | { pathname: `/models/Filter`; params?: Router.UnknownInputParams; } | { pathname: `/models/Meeting`; params?: Router.UnknownInputParams; } | { pathname: `/models/ModelProductDetails`; params?: Router.UnknownInputParams; } | { pathname: `/models/ModelProductFileOption`; params?: Router.UnknownInputParams; } | { pathname: `/models/ModelProductFilterFields`; params?: Router.UnknownInputParams; } | { pathname: `/models/ModelShareWithUsers`; params?: Router.UnknownInputParams; } | { pathname: `/models/NotificationModel`; params?: Router.UnknownInputParams; } | { pathname: `/models/ProductDetailsModel`; params?: Router.UnknownInputParams; } | { pathname: `/models/ProductListModel`; params?: Router.UnknownInputParams; } | { pathname: `/models/QuoteField`; params?: Router.UnknownInputParams; } | { pathname: `/models/Task`; params?: Router.UnknownInputParams; } | { pathname: `/models/contact_fields_model`; params?: Router.UnknownInputParams; } | { pathname: `/models/home_page_model`; params?: Router.UnknownInputParams; } | { pathname: `/models/model_account_details`; params?: Router.UnknownInputParams; } | { pathname: `/models/model_accounts_list`; params?: Router.UnknownInputParams; } | { pathname: `/models/model_meeting_list`; params?: Router.UnknownInputParams; } | { pathname: `/models/model_single_contact_details`; params?: Router.UnknownInputParams; } | { pathname: `/models/model_task_type_outcome`; params?: Router.UnknownInputParams; } | { pathname: `/models/notesListModel`; params?: Router.UnknownInputParams; } | { pathname: `/services/ConveyanceService`; params?: Router.UnknownInputParams; } | { pathname: `/tasks/create`; params?: Router.UnknownInputParams; } | { pathname: `/types/product`; params?: Router.UnknownInputParams; } | { pathname: `/ui/utils`; params?: Router.UnknownInputParams; } | { pathname: `/ui/components/EditNoteModal`; params?: Router.UnknownInputParams; } | { pathname: `/ui/helpers/AddNotesPage`; params?: Router.UnknownInputParams; } | { pathname: `/ui/helpers/common_styles`; params?: Router.UnknownInputParams; } | { pathname: `/ui/product/AddPricePage`; params?: Router.UnknownInputParams; } | { pathname: `/ui/product/AddProductPage`; params?: Router.UnknownInputParams; } | { pathname: `/ui/product/FileUploadScreen`; params?: Router.UnknownInputParams; } | { pathname: `/ui/product/ProductDetailsPage`; params?: Router.UnknownInputParams; } | { pathname: `/ui/product/ProductFiltersPage`; params?: Router.UnknownInputParams; } | { pathname: `/ui/product/ProductListPage`; params?: Router.UnknownInputParams; } | { pathname: `/ui/product/save-view`; params?: Router.UnknownInputParams; } | { pathname: `/ui/screens/NotesListScreen`; params?: Router.UnknownInputParams; } | { pathname: `/ui/settings/SettingsPage`; params?: Router.UnknownInputParams; } | { pathname: `/ui/settings/about`; params?: Router.UnknownInputParams; } | { pathname: `/ui/settings/alerts`; params?: Router.UnknownInputParams; } | { pathname: `/ui/settings/assignment`; params?: Router.UnknownInputParams; } | { pathname: `/ui/settings/call-preferences`; params?: Router.UnknownInputParams; } | { pathname: `/ui/settings/daily-digest`; params?: Router.UnknownInputParams; } | { pathname: `/ui/settings/email-tracking`; params?: Router.UnknownInputParams; } | { pathname: `/ui/settings/notifications`; params?: Router.UnknownInputParams; } | { pathname: `/ui/settings/offline-preferences`; params?: Router.UnknownInputParams; } | { pathname: `/ui/settings/reminders`; params?: Router.UnknownInputParams; };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/CreateAccounts`; params?: Router.UnknownOutputParams; } | { pathname: `/CreateDeals`; params?: Router.UnknownOutputParams; } | { pathname: `/DealsAddProduct`; params?: Router.UnknownOutputParams; } | { pathname: `/DealsAddQuote`; params?: Router.UnknownOutputParams; } | { pathname: `/DealsProductList`; params?: Router.UnknownOutputParams; } | { pathname: `/DealsQuoteAddProduct`; params?: Router.UnknownOutputParams; } | { pathname: `/DealsQuoteEdit`; params?: Router.UnknownOutputParams; } | { pathname: `/DealsQuoteList`; params?: Router.UnknownOutputParams; } | { pathname: `/DealsQuotesDetail`; params?: Router.UnknownOutputParams; } | { pathname: `/EditDeal`; params?: Router.UnknownOutputParams; } | { pathname: `/MonthlyCalendarView`; params?: Router.UnknownOutputParams; } | { pathname: `/RelatedAccountsList`; params?: Router.UnknownOutputParams; } | { pathname: `/RelatedContactsList`; params?: Router.UnknownOutputParams; } | { pathname: `/RelatedDealsList`; params?: Router.UnknownOutputParams; } | { pathname: `/SelectAccountForContact`; params?: Router.UnknownOutputParams; } | { pathname: `/SelectContactForAccount`; params?: Router.UnknownOutputParams; } | { pathname: `/SelectDealForContact`; params?: Router.UnknownOutputParams; } | { pathname: `/TaskDetailScreen`; params?: Router.UnknownOutputParams; } | { pathname: `/TaskListScreen`; params?: Router.UnknownOutputParams; } | { pathname: `/UpdateAccounts`; params?: Router.UnknownOutputParams; } | { pathname: `/UpdateDeals`; params?: Router.UnknownOutputParams; } | { pathname: `/about`; params?: Router.UnknownOutputParams; } | { pathname: `/accountDetail`; params?: Router.UnknownOutputParams; } | { pathname: `/attendance-empty-state-demo`; params?: Router.UnknownOutputParams; } | { pathname: `/attendance-storage-demo`; params?: Router.UnknownOutputParams; } | { pathname: `/attendance-toggle-demo`; params?: Router.UnknownOutputParams; } | { pathname: `/attendance-validation-demo`; params?: Router.UnknownOutputParams; } | { pathname: `/attendance`; params?: Router.UnknownOutputParams; } | { pathname: `/claim`; params?: Router.UnknownOutputParams; } | { pathname: `/claimHistory`; params?: Router.UnknownOutputParams; } | { pathname: `/contact`; params?: Router.UnknownOutputParams; } | { pathname: `/contactDetail`; params?: Router.UnknownOutputParams; } | { pathname: `/contacts_from_details`; params?: Router.UnknownOutputParams; } | { pathname: `/contactus`; params?: Router.UnknownOutputParams; } | { pathname: `/conveyance-empty-state-demo`; params?: Router.UnknownOutputParams; } | { pathname: `/conveyance`; params?: Router.UnknownOutputParams; } | { pathname: `/conveyanceDetails`; params?: Router.UnknownOutputParams; } | { pathname: `/dealDetails`; params?: Router.UnknownOutputParams; } | { pathname: `/deals`; params?: Router.UnknownOutputParams; } | { pathname: `/fcm-test`; params?: Router.UnknownOutputParams; } | { pathname: `/fcm-token-direct`; params?: Router.UnknownOutputParams; } | { pathname: `/files`; params?: Router.UnknownOutputParams; } | { pathname: `/forced-fcm`; params?: Router.UnknownOutputParams; } | { pathname: `/imageViewer`; params?: Router.UnknownOutputParams; } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/loading`; params?: Router.UnknownOutputParams; } | { pathname: `/login`; params?: Router.UnknownOutputParams; } | { pathname: `/map`; params?: Router.UnknownOutputParams; } | { pathname: `/notes-add`; params?: Router.UnknownOutputParams; } | { pathname: `/notifications`; params?: Router.UnknownOutputParams; } | { pathname: `/privacy`; params?: Router.UnknownOutputParams; } | { pathname: `/terms`; params?: Router.UnknownOutputParams; } | { pathname: `/testNavigation`; params?: Router.UnknownOutputParams; } | { pathname: `/updateClaim`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/contacts` | `/contacts`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/distributors` | `/distributors`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/orders` | `/orders`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/plus` | `/plus`; params?: Router.UnknownOutputParams; } | { pathname: `/components/Calendar`; params?: Router.UnknownOutputParams; } | { pathname: `/components/InAppNotification`; params?: Router.UnknownOutputParams; } | { pathname: `/components/MarqueeText`; params?: Router.UnknownOutputParams; } | { pathname: `/components/RecordsPopup`; params?: Router.UnknownOutputParams; } | { pathname: `/components/TransportModeModal`; params?: Router.UnknownOutputParams; } | { pathname: `/config/api`; params?: Router.UnknownOutputParams; } | { pathname: `/conveyanceMap/view`; params?: Router.UnknownOutputParams; } | { pathname: `/dynamic_fields/DynamicForm`; params?: Router.UnknownOutputParams; } | { pathname: `/dynamic_fields/DynamicFormField`; params?: Router.UnknownOutputParams; } | { pathname: `/hooks/useConveyance`; params?: Router.UnknownOutputParams; } | { pathname: `/meetings/createMeeting`; params?: Router.UnknownOutputParams; } | { pathname: `/meetings/detailedMeeting`; params?: Router.UnknownOutputParams; } | { pathname: `/meetings/editMeeting`; params?: Router.UnknownOutputParams; } | { pathname: `/meetings/meetingList`; params?: Router.UnknownOutputParams; } | { pathname: `/models/Account`; params?: Router.UnknownOutputParams; } | { pathname: `/models/AccountField`; params?: Router.UnknownOutputParams; } | { pathname: `/models/Attendance`; params?: Router.UnknownOutputParams; } | { pathname: `/models/Claim`; params?: Router.UnknownOutputParams; } | { pathname: `/models/ClaimHistory`; params?: Router.UnknownOutputParams; } | { pathname: `/models/Contact`; params?: Router.UnknownOutputParams; } | { pathname: `/models/Conveyance`; params?: Router.UnknownOutputParams; } | { pathname: `/models/Deal`; params?: Router.UnknownOutputParams; } | { pathname: `/models/DealField`; params?: Router.UnknownOutputParams; } | { pathname: `/models/DealsProductsListModel`; params?: Router.UnknownOutputParams; } | { pathname: `/models/DynamicField`; params?: Router.UnknownOutputParams; } | { pathname: `/models/Filter`; params?: Router.UnknownOutputParams; } | { pathname: `/models/Meeting`; params?: Router.UnknownOutputParams; } | { pathname: `/models/ModelProductDetails`; params?: Router.UnknownOutputParams; } | { pathname: `/models/ModelProductFileOption`; params?: Router.UnknownOutputParams; } | { pathname: `/models/ModelProductFilterFields`; params?: Router.UnknownOutputParams; } | { pathname: `/models/ModelShareWithUsers`; params?: Router.UnknownOutputParams; } | { pathname: `/models/NotificationModel`; params?: Router.UnknownOutputParams; } | { pathname: `/models/ProductDetailsModel`; params?: Router.UnknownOutputParams; } | { pathname: `/models/ProductListModel`; params?: Router.UnknownOutputParams; } | { pathname: `/models/QuoteField`; params?: Router.UnknownOutputParams; } | { pathname: `/models/Task`; params?: Router.UnknownOutputParams; } | { pathname: `/models/contact_fields_model`; params?: Router.UnknownOutputParams; } | { pathname: `/models/home_page_model`; params?: Router.UnknownOutputParams; } | { pathname: `/models/model_account_details`; params?: Router.UnknownOutputParams; } | { pathname: `/models/model_accounts_list`; params?: Router.UnknownOutputParams; } | { pathname: `/models/model_meeting_list`; params?: Router.UnknownOutputParams; } | { pathname: `/models/model_single_contact_details`; params?: Router.UnknownOutputParams; } | { pathname: `/models/model_task_type_outcome`; params?: Router.UnknownOutputParams; } | { pathname: `/models/notesListModel`; params?: Router.UnknownOutputParams; } | { pathname: `/services/ConveyanceService`; params?: Router.UnknownOutputParams; } | { pathname: `/tasks/create`; params?: Router.UnknownOutputParams; } | { pathname: `/types/product`; params?: Router.UnknownOutputParams; } | { pathname: `/ui/utils`; params?: Router.UnknownOutputParams; } | { pathname: `/ui/components/EditNoteModal`; params?: Router.UnknownOutputParams; } | { pathname: `/ui/helpers/AddNotesPage`; params?: Router.UnknownOutputParams; } | { pathname: `/ui/helpers/common_styles`; params?: Router.UnknownOutputParams; } | { pathname: `/ui/product/AddPricePage`; params?: Router.UnknownOutputParams; } | { pathname: `/ui/product/AddProductPage`; params?: Router.UnknownOutputParams; } | { pathname: `/ui/product/FileUploadScreen`; params?: Router.UnknownOutputParams; } | { pathname: `/ui/product/ProductDetailsPage`; params?: Router.UnknownOutputParams; } | { pathname: `/ui/product/ProductFiltersPage`; params?: Router.UnknownOutputParams; } | { pathname: `/ui/product/ProductListPage`; params?: Router.UnknownOutputParams; } | { pathname: `/ui/product/save-view`; params?: Router.UnknownOutputParams; } | { pathname: `/ui/screens/NotesListScreen`; params?: Router.UnknownOutputParams; } | { pathname: `/ui/settings/SettingsPage`; params?: Router.UnknownOutputParams; } | { pathname: `/ui/settings/about`; params?: Router.UnknownOutputParams; } | { pathname: `/ui/settings/alerts`; params?: Router.UnknownOutputParams; } | { pathname: `/ui/settings/assignment`; params?: Router.UnknownOutputParams; } | { pathname: `/ui/settings/call-preferences`; params?: Router.UnknownOutputParams; } | { pathname: `/ui/settings/daily-digest`; params?: Router.UnknownOutputParams; } | { pathname: `/ui/settings/email-tracking`; params?: Router.UnknownOutputParams; } | { pathname: `/ui/settings/notifications`; params?: Router.UnknownOutputParams; } | { pathname: `/ui/settings/offline-preferences`; params?: Router.UnknownOutputParams; } | { pathname: `/ui/settings/reminders`; params?: Router.UnknownOutputParams; };
      href: Router.RelativePathString | Router.ExternalPathString | `/CreateAccounts${`?${string}` | `#${string}` | ''}` | `/CreateDeals${`?${string}` | `#${string}` | ''}` | `/DealsAddProduct${`?${string}` | `#${string}` | ''}` | `/DealsAddQuote${`?${string}` | `#${string}` | ''}` | `/DealsProductList${`?${string}` | `#${string}` | ''}` | `/DealsQuoteAddProduct${`?${string}` | `#${string}` | ''}` | `/DealsQuoteEdit${`?${string}` | `#${string}` | ''}` | `/DealsQuoteList${`?${string}` | `#${string}` | ''}` | `/DealsQuotesDetail${`?${string}` | `#${string}` | ''}` | `/EditDeal${`?${string}` | `#${string}` | ''}` | `/MonthlyCalendarView${`?${string}` | `#${string}` | ''}` | `/RelatedAccountsList${`?${string}` | `#${string}` | ''}` | `/RelatedContactsList${`?${string}` | `#${string}` | ''}` | `/RelatedDealsList${`?${string}` | `#${string}` | ''}` | `/SelectAccountForContact${`?${string}` | `#${string}` | ''}` | `/SelectContactForAccount${`?${string}` | `#${string}` | ''}` | `/SelectDealForContact${`?${string}` | `#${string}` | ''}` | `/TaskDetailScreen${`?${string}` | `#${string}` | ''}` | `/TaskListScreen${`?${string}` | `#${string}` | ''}` | `/UpdateAccounts${`?${string}` | `#${string}` | ''}` | `/UpdateDeals${`?${string}` | `#${string}` | ''}` | `/about${`?${string}` | `#${string}` | ''}` | `/accountDetail${`?${string}` | `#${string}` | ''}` | `/attendance-empty-state-demo${`?${string}` | `#${string}` | ''}` | `/attendance-storage-demo${`?${string}` | `#${string}` | ''}` | `/attendance-toggle-demo${`?${string}` | `#${string}` | ''}` | `/attendance-validation-demo${`?${string}` | `#${string}` | ''}` | `/attendance${`?${string}` | `#${string}` | ''}` | `/claim${`?${string}` | `#${string}` | ''}` | `/claimHistory${`?${string}` | `#${string}` | ''}` | `/contact${`?${string}` | `#${string}` | ''}` | `/contactDetail${`?${string}` | `#${string}` | ''}` | `/contacts_from_details${`?${string}` | `#${string}` | ''}` | `/contactus${`?${string}` | `#${string}` | ''}` | `/conveyance-empty-state-demo${`?${string}` | `#${string}` | ''}` | `/conveyance${`?${string}` | `#${string}` | ''}` | `/conveyanceDetails${`?${string}` | `#${string}` | ''}` | `/dealDetails${`?${string}` | `#${string}` | ''}` | `/deals${`?${string}` | `#${string}` | ''}` | `/fcm-test${`?${string}` | `#${string}` | ''}` | `/fcm-token-direct${`?${string}` | `#${string}` | ''}` | `/files${`?${string}` | `#${string}` | ''}` | `/forced-fcm${`?${string}` | `#${string}` | ''}` | `/imageViewer${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `/loading${`?${string}` | `#${string}` | ''}` | `/login${`?${string}` | `#${string}` | ''}` | `/map${`?${string}` | `#${string}` | ''}` | `/notes-add${`?${string}` | `#${string}` | ''}` | `/notifications${`?${string}` | `#${string}` | ''}` | `/privacy${`?${string}` | `#${string}` | ''}` | `/terms${`?${string}` | `#${string}` | ''}` | `/testNavigation${`?${string}` | `#${string}` | ''}` | `/updateClaim${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/contacts${`?${string}` | `#${string}` | ''}` | `/contacts${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/distributors${`?${string}` | `#${string}` | ''}` | `/distributors${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/orders${`?${string}` | `#${string}` | ''}` | `/orders${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/plus${`?${string}` | `#${string}` | ''}` | `/plus${`?${string}` | `#${string}` | ''}` | `/components/Calendar${`?${string}` | `#${string}` | ''}` | `/components/InAppNotification${`?${string}` | `#${string}` | ''}` | `/components/MarqueeText${`?${string}` | `#${string}` | ''}` | `/components/RecordsPopup${`?${string}` | `#${string}` | ''}` | `/components/TransportModeModal${`?${string}` | `#${string}` | ''}` | `/config/api${`?${string}` | `#${string}` | ''}` | `/conveyanceMap/view${`?${string}` | `#${string}` | ''}` | `/dynamic_fields/DynamicForm${`?${string}` | `#${string}` | ''}` | `/dynamic_fields/DynamicFormField${`?${string}` | `#${string}` | ''}` | `/hooks/useConveyance${`?${string}` | `#${string}` | ''}` | `/meetings/createMeeting${`?${string}` | `#${string}` | ''}` | `/meetings/detailedMeeting${`?${string}` | `#${string}` | ''}` | `/meetings/editMeeting${`?${string}` | `#${string}` | ''}` | `/meetings/meetingList${`?${string}` | `#${string}` | ''}` | `/models/Account${`?${string}` | `#${string}` | ''}` | `/models/AccountField${`?${string}` | `#${string}` | ''}` | `/models/Attendance${`?${string}` | `#${string}` | ''}` | `/models/Claim${`?${string}` | `#${string}` | ''}` | `/models/ClaimHistory${`?${string}` | `#${string}` | ''}` | `/models/Contact${`?${string}` | `#${string}` | ''}` | `/models/Conveyance${`?${string}` | `#${string}` | ''}` | `/models/Deal${`?${string}` | `#${string}` | ''}` | `/models/DealField${`?${string}` | `#${string}` | ''}` | `/models/DealsProductsListModel${`?${string}` | `#${string}` | ''}` | `/models/DynamicField${`?${string}` | `#${string}` | ''}` | `/models/Filter${`?${string}` | `#${string}` | ''}` | `/models/Meeting${`?${string}` | `#${string}` | ''}` | `/models/ModelProductDetails${`?${string}` | `#${string}` | ''}` | `/models/ModelProductFileOption${`?${string}` | `#${string}` | ''}` | `/models/ModelProductFilterFields${`?${string}` | `#${string}` | ''}` | `/models/ModelShareWithUsers${`?${string}` | `#${string}` | ''}` | `/models/NotificationModel${`?${string}` | `#${string}` | ''}` | `/models/ProductDetailsModel${`?${string}` | `#${string}` | ''}` | `/models/ProductListModel${`?${string}` | `#${string}` | ''}` | `/models/QuoteField${`?${string}` | `#${string}` | ''}` | `/models/Task${`?${string}` | `#${string}` | ''}` | `/models/contact_fields_model${`?${string}` | `#${string}` | ''}` | `/models/home_page_model${`?${string}` | `#${string}` | ''}` | `/models/model_account_details${`?${string}` | `#${string}` | ''}` | `/models/model_accounts_list${`?${string}` | `#${string}` | ''}` | `/models/model_meeting_list${`?${string}` | `#${string}` | ''}` | `/models/model_single_contact_details${`?${string}` | `#${string}` | ''}` | `/models/model_task_type_outcome${`?${string}` | `#${string}` | ''}` | `/models/notesListModel${`?${string}` | `#${string}` | ''}` | `/services/ConveyanceService${`?${string}` | `#${string}` | ''}` | `/tasks/create${`?${string}` | `#${string}` | ''}` | `/types/product${`?${string}` | `#${string}` | ''}` | `/ui/utils${`?${string}` | `#${string}` | ''}` | `/ui/components/EditNoteModal${`?${string}` | `#${string}` | ''}` | `/ui/helpers/AddNotesPage${`?${string}` | `#${string}` | ''}` | `/ui/helpers/common_styles${`?${string}` | `#${string}` | ''}` | `/ui/product/AddPricePage${`?${string}` | `#${string}` | ''}` | `/ui/product/AddProductPage${`?${string}` | `#${string}` | ''}` | `/ui/product/FileUploadScreen${`?${string}` | `#${string}` | ''}` | `/ui/product/ProductDetailsPage${`?${string}` | `#${string}` | ''}` | `/ui/product/ProductFiltersPage${`?${string}` | `#${string}` | ''}` | `/ui/product/ProductListPage${`?${string}` | `#${string}` | ''}` | `/ui/product/save-view${`?${string}` | `#${string}` | ''}` | `/ui/screens/NotesListScreen${`?${string}` | `#${string}` | ''}` | `/ui/settings/SettingsPage${`?${string}` | `#${string}` | ''}` | `/ui/settings/about${`?${string}` | `#${string}` | ''}` | `/ui/settings/alerts${`?${string}` | `#${string}` | ''}` | `/ui/settings/assignment${`?${string}` | `#${string}` | ''}` | `/ui/settings/call-preferences${`?${string}` | `#${string}` | ''}` | `/ui/settings/daily-digest${`?${string}` | `#${string}` | ''}` | `/ui/settings/email-tracking${`?${string}` | `#${string}` | ''}` | `/ui/settings/notifications${`?${string}` | `#${string}` | ''}` | `/ui/settings/offline-preferences${`?${string}` | `#${string}` | ''}` | `/ui/settings/reminders${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/CreateAccounts`; params?: Router.UnknownInputParams; } | { pathname: `/CreateDeals`; params?: Router.UnknownInputParams; } | { pathname: `/DealsAddProduct`; params?: Router.UnknownInputParams; } | { pathname: `/DealsAddQuote`; params?: Router.UnknownInputParams; } | { pathname: `/DealsProductList`; params?: Router.UnknownInputParams; } | { pathname: `/DealsQuoteAddProduct`; params?: Router.UnknownInputParams; } | { pathname: `/DealsQuoteEdit`; params?: Router.UnknownInputParams; } | { pathname: `/DealsQuoteList`; params?: Router.UnknownInputParams; } | { pathname: `/DealsQuotesDetail`; params?: Router.UnknownInputParams; } | { pathname: `/EditDeal`; params?: Router.UnknownInputParams; } | { pathname: `/MonthlyCalendarView`; params?: Router.UnknownInputParams; } | { pathname: `/RelatedAccountsList`; params?: Router.UnknownInputParams; } | { pathname: `/RelatedContactsList`; params?: Router.UnknownInputParams; } | { pathname: `/RelatedDealsList`; params?: Router.UnknownInputParams; } | { pathname: `/SelectAccountForContact`; params?: Router.UnknownInputParams; } | { pathname: `/SelectContactForAccount`; params?: Router.UnknownInputParams; } | { pathname: `/SelectDealForContact`; params?: Router.UnknownInputParams; } | { pathname: `/TaskDetailScreen`; params?: Router.UnknownInputParams; } | { pathname: `/TaskListScreen`; params?: Router.UnknownInputParams; } | { pathname: `/UpdateAccounts`; params?: Router.UnknownInputParams; } | { pathname: `/UpdateDeals`; params?: Router.UnknownInputParams; } | { pathname: `/about`; params?: Router.UnknownInputParams; } | { pathname: `/accountDetail`; params?: Router.UnknownInputParams; } | { pathname: `/attendance-empty-state-demo`; params?: Router.UnknownInputParams; } | { pathname: `/attendance-storage-demo`; params?: Router.UnknownInputParams; } | { pathname: `/attendance-toggle-demo`; params?: Router.UnknownInputParams; } | { pathname: `/attendance-validation-demo`; params?: Router.UnknownInputParams; } | { pathname: `/attendance`; params?: Router.UnknownInputParams; } | { pathname: `/claim`; params?: Router.UnknownInputParams; } | { pathname: `/claimHistory`; params?: Router.UnknownInputParams; } | { pathname: `/contact`; params?: Router.UnknownInputParams; } | { pathname: `/contactDetail`; params?: Router.UnknownInputParams; } | { pathname: `/contacts_from_details`; params?: Router.UnknownInputParams; } | { pathname: `/contactus`; params?: Router.UnknownInputParams; } | { pathname: `/conveyance-empty-state-demo`; params?: Router.UnknownInputParams; } | { pathname: `/conveyance`; params?: Router.UnknownInputParams; } | { pathname: `/conveyanceDetails`; params?: Router.UnknownInputParams; } | { pathname: `/dealDetails`; params?: Router.UnknownInputParams; } | { pathname: `/deals`; params?: Router.UnknownInputParams; } | { pathname: `/fcm-test`; params?: Router.UnknownInputParams; } | { pathname: `/fcm-token-direct`; params?: Router.UnknownInputParams; } | { pathname: `/files`; params?: Router.UnknownInputParams; } | { pathname: `/forced-fcm`; params?: Router.UnknownInputParams; } | { pathname: `/imageViewer`; params?: Router.UnknownInputParams; } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/loading`; params?: Router.UnknownInputParams; } | { pathname: `/login`; params?: Router.UnknownInputParams; } | { pathname: `/map`; params?: Router.UnknownInputParams; } | { pathname: `/notes-add`; params?: Router.UnknownInputParams; } | { pathname: `/notifications`; params?: Router.UnknownInputParams; } | { pathname: `/privacy`; params?: Router.UnknownInputParams; } | { pathname: `/terms`; params?: Router.UnknownInputParams; } | { pathname: `/testNavigation`; params?: Router.UnknownInputParams; } | { pathname: `/updateClaim`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/contacts` | `/contacts`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/distributors` | `/distributors`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/orders` | `/orders`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/plus` | `/plus`; params?: Router.UnknownInputParams; } | { pathname: `/components/Calendar`; params?: Router.UnknownInputParams; } | { pathname: `/components/InAppNotification`; params?: Router.UnknownInputParams; } | { pathname: `/components/MarqueeText`; params?: Router.UnknownInputParams; } | { pathname: `/components/RecordsPopup`; params?: Router.UnknownInputParams; } | { pathname: `/components/TransportModeModal`; params?: Router.UnknownInputParams; } | { pathname: `/config/api`; params?: Router.UnknownInputParams; } | { pathname: `/conveyanceMap/view`; params?: Router.UnknownInputParams; } | { pathname: `/dynamic_fields/DynamicForm`; params?: Router.UnknownInputParams; } | { pathname: `/dynamic_fields/DynamicFormField`; params?: Router.UnknownInputParams; } | { pathname: `/hooks/useConveyance`; params?: Router.UnknownInputParams; } | { pathname: `/meetings/createMeeting`; params?: Router.UnknownInputParams; } | { pathname: `/meetings/detailedMeeting`; params?: Router.UnknownInputParams; } | { pathname: `/meetings/editMeeting`; params?: Router.UnknownInputParams; } | { pathname: `/meetings/meetingList`; params?: Router.UnknownInputParams; } | { pathname: `/models/Account`; params?: Router.UnknownInputParams; } | { pathname: `/models/AccountField`; params?: Router.UnknownInputParams; } | { pathname: `/models/Attendance`; params?: Router.UnknownInputParams; } | { pathname: `/models/Claim`; params?: Router.UnknownInputParams; } | { pathname: `/models/ClaimHistory`; params?: Router.UnknownInputParams; } | { pathname: `/models/Contact`; params?: Router.UnknownInputParams; } | { pathname: `/models/Conveyance`; params?: Router.UnknownInputParams; } | { pathname: `/models/Deal`; params?: Router.UnknownInputParams; } | { pathname: `/models/DealField`; params?: Router.UnknownInputParams; } | { pathname: `/models/DealsProductsListModel`; params?: Router.UnknownInputParams; } | { pathname: `/models/DynamicField`; params?: Router.UnknownInputParams; } | { pathname: `/models/Filter`; params?: Router.UnknownInputParams; } | { pathname: `/models/Meeting`; params?: Router.UnknownInputParams; } | { pathname: `/models/ModelProductDetails`; params?: Router.UnknownInputParams; } | { pathname: `/models/ModelProductFileOption`; params?: Router.UnknownInputParams; } | { pathname: `/models/ModelProductFilterFields`; params?: Router.UnknownInputParams; } | { pathname: `/models/ModelShareWithUsers`; params?: Router.UnknownInputParams; } | { pathname: `/models/NotificationModel`; params?: Router.UnknownInputParams; } | { pathname: `/models/ProductDetailsModel`; params?: Router.UnknownInputParams; } | { pathname: `/models/ProductListModel`; params?: Router.UnknownInputParams; } | { pathname: `/models/QuoteField`; params?: Router.UnknownInputParams; } | { pathname: `/models/Task`; params?: Router.UnknownInputParams; } | { pathname: `/models/contact_fields_model`; params?: Router.UnknownInputParams; } | { pathname: `/models/home_page_model`; params?: Router.UnknownInputParams; } | { pathname: `/models/model_account_details`; params?: Router.UnknownInputParams; } | { pathname: `/models/model_accounts_list`; params?: Router.UnknownInputParams; } | { pathname: `/models/model_meeting_list`; params?: Router.UnknownInputParams; } | { pathname: `/models/model_single_contact_details`; params?: Router.UnknownInputParams; } | { pathname: `/models/model_task_type_outcome`; params?: Router.UnknownInputParams; } | { pathname: `/models/notesListModel`; params?: Router.UnknownInputParams; } | { pathname: `/services/ConveyanceService`; params?: Router.UnknownInputParams; } | { pathname: `/tasks/create`; params?: Router.UnknownInputParams; } | { pathname: `/types/product`; params?: Router.UnknownInputParams; } | { pathname: `/ui/utils`; params?: Router.UnknownInputParams; } | { pathname: `/ui/components/EditNoteModal`; params?: Router.UnknownInputParams; } | { pathname: `/ui/helpers/AddNotesPage`; params?: Router.UnknownInputParams; } | { pathname: `/ui/helpers/common_styles`; params?: Router.UnknownInputParams; } | { pathname: `/ui/product/AddPricePage`; params?: Router.UnknownInputParams; } | { pathname: `/ui/product/AddProductPage`; params?: Router.UnknownInputParams; } | { pathname: `/ui/product/FileUploadScreen`; params?: Router.UnknownInputParams; } | { pathname: `/ui/product/ProductDetailsPage`; params?: Router.UnknownInputParams; } | { pathname: `/ui/product/ProductFiltersPage`; params?: Router.UnknownInputParams; } | { pathname: `/ui/product/ProductListPage`; params?: Router.UnknownInputParams; } | { pathname: `/ui/product/save-view`; params?: Router.UnknownInputParams; } | { pathname: `/ui/screens/NotesListScreen`; params?: Router.UnknownInputParams; } | { pathname: `/ui/settings/SettingsPage`; params?: Router.UnknownInputParams; } | { pathname: `/ui/settings/about`; params?: Router.UnknownInputParams; } | { pathname: `/ui/settings/alerts`; params?: Router.UnknownInputParams; } | { pathname: `/ui/settings/assignment`; params?: Router.UnknownInputParams; } | { pathname: `/ui/settings/call-preferences`; params?: Router.UnknownInputParams; } | { pathname: `/ui/settings/daily-digest`; params?: Router.UnknownInputParams; } | { pathname: `/ui/settings/email-tracking`; params?: Router.UnknownInputParams; } | { pathname: `/ui/settings/notifications`; params?: Router.UnknownInputParams; } | { pathname: `/ui/settings/offline-preferences`; params?: Router.UnknownInputParams; } | { pathname: `/ui/settings/reminders`; params?: Router.UnknownInputParams; };
    }
  }
}
