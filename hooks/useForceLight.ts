import { useEffect } from 'react';
import { Appearance, Platform } from 'react-native';

/**
 * Hook to force light mode throughout the app
 * This should be called at the root of your app
 */
export function useForceLight() {
  useEffect(() => {
    // Force light mode
    Appearance.setColorScheme('light');

    // For iOS, we need to handle appearance changes
    // as the system might try to override our setting
    const subscription = Appearance.addChangeListener(() => {
      // Re-force light mode if system tries to change it
      Appearance.setColorScheme('light');
    });

    // Clean up when component unmounts
    return () => {
      // Remove the appearance change listener
      subscription.remove();
      // Reset to system default
      Appearance.setColorScheme(null);
    };
  }, []);
} 