/**
 * Custom hook for attendance validation
 * Provides attendance status checking and action validation
 */

import { useState, useEffect, useCallback } from 'react';
import { Alert } from 'react-native';
import { attendanceStorageService, StoredAttendanceData } from '../services/AttendanceStorageService';
import { router } from 'expo-router';

export interface AttendanceValidationResult {
  canPerform: boolean;
  status: string | null;
  message: string;
}

export interface UseAttendanceValidationReturn {
  attendanceStatus: StoredAttendanceData | null;
  isLoading: boolean;
  canPerformActions: boolean;
  validationResult: AttendanceValidationResult | null;
  checkAttendanceStatus: () => Promise<void>;
  validateAndExecute: (action: () => Promise<void> | void, actionName?: string) => Promise<void>;
  showAttendanceAlert: (message?: string) => void;
  refreshAttendanceStatus: () => Promise<void>;
}

export const useAttendanceValidation = (): UseAttendanceValidationReturn => {
  const [attendanceStatus, setAttendanceStatus] = useState<StoredAttendanceData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [canPerformActions, setCanPerformActions] = useState(false);
  const [validationResult, setValidationResult] = useState<AttendanceValidationResult | null>(null);

  /**
   * Check current attendance status
   */
  const checkAttendanceStatus = useCallback(async () => {
    try {
      setIsLoading(true);
      
      // Get current attendance status
      const currentStatus = await attendanceStorageService.getCurrentAttendanceStatus();
      setAttendanceStatus(currentStatus);
      
      // Check if actions can be performed
      const validation = await attendanceStorageService.canPerformActions();
      setValidationResult(validation);
      setCanPerformActions(validation.canPerform);
      
      console.log('🔍 Attendance validation result:', validation);
    } catch (error) {
      console.error('❌ Error checking attendance status:', error);
      setValidationResult({
        canPerform: false,
        status: null,
        message: 'Error checking attendance status. Please try again.'
      });
      setCanPerformActions(false);
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Refresh attendance status from storage
   */
  const refreshAttendanceStatus = useCallback(async () => {
    await checkAttendanceStatus();
  }, [checkAttendanceStatus]);

  /**
   * Show attendance alert with navigation option
   */
  const showAttendanceAlert = useCallback((customMessage?: string) => {
    const message = customMessage || validationResult?.message || 'Please check your attendance status.';
    
    Alert.alert(
      'Attendance Required',
      message,
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Go to Home',
          onPress: () => {
            router.push('/(tabs)/' as any);
          }
        }
      ]
    );
  }, [validationResult]);

  /**
   * Validate attendance and execute action if allowed
   */
  const validateAndExecute = useCallback(async (
    action: () => Promise<void> | void,
    actionName: string = 'action'
  ) => {
    try {
      // Refresh attendance status before validation
      await checkAttendanceStatus();
      
      if (!canPerformActions || !validationResult?.canPerform) {
        const message = validationResult?.message || 'Cannot perform this action due to attendance status.';
        
        // Show specific messages based on status
        if (validationResult?.status === 'Pending') {
          showAttendanceAlert(`Cannot ${actionName}. Please punch in attendance first.`);
        } else if (validationResult?.status === 'Completed' || validationResult?.status === 'Closed') {
          showAttendanceAlert(`Cannot ${actionName}. Attendance is closed for today.`);
        } else {
          showAttendanceAlert(message);
        }
        return;
      }
      
      // Execute the action if validation passes
      await action();
    } catch (error) {
      console.error(`❌ Error executing ${actionName}:`, error);
      Alert.alert('Error', `Failed to ${actionName}. Please try again.`);
    }
  }, [canPerformActions, validationResult, checkAttendanceStatus, showAttendanceAlert]);

  /**
   * Get attendance status message for UI display
   */
  const getStatusMessage = useCallback(() => {
    if (!attendanceStatus) {
      return 'No attendance record found';
    }
    
    switch (attendanceStatus.status.toLowerCase()) {
      case 'pending':
        return 'Attendance is pending - Please punch in';
      case 'inprogress':
      case 'checkin':
        return 'Attendance is active - Actions allowed';
      case 'completed':
      case 'closed':
        return 'Attendance is closed - No actions allowed';
      default:
        return `Attendance status: ${attendanceStatus.status}`;
    }
  }, [attendanceStatus]);

  /**
   * Get status color for UI display
   */
  const getStatusColor = useCallback(() => {
    if (!attendanceStatus) return '#8E8E93'; // Gray
    
    switch (attendanceStatus.status.toLowerCase()) {
      case 'pending':
        return '#FF9500'; // Orange
      case 'inprogress':
      case 'checkin':
        return '#4CD964'; // Green
      case 'completed':
      case 'closed':
        return '#007AFF'; // Blue
      default:
        return '#8E8E93'; // Gray
    }
  }, [attendanceStatus]);

  /**
   * Check if specific action type is allowed
   */
  const isActionAllowed = useCallback((actionType: 'task' | 'meeting' | 'general' = 'general') => {
    if (!validationResult) return false;
    
    // All action types follow the same attendance rules for now
    // Can be extended later for different rules per action type
    return validationResult.canPerform;
  }, [validationResult]);

  // Load attendance status on mount
  useEffect(() => {
    checkAttendanceStatus();
  }, [checkAttendanceStatus]);

  return {
    attendanceStatus,
    isLoading,
    canPerformActions,
    validationResult,
    checkAttendanceStatus,
    validateAndExecute,
    showAttendanceAlert,
    refreshAttendanceStatus,
    // Additional utility functions
    getStatusMessage,
    getStatusColor,
    isActionAllowed
  } as UseAttendanceValidationReturn & {
    getStatusMessage: () => string;
    getStatusColor: () => string;
    isActionAllowed: (actionType?: 'task' | 'meeting' | 'general') => boolean;
  };
};

export default useAttendanceValidation;
