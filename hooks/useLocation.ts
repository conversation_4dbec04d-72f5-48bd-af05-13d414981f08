import { useState, useEffect } from 'react';
import * as Location from 'expo-location';
import { Platform, Alert } from 'react-native';

type LocationType = {
  latitude: number;
  longitude: number;
  accuracy?: number;
};

type UseLocationReturn = {
  location: LocationType | null;
  errorMsg: string | null;
  loading: boolean;
  requestPermissions: () => Promise<boolean>;
  startWatchingLocation: () => Promise<void>;
  stopWatchingLocation: () => void;
};

export default function useLocation(): UseLocationReturn {
  const [location, setLocation] = useState<LocationType | null>(null);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [locationWatchId, setLocationWatchId] = useState<Location.LocationSubscription | undefined>(undefined);

  // Function to request location permissions
  const requestPermissions = async (): Promise<boolean> => {
    try {
      setLoading(true);
      
      // Request foreground permissions first
      const { status: foregroundStatus } = await Location.requestForegroundPermissionsAsync();
      
      if (foregroundStatus !== 'granted') {
        setErrorMsg('Permission to access location was denied');
        setLoading(false);
        return false;
      }

      // If we're on iOS or Android, also request background permissions
      if (Platform.OS !== 'web') {
        // Background permissions are more sensitive, so we explain why we need them
        if (Platform.OS === 'ios') {
          Alert.alert(
            'Background Location Access',
            'This app needs background location access to show your location on the map even when the app is in the background.',
            [
              {
                text: 'Cancel',
                style: 'cancel',
              },
              {
                text: 'Allow',
                onPress: async () => {
                  const { status: backgroundStatus } = await Location.requestBackgroundPermissionsAsync();
                  if (backgroundStatus !== 'granted') {
                    console.log('Background location permission not granted');
                  }
                },
              },
            ]
          );
        } else {
          // For Android
          const { status: backgroundStatus } = await Location.requestBackgroundPermissionsAsync();
          if (backgroundStatus !== 'granted') {
            console.log('Background location permission not granted');
          }
        }
      }

      // Get current location once permissions are granted
      const location = await Location.getCurrentPositionAsync({});
      setLocation({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        accuracy: location.coords.accuracy,
      });
      
      setLoading(false);
      return true;
    } catch (error) {
      console.error('Error requesting permissions:', error);
      setErrorMsg('Error requesting location permissions');
      setLoading(false);
      return false;
    }
  };

  // Function to start watching location
  const startWatchingLocation = async (): Promise<void> => {
    try {
      // Request permissions first
      const permissionsGranted = await requestPermissions();
      
      if (!permissionsGranted) {
        return;
      }

      // Start watching location with high accuracy
      const watchId = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.High,
          timeInterval: 5000,
          distanceInterval: 10,
        },
        (newLocation) => {
          setLocation({
            latitude: newLocation.coords.latitude,
            longitude: newLocation.coords.longitude,
            accuracy: newLocation.coords.accuracy,
          });
        }
      );
      
      setLocationWatchId(watchId);
    } catch (error) {
      console.error('Error watching location:', error);
      setErrorMsg('Error watching location');
    }
  };

  // Function to stop watching location
  const stopWatchingLocation = (): void => {
    if (locationWatchId) {
      locationWatchId.remove();
      setLocationWatchId(undefined);
    }
  };

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (locationWatchId) {
        locationWatchId.remove();
      }
    };
  }, [locationWatchId]);

  return {
    location,
    errorMsg,
    loading,
    requestPermissions,
    startWatchingLocation,
    stopWatchingLocation,
  };
} 