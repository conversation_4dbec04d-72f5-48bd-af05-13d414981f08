/**
 * useApi Hook
 * A React hook for making API requests with loading, error, and data states
 */

import { useState, useCallback, useEffect } from 'react';
import { apiService, ApiResponse, ApiError } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';

interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: ApiError | null;
}

type ApiMethod = 'get' | 'post' | 'put' | 'patch' | 'delete';

interface UseApiConfig {
  immediate?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: ApiError) => void;
}

/**
 * A hook for making API requests with built-in loading and error states
 * @param method The HTTP method to use (get, post, put, patch, delete)
 * @param endpoint The API endpoint to call (from API_ENDPOINTS)
 * @param config Additional configuration
 */
export function useApi<T = any>(
  method: ApiMethod,
  endpoint: string,
  config: UseApiConfig = {}
) {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: config.immediate === true,
    error: null,
  });

  // Execute the API request
  const execute = useCallback(
    async (payload?: any, params?: Record<string, any>) => {
      try {
        setState(prev => ({ ...prev, loading: true, error: null }));

        let response: ApiResponse<T>;

        switch (method) {
          case 'get':
            response = await apiService.get<T>(endpoint, params);
            break;
          case 'post':
            response = await apiService.post<T>(endpoint, payload, params);
            break;
          case 'put':
            response = await apiService.put<T>(endpoint, payload, params);
            break;
          case 'patch':
            response = await apiService.patch<T>(endpoint, payload, params);
            break;
          case 'delete':
            response = await apiService.delete<T>(endpoint, params);
            break;
          default:
            throw new Error(`Unsupported method: ${method}`);
        }

        setState({ data: response.data, loading: false, error: null });
        
        if (config.onSuccess) {
          config.onSuccess(response.data);
        }

        return response.data;
      } catch (error) {
        const apiError = error as ApiError;
        setState({ data: null, loading: false, error: apiError });
        
        if (config.onError) {
          config.onError(apiError);
        }
        
        throw apiError;
      }
    },
    [method, endpoint, config]
  );

  // If immediate is true, execute the request immediately
  useEffect(() => {
    if (config.immediate) {
      execute();
    }
  }, [execute]);

  return {
    ...state,
    execute,
    // Helper for resetting the state
    reset: useCallback(
      () => setState({ data: null, loading: false, error: null }),
      []
    ),
  };
}

/**
 * Example usage for specific endpoints
 */

// Login hook
export function useLogin() {
  return useApi('post', API_ENDPOINTS.LOGIN);
}

// Get contacts hook
export function useGetContacts() {
  return useApi('get', API_ENDPOINTS.CONTACTS);
}

// Get single contact hook
export function useGetContact(id?: string) {
  const endpoint = id 
    ? API_ENDPOINTS.CONTACT_DETAILS.replace(':id', id)
    : API_ENDPOINTS.CONTACT_DETAILS;
    
  return useApi('get', endpoint);
} 