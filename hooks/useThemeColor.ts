/**
 * Color theme utility hooks
 * https://docs.expo.dev/guides/color-schemes/
 */

import { Colors, getColors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

/**
 * Hook to access a specific color from the theme
 * @deprecated Consider using useColors() instead for access to the full color object
 */
export function useThemeColor(
  props: { light?: string; dark?: string },
  colorName: keyof typeof Colors.light & keyof typeof Colors.dark
) {
  const theme = useColorScheme() ?? 'light';
  const colorFromProps = props[theme];

  if (colorFromProps) {
    return colorFromProps;
  } else {
    return Colors[theme][colorName];
  }
}

/**
 * Hook to access the entire colors object for current theme
 * This is the recommended way to access colors in the application
 */
export function useColors() {
  const colorScheme = useColorScheme() ?? 'light';
  return getColors(colorScheme);
}
