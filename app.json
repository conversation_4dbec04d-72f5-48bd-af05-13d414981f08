{"expo": {"name": "salescrm", "slug": "salescrm", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "jsEngine": "jsc", "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "infoPlist": {"NSCameraUsageDescription": "This app uses the camera to take photos for file attachments in the CRM.", "NSPhotoLibraryUsageDescription": "This app accesses your photos to attach them to CRM records.", "NSPhotoLibraryAddUsageDescription": "This app saves photos to your library when you take pictures within the app.", "NSLocationWhenInUseUsageDescription": "This app needs access to your location to show your position on the map.", "NSLocationAlwaysAndWhenInUseUsageDescription": "This app needs access to your location to track your position even when the app is in the background.", "NSLocationAlwaysUsageDescription": "This app needs access to your location to track your position even when the app is in the background."}, "bundleIdentifier": "com.anonymous.salescrm", "config": {"googleMapsApiKey": "AIzaSyC5fimbpL3Ju-b-ryAEBIe0edoM0eHcCRU"}, "icon": "./assets/images/icon.png", "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "permissions": ["android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.MEDIA_LIBRARY", "android.permission.RECORD_AUDIO", "android.permission.ACCESS_MEDIA_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_BACKGROUND_LOCATION"], "package": "com.anonymous.salescrm", "config": {"googleMaps": {"apiKey": "AIzaSyC5fimbpL3Ju-b-ryAEBIe0edoM0eHcCRU"}}, "icon": "./assets/images/icon.png", "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": [["expo-router", {"origin": "https://github.com/expo/router/tree/main"}], ["expo-splash-screen", {"image": "./assets/images/splash.png", "imageWidth": 300, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-image-picker", {"photosPermission": "The app accesses your photos to attach them to CRM records.", "cameraPermission": "The app uses the camera to take photos for file attachments in the CRM."}], ["expo-media-library", {"photosPermission": "The app accesses your photos to attach them to CRM records.", "savePhotosPermission": "The app saves photos to your library when you take pictures within the app.", "isAccessMediaLocationEnabled": true}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "This app needs access to your location to show your position on the map and track your location in the background.", "locationAlwaysPermission": "This app needs access to your location to track your position even when the app is in the background.", "locationWhenInUsePermission": "This app needs access to your location to show your position on the map."}]], "experiments": {"typedRoutes": true, "tsconfigPaths": true}, "extra": {"API_URL": "http://13.202.88.90:5000/api/", "router": {"origin": false}, "eas": {"projectId": "98e427e1-f926-427d-94a1-eb409f146678"}}}}