# Google Maps Integration for SalesCRM

This document explains how to set up and use the Google Maps integration in our SalesCRM app, which works across web, iOS, and Android platforms.

## Setup Instructions

### 1. Get Google Maps API Keys

You need to obtain API keys from the Google Cloud Console:

1. Visit the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the following APIs:
   - Maps JavaScript API (for web)
   - Maps SDK for iOS
   - Maps SDK for Android
   - Geocoding API
4. Create API keys for each platform (you can use restrictions to limit usage to your app)

### 2. Update Configuration Files

#### Update `config/mapConfig.ts`

Replace the placeholder API keys with your actual keys:

```typescript
export const MAPS_CONFIG = {
  apiKeys: {
    ios: 'YOUR_IOS_API_KEY',
    android: 'YOUR_ANDROID_API_KEY',
    web: 'YOUR_WEB_API_KEY',
  },
  // ... rest of the config
};
```

#### Update `app.json`

Replace the placeholder API keys:

```json
"ios": {
  // ... other iOS config
  "config": {
    "googleMapsApiKey": "YOUR_IOS_API_KEY"
  }
},
"android": {
  // ... other Android config
  "config": {
    "googleMaps": {
      "apiKey": "YOUR_ANDROID_API_KEY"
    }
  }
}
```

### 3. iOS-Specific Setup

For iOS, additional configuration may be needed in Xcode after building:

1. Run `expo prebuild` to generate native code
2. Open the iOS project in Xcode
3. Make sure the following entries are in your `Info.plist`:
   - `NSLocationWhenInUseUsageDescription`
   - `NSLocationAlwaysAndWhenInUseUsageDescription`
   - `NSLocationAlwaysUsageDescription`

### 4. Android-Specific Setup

For Android, check that the following entries are in your `AndroidManifest.xml`:

```xml
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
```

## Using the Map Components

### Basic Map Usage

```jsx
import CustomMap from '../components/CustomMap';

function MyScreen() {
  return (
    <View style={styles.container}>
      <CustomMap 
        showsUserLocation={true}
        style={{ height: 400 }}
      />
    </View>
  );
}
```

### Map with Custom Markers

```jsx
import CustomMap from '../components/CustomMap';

function MapWithMarkers() {
  const markers = [
    {
      id: '1',
      coordinate: {
        latitude: 37.78825,
        longitude: -122.4324,
      },
      title: 'Marker 1',
      description: 'This is a sample marker',
    },
    // Add more markers as needed
  ];

  return (
    <View style={styles.container}>
      <CustomMap 
        markers={markers}
        showsUserLocation={true}
      />
    </View>
  );
}
```

### Using the Location Service

```jsx
import { LocationService } from '../services/LocationService';

async function getMyLocation() {
  // Request permissions and get current position
  const location = await LocationService.getCurrentPosition();
  
  if (location) {
    console.log('My coordinates:', location.coords);
    
    // Get address from coordinates
    const address = await LocationService.getAddressFromCoordinates(
      location.coords.latitude, 
      location.coords.longitude
    );
    
    console.log('My address:', address);
  }
}

// Watch for location changes
async function startLocationTracking() {
  const subscription = await LocationService.watchPositionChanges((location) => {
    console.log('Location updated:', location.coords);
  });
  
  // Stop tracking when done
  if (subscription) {
    subscription.remove();
  }
}
```

## Troubleshooting

### Common Issues

1. **Maps not showing on iOS/Android:**
   - Check that you've correctly set the API keys in both `app.json` and `mapConfig.ts`
   - Make sure you've enabled the correct APIs in the Google Cloud Console

2. **Location permissions not working:**
   - Ensure all the required permissions are correctly set in `app.json`
   - Try testing on a physical device as simulators may have limitations

3. **Web Map displays "For development purposes only" watermark:**
   - This indicates that your API key is not properly set up with billing enabled
   - Set up billing in the Google Cloud Console
   - Check that your API key has the correct restrictions (or none for testing)

### Testing Permissions

Use the LocationService to test if permissions are working:

```jsx
async function testPermissions() {
  const hasPermissions = await LocationService.requestLocationPermissions();
  console.log('Has location permissions:', hasPermissions);
  
  const servicesEnabled = await LocationService.isLocationServicesEnabled();
  console.log('Location services enabled:', servicesEnabled);
}
```

## Additional Resources

- [Expo Location Documentation](https://docs.expo.dev/versions/latest/sdk/location/)
- [React Native Maps Documentation](https://github.com/react-native-maps/react-native-maps)
- [Google Maps Platform Documentation](https://developers.google.com/maps/documentation) 