import { registerRootComponent } from 'expo';
import { AppRegistry } from 'react-native';
import { name as appName } from './app.json';
import LogBox from 'react-native/Libraries/LogBox';

// Ignore all LogBox notifications in the UI
LogBox.ignoreAllLogs();

import React from 'react';

// Import the expo-router entry point
import 'expo-router/entry';

// No need to register App component directly when using expo-router
// The registration is handled by expo-router
