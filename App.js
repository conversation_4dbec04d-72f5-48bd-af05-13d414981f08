import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Platform, TouchableOpacity, Linking, AppState, Alert } from 'react-native';
import { getApp, initializeApp } from '@react-native-firebase/app';
import { getMessaging, getToken } from '@react-native-firebase/messaging';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { setupForcedFCM, checkGooglePlayServices, checkPlayServicesAvailability } from './utils/forcedFCM';
import LogBox from 'react-native/Libraries/LogBox';

// Ignore all LogBox notifications in the UI
LogBox.ignoreAllLogs();

import React from 'react';

// Firebase config with explicit values
const FIREBASE_CONFIG = {
  apiKey: "AIzaSyCh5gE623XX2PvCGIBOxJpqzKBicZNHVek",
  projectId: "salescrm-f2511",
  appId: "1:880620206085:android:d6697f8f77f46b66b3c46c",
  messagingSenderId: "880620206085",
  databaseURL: "https://salescrm-f2511.firebaseio.com",
  storageBucket: "salescrm-f2511.appspot.com"
};

// Force initialize Firebase at the application level for FCM
const initializeFirebaseDirectly = async () => {
  try {
    let app;
    if (!getApp().length) {
      app = await initializeApp(FIREBASE_CONFIG);
    } else {
      app = getApp();
    }
    
    // Verify installation
    const hasValidConfig = app.options.apiKey === FIREBASE_CONFIG.apiKey;
    console.log('[APP.JS] Firebase config valid:', hasValidConfig);
    
    return true;
  } catch (error) {
    console.error('[APP.JS] Firebase init error:', error);
    return false;
  }
};

// Improved FCM token retrieval with exponential backoff
const getTokenWithForcedRetry = async (maxRetries = 5) => {
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      console.log(`[APP.JS] FCM token attempt ${attempt + 1}/${maxRetries}...`);
      
      // If not first attempt, add delay with exponential backoff
      if (attempt > 0) {
        const delay = 2000 * Math.pow(2, attempt - 1); // Exponential backoff
        console.log(`[APP.JS] Waiting ${delay}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
      
      // For Android, check Google Play Services first
      if (Platform.OS === 'android' && attempt > 0) {
        const servicesStatus = await checkGooglePlayServices();
        console.log(`[APP.JS] Google Play Services status: ${JSON.stringify(servicesStatus)}`);
        
        if (!servicesStatus.available) {
          console.log('[APP.JS] Google Play Services check failed, checking availability...');
          
          // Check if Play Services app is actually installed
          const availability = await checkPlayServicesAvailability();
          
          // Alert user about potential Google Play Services issue
          let alertMessage = 'Firebase Messaging requires Google Play Services to be available and properly configured.';
          if (availability.actionRequired) {
            alertMessage += ' It looks like Google Play Services might not be installed.';
          }
          Alert.alert(
            'Google Play Services Issue',
            alertMessage,
            [
              { text: 'OK' },
              availability.actionRequired && {
                text: 'Install/Update',
                onPress: () => availability.actionUrl && Linking.openURL(availability.actionUrl),
              }
            ].filter(Boolean) 
          );
        }
      }
      
      // Request FCM token
      const token = await getMessaging().getToken();
      if (token) {
        console.log('[APP.JS] FCM token successfully retrieved!');
        return token;
      } else {
        console.log('[APP.JS] Empty token result, will retry');
      }
    } catch (error) {
      console.error(`[APP.JS] FCM token error (attempt ${attempt + 1}):`, error);
      
      // Special handling for SERVICE_NOT_AVAILABLE
      if (String(error).includes('SERVICE_NOT_AVAILABLE')) {
        console.log('[APP.JS] SERVICE_NOT_AVAILABLE detected - forcing delay');
        const serviceDelay = 8000 + (attempt * 5000);
        
        // For Android devices, try more aggressive approach
        if (Platform.OS === 'android') {
          console.log('[APP.JS] On Android - using extended delay for Google Play Services');
          
          // Check if we're in active state
          if (AppState.currentState !== 'active') {
            console.log('[APP.JS] App is not in active state, waiting for foreground');
            // Wait until app is in foreground again to proceed
            await new Promise(resolve => {
              const subscription = AppState.addEventListener('change', (nextAppState) => {
                if (nextAppState === 'active') {
                  subscription.remove();
                  resolve();
                }
              });
            });
          }
          
          // Reinitialize Firebase as a more aggressive approach
          try {
            if (getApp().length > 0) {
              console.log('[APP.JS] Deleting all Firebase apps for fresh start');
              await Promise.all(getApp().map(app => app.delete()));
            }
            await new Promise(resolve => setTimeout(resolve, 5000));
            await initializeApp(FIREBASE_CONFIG);
          } catch (reinitError) {
            console.log('[APP.JS] Firebase reinit error (continuing):', reinitError);
          }
        }
        
        await new Promise(resolve => setTimeout(resolve, serviceDelay));
      }
    }
  }
  
  console.log('[APP.JS] Failed to get FCM token after all attempts');
  return null;
};

const App = () => {
  const [fcmToken, setFcmToken] = useState(null);
  const [initialized, setInitialized] = useState(false);
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [hasFallbackToken, setHasFallbackToken] = useState(false);

  const addLog = (message) => {
    console.log(message);
    setLogs(prev => [...prev, message]);
  };

  // Initialize Firebase and get FCM token on app start
  useEffect(() => {
    const setup = async () => {
      try {
        addLog('🔄 Starting forced FCM setup...');
        setLoading(true);
        
        // Check app state - if app is in background, wait for foreground
        if (AppState.currentState !== 'active') {
          addLog('⏳ App is in background, waiting for foreground...');
          await new Promise(resolve => {
            const subscription = AppState.addEventListener('change', (nextAppState) => {
              if (nextAppState === 'active') {
                subscription.remove();
                resolve();
              }
            });
          });
          addLog('✅ App is now in foreground, continuing setup...');
        }
        
        // Use the new forced approach
        const token = await setupForcedFCM();
        
        // Check if it's a fallback token
        const isFallbackToken = token && token.includes('FALLBACK:');
        setHasFallbackToken(isFallbackToken);
        
        if (token) {
          if (isFallbackToken) {
            addLog('⚠️ Using fallback token - FCM may not be available');
          } else {
            addLog('✅ FCM token obtained successfully!');
          }
          setFcmToken(token);
          setInitialized(true);
        } else {
          addLog('❌ FCM token not available - continuing with app anyway');
        }
        
        setLoading(false);
      } catch (error) {
        addLog(`❌ Setup error: ${error.message}`);
        setLoading(false);
      }
    };
    
    setup();
  }, []);

  // Handle press on forced token test page
  const openForcedFCMTest = () => {
    if (Platform.OS === 'web') {
      addLog('FCM Token test is only available on native platforms');
      return;
    }
    
    try {
      Linking.openURL('/forced-fcm');
    } catch (error) {
      addLog(`Error opening test page: ${error.message}`);
    }
  };

  // Manual FCM token retrieval
  const forceGetFCMToken = async () => {
    try {
      setLoading(true);
      addLog('🔄 Manually retrieving FCM token...');
      
      const token = await setupForcedFCM();
      
      // Check if it's a fallback token
      const isFallbackToken = token && token.includes('FALLBACK:');
      setHasFallbackToken(isFallbackToken);
      
      if (token) {
        if (isFallbackToken) {
          addLog('⚠️ Using fallback token - FCM may not be available');
        } else {
          addLog('✅ FCM token successfully retrieved!');
        }
        setFcmToken(token);
        setInitialized(true);
      } else {
        addLog('❌ Failed to get FCM token after multiple attempts');
      }
      
      setLoading(false);
    } catch (error) {
      addLog(`❌ Error: ${error.message}`);
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Firebase FCM Test</Text>
      
      <View style={styles.statusContainer}>
        <Text style={styles.statusTitle}>Status:</Text>
        <Text style={styles.statusValue}>
          {loading ? '🔄 Loading...' : initialized ? '✅ Initialized' : '❌ Not initialized'}
        </Text>
      </View>
      
      <View style={styles.infoContainer}>
        <Text style={styles.label}>FCM Token:</Text>
        <Text style={styles.value}>
          {fcmToken 
            ? hasFallbackToken 
              ? '⚠️ Using fallback (FCM unavailable)' 
              : '✅ FCM token obtained'
            : '❌ Not available'}
        </Text>
      </View>
      
      {hasFallbackToken && (
        <View style={[styles.infoContainer, styles.warningContainer]}>
          <Text style={styles.warningText}>
            Device is using a fallback token because Firebase Cloud Messaging
            services are not available. Push notifications may not work properly.
          </Text>
        </View>
      )}
      
      <TouchableOpacity 
        style={styles.button}
        onPress={forceGetFCMToken}
        disabled={loading}
      >
        <Text style={styles.buttonText}>
          {loading ? 'Working...' : 'Force Get FCM Token'}
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity 
        style={[styles.button, styles.testButton]}
        onPress={openForcedFCMTest}
      >
        <Text style={styles.buttonText}>Open FCM Token Test Page</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    paddingTop: 60,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    elevation: 2,
  },
  statusTitle: {
    fontWeight: 'bold',
    marginRight: 10,
    fontSize: 16,
  },
  statusValue: {
    fontSize: 16,
  },
  infoContainer: {
    flexDirection: 'row',
    marginBottom: 15,
    padding: 15,
    backgroundColor: '#fff',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    elevation: 2,
  },
  warningContainer: {
    backgroundColor: '#fff3cd',
    borderColor: '#ffeeba',
    borderWidth: 1,
  },
  warningText: {
    color: '#856404',
    fontSize: 14,
  },
  label: {
    fontWeight: 'bold',
    marginRight: 10,
    fontSize: 16,
  },
  value: {
    flex: 1,
    fontSize: 16,
  },
  button: {
    backgroundColor: '#4630EB',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 15,
  },
  testButton: {
    backgroundColor: '#0E7AFE',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

export default App;