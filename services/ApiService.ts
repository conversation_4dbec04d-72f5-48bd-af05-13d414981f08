/**
 * API Service
 * A centralized service for handling all API requests in the application
 */
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError, InternalAxiosRequestConfig } from 'axios';
import { API_BASE_URL, API_ENDPOINTS, REQUEST_TIMEOUT } from '../config/api';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { log, logAPIRespose } from '@/app/ui/utils';
import { authService } from './AuthService';
import { Alert } from 'react-native';
import { CommonActions } from '@react-navigation/native';
import { navigationRef } from '../navigation/navigationRef';
import { router } from 'expo-router';

// Storage keys
const TOKEN_STORAGE_KEY = 'auth_token';

// Type definitions for better type safety
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

export interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers?: any;
}

export interface ApiError {
  status: number;
  message: string;
  data?: any;
  isApiError: true;
}

// Main API service class
class ApiService {
  private instance: AxiosInstance;
  private authToken: string | null = null;
  private isShowingAuthAlert: boolean = false;

  constructor() {
    // Create axios instance with default config
    this.instance = axios.create({
      baseURL: API_BASE_URL,
      timeout: REQUEST_TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // Setup request interceptor for auth token
    this.instance.interceptors.request.use(
      async (config: InternalAxiosRequestConfig) => {
        // If we don't have a token in memory, try to get it from storage
        if (!this.authToken) {
          try {
            this.authToken = await AsyncStorage.getItem(TOKEN_STORAGE_KEY);
            console.log('Retrieved token from storage:', !!this.authToken);
          } catch (error: unknown) {
            console.error('Failed to get auth token from storage', error);
          }
        }

        // If we have a token, add it to the request headers
        if (this.authToken && config.headers) {
          config.headers.Authorization = `Bearer ${this.authToken}`;
          console.log('Adding auth token to request:', {
            url: config.url,
            hasToken: true,
            headers: config.headers
          });
        } else {
          console.warn('No auth token available for request:', {
            url: config.url,
            hasToken: false
          });
        }
        return config;
      },
      (error: unknown) => Promise.reject(error)
    );

    // Setup response interceptor for error handling
    this.instance.interceptors.response.use(
      (response) => response,
      this.handleApiError
    );
  }

  /**
   * Set the authentication token for the API service
   * @param token The JWT auth token
   */
  public setAuthToken(token: string): void {
    this.authToken = token;
    AsyncStorage.setItem(TOKEN_STORAGE_KEY, token).catch((error) => {
      console.error('Failed to store auth token', error);
    });
  }

  /**
   * Clear the authentication token
   */
  public clearAuthToken(): void {
    this.authToken = null;
    AsyncStorage.removeItem(TOKEN_STORAGE_KEY).catch((error) => {
      console.error('Failed to remove auth token', error);
    });
  }

  /**
   * Handle API errors and format them consistently
   */
  private handleApiError = async (error: AxiosError): Promise<ApiError> => {
    console.log('API Error Details:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      headers: error.response?.headers,
      config: {
        url: error.config?.url,
        method: error.config?.method,
        headers: error.config?.headers,
        data: error.config?.data
      }
    });

    const apiError: ApiError = {
      status: error.response?.status || 500,
      message: 'An unexpected error occurred',
      isApiError: true,
    };

    if (error.response) {
      // The server responded with a status code outside the 2xx range
      apiError.data = error.response.data;

      // Safely access the message property with proper type checking
      const responseData = error.response.data as any;
      apiError.message = responseData?.message || `Error: ${error.response.status}`;

      // Handle authentication errors
      if (error.response.status === 401) {
        // Check if the error is from a login attempt
        const isLoginAttempt = error.config?.url?.includes(API_ENDPOINTS.LOGIN);

        if (!isLoginAttempt && !this.isShowingAuthAlert) {
          // Set flag to prevent multiple alerts
          this.isShowingAuthAlert = true;
          
          // Not a login attempt, so it's likely an expired token
          this.clearAuthToken();
          await authService.logout();
          
          Alert.alert(
            'Session Expired',
            'Your session has expired. Please login again.',
            [{
              text: 'OK',
              onPress: () => {
                // Reset flag when alert is dismissed
                this.isShowingAuthAlert = false;
                // Use router.replace to navigate to login
                router.replace('/login' as any);
              }
            }]
          );
        } else {
          // If it's a login attempt, we just return the error message for the login screen to handle
          console.log('Login attempt error:', error.response.data);
          // Don't show toast here, let the login screen handle the error display
        }
      }
    } else if (error.request) {
      // The request was made but no response was received
      apiError.message = 'No response received from server';
    } else {
      // Something else happened while setting up the request
      apiError.message = error.message || 'Unknown error occurred';
    }

    return Promise.reject(apiError);
  };

  /**
   * Replace path parameters in an endpoint URL
   * Example: replacePathParams('users/:id', { id: 123 }) => 'users/123'
   */
  private replacePathParams(url: string, params?: Record<string, any>): string {
    if (!params) return url;

    let processedUrl = url;
    Object.keys(params).forEach(key => {
      processedUrl = processedUrl.replace(`:${key}`, params[key]);
    });

    return processedUrl;
  }

  /**
   * Ensure URL path is properly formatted by adding a leading slash if needed
   */
  private formatEndpoint(endpoint: string): string {
    // Add leading slash if endpoint doesn't have one and is not empty
    if (endpoint && !endpoint.startsWith('/')) {
      return endpoint;
    }
    return endpoint;
  }

  /**
   * Make a request to the API
   * @param method The HTTP method to use
   * @param endpoint The API endpoint (from API_ENDPOINTS)
   * @param params Path or query parameters
   * @param data Request body data
   * @param config Additional axios request config
   */
  public async request<T = any>(
    method: HttpMethod,
    endpoint: string,
    params?: Record<string, any>,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    logAPIRespose('payloadPOST',  API_ENDPOINTS+endpoint);
    logAPIRespose('payload',  params);
    // Replace any path parameters in the endpoint
    const processedEndpoint = this.replacePathParams(endpoint, params);
    const url = this.formatEndpoint(processedEndpoint);

    // Prepare query parameters (for GET requests)
    const queryParams = method === 'GET' ? params : undefined;

    // Log the actual request URL for debugging
    console.log(`API Request: ${method} ${this.instance.defaults.baseURL}${url}`);

    try {
      const response: AxiosResponse<T> = await this.instance.request({
        method,
        url,
        params: queryParams,
        data,
        ...config,
      });

      return {
        data: response.data,
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
      };
    } catch (error) {
      if ((error as ApiError).isApiError) {
        throw error;
      }
      throw this.handleApiError(error as AxiosError);
    }
  }

  // Convenience methods for different HTTP methods

  /**
   * Make a GET request
   * @param endpoint The API endpoint
   * @param params Query parameters
   * @param config Additional axios request config
   */
  public async get<T = any>(
    endpoint: string,
    params?: Record<string, any>,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    console.log('Making GET request:', {
      url: `${this.instance.defaults.baseURL}${endpoint}`,
      params,
      headers: this.instance.defaults.headers
    });

    try {
      const response = await this.request<T>('GET', endpoint, params, undefined, config);
      console.log("GET_Response: "+endpoint+" \n " + JSON.stringify(response.data));
      return response;
    } catch (error) {
      console.error('GET Request failed:', {
        endpoint,
        error: JSON.stringify(error, null, 2)
      });
      throw error;
    }
  }

  /**
   * Make a POST request
   * @param endpoint The API endpoint
   * @param data Request body data
   * @param params Path parameters
   * @param config Additional axios request config
   */
  public async post<T = any>(
    endpoint: string,
    data?: any,
    params?: Record<string, any>,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    logAPIRespose('payloadPOST',  endpoint);
    logAPIRespose('payload params',  JSON.stringify(params));
    console.log('Request Data:', JSON.stringify(data, null, 2));

    try {
      const result = await this.request<T>('POST', endpoint, params, data, config);
     console.log("Response_from_POST: " + endpoint + " \n " + JSON.stringify(result.data));
      return result;
    } catch (error) {
      //console.error(`❌ POST Error for ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Make a PUT request
   * @param endpoint The API endpoint
   * @param data Request body data
   * @param params Path parameters
   * @param config Additional axios request config
   */
  public async put<T = any>(
    endpoint: string,
    data?: any,
    params?: Record<string, any>,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    logAPIRespose('payloadURL',  API_ENDPOINTS+endpoint);
    logAPIRespose('payload',  data);
    var response= await this.request<T>('PUT', endpoint, params, data, config);
    console.log("Response_from_PUT: " + JSON.stringify(response.data));
    return response;
  }

  /**
   * Make a PATCH request
   * @param endpoint The API endpoint
   * @param data Request body data
   * @param params Path parameters
   * @param config Additional axios request config
   */
  public async patch<T = any>(
    endpoint: string,
    data?: any,
    params?: Record<string, any>,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    return this.request<T>('PATCH', endpoint, params, data, config);
  }

  /**
   * Make a PUT request with FormData for file uploads
   * @param endpoint The API endpoint
   * @param formData FormData object containing files and other form fields
   * @param params Path parameters
   * @param config Additional axios request config
   */
  public async putFormData<T = any>(
    endpoint: string,
    formData: FormData,
    params?: Record<string, any>,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const customConfig: AxiosRequestConfig = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      ...config,
    };

    return this.request<T>('PUT', endpoint, params, formData, customConfig);
  }

  /**
   * Make a POST request with FormData for file uploads
   * @param endpoint The API endpoint
   * @param formData FormData object containing files and other form fields
   * @param params Path parameters
   * @param config Additional axios request config
   */
  public async postFormData<T = any>(
    endpoint: string,
    formData: FormData,
    params?: Record<string, any>,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const customConfig: AxiosRequestConfig = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      ...config,
    };

    return this.request<T>('POST', endpoint, params, formData, customConfig);
  }

  /**
   * Make a DELETE request
   * @param endpoint The API endpoint
   * @param params Path parameters
   * @param config Additional axios request config
   */
  public async delete<T = any>(
    endpoint: string,
    params?: Record<string, any>,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    return this.request<T>('DELETE', endpoint, params, undefined, config);
  }
}

// Export a singleton instance of the API service
export const apiService = new ApiService();

// Export the class for testing or specialized instances
export default ApiService;
