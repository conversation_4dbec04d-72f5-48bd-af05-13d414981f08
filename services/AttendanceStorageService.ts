/**
 * Attendance Storage Service
 * Handles local storage operations for attendance data
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { ATTENDANCE_STORAGE_KEY } from './AuthService';

// Types for attendance storage
export interface StoredAttendanceData {
  id: number;
  status: 'Pending' | 'Checkin' | 'Inprogress' | 'Completed' | string;
  checkin_time: string | null;
  checkout_time: string | null;
  date: string; // YYYY-MM-DD format
  location: string | null;
  lastUpdated: string; // ISO timestamp
}

export interface AttendanceStorageData {
  [date: string]: StoredAttendanceData; // Key is date in YYYY-MM-DD format
}

class AttendanceStorageService {
  private static instance: AttendanceStorageService;

  private constructor() {}

  public static getInstance(): AttendanceStorageService {
    if (!AttendanceStorageService.instance) {
      AttendanceStorageService.instance = new AttendanceStorageService();
    }
    return AttendanceStorageService.instance;
  }

  /**
   * Save attendance data for a specific date
   */
  public async saveAttendanceForDate(date: string, attendanceData: any): Promise<void> {
    try {
      console.log('💾 Saving attendance data for date:', date, attendanceData);
      
      // Get existing attendance data
      const existingData = await this.getAllAttendanceData();
      
      // Create stored attendance data
      const storedData: StoredAttendanceData = {
        id: attendanceData.id || 0,
        status: attendanceData.status || 'Pending',
        checkin_time: attendanceData.checkin_time || null,
        checkout_time: attendanceData.checkout_time || null,
        date: date,
        location: attendanceData.location || null,
        lastUpdated: new Date().toISOString()
      };
      
      // Update the data for this date
      existingData[date] = storedData;
      
      // Save back to storage
      await AsyncStorage.setItem(ATTENDANCE_STORAGE_KEY, JSON.stringify(existingData));
      console.log('✅ Attendance data saved successfully for date:', date);
    } catch (error) {
      console.error('❌ Error saving attendance data:', error);
      throw error;
    }
  }

  /**
   * Get attendance data for a specific date
   */
  public async getAttendanceForDate(date: string): Promise<StoredAttendanceData | null> {
    try {
      console.log('📖 Loading attendance data for date:', date);
      
      const allData = await this.getAllAttendanceData();
      const attendanceData = allData[date] || null;
      
      console.log('📖 Loaded attendance data:', attendanceData);
      return attendanceData;
    } catch (error) {
      console.error('❌ Error loading attendance data for date:', error);
      return null;
    }
  }

  /**
   * Get all attendance data from storage
   */
  public async getAllAttendanceData(): Promise<AttendanceStorageData> {
    try {
      const storedData = await AsyncStorage.getItem(ATTENDANCE_STORAGE_KEY);
      
      if (!storedData) {
        console.log('📭 No attendance data found in storage');
        return {};
      }
      
      const parsedData = JSON.parse(storedData);
      console.log('📦 Loaded all attendance data from storage');
      return parsedData;
    } catch (error) {
      console.error('❌ Error loading all attendance data:', error);
      return {};
    }
  }

  /**
   * Update attendance status for a specific date
   */
  public async updateAttendanceStatus(date: string, status: string, additionalData?: Partial<StoredAttendanceData>): Promise<void> {
    try {
      console.log('🔄 Updating attendance status for date:', date, 'to:', status);
      
      const existingData = await this.getAttendanceForDate(date);
      
      if (!existingData) {
        // Create new attendance data if none exists
        await this.saveAttendanceForDate(date, {
          status,
          ...additionalData
        });
        return;
      }
      
      // Update existing data
      const updatedData: StoredAttendanceData = {
        ...existingData,
        status,
        lastUpdated: new Date().toISOString(),
        ...additionalData
      };
      
      await this.saveAttendanceForDate(date, updatedData);
      console.log('✅ Attendance status updated successfully');
    } catch (error) {
      console.error('❌ Error updating attendance status:', error);
      throw error;
    }
  }

  /**
   * Clear attendance data for a specific date
   */
  public async clearAttendanceForDate(date: string): Promise<void> {
    try {
      console.log('🗑️ Clearing attendance data for date:', date);
      
      const allData = await this.getAllAttendanceData();
      delete allData[date];
      
      await AsyncStorage.setItem(ATTENDANCE_STORAGE_KEY, JSON.stringify(allData));
      console.log('✅ Attendance data cleared for date:', date);
    } catch (error) {
      console.error('❌ Error clearing attendance data:', error);
      throw error;
    }
  }

  /**
   * Clear all attendance data
   */
  public async clearAllAttendanceData(): Promise<void> {
    try {
      console.log('🗑️ Clearing all attendance data');
      await AsyncStorage.removeItem(ATTENDANCE_STORAGE_KEY);
      console.log('✅ All attendance data cleared');
    } catch (error) {
      console.error('❌ Error clearing all attendance data:', error);
      throw error;
    }
  }

  /**
   * Get attendance data for current week
   */
  public async getCurrentWeekAttendance(): Promise<StoredAttendanceData[]> {
    try {
      const allData = await this.getAllAttendanceData();
      const currentDate = new Date();
      const weekData: StoredAttendanceData[] = [];
      
      // Get data for the past 7 days
      for (let i = 6; i >= 0; i--) {
        const date = new Date(currentDate);
        date.setDate(date.getDate() - i);
        const dateString = date.toISOString().split('T')[0]; // YYYY-MM-DD format
        
        if (allData[dateString]) {
          weekData.push(allData[dateString]);
        }
      }
      
      return weekData;
    } catch (error) {
      console.error('❌ Error getting current week attendance:', error);
      return [];
    }
  }

  /**
   * Check if attendance data exists for a date
   */
  public async hasAttendanceForDate(date: string): Promise<boolean> {
    try {
      const data = await this.getAttendanceForDate(date);
      return data !== null;
    } catch (error) {
      console.error('❌ Error checking attendance existence:', error);
      return false;
    }
  }

  /**
   * Get attendance summary for a date range
   */
  public async getAttendanceSummary(startDate: string, endDate: string): Promise<{
    totalDays: number;
    checkedInDays: number;
    pendingDays: number;
    completedDays: number;
  }> {
    try {
      const allData = await this.getAllAttendanceData();
      let totalDays = 0;
      let checkedInDays = 0;
      let pendingDays = 0;
      let completedDays = 0;

      const start = new Date(startDate);
      const end = new Date(endDate);

      for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
        const dateString = date.toISOString().split('T')[0];
        const attendanceData = allData[dateString];

        if (attendanceData) {
          totalDays++;

          switch (attendanceData.status) {
            case 'Inprogress':
            case 'Checkin':
              checkedInDays++;
              break;
            case 'Pending':
              pendingDays++;
              break;
            case 'Completed':
              completedDays++;
              break;
          }
        }
      }

      return {
        totalDays,
        checkedInDays,
        pendingDays,
        completedDays
      };
    } catch (error) {
      console.error('❌ Error getting attendance summary:', error);
      return {
        totalDays: 0,
        checkedInDays: 0,
        pendingDays: 0,
        completedDays: 0
      };
    }
  }

  /**
   * Get current attendance status for today
   */
  public async getCurrentAttendanceStatus(): Promise<StoredAttendanceData | null> {
    try {
      const today = new Date().toISOString().split('T')[0];
      return await this.getAttendanceForDate(today);
    } catch (error) {
      console.error('❌ Error getting current attendance status:', error);
      return null;
    }
  }

  /**
   * Check if actions are allowed based on attendance status
   */
  public async canPerformActions(): Promise<{
    canPerform: boolean;
    status: string | null;
    message: string;
  }> {
    try {
      const currentAttendance = await this.getCurrentAttendanceStatus();

      if (!currentAttendance) {
        return {
          canPerform: false,
          status: null,
          message: 'No attendance record found. Please punch in attendance first.'
        };
      }

      switch (currentAttendance.status.toLowerCase()) {
        case 'pending':
          return {
            canPerform: false,
            status: 'Pending',
            message: 'Attendance is pending. Please punch in attendance to perform actions.'
          };
        case 'inprogress':
        case 'checkin':
          return {
            canPerform: true,
            status: 'Inprogress',
            message: 'Actions can be performed.'
          };
        case 'completed':
        case 'closed':
          return {
            canPerform: false,
            status: 'Completed',
            message: 'Attendance is closed. Actions cannot be performed.'
          };
        default:
          return {
            canPerform: false,
            status: currentAttendance.status,
            message: 'Unknown attendance status. Please check your attendance.'
          };
      }
    } catch (error) {
      console.error('❌ Error checking action permissions:', error);
      return {
        canPerform: false,
        status: null,
        message: 'Error checking attendance status. Please try again.'
      };
    }
  }
}

// Export singleton instance
export const attendanceStorageService = AttendanceStorageService.getInstance();
export type { AttendanceStorageService };
