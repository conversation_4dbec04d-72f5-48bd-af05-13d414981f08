import * as Location from 'expo-location';
import { Platform, Alert } from 'react-native';

/**
 * Service for handling location permissions and interactions across platforms
 */
export class LocationService {
  /**
   * Request location permissions appropriate for the platform
   * @returns {Promise<boolean>} Whether permissions were granted
   */
  static async requestLocationPermissions(): Promise<boolean> {
    try {
      // First, request foreground permissions (required for all platforms)
      const { status: foregroundStatus } = await Location.requestForegroundPermissionsAsync();
      
      if (foregroundStatus !== 'granted') {
        console.log('Foreground location permission denied');
        return false;
      }
      
      // For mobile platforms, also request background permissions if needed
      if (Platform.OS !== 'web') {
        return await this.requestBackgroundPermissions();
      }
      
      return true;
    } catch (error) {
      console.error('Error requesting location permissions:', error);
      return false;
    }
  }
  
  /**
   * Request background location permissions (iOS and Android specific)
   * @returns {Promise<boolean>} Whether background permissions were granted
   */
  static async requestBackgroundPermissions(): Promise<boolean> {
    // iOS requires explicit user confirmation via an Alert
    if (Platform.OS === 'ios') {
      return new Promise((resolve) => {
        Alert.alert(
          'Background Location Access',
          'This app needs background location access to track your location even when the app is in the background.',
          [
            {
              text: 'Deny',
              style: 'cancel',
              onPress: () => resolve(false),
            },
            {
              text: 'Allow',
              onPress: async () => {
                const { status } = await Location.requestBackgroundPermissionsAsync();
                resolve(status === 'granted');
              },
            },
          ]
        );
      });
    } 
    // Android
    else if (Platform.OS === 'android') {
      const { status } = await Location.requestBackgroundPermissionsAsync();
      return status === 'granted';
    }
    
    return false;
  }
  
  /**
   * Check if location services are enabled on the device
   * @returns {Promise<boolean>} Whether location services are enabled
   */
  static async isLocationServicesEnabled(): Promise<boolean> {
    try {
      const enabled = await Location.hasServicesEnabledAsync();
      return enabled;
    } catch (error) {
      console.error('Error checking location services:', error);
      return false;
    }
  }
  
  /**
   * Get the current position with high accuracy
   */
  static async getCurrentPosition(): Promise<Location.LocationObject | null> {
    try {
      const hasPermissions = await this.requestLocationPermissions();
      
      if (!hasPermissions) {
        console.log('Location permissions not granted');
        return null;
      }
      
      const isServiceEnabled = await this.isLocationServicesEnabled();
      
      if (!isServiceEnabled) {
        console.log('Location services not enabled');
        return null;
      }
      
      return await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });
    } catch (error) {
      console.error('Error getting current position:', error);
      return null;
    }
  }
  
  /**
   * Start watching position changes
   * @param {Function} callback - Callback function to handle location updates
   * @returns {Promise<Location.LocationSubscription | null>} Subscription object or null if failed
   */
  static async watchPositionChanges(
    callback: (location: Location.LocationObject) => void
  ): Promise<Location.LocationSubscription | null> {
    try {
      const hasPermissions = await this.requestLocationPermissions();
      
      if (!hasPermissions) {
        console.log('Location permissions not granted');
        return null;
      }
      
      return await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.High,
          timeInterval: 5000, // Update every 5 seconds
          distanceInterval: 10, // Update if moved by 10 meters
        },
        callback
      );
    } catch (error) {
      console.error('Error watching position:', error);
      return null;
    }
  }
  
  /**
   * Get the distance between two coordinates in meters
   */
  static getDistanceBetweenCoordinates(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number {
    // Implementation of Haversine formula to calculate distance between two points
    const R = 6371e3; // Earth radius in meters
    const φ1 = (lat1 * Math.PI) / 180;
    const φ2 = (lat2 * Math.PI) / 180;
    const Δφ = ((lat2 - lat1) * Math.PI) / 180;
    const Δλ = ((lon2 - lon1) * Math.PI) / 180;
    
    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    
    return R * c; // Distance in meters
  }
  
  /**
   * Get a formatted address from coordinates using Expo Location's reverseGeocodeAsync
   */
  static async getAddressFromCoordinates(
    latitude: number,
    longitude: number
  ): Promise<Location.LocationGeocodedAddress | null> {
    try {
      const results = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      });
      
      return results.length > 0 ? results[0] : null;
    } catch (error) {
      console.error('Error reverse geocoding:', error);
      return null;
    }
  }
} 