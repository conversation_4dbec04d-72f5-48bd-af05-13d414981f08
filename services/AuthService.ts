/**
 * Auth Service
 * Handles authentication-related functionality using our API service
 */

import { apiService } from './ApiService';
import { API_BASE_URL, API_ENDPOINTS } from '../config/api';
import { User, UserData, LoginResponse, LoginCredentials } from '../models/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getFCMToken } from '../utils/firebaseNotifications';
import { router } from 'expo-router';

// Types
export interface RegisterCredentials {
  name: string;
  email: string;
  password: string;
  passwordConfirmation: string;
}

// Storage keys
export const USER_STORAGE_KEY = 'user_data';
export const USER_STORAGE_ALWAYS = 'user_data_always';
export const SALES_ACTIVITIES_STORAGE_KEY = 'sales_activities';
export const RECORDS_STORAGE_KEY = 'records';
export const ATTENDANCE_STORAGE_KEY = 'attendance_data';

export const TOKEN_STORAGE_KEY = 'auth_token';

class AuthService {
  private currentUser: User | null = null;
  private static instance: AuthService;

  private constructor() {}

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * Login with email and password
   * @param credentials Login credentials
   * @returns User instance
   */
  public async login(credentials: LoginCredentials): Promise<User> {
    try {
      console.log('🚀 Login API call - Getting FCM token...');
      const fcmToken = await getFCMToken();
      
      const loginPayload = {
        ...credentials,
        fcm_token: fcmToken
      };
      
      console.log('🚀 Login API call - Payload:', JSON.stringify(loginPayload, null, 2));
      console.log('🚀 Login API URL:', API_BASE_URL + API_ENDPOINTS.LOGIN);
      
      // Creating a custom config to ensure proper connection to the server
      const customConfig = {
        timeout: 15000, // Increase timeout to 15 seconds
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        }
      };
      
      const response = await apiService.post<LoginResponse>(
        API_ENDPOINTS.LOGIN, 
        loginPayload,
        undefined,
        customConfig
      );
      
      console.log('✅ Login API Response:', JSON.stringify(response, null, 2));
      
      if (!response.data.user || !response.data.user.token) {
        console.error('❌ Invalid login response format:', response.data);
        throw new Error('Invalid login response format');
      }
      
      // Create user instance from response
      const user = new User(response.data.user);
      
      // Store user data
      await this.saveUserData(user);
      await AsyncStorage.setItem(USER_STORAGE_ALWAYS, JSON.stringify(response.data));
      
      // Set auth token for API calls
      apiService.setAuthToken(user.token);
      
      // Set current user
      this.currentUser = user;
      
      console.log('🔑 User logged in successfully');
      return user;
    } catch (error) {
      console.error('❌ Login failed:', error);
      throw error;
    }
  }

  /**
   * Get the current user if available
   */
  public getCurrentUser(): User | null {
    return this.currentUser;
  }

  /**
   * Load user data from storage
   */
  public async loadUserFromStorage(): Promise<User | null> {
    try {
      console.log('🔍 Checking for stored user data...');
      const userDataString = await AsyncStorage.getItem(USER_STORAGE_KEY);
      
      if (!userDataString) {
        console.log('📭 No user data found in storage');
        return null;
      }
      
      console.log('📦 User data found in storage, parsing...');
      const userData = JSON.parse(userDataString);
      
      if (!userData || !userData.token) {
        console.log('⚠️ Invalid user data format in storage');
        return null;
      }
      
      const user = User.fromJSON(userData);
      
      // Set current user
      this.currentUser = user;
      
      // Restore auth token for API calls
      apiService.setAuthToken(user.token);
      
      console.log('✅ Successfully loaded user from storage');
      return user;
    } catch (error) {
      console.error('❌ Error loading user from storage:', error);
      return null;
    }
  }

  /**
   * Save user data to storage
   */
  private async saveUserData(user: User): Promise<void> {
    try {
      console.log('🔑 Saving user data to storage:', JSON.stringify(user.toJSON(), null, 2));
      console.log('🔑 User token:', user.token);
      await AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(user.toJSON()));
      await AsyncStorage.setItem(TOKEN_STORAGE_KEY, user.token);
    } catch (error) {
      console.error('Failed to save user to storage:', error);
      throw error;
    }
  }

  /**
   * Register a new user
   * @param credentials Registration details
   */
  public async register(credentials: RegisterCredentials) {
    try {
      const response = await apiService.post(
        API_ENDPOINTS.REGISTER, 
        credentials
      );
      return response.data;
    } catch (error) {
      console.error('Registration failed:', error);
      throw error;
    }
  }

  /**
   * Logout the current user
   */
  public async logout() {
    try {
      // Call logout endpoint (if available)
      const response = await apiService.post(API_ENDPOINTS.LOGOUT);
      console
         apiService.clearAuthToken();
      await AsyncStorage.removeItem(USER_STORAGE_KEY);
      await AsyncStorage.removeItem(TOKEN_STORAGE_KEY);
      router.replace('/login');
    } catch (error) {
      //console.error('Logout API call failed:', error);
      console.log('Logout API call failed:', error);
      // Continue with local logout even if API call fails
       apiService.clearAuthToken();
      await AsyncStorage.removeItem(USER_STORAGE_KEY);
      await AsyncStorage.removeItem(TOKEN_STORAGE_KEY);
      router.replace('/login');
    } finally {
      // Clear the auth token and user data
      apiService.clearAuthToken();
      await AsyncStorage.removeItem(USER_STORAGE_KEY);
      await AsyncStorage.removeItem(TOKEN_STORAGE_KEY);
      this.currentUser = null;
      router.replace('/login');
    }
  }

  /**
   * Check if user is currently authenticated
   */
  public async isAuthenticated(): Promise<boolean> {
    try {
      const user = await this.loadUserFromStorage();
      
      if (!user) {
        return false;
      }
      
      // Make a request to get user profile to check if token is valid
      await apiService.get(API_ENDPOINTS.USER_PROFILE);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Request password reset
   * @param email User's email address
   */
  public async forgotPassword(email: string) {
    try {
      const response = await apiService.post(
        API_ENDPOINTS.FORGOT_PASSWORD, 
        { email }
      );
      return response.data;
    } catch (error) {
      console.error('Forgot password request failed:', error);
      throw error;
    }
  }

  /**
   * Reset password with token
   * @param token Reset token from email
   * @param newPassword New password
   */
  public async resetPassword(token: string, newPassword: string) {
    try {
      const response = await apiService.post(
        API_ENDPOINTS.RESET_PASSWORD, 
        { 
          token,
          password: newPassword,
          password_confirmation: newPassword
        }
      );
      return response.data;
    } catch (error) {
      console.error('Password reset failed:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const authService = AuthService.getInstance();
// Export the class for type usage
export type { AuthService }; 