declare module 'react-native-audio-toolkit' {
  export class Recorder {
    constructor(filename: string);
    prepare(callback: (err: <PERSON>rror | null, fsPath: string | null) => void): Recorder;
    record(callback: (err: Error | null) => void): void;
    stop(callback: (err: Error | null) => void): void;
    destroy(): void;
  }

  export class Player {
    constructor(filename: string);
    prepare(callback: (err: Error | null) => void): Player;
    play(callback: (err: Error | null) => void): void;
    stop(callback: (err: Error | null) => void): void;
    destroy(): void;
  }
} 