/**
 * Attendance Status Indicator Component
 * Shows current attendance status and action permissions
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useColors } from '../hooks/useThemeColor';
import useAttendanceValidation from '../hooks/useAttendanceValidation';

interface AttendanceStatusIndicatorProps {
  showDetails?: boolean;
  onPress?: () => void;
  style?: any;
  compact?: boolean;
}

export const AttendanceStatusIndicator: React.FC<AttendanceStatusIndicatorProps> = ({
  showDetails = true,
  onPress,
  style,
  compact = false
}) => {
  const colors = useColors();
  const { 
    attendanceStatus, 
    isLoading, 
    canPerformActions, 
    validationResult,
    getStatusMessage,
    getStatusColor
  } = useAttendanceValidation();

  const getStatusIcon = (status: string | null) => {
    if (!status) return 'help-circle-outline';
    
    switch (status.toLowerCase()) {
      case 'pending':
        return 'time-outline';
      case 'inprogress':
      case 'checkin':
        return 'checkmark-circle';
      case 'completed':
      case 'closed':
        return 'checkmark-done-circle';
      default:
        return 'help-circle-outline';
    }
  };

  const getActionMessage = () => {
    if (!validationResult) return '';
    
    if (canPerformActions) {
      return 'Actions allowed';
    } else {
      switch (validationResult.status?.toLowerCase()) {
        case 'pending':
          return 'Punch in required';
        case 'completed':
        case 'closed':
          return 'Attendance closed';
        default:
          return 'Actions restricted';
      }
    }
  };

  if (isLoading) {
    return (
      <View style={[styles.container, styles.loadingContainer, style]}>
        <ActivityIndicator size="small" color={colors.primary} />
        {!compact && (
          <Text style={[styles.loadingText, { color: colors.text.secondary }]}>
            Checking attendance...
          </Text>
        )}
      </View>
    );
  }

  const statusColor = getStatusColor();
  const statusIcon = getStatusIcon(validationResult?.status);
  const actionMessage = getActionMessage();

  const containerStyle = [
    styles.container,
    compact ? styles.compactContainer : styles.fullContainer,
    { backgroundColor: colors.background.secondary },
    style
  ];

  const content = (
    <>
      <View style={styles.statusRow}>
        <View style={styles.statusInfo}>
          <Ionicons 
            name={statusIcon} 
            size={compact ? 16 : 20} 
            color={statusColor} 
          />
          <Text style={[
            compact ? styles.compactStatusText : styles.statusText, 
            { color: statusColor }
          ]}>
            {validationResult?.status || 'Unknown'}
          </Text>
        </View>
        
        {!compact && (
          <View style={[
            styles.actionIndicator,
            { backgroundColor: canPerformActions ? '#4CD964' : '#FF3B30' }
          ]}>
            <Ionicons 
              name={canPerformActions ? 'checkmark' : 'close'} 
              size={12} 
              color="white" 
            />
          </View>
        )}
      </View>

      {showDetails && !compact && (
        <View style={styles.detailsContainer}>
          <Text style={[styles.messageText, { color: colors.text.secondary }]}>
            {getStatusMessage()}
          </Text>
          <Text style={[
            styles.actionText, 
            { color: canPerformActions ? '#4CD964' : '#FF3B30' }
          ]}>
            {actionMessage}
          </Text>
        </View>
      )}

      {compact && (
        <Text style={[styles.compactMessage, { color: colors.text.tertiary }]}>
          {actionMessage}
        </Text>
      )}
    </>
  );

  if (onPress) {
    return (
      <TouchableOpacity style={containerStyle} onPress={onPress}>
        {content}
      </TouchableOpacity>
    );
  }

  return (
    <View style={containerStyle}>
      {content}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    padding: 12,
    marginVertical: 4,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginLeft: 8,
    fontSize: 12,
  },
  fullContainer: {
    padding: 12,
  },
  compactContainer: {
    padding: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  statusInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  compactStatusText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  actionIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  detailsContainer: {
    gap: 4,
  },
  messageText: {
    fontSize: 12,
    lineHeight: 16,
  },
  actionText: {
    fontSize: 11,
    fontWeight: '500',
  },
  compactMessage: {
    fontSize: 10,
    marginLeft: 8,
  },
});

export default AttendanceStatusIndicator;
