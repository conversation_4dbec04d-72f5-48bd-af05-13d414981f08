import React from 'react';
import { View, Platform, StatusBar } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export const StatusBarConfig = () => {
  const insets = useSafeAreaInsets();
  
  return (
    <>
      <View 
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: Platform.OS === 'ios' ? insets.top : StatusBar.currentHeight,
          backgroundColor: '#0F96BB',
          zIndex: 999,
        }}
      />
      <StatusBar
        backgroundColor="transparent"
        barStyle="light-content"
        translucent={true}
      />
    </>
  );
}; 