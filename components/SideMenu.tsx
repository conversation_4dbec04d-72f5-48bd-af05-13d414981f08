import React, { useMemo } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  TouchableOpacity, 
  Image, 
  ScrollView,
  SafeAreaView,
  StatusBar,
  Platform,
  Dimensions,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import { router } from 'expo-router';
import Constants from 'expo-constants';
import { useColors } from '@/hooks/useThemeColor';

// Helper function to capitalize first letter
const capitalizeFirstLetter = (string: string) => {
  if (!string) return '';
  return string.charAt(0).toUpperCase() + string.slice(1);
};

// Define menu items with their icons
const MENU_ITEMS = [
  { 
    id: 'attendance', 
    title: 'Attendance History', 
    icon: 'time-outline',
    route: '/attendance'
  },
  { 
    id: 'conveyance', 
    title: 'Conveyance History', 
    icon: 'car-outline',
    route: '/conveyance'
  },
  { 
    id: 'claim', 
    title: 'Claim History', 
    icon: 'cash-outline',
    route: '/claim'
  },
  { 
    id: 'settings', 
    title: 'Settings', 
    icon: 'settings-outline',
    route: '/ui/settings/SettingsPage'
  },
 /* { 
    visible: false,
    id: 'products', 
    showArrow: false,

    title: 'Products', 
    icon: 'cube-outline',
    route: '/ui/product/ProductListPage'
  },*/


  /*
  { 
    id: 'about', 
    title: 'About Us', 
    icon: 'alert-circle-outline',
    route: '/about'
  },
  { 
    id: 'contact', 
    title: 'Contact Us', 
    icon: 'help-circle-outline',
    route: '/contactus'
  },
  { 
    id: 'privacy', 
    title: 'Privacy Policy', 
    icon: 'shield-outline',
    route: '/privacy'
  },
  { 
    id: 'terms', 
    title: 'Terms And Conditions', 
    icon: 'document-text-outline',
    route: '/terms'
  },
  { 
    id: 'accountDelete', 
    title: 'Account Delete', 
    icon: 'star-outline',
    route: '/account-delete',
    showArrow: true
  },*/
  { 
    id: 'logout', 
    title: 'Logout', 
    icon: 'log-out-outline',
    showArrow: true
  },
];

type MenuItemProps = {
  title: string;
  icon: string;
  onPress: () => void;
  showArrow?: boolean;
};

const MenuItem = ({ title, icon, onPress, showArrow = false }: MenuItemProps) => {
  const colors = useColors();
  const styles = useMemo(() => createStyles(colors), [colors]);
  
  return (
    <TouchableOpacity style={styles.menuItem} onPress={onPress}>
      <Ionicons name={icon as any} size={24} color={colors.text.secondary} style={styles.menuIcon} />
      <Text style={styles.menuText}>{title}</Text>
      {showArrow && (
        <Ionicons name="chevron-forward" size={20} color={colors.text.secondary} style={styles.arrowIcon} />
      )}
    </TouchableOpacity>
  );
};

type SideMenuProps = {
  isVisible: boolean;
  onClose: () => void;
};

export const SideMenu = ({ isVisible, onClose }: SideMenuProps) => {
  const { signOut, user } = useAuth();
  const colors = useColors();
  const styles = useMemo(() => createStyles(colors), [colors]);
  
  if (!isVisible) return null;
  
  const handleMenuItemPress = (item: typeof MENU_ITEMS[0]) => {
    if (item.id === 'logout') {
      Alert.alert(
        "Logout",
        "Are you sure you want to logout?",
        [
          {
            text: "Cancel",
            style: "cancel"
          },
          { 
            text: "Logout", 
            onPress: () => {
              signOut();
              onClose();
            },
            style: "destructive"
          }
        ]
      );
    } else if (item.id === 'accountDelete') {
      Alert.alert(
        "Delete Account",
        "Are you sure you want to delete your account? This action cannot be undone.",
        [
          {
            text: "Cancel",
            style: "cancel"
          },
          { 
            text: "Delete", 
            onPress: () => {
              // Handle account deletion logic here
              console.log("Account deletion requested");
              onClose();
              // Navigate to account deletion confirmation or process
              if (item.route) {
                router.push(item.route as any);
              }
            },
            style: "destructive"
          }
        ]
      );
    } else if (item.route) {
      // For regular navigation items
      console.log('Navigating to:', item.route);
      router.push(item.route as any);
      onClose();
    }
  };
  
  // Get accurate status bar height using Constants
  const statusBarHeight = Constants.statusBarHeight || 0;
  const windowHeight = Dimensions.get('window').height;
  
  return (
    <View style={[styles.container, { height: windowHeight }]}>
      {/* Profile Section with space for status bar */}
      <View style={[styles.profileSection, { paddingTop: statusBarHeight + 10 }]}>
        <View style={styles.profileImageContainer}>
          <Image 
            source={require('../assets/images/icon.png')} 
            style={styles.profileImage}
          />
        </View>
        <View style={styles.profileInfo}>
          <Text style={styles.profileName}>{capitalizeFirstLetter(user?.username || 'Guest')}</Text>
          <Text style={styles.profilePhone}>{user?.mobile || 'No mobile'}</Text>
        </View>
      </View>
      
      {/* Menu Items */}
      <ScrollView style={styles.menuSection} showsVerticalScrollIndicator={false}>
        {MENU_ITEMS.map((item) => (
          <MenuItem 
            key={item.id}
            title={item.title} 
            icon={item.icon} 
            onPress={() => handleMenuItemPress(item)} 
            showArrow={item.showArrow}
          />
        ))}
      </ScrollView>
    </View>
  );
};

const createStyles = (colors: ReturnType<typeof useColors>) => StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '80%',
    backgroundColor: colors.background.primary,
    zIndex: 1000,
    shadowColor: colors.text.primary,
    shadowOffset: { width: 2, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 10,
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#0F96BB',
    paddingBottom: 30,
  },
  profileImageContainer: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: colors.background.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    overflow: 'hidden',
  },
  profileImage: {
    width: 70,
    height: 70,
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: colors.text.inverse,
    marginBottom: 4,
  },
  profilePhone: {
    fontSize: 18,
    color: colors.text.inverse,
  },
  menuSection: {
    flex: 1,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
  },
  menuIcon: {
    marginRight: 16,
    width: 24,
    opacity: 0.7,
  },
  menuText: {
    fontSize: 16,
    flex: 1,
    color: colors.text.primary,
  },
  arrowIcon: {
    marginLeft: 'auto',
  },
}); 