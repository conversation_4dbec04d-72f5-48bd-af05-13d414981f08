/**
 * Attendance Status Card Component
 * Displays attendance information and status for a specific date
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { attendanceStorageService, StoredAttendanceData } from '../services/AttendanceStorageService';
import { useColors } from '../hooks/useThemeColor';

interface AttendanceStatusCardProps {
  date: string; // YYYY-MM-DD format
  onRefresh?: () => void;
  showDetails?: boolean;
}

export const AttendanceStatusCard: React.FC<AttendanceStatusCardProps> = ({
  date,
  onRefresh,
  showDetails = true
}) => {
  const [attendanceData, setAttendanceData] = useState<StoredAttendanceData | null>(null);
  const [loading, setLoading] = useState(true);
  const colors = useColors();

  useEffect(() => {
    loadAttendanceData();
  }, [date]);

  const loadAttendanceData = async () => {
    try {
      setLoading(true);
      const data = await attendanceStorageService.getAttendanceForDate(date);
      setAttendanceData(data);
    } catch (error) {
      console.error('Error loading attendance data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Inprogress':
        return '#4CD964'; // Green
      case 'Completed':
        return '#007AFF'; // Blue
      case 'Pending':
        return '#FF9500'; // Orange
      default:
        return '#8E8E93'; // Gray
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Inprogress':
        return 'checkmark-circle';
      case 'Completed':
        return 'checkmark-done-circle';
      case 'Pending':
        return 'time-outline';
      default:
        return 'help-circle-outline';
    }
  };

  const formatTime = (timeString: string | null) => {
    if (!timeString) return '--:--';
    
    try {
      // Handle different time formats
      if (timeString.includes('T')) {
        // ISO format
        const date = new Date(timeString);
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      } else {
        // Already formatted time
        return timeString;
      }
    } catch (error) {
      return timeString;
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString([], { 
        weekday: 'short', 
        month: 'short', 
        day: 'numeric' 
      });
    } catch (error) {
      return dateString;
    }
  };

  const handleClearData = () => {
    Alert.alert(
      'Clear Attendance Data',
      'Are you sure you want to clear the stored attendance data for this date?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            try {
              await attendanceStorageService.clearAttendanceForDate(date);
              setAttendanceData(null);
              onRefresh?.();
            } catch (error) {
              console.error('Error clearing attendance data:', error);
            }
          }
        }
      ]
    );
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.loadingContainer]}>
        <ActivityIndicator size="small" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.text.secondary }]}>
          Loading attendance...
        </Text>
      </View>
    );
  }

  if (!attendanceData) {
    return (
      <View style={[styles.container, styles.noDataContainer, { backgroundColor: colors.background.secondary }]}>
        <Ionicons name="calendar-outline" size={24} color={colors.text.tertiary} />
        <Text style={[styles.noDataText, { color: colors.text.tertiary }]}>
          No attendance data stored for {formatDate(date)}
        </Text>
        {onRefresh && (
          <TouchableOpacity onPress={onRefresh} style={styles.refreshButton}>
            <Text style={[styles.refreshButtonText, { color: colors.primary }]}>
              Refresh
            </Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <View style={styles.header}>
        <View style={styles.statusContainer}>
          <Ionicons 
            name={getStatusIcon(attendanceData.status)} 
            size={20} 
            color={getStatusColor(attendanceData.status)} 
          />
          <Text style={[styles.statusText, { color: getStatusColor(attendanceData.status) }]}>
            {attendanceData.status}
          </Text>
        </View>
        <Text style={[styles.dateText, { color: colors.text.secondary }]}>
          {formatDate(date)}
        </Text>
      </View>

      {showDetails && (
        <View style={styles.detailsContainer}>
          <View style={styles.timeRow}>
            <View style={styles.timeItem}>
              <Text style={[styles.timeLabel, { color: colors.text.secondary }]}>Check In</Text>
              <Text style={[styles.timeValue, { color: colors.text.primary }]}>
                {formatTime(attendanceData.checkin_time)}
              </Text>
            </View>
            <View style={styles.timeItem}>
              <Text style={[styles.timeLabel, { color: colors.text.secondary }]}>Check Out</Text>
              <Text style={[styles.timeValue, { color: colors.text.primary }]}>
                {formatTime(attendanceData.checkout_time)}
              </Text>
            </View>
          </View>

          {attendanceData.location && (
            <View style={styles.locationContainer}>
              <Ionicons name="location-outline" size={16} color={colors.text.secondary} />
              <Text style={[styles.locationText, { color: colors.text.secondary }]} numberOfLines={2}>
                {attendanceData.location}
              </Text>
            </View>
          )}

          <View style={styles.metadataContainer}>
            <Text style={[styles.metadataText, { color: colors.text.tertiary }]}>
              Last updated: {new Date(attendanceData.lastUpdated).toLocaleString()}
            </Text>
          </View>
        </View>
      )}

      <View style={styles.actions}>
        {onRefresh && (
          <TouchableOpacity onPress={onRefresh} style={styles.actionButton}>
            <Ionicons name="refresh-outline" size={16} color={colors.primary} />
            <Text style={[styles.actionButtonText, { color: colors.primary }]}>Refresh</Text>
          </TouchableOpacity>
        )}
        <TouchableOpacity onPress={handleClearData} style={styles.actionButton}>
          <Ionicons name="trash-outline" size={16} color="#FF3B30" />
          <Text style={[styles.actionButtonText, { color: '#FF3B30' }]}>Clear</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    marginLeft: 8,
    fontSize: 14,
  },
  noDataContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  noDataText: {
    fontSize: 14,
    textAlign: 'center',
    marginVertical: 8,
  },
  refreshButton: {
    marginTop: 8,
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  refreshButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 6,
  },
  dateText: {
    fontSize: 14,
  },
  detailsContainer: {
    marginBottom: 12,
  },
  timeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  timeItem: {
    flex: 1,
    alignItems: 'center',
  },
  timeLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  timeValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  locationText: {
    fontSize: 12,
    marginLeft: 6,
    flex: 1,
  },
  metadataContainer: {
    marginTop: 8,
  },
  metadataText: {
    fontSize: 10,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
  },
  actionButtonText: {
    fontSize: 12,
    marginLeft: 4,
    fontWeight: '500',
  },
});

export default AttendanceStatusCard;
