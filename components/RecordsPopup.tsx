import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, Modal, TouchableOpacity, ActivityIndicator, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect, useRouter } from 'expo-router';
import { useRecords, Activity } from '../context/RecordsContext';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { DATE_FORMAT, readUserScope, showSnackbar } from '@/app/ui/utils';
import { convertDate } from '@/app/ui/utils';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { RECORDS_STORAGE_KEY, SALES_ACTIVITIES_STORAGE_KEY } from '@/services/AuthService';
import type { IconType } from '@/app/ui/utils';

interface RecordsPopupProps {
  visible: boolean;
  onClose: () => void;
  callFrom: 'contacts' | 'distributors' | 'orders' | 'deals';
}

const RecordsPopup: React.FC<RecordsPopupProps> = ({ visible, onClose, callFrom }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { recordTypes, salesActivities, updateRecordTypes, updateSalesActivities } = useRecords();
const [accessScope, setAccessScope] = useState<any>(null);
  
  useFocusEffect(
  useCallback(() => {
    const fetchAccessScope = async () => {
      const scope = await readUserScope();
      console.log("accessScope:contac " + JSON.stringify(scope));
      setAccessScope(scope);
    };
    fetchAccessScope();
  }, [])
);
  useEffect(() => {
    if (visible) {
      console.log("RecordsPopup opened - current recordTypes:", recordTypes);
      console.log("RecordsPopup opened - current salesActivities:", salesActivities);
      console.log("RecordsPopup - Storage keys:", {
        RECORDS_STORAGE_KEY,
        SALES_ACTIVITIES_STORAGE_KEY
      });
      fetchLocalActivities();
    }
  }, [visible]);

  const fetchLocalActivities = async () => {
    try {

    var userData = await AsyncStorage.getItem('user_data_always');
    var userDataJson=JSON.parse(userData!);
    var territories=userDataJson.user.territories;
    console.log("userScopesdsd: "+JSON.stringify(territories));

      console.log("Fetching local activities...");
      const records = await AsyncStorage.getItem(RECORDS_STORAGE_KEY);
      console.log("Raw records from storage:", records);
      if (records) {
        const parsedRecords = JSON.parse(records);
        console.log("Parsed records:", parsedRecords);
        if (Array.isArray(parsedRecords)) {
          console.log("Records is an array with length:", parsedRecords.length);
          updateRecordTypes(parsedRecords);
        } else {
          console.log("Records is not an array:", typeof parsedRecords);
        }
      } else {
        console.log("No records found in storage");
      }

      const sales = await AsyncStorage.getItem(SALES_ACTIVITIES_STORAGE_KEY);
      console.log("Raw sales activities from storage:", sales);
      if (sales) {
        const parsedSales = JSON.parse(sales);
        console.log("Parsed sales activities:", parsedSales);
        if (Array.isArray(parsedSales)) {
          console.log("Sales activities is an array with length:", parsedSales.length);
          updateSalesActivities(parsedSales);
        } else {
          console.log("Sales activities is not an array:", typeof parsedSales);
        }
      } else {
        console.log("No sales activities found in storage");
      }
    } catch (err) {
      console.error('Error fetching activities:', err);
      let errorMessage = 'Failed to load activities';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const fetchActivities = async () => {
    try {
      setLoading(true);
      setError(null);
      const now = new Date();
    
      // Format date as YYYY-MM-DD using the conversion function
      const date = convertDate(now.toISOString(), DATE_FORMAT.SYSTEM_FORMAT, DATE_FORMAT.DATE_FORMAT);
      console.log("datePassing: " + date);
      

      const response = await apiService.post(API_ENDPOINTS.HOME_PAGE,{ date: date });
      const data = response.data;

      if (!data || !data.response) {
        throw new Error('Invalid response format');
      }

      // Update record types
      if (data.response.records) {
        if (!Array.isArray(data.response.records)) {
          throw new Error('Records data is not in the expected format');
        }
        await updateRecordTypes(data.response.records);
      }

      // Update sales activities
      if (data.response.sales_activities) {
        if (!Array.isArray(data.response.sales_activities)) {
          throw new Error('Sales activities data is not in the expected format');
        }
        await updateSalesActivities(data.response.sales_activities);
      }
    } catch (err) {
      console.error('Error fetching activities:', err);
      let errorMessage = 'Failed to load activities';
      
      if (err instanceof Error) {
        if (err.message.includes('Network Error')) {
          errorMessage = 'Network error. Please check your connection.';
        } else if (err.message.includes('timeout')) {
          errorMessage = 'Request timed out. Please try again.';
        } else if (err.message.includes('401')) {
          errorMessage = 'Session expired. Please login again.';
        } else if (err.message.includes('403')) {
          errorMessage = 'You don\'t have permission to access this feature.';
        } else {
          errorMessage = err.message || errorMessage;
        }
      }
      
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const getIconForActivity = (activity: Activity): IconType => {
    // Map activity names to icons
    const iconMap: Record<string, IconType> = {
      'Task': 'checkmark-circle-outline',
      'Meeting': 'calendar-outline',
      'Voice Notes': 'mic-outline',
      'Contact': 'people-outline',
      'Account': 'business-outline',
      'Deal': 'trending-up-outline',
      'Product': 'cube-outline',
      'Quotation': 'document-text-outline',
      'Call Log': 'call-outline',
      'Note': 'pencil',
      'Email': 'mail-outline',
      'SMS': 'chatbubble-outline',
      'Visit': 'location-outline',
      'Survey': 'clipboard-outline',
      'Complaint': 'alert-circle-outline',
      'Feedback': 'chatbubble-ellipses-outline',
      'Order': 'cart-outline',
      'Invoice': 'receipt-outline',
      'Payment': 'wallet-outline',
      'Expense': 'cash-outline',
      'Report': 'bar-chart-outline',
      'Campaign': 'megaphone-outline',
      'Lead': 'person-add-outline',
      'Opportunity': 'trophy-outline',
      'Contract': 'document-outline',
      'Proposal': 'document-text-outline',
      'Quote': 'pricetag-outline',
      'Follow Up': 'refresh-outline',
      'Reminder': 'notifications-outline',
      'Schedule': 'time-outline'
    };
    
    return iconMap[activity.activity_name] || 'document-outline';
  };

  const handleActivityPress = (activity: Activity) => {
    console.log('Activity pressed:', activity.activity_name);
    
    // Navigate based on the activity type
    switch (activity.activity_name.toLowerCase()) {
      case 'task':
        if (accessScope?.task?.create) {
          router.push({
            pathname: '/tasks/create',
            params: { callFrom }
          });
        } else {
          showSnackbar('Task creation is not available for your account.');
        }
        break;
      case 'meeting':
        if (accessScope?.appointment?.create) {
          router.push({
            pathname: '/meetings/createMeeting',
            params: { callFrom }
          });
        } else {
          showSnackbar('Meeting creation is not available for your account.');
        }
        break;
      case 'contact':
        if (accessScope?.contact?.create) {
          router.push('/contact');
        } else {
          showSnackbar('Contact creation is not available for your account.');
        }
        break;
      case 'account':
        if (accessScope?.sales_account?.create) { 
          router.push('/CreateAccounts');
        } else {
          showSnackbar('Account creation is not available for your account.');
        }
        break;
      case 'opportunity':  
        if (accessScope?.deal?.create) {
          router.push('/CreateDeals');
        } else {
          showSnackbar('Deal creation is not available for your account.');
        }
        break;
      case 'product':
        router.push('/DealsAddProduct');
        break;
      case 'quotation':
        router.push('/DealsAddQuote');
            break;
      case 'call log':
        
           Alert.alert('Coming Soon', `${activity.activity_name} creation will be available soon!`);
            break;
      case 'voice notes':
        if (accessScope?.note?.create) {
            router.push({
              pathname: '/ui/helpers/AddNotesPage',
              params: { id: '0' , type: "accounts"}
             })
        } else {
          showSnackbar('Voice notes creation is not available for your account.');
        }
            break;
            
      default:
            Alert.alert('Coming Soon', `${activity.activity_name} creation will be available soon!`);
            break
    }
    
    onClose();
  };

  const renderErrorState = () => (
    <View style={styles.errorContainer}>
      <Text style={styles.errorText}>{error}</Text>
      <TouchableOpacity 
        style={styles.retryButton} 
        onPress={fetchActivities}
      >
        <Text style={styles.retryButtonText}>Retry</Text>
      </TouchableOpacity>
    </View>
  );

  const renderLoadingState = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color="#0F96BB" />
      <Text style={styles.loadingText}>Loading activities...</Text>
    </View>
  );

  const renderActivityItem = (activity: Activity) => (
    <TouchableOpacity
      key={activity.id}
      style={styles.activityItem}
      onPress={() => handleActivityPress(activity)}
    >
      <View style={styles.activityIcon}>
        <Ionicons name={getIconForActivity(activity)} size={28} color="#0F96BB" />
      </View>
      <Text style={styles.activityText}>{activity.activity_name}</Text>
    </TouchableOpacity>
  );

  if (!visible) return null;

  return (
    <Modal
      transparent={true}
      visible={visible}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.popupOverlay}>
        <TouchableOpacity 
          style={styles.closePopupArea}
          onPress={onClose}
        />
        
        <View style={styles.popup}>
          <Text style={styles.popupTitle}>RECORDS</Text>
          
          <View style={styles.popupRecords}>
            {loading ? renderLoadingState() : 
             error ? renderErrorState() : 
             recordTypes.length === 0 ? (
               <View style={styles.emptyContainer}>
                 <Text style={styles.emptyText}>No records available</Text>
               </View>
             ) : (
               recordTypes.map(renderActivityItem)
             )}
          </View>
          
          <Text style={styles.popupTitle}>SALES ACTIVITIES</Text>
          
          <View style={styles.popupActivities}>
            {loading ? renderLoadingState() : 
             error ? renderErrorState() : 
             salesActivities.length === 0 ? (
               <View style={styles.emptyContainer}>
                 <Text style={styles.emptyText}>No sales activities available</Text>
               </View>
             ) : (
               salesActivities.map(renderActivityItem)
             )}
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  popupOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 1000,
  },
  closePopupArea: {
    flex: 1,
  },
  popup: {
    backgroundColor: '#C4E8F5',
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  popupTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  popupRecords: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  popupActivities: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  activityItem: {
    alignItems: 'center',
    width: '33%',
    marginBottom: 16,
  },
  activityIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  activityText: {
    fontSize: 12,
    color: '#333',
    textAlign: 'center',
  },
  loadingContainer: {
    width: '100%',
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    color: '#666',
  },
  errorContainer: {
    width: '100%',
    padding: 20,
    alignItems: 'center',
  },
  errorText: {
    color: '#FF3B30',
    marginBottom: 10,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#0F96BB',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  emptyContainer: {
    width: '100%',
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    color: '#666',
    textAlign: 'center',
  },
});

export default RecordsPopup; 