import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, View, Platform, Text, ActivityIndicator, Dimensions } from 'react-native';
import MapView, { Marker, PROVIDER_GOOGLE, Region } from 'react-native-maps';
import * as Location from 'expo-location';
import { getGoogleMapsApiKey, MAPS_CONFIG } from '../config/mapConfig';

type CustomMapProps = {
  initialRegion?: Region;
  markers?: Array<{
    id: string;
    coordinate: {
      latitude: number;
      longitude: number;
    };
    title?: string;
    description?: string;
  }>;
  showsUserLocation?: boolean;
  onRegionChange?: (region: Region) => void;
  style?: any;
};

const DEFAULT_REGION = MAPS_CONFIG.defaultLocation;

export default function CustomMap({
  initialRegion,
  markers = [],
  showsUserLocation = true,
  onRegionChange,
  style,
}: CustomMapProps) {
  const [region, setRegion] = useState<Region>({
    latitude: initialRegion?.latitude || DEFAULT_REGION.latitude,
    longitude: initialRegion?.longitude || DEFAULT_REGION.longitude,
    latitudeDelta: initialRegion?.latitudeDelta || MAPS_CONFIG.defaultSettings.latitudeDelta,
    longitudeDelta: initialRegion?.longitudeDelta || MAPS_CONFIG.defaultSettings.longitudeDelta,
  });
  const [loading, setLoading] = useState<boolean>(true);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const mapRef = useRef<MapView | null>(null);
  const apiKey = getGoogleMapsApiKey();

  useEffect(() => {
    let isMounted = true;

    const getLocation = async () => {
      try {
        setLoading(true);
        
        // Request location permission
        const { status } = await Location.requestForegroundPermissionsAsync();
        
        if (status !== 'granted') {
          setErrorMsg('Permission to access location was denied');
          setLoading(false);
          return;
        }

        // Get current location
        const location = await Location.getCurrentPositionAsync({});
        
        if (isMounted && !initialRegion) {
          const newRegion = {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
            latitudeDelta: MAPS_CONFIG.defaultSettings.latitudeDelta,
            longitudeDelta: MAPS_CONFIG.defaultSettings.longitudeDelta,
          };
          setRegion(newRegion);
        }
      } catch (error) {
        if (isMounted) {
          setErrorMsg('Error getting location');
          console.error('Error getting location:', error);
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    if (!initialRegion) {
      getLocation();
    } else {
      setLoading(false);
    }

    return () => {
      isMounted = false;
    };
  }, [initialRegion]);

  const handleRegionChange = (newRegion: Region) => {
    setRegion(newRegion);
    if (onRegionChange) {
      onRegionChange(newRegion);
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.center]}>
        <ActivityIndicator size="large" color="#0F96BB" />
        <Text style={styles.loadingText}>Loading map...</Text>
      </View>
    );
  }

  if (errorMsg) {
    return (
      <View style={[styles.container, styles.center]}>
        <Text style={styles.errorText}>{errorMsg}</Text>
      </View>
    );
  }

  // For web platform
  if (Platform.OS === 'web') {
    return (
      <View style={[styles.container, style]}>
        <iframe
          title="Google Maps"
          width="100%"
          height="100%"
          frameBorder="0"
          style={{ border: 0 }}
          src={`https://www.google.com/maps/embed/v1/view?key=${apiKey}&center=${region.latitude},${region.longitude}&zoom=${MAPS_CONFIG.defaultSettings.zoomLevel}`}
          allowFullScreen
        />
      </View>
    );
  }

  // For iOS and Android
  return (
    <View style={[styles.container, style]}>
      <MapView
        ref={mapRef}
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        region={region}
        onRegionChangeComplete={handleRegionChange}
        showsUserLocation={showsUserLocation}
        showsMyLocationButton={true}
        showsCompass={true}
        showsScale={true}
        zoomEnabled={true}
        rotateEnabled={true}
        toolbarEnabled={true}
      >
        {markers.map((marker) => (
          <Marker
            key={marker.id}
            coordinate={marker.coordinate}
            title={marker.title}
            description={marker.description}
          />
        ))}
      </MapView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    height: 300,
    width: '100%',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  center: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
}); 