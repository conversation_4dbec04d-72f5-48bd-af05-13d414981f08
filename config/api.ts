/**
 * API Configuration
 * This file contains all API-related configuration including base URL and endpoints
 */
import Constants from "expo-constants";

// Fallbacks API URL in case environment variables are not set
const DEFAULT_API_URL = 'http://13.202.88.90:5000/api/';

// Safely access API_URL with fallback
const API_URL = Constants.expoConfig?.extra?.API_URL || DEFAULT_API_URL;

// Base URL for all API requests
export const API_BASE_URL = API_URL;

// Base URL for uploaded files
export const UPLOADS_BASE_URL = 'http://13.202.88.90:5000/public/uploads/';

// Define all API endpoints here
export const API_ENDPOINTS = {



  // Auth
  LOGIN: 'auth/login',
  REGISTER: 'auth/register',
  FORGOT_PASSWORD: 'auth/forgot-password',
  RESET_PASSWORD: 'auth/reset-password',
  LOGOUT: 'auth/logout',
  DISTRIBUTORS:'contacts',
  // User
  USER_PROFILE: 'user/profile',
  UPDATE_PROFILE: 'user/profile',

  // Contacts
  CONTACTS: 'contacts',
  CONTACTS_LIST: 'contacts/contactListWithFilters',
  CONTACT_DETAILS: 'contacts/:id',
  CONTACTS_DYNAMIC_FIELD:'contacts/fields',
  CONTACTS_FILTERS:'contacts/filters',
  CONTACT_FILTERS_ADD_LIST:'contacts/fields/fieldsForCreateFilters',
  CONTACTS_FILTERS_UPDATE:'contacts/filtersViewUpdate',

  HOME_PAGE:'salesactivities/menus',
  CRETAE_CONTACT:'contacts',
  TASK_LIST:'tasks',
  TASK_TYPE:'tasks/fields',


  // Accounts
  SALES_ACCOUNTS: 'sales_accounts',
  ACCOUNT_DETAILS: 'sales_accounts/:id',
  ACCOUNT_FIELDS: 'sales_accounts/fields',
  SALES_ACCOUNTS_FILTERS:'sales_accounts/filters',
    ACCOUNT_FILTERS_ADD_LIST:'sales_accounts/fieldsForCreateFilters',
    ACCOUNT_FILTERS_UPDATE:'sales_accounts/filtersViewUpdate',
    ACCOUNT_LIST_WITH_FILTERS:'sales_accounts/accountListWithFilters',


  //Accounts
  ACCOUNTS_LIST: 'sales_accounts',
  // Deals
  DEALS: 'deals',
  DEAL_DETAILS: 'deals/:id',
  DEAL_FIELDS: 'deal/fields',
  CREATE_DEAL: 'deals',
  DEALS_FILTERS:'deals/filters',
  DEAL_FILTERS_ADD_LIST:'deals/fieldsForCreateFilters',
  DEAL_LIST_WITH_FILTERS:'deals/dealListWithFilters',
  DEAL_FILTERS_UPDATE:'deals/filtersViewUpdate',

  // Conveyance
  CONVEYANCE: 'conveyance',
  CONVEYANCE_DETAILS: 'conveyance/:id',
  CONVEYANCE_LIST: 'salesactivities/conveyance/list',
  CLAIM_HISTORY: 'conveyance/history/status',
  CLAIM_HISTORYLIST: 'conveyance/claimhistory',
  CONVEYANCE_CLAIM_APPLY: 'conveyance/claimapply',
  CLAIM_UPDATE: 'conveyance/update',

  //Attendance
  ATTENDANCE:'salesactivities/attendance',
  ATTENDANCE_RECORDS: 'attendance/records',
  ATTENDANCE_HISTORY: 'salesactivities/attendance/history',

  // Tasks
  TASKS: 'tasks',
  TASK_DETAILS: 'tasks/:id',
  TASKS_BY_CONTACT: 'contacts/:id/tasks',
  TASKS_BY_ACCOUNT: 'sales_accounts/:id/tasks',
  TASKS_BY_DEAL: 'deals/:id/tasks',
  TASK_UPDATE: 'tasks/:id',

  // Meetings
  MEETINGS: 'meetings',
  MEETING_DETAILS: 'meetings/:id',
  MEETING_ADD:'salesactivities/appointments',
  DEAL_MEETING:'deals/:id/meetings',
  CONTACT_MEETING:'contacts/:id/meetings',
  ACCOUNT_MEETING:'sales_accounts/:id/meetings',

  // Notifications
  NOTIFICATIONS: 'notifications',
  NOTIFICATION_DETAILS: 'notifications/:id',
  MARK_NOTIFICATION_READ: 'notifications/:id',

  //Notes
  NOTES: 'notes',
  NOTES_ADD: 'notes/addNoteWithFile',
  UPDATE_NOTE: 'notes',
  DELETE_NOTE: 'notes',

  // Quotes
  QUOTATION_FIELDS: 'quotation-fields',
  CREATE_QUOTATION: 'quotation-fields/quotation/add',
  QUOTATIONS: 'quotations',
  QUOTATION_DETAILS: 'quotations/:id',
  QUOTATION_LIST: 'quotations/list?deal_id=',
  QUOTATION_PRODUCTS_MANAGE: 'quotations/products/manage',
  QUOTATION_PRODUCT_DELETE: 'quotations/product/delete',

  //Products
  PRODUCTS: 'products',
  PRODUCT_FIELDS: 'product/fields',
  PRODUCT_FILTER: 'products/productListWithFilters',
  PRODUCT_FILTERS:"products/filters",
  PRODUCT_CATEGORIES: 'products/categories/view',
  DEALS_PRODUCTS_LIST : 'products/deal/products/list',
  DEALS_PRODUCTS_REMOVE: 'products/deal/product/remove',
  PRODUCT_FILES_ADD: 'products/file/upload',
  DEAL_PRODUCTS: 'products/deal/products',
  DEAL_PRODUCTS_UPDATE: 'products/deal/products',
  PRODUCT_FILTERS_UPDATE:'products/filtersViewUpdate',

  //PRICE
  ADD_PRICE:'products/update-price',
  FILTERS_LIST :"products/fields",
  PRODUCT_FILTERS_ADD_LIST:'products/fields',
  SAVE_VIEW :"products/views",
//Products
// PRODUCTS: 'products',
// PRODUCT_FIELDS: 'product/fields',
// PRODUCT_FILTER: 'products/filter',
// PRODUCT_FILES_ADD: 'products/file/upload',
PRODUCT_FILES_DELETE: 'products/file/delete',

//PRICE
// ADD_PRICE:'products/update-price',
// FILTERS_LIST :"products/fields",
// SAVE_VIEW :"products/views",
PRICE_DELETE:'products/delete-price',
SARE_WITH_USERS:'products/filter/lookup-data',

  // Files
  GET_FILES: 'files/list/',
  UPLOAD_FILE: 'files/upload',
  DELETE_FILE: 'files',

};

// HTTP status codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
};

// Request timeout in milliseconds
export const REQUEST_TIMEOUT = 30000; // 30 seconds