/**
 * Configuration for Google Maps API keys and settings
 * You need to replace the placeholder API keys with your own keys from Google Cloud Console
 * Visit: https://console.cloud.google.com/google/maps-apis/overview
 */

export const MAPS_CONFIG = {
  // Google Maps API keys for different platforms
  apiKeys: {
    ios: 'AIzaSyC5fimbpL3Ju-b-ryAEBIe0edoM0eHcCRU', // Replace with your iOS API key
    android: 'AIzaSyC5fimbpL3Ju-b-ryAEBIe0edoM0eHcCRU', // Replace with your Android API key
    web: 'AIzaSyC5fimbpL3Ju-b-ryAEBIe0edoM0eHcCRU', // Replace with your Web API key
  },
  
  // Default map settings
  defaultSettings: {
    zoomLevel: 15,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  },
  
  // Default location if user location cannot be determined
  defaultLocation: {
    latitude: 37.78825,
    longitude: -122.4324,
    description: 'Default Location',
  },
};

/**
 * Get the appropriate Google Maps API key based on the current platform
 */
export function getGoogleMapsApiKey(): string {
  if (typeof window !== 'undefined') {
    return MAPS_CONFIG.apiKeys.web;
  }
  
  // React Native Platform check
  try {
    const Platform = require('react-native').Platform;
    
    if (Platform.OS === 'ios') {
      return MAPS_CONFIG.apiKeys.ios;
    }
    
    if (Platform.OS === 'android') {
      return MAPS_CONFIG.apiKeys.android;
    }
  } catch (e) {
    // Fallback to web if react-native Platform is not available
    return MAPS_CONFIG.apiKeys.web;
  }
  
  // Default fallback
  return MAPS_CONFIG.apiKeys.web;
} 