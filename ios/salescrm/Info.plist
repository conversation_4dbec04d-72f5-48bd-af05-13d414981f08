<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>salescrm</string>
	<key>CFBundleExecutable</key>
	<string>salescrm</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>salescrm</string>
	<key>CFBundlePackageType</key>
	<string>$(PRODUCT_BUNDLE_PACKAGE_TYPE)</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0.0</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>myapp</string>
				<string>com.anonymous.salescrm</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>GMSApiKey</key>
	<string>AIzaSyC5fimbpL3Ju-b-ryAEBIe0edoM0eHcCRU</string>
	<key>LSMinimumSystemVersion</key>
	<string>12.0</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSAllowsArbitraryLoadsInWebContent</key>
		<true/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>************</key>
			<dict>
				<key>NSIncludesSubdomains</key>
				<true/>
				<key>NSTemporaryExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>We need access to media library to save your recordings</string>
	<key>NSCameraUsageDescription</key>
	<string>The app uses the camera to take photos for file attachments in the CRM.</string>
	<key>NSLocalNetworkUsageDescription</key>
	<string>This app requires access to local network to communicate with the local server.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>This app uses your location to display nearby maps and location-based features.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>This app uses your location to display nearby maps and location-based features.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app uses your location to display nearby maps and location-based features.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>We need access to your microphone to record voice notes</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>The app saves photos to your library when you take pictures within the app.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>The app accesses your photos to attach them to CRM records.</string>
	<key>NSUserActivityTypes</key>
	<array>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER).expo.index_route</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>SplashScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UIRequiresFullScreen</key>
	<false/>
	<key>UIStatusBarStyle</key>
	<string></string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Automatic</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
