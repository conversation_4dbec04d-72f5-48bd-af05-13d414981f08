#!/bin/bash

# This script fixes path issues in the React Native project
# It's specifically designed to handle spaces in directory names

# Get the absolute path of the project directory
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
echo "Project directory: $PROJECT_DIR"

# Create a symlink without spaces to the project directory
SYMLINK_DIR="/tmp/easy_crm_project"
echo "Creating symlink: $SYMLINK_DIR -> $PROJECT_DIR"
rm -f "$SYMLINK_DIR"
ln -s "$PROJECT_DIR" "$SYMLINK_DIR"

echo "Symlink created. You can now build the project using the symlink path."
echo "To use the symlink, run your build commands from $SYMLINK_DIR instead of the original path."
echo "For example: cd $SYMLINK_DIR && npx expo run:ios"
