/**
 * FCM Token Test Script
 * 
 * This script will help test FCM token generation directly.
 * Run it with: npx react-native run-script scripts/test-fcm-token.js
 */

import { AppRegistry, Platform } from 'react-native';
import React, { useEffect } from 'react';
import { SafeAreaView, Text, ScrollView, View, StyleSheet } from 'react-native';
import { initializeFirebase } from '../utils/firebaseConfig';
import messaging from '@react-native-firebase/messaging';

const FCMTokenTest = () => {
  const [logs, setLogs] = React.useState([]);
  const [token, setToken] = React.useState(null);

  const addLog = (message) => {
    console.log(message);
    setLogs((prevLogs) => [...prevLogs, { id: Date.now(), message }]);
  };

  const testFCMToken = async () => {
    try {
      addLog('Starting FCM token test...');
      addLog(`Platform: ${Platform.OS}`);

      // Initialize Firebase
      addLog('Initializing Firebase...');
      await initializeFirebase();
      addLog('Firebase initialized');

      // Request permissions
      addLog('Requesting notification permissions...');
      const authStatus = await messaging().requestPermission();
      addLog(`Permission status: ${authStatus}`);

      // Get token
      addLog('Requesting FCM token...');
      const fcmToken = await messaging().getToken();
      
      if (fcmToken) {
        addLog('FCM token obtained successfully! 🎉');
        addLog(`Token: ${fcmToken.substring(0, 20)}...`);
        setToken(fcmToken);
      } else {
        addLog('Failed to get FCM token - empty result ❌');
      }
    } catch (error) {
      addLog(`Error: ${error.message} ❌`);
      console.error(error);
    }
  };

  useEffect(() => {
    testFCMToken();
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <Text style={styles.title}>FCM Token Test</Text>
      
      {token ? (
        <View style={styles.tokenContainer}>
          <Text style={styles.tokenTitle}>FCM Token:</Text>
          <Text style={styles.token}>{token}</Text>
        </View>
      ) : (
        <Text style={styles.noToken}>Waiting for token...</Text>
      )}
      
      <Text style={styles.logsTitle}>Logs:</Text>
      <ScrollView style={styles.logContainer}>
        {logs.map((log) => (
          <Text key={log.id} style={styles.logText}>
            {log.message}
          </Text>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  tokenContainer: {
    backgroundColor: '#e6f7ff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  tokenTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  token: {
    fontSize: 14,
    fontFamily: 'monospace',
    backgroundColor: '#fff',
    padding: 10,
    borderRadius: 4,
  },
  noToken: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#999',
  },
  logsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  logContainer: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 10,
  },
  logText: {
    fontSize: 14,
    marginBottom: 5,
    fontFamily: 'monospace',
  },
});

AppRegistry.registerComponent('FCMTokenTest', () => FCMTokenTest);

// Allow importing directly
export default FCMTokenTest; 