const fs = require('fs');
const path = require('path');

// Paths to check
const androidConfigPath = path.join(__dirname, '..', 'android', 'app', 'google-services.json');
const iosConfigPath = path.join(__dirname, '..', 'ios', 'GoogleService-Info.plist');

console.log('Checking Firebase configuration files...');

// Check Android config
if (fs.existsSync(androidConfigPath)) {
  console.log('✅ Found google-services.json for Android');
} else {
  console.log('❌ Missing google-services.json for Android');
  console.log('Please create this file in the Firebase Console and place it in:');
  console.log(androidConfigPath);
  console.log('\nInstructions:');
  console.log('1. Go to Firebase Console: https://console.firebase.google.com/');
  console.log('2. Select your project');
  console.log('3. Click "Add app" and select Android');
  console.log('4. Enter your Android package name (from android/app/build.gradle)');
  console.log('5. Download the google-services.json file');
  console.log('6. Place it in the android/app directory');
}

// Check iOS config
if (fs.existsSync(iosConfigPath)) {
  console.log('✅ Found GoogleService-Info.plist for iOS');
} else {
  console.log('❌ Missing GoogleService-Info.plist for iOS');
  console.log('Please create this file in the Firebase Console and place it in:');
  console.log(iosConfigPath);
  console.log('\nInstructions:');
  console.log('1. Go to Firebase Console: https://console.firebase.google.com/');
  console.log('2. Select your project');
  console.log('3. Click "Add app" and select iOS');
  console.log('4. Enter your iOS bundle ID');
  console.log('5. Download the GoogleService-Info.plist file');
  console.log('6. Place it in the ios directory');
}

console.log('\nFor more help: https://rnfirebase.io/'); 