/**
 * <PERSON><PERSON><PERSON> to test Firebase initialization directly
 * Run with: node scripts/test-firebase.js
 */

// Firebase project credentials from google-services.json
const projectCredentials = {
  projectId: "salescrm-f2511",
  apiKey: "AIzaSyCh5gE623XX2PvCGIBOxJpqzKBicZNHVek"
};

const main = async () => {
  try {
    console.log('\n=== Testing Firebase Configuration ===\n');
    
    // Test 1: Validate config values
    console.log('Test 1: Validating Firebase config values');
    const requiredFields = [
      { key: 'projectId', value: projectCredentials.projectId },
      { key: 'apiKey', value: projectCredentials.apiKey }
    ];
    
    let validConfig = true;
    for (const field of requiredFields) {
      if (!field.value) {
        console.log(`❌ Missing required field: ${field.key}`);
        validConfig = false;
      } else {
        console.log(`✅ Found ${field.key}: ${field.value}`);
      }
    }
    
    if (validConfig) {
      console.log('✅ Firebase config values look valid\n');
    } else {
      console.log('❌ Firebase config has missing values\n');
    }
    
    // Test 2: Check google-services.json
    console.log('Test 2: Checking google-services.json file');
    const fs = require('fs');
    const path = require('path');
    const googleServicesPath = path.join(__dirname, '..', 'android', 'app', 'google-services.json');
    
    if (fs.existsSync(googleServicesPath)) {
      console.log('✅ google-services.json file exists');
      
      try {
        const googleServices = JSON.parse(fs.readFileSync(googleServicesPath, 'utf8'));
        
        if (googleServices.project_info && googleServices.project_info.project_id) {
          console.log(`✅ Project ID in file: ${googleServices.project_info.project_id}`);
        } else {
          console.log('❌ Missing project_info in google-services.json');
        }
        
        if (googleServices.client && 
            googleServices.client[0] && 
            googleServices.client[0].api_key && 
            googleServices.client[0].api_key[0] && 
            googleServices.client[0].api_key[0].current_key) {
          console.log(`✅ API Key exists in file: ${googleServices.client[0].api_key[0].current_key.substring(0, 5)}...`);
        } else {
          console.log('❌ Missing API key in google-services.json');
        }
      } catch (e) {
        console.log('❌ Error parsing google-services.json:', e);
      }
    } else {
      console.log('❌ google-services.json file not found at:', googleServicesPath);
    }
    
    console.log('\n=== Firebase configuration test complete ===');
    console.log('\nNext steps:');
    console.log('1. If tests passed, try rebuilding your Android app');
    console.log('2. Clean project: cd android && ./gradlew clean');
    console.log('3. If still failing, check Android build for other errors');
    
  } catch (error) {
    console.error('Error testing Firebase config:', error);
  }
};

main(); 