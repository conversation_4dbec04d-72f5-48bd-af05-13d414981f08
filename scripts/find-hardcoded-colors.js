/**
 * Find Hardcoded Colors Script
 * 
 * This script scans the codebase for files that contain hardcoded color values
 * and outputs a report to help with migration to the centralized color system.
 * 
 * Usage: node scripts/find-hardcoded-colors.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Directories to exclude from the search
const EXCLUDED_DIRS = [
  'node_modules',
  '.git',
  '.expo',
  'build',
  'dist',
  'ios',
  'android',
  'scripts' // Exclude this script itself
];

// File extensions to include in the search
const INCLUDED_EXTENSIONS = [
  '.js',
  '.jsx',
  '.ts',
  '.tsx'
];

// Regex patterns to match hardcoded colors
const COLOR_PATTERNS = [
  /'#[0-9A-Fa-f]{3,8}'/g, // '#fff', '#FFFFFF', etc. in single quotes
  /"#[0-9A-Fa-f]{3,8}"/g, // "#fff", "#FFFFFF", etc. in double quotes
  /color:\s*['"]#[0-9A-Fa-f]{3,8}['"]/g, // color: '#fff', color: "#FFFFFF", etc.
  /backgroundColor:\s*['"]#[0-9A-Fa-f]{3,8}['"]/g, // backgroundColor: '#fff', etc.
  /borderColor:\s*['"]#[0-9A-Fa-f]{3,8}['"]/g, // borderColor: '#fff', etc.
  /shadowColor:\s*['"]#[0-9A-Fa-f]{3,8}['"]/g, // shadowColor: '#fff', etc.
];

// Result object to store findings
const results = {
  totalFiles: 0,
  filesWithColors: 0,
  colorsByFile: {}
};

/**
 * Recursively walks directories looking for files to scan
 */
function walkDir(dir) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // Skip excluded directories
      if (!EXCLUDED_DIRS.includes(file)) {
        walkDir(filePath);
      }
    } else if (stat.isFile()) {
      // Only check files with included extensions
      const ext = path.extname(filePath);
      if (INCLUDED_EXTENSIONS.includes(ext)) {
        scanFile(filePath);
      }
    }
  });
}

/**
 * Scans a file for hardcoded color values
 */
function scanFile(filePath) {
  results.totalFiles++;
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(process.cwd(), filePath);
    const colors = findColorsInContent(content);
    
    if (colors.length > 0) {
      results.filesWithColors++;
      results.colorsByFile[relativePath] = colors;
    }
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error.message);
  }
}

/**
 * Extracts all color values from file content
 */
function findColorsInContent(content) {
  let colors = [];
  
  COLOR_PATTERNS.forEach(pattern => {
    const matches = content.match(pattern);
    if (matches) {
      colors = colors.concat(matches);
    }
  });
  
  // Remove duplicates
  return [...new Set(colors)];
}

/**
 * Generates a report of findings
 */
function generateReport() {
  const filesWithColors = Object.keys(results.colorsByFile);
  
  console.log('\n=== Hardcoded Colors Report ===\n');
  console.log(`Total files scanned: ${results.totalFiles}`);
  console.log(`Files with hardcoded colors: ${results.filesWithColors}`);
  console.log(`Percentage: ${Math.round((results.filesWithColors / results.totalFiles) * 100)}%`);
  
  console.log('\n=== Files to Migrate ===\n');
  
  // Sort files by number of color occurrences (descending)
  filesWithColors
    .sort((a, b) => results.colorsByFile[b].length - results.colorsByFile[a].length)
    .forEach(file => {
      const colors = results.colorsByFile[file];
      console.log(`${file} (${colors.length} color instances)`);
      
      // Show up to 5 example colors per file
      if (colors.length > 0) {
        console.log('  Examples:');
        colors.slice(0, 5).forEach(color => {
          console.log(`    ${color}`);
        });
        if (colors.length > 5) {
          console.log(`    ... and ${colors.length - 5} more`);
        }
        console.log('');
      }
    });
  
  console.log('\n=== Migration Strategy ===\n');
  console.log('1. Start with components with the most color instances');
  console.log('2. Use the useColors() hook from hooks/useThemeColor.ts');
  console.log('3. Follow the guide in constants/ColorMigrationGuide.md');
  console.log('\nRun this script again after migrations to track progress.');
}

console.log('Scanning for hardcoded colors...');
walkDir(process.cwd());
generateReport(); 