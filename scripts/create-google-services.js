/**
 * <PERSON><PERSON>t to create a properly formatted google-services.json file
 * Run with: node scripts/create-google-services.js
 */

const fs = require('fs');
const path = require('path');

// Firebase project config from firebaseConfig.ts
const firebaseConfig = {
  apiKey: "AIzaSyCh5gE623XX2PvCGIBOxJpqzKBicZNHVek",
  projectId: "salescrm-f2511",
  appId: "1:880620206085:android:d6697f8f77f46b66b3c46c",
  messagingSenderId: "880620206085",
  databaseURL: "https://salescrm-f2511.firebaseio.com",
  storageBucket: "salescrm-f2511.appspot.com"
};

// Parse the appId to extract project number
const appIdParts = firebaseConfig.appId.split(':');
const projectNumber = appIdParts[1] || firebaseConfig.messagingSenderId;

// Package name from android/app/build.gradle
const packageName = "com.anonymous.salescrm";

// Create google-services.json structure
const googleServicesJson = {
  "project_info": {
    "project_number": projectNumber,
    "firebase_url": firebaseConfig.databaseURL,
    "project_id": firebaseConfig.projectId,
    "storage_bucket": firebaseConfig.storageBucket
  },
  "client": [
    {
      "client_info": {
        "mobilesdk_app_id": firebaseConfig.appId,
        "android_client_info": {
          "package_name": packageName
        }
      },
      "oauth_client": [],
      "api_key": [
        {
          "current_key": firebaseConfig.apiKey
        }
      ],
      "services": {
        "appinvite_service": {
          "other_platform_oauth_client": []
        }
      }
    }
  ],
  "configuration_version": "1"
};

// Output paths
const googleServicesPath = path.join(__dirname, '..', 'android', 'app', 'google-services.json');

// Write the file
fs.writeFileSync(
  googleServicesPath, 
  JSON.stringify(googleServicesJson, null, 2),
  'utf8'
);

console.log(`✅ Created google-services.json at ${googleServicesPath}`);
console.log('Please rebuild your Android app for changes to take effect.'); 