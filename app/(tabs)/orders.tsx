import React, { useState, useMemo, useEffect, useRef, useCallback } from 'react';
import { View, FlatList, Text, StyleSheet, TouchableOpacity, ActivityIndicator, TextInput, Dimensions, Modal, Animated, Alert, ScrollView, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView, Edge } from 'react-native-safe-area-context';
import { useRouter, useFocusEffect } from 'expo-router';
import { Deal, DealsResponse } from '../models/Deal';
import { apiService } from '../../services/ApiService';
import { API_ENDPOINTS } from '../../config/api';
import { useColors } from '@/hooks/useThemeColor';
import { CALL_FROM, readUserScope } from '../ui/utils';
import { useAuth } from '../../context/AuthContext';
import { FiltersResponse, SavedFilter } from '../models/Filter';
import { Appbar } from 'react-native-paper';
import RecordsPopup from '../../components/RecordsPopup';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Add storage key constant
export const ORDERS_FILTER_STORAGE_KEY = '@orders_filter';

// Helper function to capitalize first letter of each word
const capitalizeWords = (str: string | null): string => {
  if (!str) return '';
  return str
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

const OpportunityItem = ({ name, amount, deal_stage, closed_date, id }: Deal) => {
  const colors = useColors();
  const styles = useMemo(() => createStyles(colors), [colors]);
  const router = useRouter();

  const handleOpportunityPress = () => {
    console.log('Opportunity tapped, navigating to dealDetails with ID:', id);
    // Navigate to opportunity details using the object-based approach
    router.push({
      pathname: 'dealDetails',
      params: { dealId: id.toString() }
    } as any);
  };

  return (
    <TouchableOpacity 
      style={styles.item} 
      onPress={handleOpportunityPress}
      activeOpacity={0.7}
    >
      <View style={styles.textContainer}>
        <Text style={styles.title}>{capitalizeWords(name)}</Text>
        <Text style={styles.status}>{capitalizeWords(deal_stage) || 'New'}</Text>
        {closed_date && <Text style={styles.closedText}>Closes: {closed_date}</Text>}
      </View>
      <View style={styles.amountContainer}>
        <Text style={styles.amount}>₹ {(Number(amount) || 0).toFixed(2)}</Text>
      </View>
    </TouchableOpacity>
  );
};

interface FilterPayload {
  title: string;
  filter: Array<{
    field_name: string;
    field_label: string;
    value_id: string;
    contains: string;
  }>;
  share_with: string;
  selected_users: string[];
  accessibility: string;
  prevent_sharing: boolean;
}

function DealsScreen() {
  const [deals, setDeals] = useState<Deal[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [showPopup, setShowPopup] = useState(false);
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredDeals, setFilteredDeals] = useState<Deal[]>([]);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [drawerSearchQuery, setDrawerSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<SavedFilter | null>(null);
  const [savedFilters, setSavedFilters] = useState<SavedFilter[]>([]);
  const [filteredSavedFilters, setFilteredSavedFilters] = useState<SavedFilter[]>([]);
  const slideAnim = useRef(new Animated.Value(-Dimensions.get('window').width * 0.8)).current;
  const [isSearchFocused, setIsSearchFocused] = useState(false);

  const router = useRouter();
  const colors = useColors();
  const { user } = useAuth();
  const styles = useMemo(() => createStyles(colors), [colors]);
  const [accessScope, setAccessScope] = useState<any>(null);
  
  useFocusEffect(
  useCallback(() => {
    const fetchAccessScope = async () => {
      const scope = await readUserScope();
      console.log("accessScope:orders  " + JSON.stringify(scope));
      setAccessScope(scope);
    };
    fetchAccessScope();
  }, [])
);

  
  // Sales activities data
  const SALES_ACTIVITIES = [
    { 
      id: '1', 
      title: 'Task', 
      icon: 'clipboard-outline' as const, 
      navigate: () => router.push({
        pathname: '/tasks/create',
        params: { callFrom: 'orders' }
      } as any)
    },
    { 
      id: '2', 
      title: 'Meeting', 
      icon: 'calendar-outline' as const, 
      navigate: () => router.push({
        pathname: '/meetings/createMeeting',
        params: { callFrom: 'orders' }
      } as any)
    },
  ];

  // Filter deals when search query changes
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredDeals(deals);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = deals.filter(deal => 
        (deal.name && deal.name.toLowerCase().includes(query)) ||
        (deal.deal_stage && deal.deal_stage.toLowerCase().includes(query)) ||
        (deal.amount && deal.amount.toString().includes(query))
      );
      setFilteredDeals(filtered);
    }
  }, [searchQuery, deals]);

  // Update the filter search effect
  useEffect(() => {
    const searchFilters = () => {
      try {
        console.log('🔍 Search query:', drawerSearchQuery);
        console.log('📋 Total filters before search:', savedFilters.length);

        if (!drawerSearchQuery.trim()) {
          console.log('📋 Showing all filters - empty search');
          setFilteredSavedFilters(savedFilters);
          return;
        }

        const query = drawerSearchQuery.toLowerCase().trim();
        const filtered = savedFilters.filter(filter => {
          // Check if filter and its properties exist before searching
          if (!filter) return false;

          const nameMatch = filter.name?.toLowerCase().includes(query);
          
          // Search in filter conditions
          const filterConditionsMatch = filter.filters?.some(f => 
            f.name?.toLowerCase().includes(query) ||
            f.filter?.toLowerCase().includes(query) ||
            f.contains?.toLowerCase().includes(query)
          );

          // Search in accessibility and sharing settings
          const accessibilityMatch = filter.accessibility?.toLowerCase().includes(query);
          const shareWithMatch = filter.share_with?.toLowerCase().includes(query);

          const isMatch = nameMatch || filterConditionsMatch || accessibilityMatch || shareWithMatch;
          
          if (isMatch) {
            console.log('✅ Match found:', filter.name);
          }

          return isMatch;
        });

        console.log('🔍 Search results:', {
          query: drawerSearchQuery,
          totalFilters: savedFilters.length,
          matchedFilters: filtered.length
        });

        setFilteredSavedFilters(filtered);
      } catch (error) {
        console.error('❌ Error in search:', error);
        // Fallback to showing all filters in case of error
        setFilteredSavedFilters(savedFilters);
      }
    };

    // Debounce the search to avoid too frequent updates
    const debounceTimeout = setTimeout(searchFilters, 300);
    return () => clearTimeout(debounceTimeout);
  }, [drawerSearchQuery, savedFilters]);

  const handleSearchInputChange = (text: string) => {
    console.log('🔤 Search input changed:', text);
    setDrawerSearchQuery(text);
  };

  // Drawer animation
  useEffect(() => {
    Animated.timing(slideAnim, {
      toValue: isDrawerOpen ? 0 : -Dimensions.get('window').width * 0.8,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [isDrawerOpen]);

  const fetchDeals = async (showLoader = true, filterPayload?: FilterPayload) => {
    try {
      if (showLoader) setLoading(true);
      setError(null);
      
      let response;
      
        console.log('📤 Fetching all deals');
        response = await apiService.post<DealsResponse>(API_ENDPOINTS.DEAL_LIST_WITH_FILTERS,filterPayload);
      
      
     // console.log('📥 Deals API Response:', JSON.stringify(response));
      
      if (response.data?.data) {
        // Create a copy of the deals array
        const dealsData = [...response.data.data];
        
        // Sort deals by created_at or closed_date (newest first)
        const sortedDeals = dealsData.sort((a, b) => {
          // First try to sort by created_at if available
          if (a.created_at && b.created_at) {
            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
          }
          
          // If created_at is not available, try closed_date
          if (a.closed_date && b.closed_date) {
            return new Date(b.closed_date).getTime() - new Date(a.closed_date).getTime();
          }
          
          // If one has created_at and the other doesn't, prioritize the one with created_at
          if (a.created_at && !b.created_at) return -1;
          if (!a.created_at && b.created_at) return 1;
          
          // If one has closed_date and the other doesn't, prioritize the one with closed_date
          if (a.closed_date && !b.closed_date) return -1;
          if (!a.closed_date && b.closed_date) return 1;
          
          // If neither has dates, maintain original order
          return 0;
        });
        
        console.log('✅ Deals sorted successfully: newest first');
        setDeals(sortedDeals);
        setFilteredDeals(sortedDeals);
      } else {
        console.log('⚠️ No deals found');
        setDeals([]);
        setFilteredDeals([]);
      }
    } catch (err) {
      console.error('❌ Error fetching deals:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch deals');
      setDeals([]);
      setFilteredDeals([]);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const fetchSavedFilters = async () => {
    try {
      console.log('🔄 Starting to fetch saved filters...');
      const response = await apiService.get<FiltersResponse>(API_ENDPOINTS.DEALS_FILTERS);
      console.log('📥 Raw API Response:', JSON.stringify(response, null, 2));

      if (response?.data?.filters) {
        setSavedFilters(response.data.filters);
        setFilteredSavedFilters(response.data.filters);
        console.log('✅ Filters loaded:', response.data.filters.length);
      } else {
        console.log('⚠️ No filters found in response');
        setSavedFilters([]);
        setFilteredSavedFilters([]);
      }
    } catch (error) {
      console.error('❌ Error fetching filters:', error);
      Alert.alert('Error', 'Failed to fetch filters');
    }
  };

  const handleDeleteFilter = async (filterId: number) => {
    try {
      await apiService.delete(`${API_ENDPOINTS.DEALS_FILTERS}/${filterId}`);
      if (selectedFilter?.id === filterId) {
        setSelectedFilter(null);
        await saveFilterToStorage(null);
      }
      await fetchSavedFilters();
      await fetchDeals(true);
      Alert.alert('Success', 'Filter deleted successfully');
    } catch (error) {
      console.error('❌ Error deleting filter:', error);
      Alert.alert('Error', 'Failed to delete filter');
    }
  };

  const handleEditFilter = async (filter: SavedFilter, event: any) => {
    try {
      event.stopPropagation(); // Prevent filter selection when clicking edit
      console.log('✏️ Editing filter:', {
        id: filter.id,
        name: filter.name,
        filters: filter.filters
      });

      router.push({
        pathname: 'ui/product/save-view',
        params: { 
          callFrom: CALL_FROM.DEAL_LIST,
          filterData: JSON.stringify(filter),
          isEdit: "true"
        }
      } as any);
    } catch (error) {
      console.error('❌ Error in handleEditFilter:', error);
      Alert.alert('Error', 'Failed to navigate to edit page');
    }
  };

  const handleFilterSelect = async (filter: SavedFilter) => {
    try {
      console.log('🎯 Filter Selected:', {
        id: filter.id,
        name: filter.name,
        filters: filter.filters
      });

      setSelectedFilter(filter);
      await saveFilterToStorage(filter);
      setIsDrawerOpen(false);

      // Convert SavedFilter to FilterPayload format
      const filterPayload: FilterPayload = {
        title: filter.name,
        filter: filter.filters.map((f: any) => {
          console.log('📍 Mapping filter field:', {
            name: f.name,
            field_id: f.field_id,
            contains: f.contains,
            filter: Array.isArray(f.filter) ? f.filter : [f.filter],
          });
          return {
            field_name: f.field_id,
            field_label: f.name,
            value_id:( f.contains.toLowerCase().includes('contain')||f.contains.toLowerCase().includes('between') ) ? Array.isArray(f.filter) ? f.filter : [f.filter] : f.filter  , // Ensure value_id is always an array
            contains: f.contains
          };
        }),
        share_with: filter.share_with || '',
        selected_users: filter.users || [],
        accessibility: filter.accessibility || 'Private',
        prevent_sharing: false
      };

      // Use fetchDeals with the filter payload
      await fetchDeals(true, filterPayload);
    } catch (error) {
      console.error('❌ Error in handleFilterSelect:', error);
      Alert.alert('Error', 'Failed to apply filter');
    }
  };

  // Drawer animation functions
  const handleOpenDrawer = () => {
    console.log('🔓 Opening drawer');
    setIsDrawerOpen(true);
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const handleCloseDrawer = () => {
    console.log('🔒 Closing drawer');
    Animated.timing(slideAnim, {
      toValue: -Dimensions.get('window').width * 0.8,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setIsDrawerOpen(false);
    });
  };

  const renderFilterItem = (filter: SavedFilter, index: number) => {
    const handlePress = () => {
      console.log('👆 Filter item pressed:', filter.name);
      handleFilterSelect(filter);
    };

    const handleEditPress = (event: any) => {
      event.stopPropagation();
      console.log('✏️ Edit pressed for filter:', filter.name);
      handleEditFilter(filter, event);
    };

    const handleDeletePress = (event: any) => {
      event.stopPropagation();
      console.log('🗑️ Delete pressed for filter:', filter.name);
      Alert.alert(
        'Delete Filter',
        `Are you sure you want to delete "${filter.name}"?`,
        [
          {
            text: 'Cancel',
            style: 'cancel'
          },
          {
            text: 'Delete',
            style: 'destructive',
            onPress: () => handleDeleteFilter(filter.id)
          }
        ]
      );
    };

    return (
      <TouchableOpacity
        key={filter.id || index}
        style={[
          styles.filterItem,
          selectedFilter?.id === filter.id && styles.selectedFilterItem
        ]}
        onPress={handlePress}
        activeOpacity={0.7}
      >
        <View style={styles.filterItemContent}>
          <View style={styles.filterItemHeader}>
            <Text style={[
              styles.filterItemText,
              selectedFilter?.id === filter.id && styles.selectedFilterItemText
            ]}>
              {filter.name}
            </Text>
            <View style={styles.filterItemActions}>
              <TouchableOpacity
                onPress={handleEditPress}
                style={styles.actionButton}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Ionicons name="pencil" size={18} color={colors.primary} />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={handleDeletePress}
                style={styles.actionButton}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Ionicons name="trash-outline" size={18} color="#FF4444" />
              </TouchableOpacity>
            </View>
          </View>
          {filter.filters && filter.filters.length > 0 && (
            <View style={styles.filterDetails}>
              {filter.filters.map((field, fieldIndex) => (
                <Text key={fieldIndex} style={styles.filterFieldText}>
                  {field.name}: {field.filter} ({field.contains})
                </Text>
              ))}
            </View>
          )}
          <Text style={styles.accessibilityText}>
            {filter.accessibility || 'Private'} • {filter.share_with || 'Not shared'}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderSideNavigationView = () => (
    <Modal
      visible={isDrawerOpen}
      transparent={true}
      onRequestClose={handleCloseDrawer}
      animationType="none"
    >
      <TouchableOpacity
        style={styles.drawerOverlay}
        activeOpacity={1}
        onPress={handleCloseDrawer}
      >
        <Animated.View 
          style={[
            styles.drawerContent,
            {
              transform: [{ translateX: slideAnim }]
            }
          ]}
        >
          <View style={styles.drawerHeader}>
            <Text style={styles.drawerTitle}>
              Saved Filters {filteredSavedFilters.length > 0 && `(${filteredSavedFilters.length})`}
            </Text>
            <TouchableOpacity 
              onPress={handleCloseDrawer}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Ionicons name="close" size={24} color="#fff" />
            </TouchableOpacity>
          </View>

          <View style={styles.filterSearchContainer}>
            <View style={styles.searchInputContainer}>
              <Ionicons 
                name="search" 
                size={20} 
                color="#999" 
                style={styles.searchIcon} 
              />
              <TextInput
                style={styles.filterSearchInput}
                placeholder="Search filters..."
                value={drawerSearchQuery}
                onChangeText={setDrawerSearchQuery}
                placeholderTextColor="#999"
                autoCapitalize="none"
                autoCorrect={false}
              />
              {drawerSearchQuery.length > 0 && (
                <TouchableOpacity 
                  onPress={() => {
                    console.log('🧹 Clearing search');
                    setDrawerSearchQuery('');
                    setFilteredSavedFilters(savedFilters);
                  }}
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                >
                  <Ionicons name="close-circle" size={20} color="#999" />
                </TouchableOpacity>
              )}
            </View>
          </View>

          <ScrollView style={styles.filterList} showsVerticalScrollIndicator={false}>
            {filteredSavedFilters.map((filter, index) => renderFilterItem(filter, index))}
            {filteredSavedFilters.length === 0 && (
              <View style={styles.emptySearchContainer}>
                <Text style={styles.emptySearchText}>
                  {drawerSearchQuery 
                    ? `No filters found matching "${drawerSearchQuery}"`
                    : 'No saved filters available'}
                </Text>
              </View>
            )}
          </ScrollView>
        </Animated.View>
      </TouchableOpacity>
    </Modal>
  );

  // Update header to use handleOpenDrawer
  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: '#0F96BB' }]}>
      {isSearchActive ? (
        <View style={styles.searchHeader}>
          <TouchableOpacity onPress={() => {
            setIsSearchActive(false);
            setSearchQuery('');
          }}>
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <TextInput
            style={styles.searchInput}
            placeholder="Search opportunities..."
            placeholderTextColor="rgba(255, 255, 255, 0.7)"
            value={searchQuery}
            onChangeText={setSearchQuery}
            autoFocus
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={24} color="#FFFFFF" />
            </TouchableOpacity>
          )}
        </View>
      ) : (
        <>
          <View style={styles.headerLeft}>
            <TouchableOpacity 
              style={styles.headerTitleContainer}
              onPress={handleOpenDrawer}
            >
              <Text style={[styles.headerTitle, { color: '#FFFFFF' }]}>
                {selectedFilter?.name || "Opportunities"}
              </Text>
              <Ionicons 
                name={isDrawerOpen ? "chevron-up" : "chevron-down"} 
                size={20} 
                color="#FFFFFF" 
              />
              {selectedFilter && (
                <TouchableOpacity 
                  onPress={handleClearFilter}
                  style={styles.clearFilterButton}
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                >
                  <Ionicons name="close-circle" size={16} color="#FFFFFF" />
                </TouchableOpacity>
              )}
            </TouchableOpacity>
          </View>
          <View style={styles.headerRight}>
            <TouchableOpacity 
              style={styles.headerIcon}
              onPress={() => setIsSearchActive(true)}
            >
              <Ionicons name="search" size={24} color="#FFFFFF" />
            </TouchableOpacity>
           
              <Appbar.Action 
          icon="filter-outline"
          color="#fff" 
          onPress={() => {
            router.push({
              pathname: '/ui/product/ProductFiltersPage',
              params: { callFrom: CALL_FROM.DEAL_LIST }
            } as any)
          }}
        />
            
          </View>
        </>
      )}
    </View>
  );

  // Add functions for filter persistence
  const saveFilterToStorage = async (filter: SavedFilter | null) => {
    try {
      if (filter) {
        console.log('📱 Saving filter to storage:', filter);
        await AsyncStorage.setItem(ORDERS_FILTER_STORAGE_KEY, JSON.stringify(filter));
      } else {
        console.log('📱 Clearing filter from storage');
        await AsyncStorage.removeItem(ORDERS_FILTER_STORAGE_KEY);
      }
    } catch (error) {
      console.error('❌ Error saving filter to storage:', error);
    }
  };

  const loadFilterFromStorage = async () => {
    try {
      const savedFilterStr = await AsyncStorage.getItem(ORDERS_FILTER_STORAGE_KEY);
      console.log('📱 Raw saved filter from storage:', savedFilterStr);
      
      if (savedFilterStr) {
        const filter = JSON.parse(savedFilterStr) as SavedFilter;
        console.log('📱 Parsed saved filter:', filter);
        
        // Validate the filter has required fields
        if (filter && filter.id && filter.name) {
          setSelectedFilter(filter);
          return filter;
        } else {
          console.log('⚠️ Invalid filter data in storage, clearing...');
          await AsyncStorage.removeItem(ORDERS_FILTER_STORAGE_KEY);
        }
      }
    } catch (error) {
      console.error('❌ Error loading filter from storage:', error);
      // Clear invalid storage data
      await AsyncStorage.removeItem(ORDERS_FILTER_STORAGE_KEY);
    }
    return null;
  };

  // Update useFocusEffect to load saved filter
  useFocusEffect(
    React.useCallback(() => {
      console.log('📱 Orders screen focused - loading saved filter');
      const loadData = async () => {
        try {
          // First load the saved filter
          const savedFilter = await loadFilterFromStorage();
          console.log('📱 Loaded saved filter:', savedFilter);
          
          // Then fetch deals with the filter if it exists
          if (savedFilter) {
            const filterPayload = {
              title: savedFilter.name,
              filter: savedFilter.filters.map((f: any) => ({
                field_name: f.field_id,
                field_label: f.name,
                value_id: (f.contains.toLowerCase().includes('contain') || f.contains.toLowerCase().includes('between')) 
                  ? Array.isArray(f.filter) ? f.filter : [f.filter] 
                  : f.filter,
                contains: f.contains || "contains"
              })),
              share_with: savedFilter.share_with || "selected_users",
              selected_users: savedFilter.users || ["1"],
              accessibility: savedFilter.accessibility || "Public",
              prevent_sharing: false
            };
            console.log('📱 Applying saved filter:', filterPayload);
            await fetchDeals(true, filterPayload);
          } else {
            console.log('📱 No saved filter found, fetching all deals');
            await fetchDeals(true);
          }
          
          // Finally fetch available filters
          await fetchSavedFilters();
        } catch (error) {
          console.error('❌ Error loading data:', error);
          const errorMessage = error instanceof Error 
            ? error.message 
            : 'Failed to load deals data';
          setError(errorMessage);
          Alert.alert('Error', errorMessage);
        }
      };
      loadData();
    }, [])
  );

  const handleRefresh = () => {
    setRefreshing(true);
    fetchDeals(false);
  };

  // Add handleClearFilter function
  const handleClearFilter = async () => {
    try {
      console.log('🧹 Clearing selected filter');
      setSelectedFilter(null);
      await saveFilterToStorage(null);
      await fetchDeals(true);
      handleCloseDrawer();
    } catch (error) {
      console.error('❌ Error clearing filter:', error);
      Alert.alert('Error', 'Failed to clear filter');
    }
  };

  if (loading && !refreshing) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Loading Opportunities...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => fetchDeals()}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top'] as Edge[]}>
      <StatusBar style="light" />
      
      {renderHeader()}
      {renderSideNavigationView()}

      <FlatList
        style={styles.flatList}
        contentContainerStyle={filteredDeals.length === 0 ? styles.emptyContentContainer : undefined}
        data={filteredDeals}
        renderItem={({ item }) => <OpportunityItem {...item} />}
        keyExtractor={(item) => item.id.toString()}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <Ionicons
              name="briefcase-outline"
              size={64}
              color="#CCCCCC"
              style={styles.emptyIcon}
            />
            <Text style={styles.emptyText}>
              {searchQuery ? 'No deals found matching your search' : 'No deals found'}
            </Text>
            {searchQuery && (
              <Text style={styles.emptySubText}>
                Try adjusting your search terms or filters
              </Text>
            )}
          </View>
        )}
      />
 {accessScope?.contact?.create === true && accessScope?.task?.create === true &&
          accessScope?.appointment?.create === true && accessScope?.notes?.create === true && (
      <TouchableOpacity 
        style={styles.fab}
        onPress={() => setShowPopup(true)}
      >
        <Ionicons name="add" size={24} color="#FFFFFF" />
      </TouchableOpacity>
      ) }
      {/* Records popup */}
      <RecordsPopup 
        visible={showPopup}
        onClose={() => setShowPopup(false)}
        callFrom="orders"
      />
    </SafeAreaView>
  );
}

const createStyles = (colors: ReturnType<typeof useColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#EFEFEF',
    backgroundColor: '#0F96BB',
    height: 64,
    position: 'relative',
    zIndex: 1,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: '#FFFFFF',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: 48,
    justifyContent: 'flex-end',
  },
  headerIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  searchHeader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    backgroundColor: '#0F96BB',
    zIndex: 2,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
    color: '#ffffff',
    marginHorizontal: 8,
    paddingHorizontal: 8,
  },
  item: { 
    flexDirection: 'row', 
    padding: 15, 
    borderBottomWidth: 1, 
    borderBottomColor: colors.background.tertiary 
  },
  textContainer: { 
    flex: 1 
  },
  
  loadingText: {
    fontSize: 18,
    fontWeight: '500',
    color: '#0F96BB',
    marginTop: 16,
  },
  title: { 
    fontSize: 16, 
    fontWeight: 'bold',
    color: colors.text.primary,
  },
  status: { 
    fontSize: 14, 
    color: '#4caf50',
    marginTop: 4,
  },
  closedText: { 
    fontSize: 12, 
    color: colors.text.secondary,
    marginTop: 2,
  },
  amountContainer: { 
    justifyContent: 'center' 
  },
  amount: { 
    fontSize: 14, 
    fontWeight: 'bold', 
    color: colors.text.primary, 
    backgroundColor: colors.background.tertiary, 
    padding: 5, 
    borderRadius: 5 
  },
  fab: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: colors.text.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  errorText: {
    color: 'red',
    fontSize: 16,
    marginBottom: 16,
    textAlign: 'center',
    paddingHorizontal: 32,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: colors.text.inverse,
    fontSize: 16,
    fontWeight: '500',
  },
  flatList: {
    flex: 1,
  },
  emptyContentContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 60,
  },
  emptyIcon: {
    marginBottom: 20,
  },
  emptyText: {
    fontSize: 18,
    color: '#777777',
    textAlign: 'center',
    fontWeight: '500',
    marginBottom: 8,
  },
  emptySubText: {
    fontSize: 14,
    color: '#999999',
    textAlign: 'center',
    marginTop: 8,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  popup: {
    backgroundColor: colors.background.primary,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 16,
    paddingBottom: 30,
    maxHeight: '70%',
  },
  popupTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text.primary,
    paddingHorizontal: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
  },
  popupSection: {
    marginTop: 16,
  },
  popupSectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text.primary,
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  popupItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  popupIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  popupItemText: {
    fontSize: 16,
    color: colors.text.primary,
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  drawerOverlay: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  drawerContent: {
    backgroundColor: colors.primary,
    height: '100%',
    width: '80%',
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    borderRightWidth: 1,
    borderRightColor: '#eee',
    shadowColor: '#000',
    shadowOffset: { width: 2, height: 0 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    paddingTop: Platform.OS === 'ios' ? 47 : 0,
  },
  drawerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 12,
    height: 56,
    zIndex: 1,
  },
  drawerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  filterSearchContainer: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#fff',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 40,
  },
  filterSearchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    height: 40,
    paddingVertical: 0,
  },
  filterList: {
    flex: 1,
    backgroundColor: '#fff',
  },
  filterItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#fff',
  },
  filterItemContent: {
    flex: 1,
  },
  filterItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  filterItemText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  selectedFilterItem: {
    backgroundColor: '#f5f5f5',
  },
  selectedFilterItemText: {
    color: colors.primary,
    fontWeight: '500',
  },
  searchIcon: {
    marginRight: 8,
  },
  filterDetails: {
    marginTop: 8,
    paddingLeft: 8,
  },
  filterFieldText: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  accessibilityText: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  emptySearchContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptySearchText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  drawerOverlayTouch: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
  },
  searchResultsText: {
    fontSize: 12,
    color: '#666',
    marginTop: 8,
    textAlign: 'center',
  },
  filterItemActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  actionButton: {
    padding: 4,
    borderRadius: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  clearFilterButton: {
    marginLeft: 8,
    padding: 4,
  },
});

export default DealsScreen;
