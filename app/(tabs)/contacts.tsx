import React, { useState, useMemo, useEffect, useRef, useCallback } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, FlatList, ActivityIndicator, TextInput, Dimensions, Modal, Animated, Alert, ScrollView, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView, Edge } from 'react-native-safe-area-context';
import { useRouter, useFocusEffect } from 'expo-router';
import { useColors } from '../../hooks/useThemeColor';
import { Contact as BaseContact, ContactsResponse, DisplayContact } from '../models/Contact';
import { apiService } from '../../services/ApiService';
import { API_ENDPOINTS } from '../../config/api';
import { CALL_FROM, capitalizeWords, getAvatarColor, getContrastingTextColor, RECORD_TYPES, SALES_ACTIVITIES, useAppData, IconType, Activity, readUserScope, showSnackbar, checkAllAdd } from '../ui/utils';
import { useAuth } from '../../context/AuthContext';
import { SavedFilter, FiltersResponse } from '../models/Filter';
import { Appbar } from 'react-native-paper';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { RECORDS_STORAGE_KEY, SALES_ACTIVITIES_STORAGE_KEY } from '@/services/AuthService';
import { router } from 'expo-router';
import { useRecords } from '../../context/RecordsContext';
import RecordsPopup from '../../components/RecordsPopup';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { check } from 'react-native-permissions';

interface FilterPayload {
  title: string;
  filter: Array<{
    field_name: string;
    field_label: string;
    value_id: string | string[];
    contains: string;
  }>;
  share_with: string;
  selected_users: string[];
  accessibility: string;
  prevent_sharing: boolean;
}

// Add constant for filter storage key
export const CONTACTS_FILTER_STORAGE_KEY = '@contacts_filter';

// Add type for styles
type ContactScreenStyles = ReturnType<typeof createStyles> & {
  clearFilterButton: {
    marginLeft: number;
    padding: number;
  };
};

export default function ContactsScreen() {
  const { user } = useAuth();
  const [showPopup, setShowPopup] = useState(false);
  const [contacts, setContacts] = useState<DisplayContact[]>([]);
  const [loading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredContacts, setFilteredContacts] = useState<DisplayContact[]>([]);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [drawerSearchQuery, setDrawerSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<SavedFilter | null>(null);
  const [savedFilters, setSavedFilters] = useState<SavedFilter[]>([]);
  const [filteredSavedFilters, setFilteredSavedFilters] = useState<SavedFilter[]>([]);
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const slideAnim = useRef(new Animated.Value(-Dimensions.get('window').width * 0.8)).current;
  const { recordTypes, salesActivities } = useAppData();
  const router = useRouter();
  const colors = useColors();
  const styles = useMemo(() => createStyles(colors) as ContactScreenStyles, [colors]);
  const [accessScope, setAccessScope] = useState<any>(null);
  
  useFocusEffect(
  useCallback(() => {
    const fetchAccessScope = async () => {
      const scope = await readUserScope();
      console.log("accessScope:contac " + JSON.stringify(scope));
      setAccessScope(scope);
    };
    fetchAccessScope();
  }, [])
);


  
  
  // var SALES_ACTIVITIES = [
  //   { 
  //     id: '1', 
  //     title: 'Task', 
  //     icon: 'clipboard-outline' as const, 
  //     navigate: () => router.push({ pathname: '/tasks/create' } as any)
  //   },
  //   { 
  //     id: '2', 
  //     title: 'Meeting', 
  //     icon: 'calendar-outline' as const, 
  //     navigate: () => router.push({ pathname: '/meetings/createMeeting' } as any)
  //   },
  // ];

  const fetchContacts = async (showLoader = true, filterPayload?: FilterPayload) => {
    try {
      
      console.log("fetchContacts", recordTypes.length, salesActivities.length +" "+refreshing);
      if (showLoader) setIsLoading(true);
      setError(null);
      console.log('Fetching contacts with payload:', filterPayload);
      
      let response = await apiService.post<ContactsResponse>(API_ENDPOINTS.CONTACTS_LIST, filterPayload);
      
      console.log('Contacts API Response:', JSON.stringify(response));
      
      if (response.data?.contacts) {
        const transformedData: DisplayContact[] = response.data.contacts.map((contact: BaseContact) => {
          const firstName = contact.first_name || '';
          const lastName = contact.last_name || '';
          const fullName = `${firstName} ${lastName}`.trim();
          const capitalizedName = capitalizeWords(fullName);
          return {
            ...contact,
            name: capitalizedName,
            initial: capitalizedName[0] || 'A',
            contact: contact.mobile_number || contact.work_number || contact.telephone_numbers || '',
          };
        });

        const sortedData = [...transformedData].sort((a, b) => {
          if (!a.created_at && !b.created_at) return 0;
          if (!a.created_at) return -1;
          if (!b.created_at) return 1;
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        });

        setContacts(transformedData);
        setFilteredContacts(transformedData);
      } else {
        throw new Error('No contacts data received');
      }
    } catch (err) {
      console.error('Error fetching contacts:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch contacts');
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  // Add function to save filter to AsyncStorage
  const saveFilterToStorage = async (filter: SavedFilter | null) => {
    try {
      if (filter) {
        await AsyncStorage.setItem(CONTACTS_FILTER_STORAGE_KEY, JSON.stringify(filter));
      } else {
        await AsyncStorage.removeItem(CONTACTS_FILTER_STORAGE_KEY);
      }
    } catch (error) {
      console.error('Error saving filter to storage:', error);
    }
  };

  // Add function to load filter from AsyncStorage
  const loadFilterFromStorage = async () => {
    try {
      const savedFilter = await AsyncStorage.getItem(CONTACTS_FILTER_STORAGE_KEY);
      if (savedFilter) {
        const parsedFilter = JSON.parse(savedFilter) as SavedFilter;
        setSelectedFilter(parsedFilter);
        return parsedFilter;
      }
    } catch (error) {
      console.error('Error loading filter from storage:', error);
    }
    return null;
  };

  // Modify useFocusEffect to load saved filter
  useFocusEffect(
    React.useCallback(() => {
      console.log('Screen focused, loading saved filter...');
      const loadSavedFilter = async () => {
        const savedFilter = await loadFilterFromStorage();
        console.log("savedFiltersdfsdfsdf", savedFilter);
        if (savedFilter) {
          console.log("savedFiltersdfsdfsdf", "filter found");
          // Convert SavedFilter to FilterPayload format
          const filterPayload: FilterPayload = {
            title: savedFilter.name ,
            filter: savedFilter.filters ? savedFilter.filters.map((f: any) => ({
              field_name: f.field_id,
              field_label: f.name,
              value_id: (f.contains.toLowerCase().includes('contain') || f.contains.toLowerCase().includes('between')) 
                ? Array.isArray(f.filter) ? f.filter : [f.filter] 
                : f.filter,
              contains: f.contains
            })) : [],
            share_with: savedFilter.share_with || '',
            selected_users: savedFilter.users || [],
            accessibility: savedFilter.accessibility || 'Private',
            prevent_sharing: false
          };
          console.log("savedFiltersdfsdfsdf", filterPayload);
          await fetchContacts(true, filterPayload);
        } else {
          console.log("savedFiltersdfsdfsdf", "no filter");
          fetchContacts();
        }
      };
      loadSavedFilter();
      return () => {
        // Cleanup if needed
      };
    }, [])
  );

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      // If there's a selected filter, use it for refresh
      if (selectedFilter) {
        const filterPayload: FilterPayload = {
          title: selectedFilter.name,
          filter: selectedFilter.filters.map((f: any) => ({
            field_name: f.field_id,
            field_label: f.name,
            value_id: (f.contains.toLowerCase().includes('contain') || f.contains.toLowerCase().includes('between')) 
              ? Array.isArray(f.filter) ? f.filter : [f.filter] 
              : f.filter,
            contains: f.contains
          })),
          share_with: selectedFilter.share_with || '',
          selected_users: selectedFilter.users || [],
          accessibility: selectedFilter.accessibility || 'Private',
          prevent_sharing: false
        };
        await fetchContacts(false, filterPayload);
      } else {
        // If no filter is selected, fetch without filter
        await fetchContacts(false);
      }
    } catch (error) {
      console.error('Error refreshing contacts:', error);
      showSnackbar('Failed to refresh contacts');
    } finally {
      setRefreshing(false);
    }
  };
  
  const handleContactPress = (contact: DisplayContact) => {
    // console.log("accessScope", accessScope?.contact?.view);
    // if (accessScope?.contact?.view === 'global') {
      try {
        const contactString = JSON.stringify(contact);
        router.push({
          pathname: '/contactDetail',
          params: { contact: contactString }
        } as any);
      } catch (error) {
        console.error('Error navigating to contact detail:', error);
      }
    // } else { 
    //   showSnackbar('You are not authorized to view this contact');
    // }
  };

  const getInitials = (contact: DisplayContact) => {
    const firstInitial = contact.first_name?.[0] || '';
    const lastInitial = contact.last_name?.[0] || '';
    return (firstInitial + lastInitial).toUpperCase();
  };

  const renderContactItem = ({ item }: { item: DisplayContact }) => {
    const avatarBgColor = getAvatarColor(`${item.first_name} ${item.last_name}`);
    const textColor = getContrastingTextColor(avatarBgColor);
    return (
      <TouchableOpacity 
        style={styles.contactItem}
        onPress={() => handleContactPress(item)}
      >
        <View style={[styles.contactAvatar, { backgroundColor: avatarBgColor }]}>
          <Text style={[styles.contactInitial, { color: textColor }]}>{getInitials(item)}</Text>
        </View>
        <View style={styles.contactInfo}>
          <Text style={styles.contactName}>{`${capitalizeWords(item.first_name)} ${capitalizeWords(item.last_name) || ''}`}</Text>
          <Text style={styles.contactDetails}>{item.emails || 'No Email'}</Text>
          {item.job_title && <Text style={styles.contactDetails}>{item.job_title}</Text>}
        </View>
      </TouchableOpacity>
    );
  };

  // Filter contacts when search query changes
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredContacts(contacts);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = contacts.filter(contact => 
        `${contact.first_name} ${contact.last_name}`.toLowerCase().includes(query) ||
        (contact.emails && contact.emails.toLowerCase().includes(query)) ||
        (contact.job_title && contact.job_title.toLowerCase().includes(query))
      );
      setFilteredContacts(filtered);
    }
  }, [searchQuery, contacts]);

  const handleFilterPress = () => {
    console.log("filterData", JSON.stringify(CALL_FROM.CONTACT_LIST));
    router.push({
      pathname: '/ui/product/ProductFiltersPage',
      params: { callFrom: CALL_FROM.CONTACT_LIST }
    } as any);
  };

  const handleOpenDrawer = () => {
    console.log('🔓 Opening drawer');
    setIsDrawerOpen(true);
    fetchSavedFilters();
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const handleCloseDrawer = () => {
    console.log('🔒 Closing drawer');
    Animated.timing(slideAnim, {
      toValue: -Dimensions.get('window').width * 0.8,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setIsDrawerOpen(false);
    });
  };

  const fetchSavedFilters = async () => {
    try {
      console.log('🔄 Starting to fetch saved filters...');
      const response = await apiService.get<FiltersResponse>(API_ENDPOINTS.CONTACTS_FILTERS);
      console.log('📥 Raw API Response:', response);
      
      if (response?.data?.filters) {
        setSavedFilters(response.data.filters);
        setFilteredSavedFilters(response.data.filters);
        console.log('✅ Filters loaded:', response.data.filters.length);
      } else {
        console.log('⚠️ No filters found in response');
        setSavedFilters([]);
        setFilteredSavedFilters([]);
      }
    } catch (error) {
      console.error('❌ Error fetching filters:', error);
      Alert.alert('Error', 'Failed to fetch filters');
    }
  };

  // Modify handleFilterSelect to save filter
  const handleFilterSelect = async (filter: SavedFilter) => {
    try {
      console.log('🎯 Filter Selected:', {
        id: filter.id,
        name: filter.name,
        filters: filter.filters
      });

      setSelectedFilter(filter);
      await saveFilterToStorage(filter); // Save filter to storage
      setIsDrawerOpen(false);

      // Convert SavedFilter to FilterPayload format
      const filterPayload: FilterPayload = {
        title: filter.name,
        filter: filter.filters.map((f: any) => ({
          field_name: f.field_id,
          field_label: f.name,
          value_id: (f.contains.toLowerCase().includes('contain') || f.contains.toLowerCase().includes('between')) 
            ? Array.isArray(f.filter) ? f.filter : [f.filter] 
            : f.filter,
          contains: f.contains
        })),
        share_with: filter.share_with || '',
        selected_users: filter.users || [],
        accessibility: filter.accessibility || 'Private',
        prevent_sharing: false
      };

      await fetchContacts(true, filterPayload);
    } catch (error) {
      console.error('❌ Error in handleFilterSelect:', error);
      Alert.alert('Error', 'Failed to apply filter');
    }
  };

  // Modify handleDeleteFilter to clear storage if deleting selected filter
  const handleDeleteFilter = async (filterId: number) => {
    try {
      console.log('🗑️ Deleting filter:', filterId);
      
      await apiService.delete(`${API_ENDPOINTS.CONTACTS_FILTERS}/${filterId}`);
      console.log('✅ Filter deleted from API');
      
      // Remove the filter from local state
      setSavedFilters(prev => prev.filter(f => f.id !== filterId));
      setFilteredSavedFilters(prev => prev.filter(f => f.id !== filterId));
      
      // If the deleted filter was selected, clear the selection and storage
      if (selectedFilter?.id === filterId) {
        console.log('🔄 Clearing selected filter as it was deleted');
        setSelectedFilter(null);
        await saveFilterToStorage(null); // Clear saved filter
        await fetchContacts(true); // Fetch contacts without filter
      }
      
      console.log('✅ Filter deleted successfully');
      Alert.alert('Success', 'Filter deleted successfully');
      
      // Refresh the filters list
      await handleRefresh();
    } catch (error) {
      console.error('❌ Error in handleDeleteFilter:', error);
      Alert.alert('Error', 'Failed to delete filter');
    }
  };

  // Add function to clear filter
  const handleClearFilter = async () => {
    setSelectedFilter(null);
    await saveFilterToStorage(null);
    await fetchContacts(true);
  };

  // Modify header title container to include clear filter option
  const renderHeaderTitleContainer = () => (
    <TouchableOpacity 
      style={styles.headerTitleContainer}
      onPress={handleOpenDrawer}
    >
      <Text style={[styles.headerTitle, { color: '#FFFFFF' }]}>
        {selectedFilter?.name || "Contacts"}
      </Text>
      <Ionicons 
        name={isDrawerOpen ? "chevron-up" : "chevron-down"} 
        size={20} 
        color="#FFFFFF" 
      />
      {selectedFilter && (
        <TouchableOpacity 
          onPress={handleClearFilter}
          style={styles.clearFilterButton}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons name="close-circle" size={16} color="#FFFFFF" />
        </TouchableOpacity>
      )}
    </TouchableOpacity>
  );

  // Effect to handle drawer search
  useEffect(() => {
    const searchFilters = () => {
      try {
        console.log('🔍 Search query:', drawerSearchQuery);
        console.log('📋 Total filters before search:', savedFilters.length);

        if (!drawerSearchQuery.trim()) {
          console.log('📋 Showing all filters - empty search');
          setFilteredSavedFilters(savedFilters);
          return;
        }

        const query = drawerSearchQuery.toLowerCase().trim();
        const filtered = savedFilters.filter(filter => {
          if (!filter) return false;

          // Search in filter name
          const nameMatch = filter.name?.toLowerCase().includes(query);

          // Search in filter conditions
          const filterConditionsMatch = filter.filters?.some(f => {
            if (!f) return false;

            // Convert all values to string for searching
            const filterValue = Array.isArray(f.filter) 
              ? f.filter.map(v => String(v)).join(' ')
              : String(f.filter || '');

            return (
              (f.name?.toLowerCase().includes(query)) ||
              (filterValue.toLowerCase().includes(query)) ||
              (f.contains?.toLowerCase().includes(query))
            );
          });

          // Search in accessibility and sharing settings
          const accessibilityMatch = String(filter.accessibility || '').toLowerCase().includes(query);
          const shareWithMatch = String(filter.share_with || '').toLowerCase().includes(query);

          // Search in users array if it exists
          const usersMatch = Array.isArray(filter.users) 
            ? filter.users.some(user => String(user).toLowerCase().includes(query))
            : false;

          const isMatch = nameMatch || filterConditionsMatch || accessibilityMatch || shareWithMatch || usersMatch;
          
          if (isMatch) {
            console.log('✅ Match found:', filter.name);
          }

          return isMatch;
        });

        console.log('🔍 Search results:', {
          query: drawerSearchQuery,
          totalFilters: savedFilters.length,
          matchedFilters: filtered.length,
          matchedFiltersList: filtered.map(f => f.name)
        });

        setFilteredSavedFilters(filtered);
      } catch (error) {
        console.error('❌ Error in search:', error);
        // In case of error, show all filters
        setFilteredSavedFilters(savedFilters);
      }
    };

    // Debounce the search to avoid too frequent updates
    const debounceTimeout = setTimeout(searchFilters, 300);
    return () => clearTimeout(debounceTimeout);
  }, [drawerSearchQuery, savedFilters]);

  const renderFilterItem = (filter: SavedFilter, index: number) => {
    const handlePress = () => {
      console.log('👆 Filter item pressed:', filter.name);
      handleFilterSelect(filter);
    };

    return (
      <TouchableOpacity
        key={filter.id || index}
        style={[
          styles.filterItem,
          selectedFilter?.id === filter.id && styles.selectedFilterItem
        ]}
        onPress={handlePress}
        activeOpacity={0.7}
      >
        <View style={styles.filterItemContent}>
          <View style={styles.filterItemHeader}>
            <Text style={[
              styles.filterItemText,
              selectedFilter?.id === filter.id && styles.selectedFilterItemText
            ]}>
              {filter.name}
            </Text>
            <View style={styles.filterItemActions}>
              <TouchableOpacity
                onPress={(e) => handleEditFilter(filter, e)}
                style={styles.actionButton}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Ionicons name="pencil" size={18} color={colors.primary} />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={(e) => {
                  e.stopPropagation();
                  Alert.alert(
                    'Delete Filter',
                    `Are you sure you want to delete "${filter.name}"?`,
                    [
                      {
                        text: 'Cancel',
                        style: 'cancel'
                      },
                      {
                        text: 'Delete',
                        style: 'destructive',
                        onPress: () => handleDeleteFilter(filter.id)
                      }
                    ]
                  );
                }}
                style={styles.actionButton}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Ionicons name="trash-outline" size={18} color="#FF4444" />
              </TouchableOpacity>
            </View>
          </View>
          {filter.filters && filter.filters.length > 0 && (
            <View style={styles.filterDetails}>
              {filter.filters.map((field, fieldIndex) => (
                <Text key={fieldIndex} style={styles.filterFieldText}>
                  {field.name}: {field.filter} ({field.contains})
                </Text>
              ))}
            </View>
          )}
          <Text style={styles.accessibilityText}>
            {filter.accessibility || 'Private'} • {filter.share_with || 'Not shared'}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderSideNavigationView = () => (
    <Modal
      visible={isDrawerOpen}
      transparent={true}
      onRequestClose={handleCloseDrawer}
      animationType="none"
    >
      <TouchableOpacity
        style={styles.drawerOverlay}
        activeOpacity={1}
        onPress={handleCloseDrawer}
      >
        <Animated.View 
          style={[
            styles.drawerContent,
            {
              transform: [{ translateX: slideAnim }]
            }
          ]}
        >
          <View style={styles.drawerHeader}>
            <Text style={styles.drawerTitle}>
              Saved Filters {filteredSavedFilters.length > 0 && `(${filteredSavedFilters.length})`}
            </Text>
            <TouchableOpacity 
              onPress={handleCloseDrawer}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Ionicons name="close" size={24} color="#fff" />
            </TouchableOpacity>
          </View>

          <View style={styles.filterSearchContainer}>
            <View style={[
              styles.searchInputContainer,
              isSearchFocused && styles.searchInputContainerFocused
            ]}>
              <Ionicons 
                name="search" 
                size={20} 
                color="#999" 
                style={styles.searchIcon} 
              />
              <TextInput
                style={[
                  styles.filterSearchInput,
                  isSearchFocused && styles.filterSearchInputFocused
                ]}
                placeholder="Search filters..."
                value={drawerSearchQuery}
                onChangeText={setDrawerSearchQuery}
                placeholderTextColor="#999"
                autoCapitalize="none"
                autoCorrect={false}
                onFocus={() => setIsSearchFocused(true)}
                onBlur={() => setIsSearchFocused(false)}
              />
              {drawerSearchQuery.length > 0 && (
                <TouchableOpacity 
                  onPress={() => {
                    console.log('🧹 Clearing search');
                    setDrawerSearchQuery('');
                    setFilteredSavedFilters(savedFilters);
                  }}
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                >
                  <Ionicons name="close-circle" size={20} color="#999" />
                </TouchableOpacity>
              )}
            </View>
          </View>

          <ScrollView style={styles.filterList} showsVerticalScrollIndicator={false}>
            {filteredSavedFilters.map((filter, index) => renderFilterItem(filter, index))}
            {filteredSavedFilters.length === 0 && (
              <View style={styles.emptySearchContainer}>
                <Text style={styles.emptySearchText}>
                  {drawerSearchQuery 
                    ? `No filters found matching "${drawerSearchQuery}"`
                    : 'No saved filters available'}
                </Text>
              </View>
            )}
          </ScrollView>
        </Animated.View>
      </TouchableOpacity>
    </Modal>
  );

  const handleActivityPress = (activity: Activity) => {
    if (activity.activity_name === 'Task' || activity.activity_name.toLowerCase().includes('task')) {
      router.push({
        pathname: '/(tabs)/tasks/create' as any,
        params: { callFrom: 'contacts' }
      });
    } else if (activity.activity_name === 'Meeting' || activity.activity_name.toLowerCase().includes('meeting')) {
      router.push({
        pathname: '/(tabs)/meetings/createMeeting' as any,
        params: { callFrom: 'contacts' }
      });
    } else if (activity.activity_name === 'Contact' || activity.activity_name.toLowerCase().includes('contact')) {
      router.push({
        pathname: '/(tabs)/contact' as any,
        params: { callFrom: 'contacts' }
      });
    } else if (activity.activity_name === 'Account' || activity.activity_name.toLowerCase().includes('account')) {
      router.push({
        pathname: '/(tabs)/CreateAccounts' as any
      });
    } else if (activity.activity_name === 'Deal' || activity.activity_name.toLowerCase().includes('deal')) {
      router.push({
        pathname: '/CreateDeals' as any
      });
    } else if (activity.activity_name === 'Product' || activity.activity_name.toLowerCase().includes('product')) {
      router.push({
        pathname: '/ui/product/ProductListPage' as any
      });
    } else if (activity.activity_name === 'Quotation' || activity.activity_name.toLowerCase().includes('quotation')) {
      router.push({
        pathname: '/(tabs)/DealsQuoteList' as any
      });
    } else {
      // For other record types
      Alert.alert('Coming Soon', `${activity.activity_name} creation will be available soon!`);
    }
    setShowPopup(false);
  };

  // Add back handleEditFilter function
  const handleEditFilter = async (filter: SavedFilter, event: any) => {
    try {
      event.stopPropagation(); // Prevent filter selection when clicking edit
      console.log('✏️ Editing filter:', {
        id: filter.id,
        name: filter.name,
        filters: filter.filters
      });

      router.push({
        pathname: '/ui/product/save-view',
        params: { 
          callFrom: CALL_FROM.CONTACT_LIST,
          filterData: JSON.stringify(filter),
          isEdit: "true"
        }
      } as any);
      setIsDrawerOpen(false);
    } catch (error) {
      console.error('❌ Error in handleEditFilter:', error);
      Alert.alert('Error', 'Failed to navigate to edit page');
    }
  };

  if (loading && !refreshing) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Loading contacts...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => fetchContacts()}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }
  
  return (
    <SafeAreaView style={styles.container} edges={['top'] as Edge[]}>
      <StatusBar style="dark" />
      
      {/* Header */}
      <View style={[styles.header, { backgroundColor: '#0F96BB' }]}>
        {!isSearchActive ? (
          <>
            <View style={styles.headerLeft}>
              {renderHeaderTitleContainer()}
            </View>
            <View style={styles.headerRight}>
              <TouchableOpacity 
                style={styles.headerIcon}
                onPress={() => setIsSearchActive(true)}
              >
                <Ionicons name="search" size={24} color="#FFFFFF" />
              </TouchableOpacity>
              <Appbar.Action 
          icon="filter-outline"
          color="#fff" 
          onPress={() => {
            router.push({
              pathname: '/ui/product/ProductFiltersPage',
              params: { callFrom: CALL_FROM.CONTACT_LIST }
            } as any)
          }}
        />
            </View>
          </>
        ) : (
          <View style={styles.searchHeader}>
            <TouchableOpacity 
              style={styles.headerIcon}
              onPress={() => {
                setIsSearchActive(false);
                setSearchQuery('');
              }}
            >
              <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
            </TouchableOpacity>
            <TextInput
              style={styles.searchInput}
              placeholder="Search contacts..."
              placeholderTextColor="rgba(255, 255, 255, 0.7)"
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoFocus
              returnKeyType="search"
              selectionColor="#FFFFFF"
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity 
                style={styles.headerIcon}
                onPress={() => setSearchQuery('')}
              >
                <Ionicons name="close-circle" size={24} color="#FFFFFF" />
              </TouchableOpacity>
            )}
          </View>
        )}
      </View>
      
      {/* Side Navigation */}
      {renderSideNavigationView()}
      
      {/* Contact List */}
      <FlatList
        data={filteredContacts}
        keyExtractor={(item) => item.id.toString()}
        renderItem={renderContactItem}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        contentContainerStyle={filteredContacts.length === 0 && !loading ? styles.emptyContainer : undefined}
        ListEmptyComponent={
          !loading ? (
            <View style={styles.emptyContainer}>
              <Ionicons
              name="people-outline"
              size={64}
              color="#CCCCCC"
              style={styles.emptyIcon}
            />
              <Text style={styles.emptyText}>
                {searchQuery ? 'No contacts found matching your search' : 'No contacts found'}
              </Text>
            </View>
          ) : null
        }
      />
      
      {/* Floating action button */}
      {accessScope?.contact?.create === true && accessScope?.task?.create === true &&
          accessScope?.appointment?.create === true && accessScope?.notes?.create === true && (
        <TouchableOpacity 
          style={styles.fab}
          onPress={() => setShowPopup(true)}
        >
          <Ionicons name="add" size={24} color={colors.text.inverse} />
        </TouchableOpacity>
      )}
      {/* Records popup */}
      <RecordsPopup 
        visible={showPopup}
        onClose={() => setShowPopup(false)}
        callFrom="contacts"
      />
    </SafeAreaView>
  );
}

const createStyles = (colors: ReturnType<typeof useColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  
  loadingText: {
    fontSize: 18,
    fontWeight: '500',
    color: '#0F96BB',
    marginTop: 16,
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
    height: 56,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: colors.primary,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  contactAvatar: {
    width: 42,
    height: 42,
    borderRadius: 21,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  contactInitial: {
    color: colors.text.inverse,
    fontSize: 18,
    fontWeight: 'bold',
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    color: colors.text.primary,
    fontWeight: '500',
    marginBottom: 4,
  },
  contactDetails: {
    fontSize: 14,
    color: colors.text.secondary,
    marginBottom: 2,
  },
  separator: {
    height: 1,
    backgroundColor: colors.background.tertiary,
    marginLeft: 72,
  },
  fab: {
    position: 'absolute',
    right: 16,
    bottom: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: colors.text.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  popup: {
    backgroundColor: colors.background.primary,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 16,
    paddingBottom: 30,
    maxHeight: '70%',
  },
  popupTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text.primary,
    paddingHorizontal: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
  },
  popupSection: {
    marginTop: 16,
  },
  popupItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  popupIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  popupItemText: {
    fontSize: 16,
    color: colors.text.primary,
  },
  errorText: {
    color: 'red',
    fontSize: 16,
    marginBottom: 16,
    textAlign: 'center',
    paddingHorizontal: 32,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: colors.text.inverse,
    fontSize: 16,
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyIcon: {
    marginBottom: 20,
  },
  emptyText: {
    fontSize: 18,
    color: '#777777',
    textAlign: 'center',
    fontWeight: '500',
    marginBottom: 8,
  },
  searchHeader: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 8,
    position: 'absolute',
    left: 16,
    right: 16,
    height: '100%',
    backgroundColor: '#0F96BB',
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
    color: '#FFFFFF',
    marginHorizontal: 8,
    paddingHorizontal: 8,
  },
  drawerOverlay: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  drawerContent: {
    backgroundColor: 'rgba(255, 255, 255, 0.5)', 
    height: '100%',
    width: '80%',
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    borderRightWidth: 1,
    borderRightColor: '#eee',
    shadowColor: '#000',
    shadowOffset: { width: 2, height: 0 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    paddingTop: Platform.OS === 'ios' ? 47 : 0,
  },
  drawerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 12,
    height: 56,
    zIndex: 1,
  },
  drawerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  filterSearchContainer: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#fff',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 20,
    paddingHorizontal: 12,
    height: 40,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  searchInputContainerFocused: {
    backgroundColor: '#fff',
    borderColor: colors.primary,
  },
  searchIcon: {
    marginRight: 8,
  },
  filterSearchInput: {
    flex: 1,
    fontSize: 14,
    color: '#333',
    paddingVertical: 8,
    paddingHorizontal: 0,
  },
  filterSearchInputFocused: {
    color: colors.primary,
  },
  filterList: {
    flex: 1,
    backgroundColor: '#fff',
  },
  filterItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#fff',
  },
  filterItemContent: {
    flex: 1,
  },
  filterItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  filterItemText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
    fontWeight: '400',
  },
  selectedFilterItem: {
    backgroundColor: '#f5f5f5',
  },
  selectedFilterItemText: {
    color: colors.primary,
    fontWeight: '500',
  },
  filterItemActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  actionButton: {
    padding: 4,
    borderRadius: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  filterDetails: {
    marginTop: 8,
    paddingLeft: 8,
  },
  filterFieldText: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  accessibilityText: {
    fontSize: 12,
    color: '#666',
    marginTop: 8,
  },
  emptySearchContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptySearchText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  clearFilterButton: {
    marginLeft: 8,
    padding: 4,
  },
}); 