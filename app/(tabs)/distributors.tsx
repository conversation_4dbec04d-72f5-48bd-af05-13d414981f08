import React, { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, FlatList, ActivityIndicator, Alert, Platform, Dimensions, TextInput, Modal, Animated, ScrollView, ViewStyle, TextStyle } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView, Edge } from 'react-native-safe-area-context';
import { useRouter, useFocusEffect } from 'expo-router';
import { API_ENDPOINTS } from '../../config/api';
import { apiService } from '../../services/ApiService';
import { useAuth } from '../../context/AuthContext';
import { Account, AccountsResponse, DisplayAccount } from '../models/Account';
import { CALL_FROM, capitalizeWords, getAvatarColor, getContrastingTextColor, readUserScope } from '../ui/utils';
import { useColors } from '../../hooks/useThemeColor';
import { SavedFilter, FiltersResponse } from '../models/Filter';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { Appbar } from 'react-native-paper';
import { useRecords } from '../../context/RecordsContext';
import RecordsPopup from '../../components/RecordsPopup';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Add interface for filter field
interface FilterField {
  name: string;
  field_id: string;
  filter: string | string[];
  contains: string;
}

// Add storage key constant
export const DISTRIBUTORS_FILTER_STORAGE_KEY = '@distributors_filter';

// Add type definition for styles
type DistributorScreenStyles = {
  // Container and Layout
  container: ViewStyle;
  header: ViewStyle;
  headerLeft: ViewStyle;
  headerTitle: TextStyle;
  headerTitleContainer: ViewStyle;
  headerRight: ViewStyle;
  headerIcon: ViewStyle;
  
  // Error View
  errorContent: ViewStyle;
  errorContainer: ViewStyle;
  errorIcon: ViewStyle;
  errorText: TextStyle;
  retryButton: ViewStyle;
  retryButtonText: TextStyle;
  
  // Loading View
  loadingContainer: ViewStyle;
  loadingText: TextStyle;
  
  // Filter Styles
  filterItem: ViewStyle;
  filterItemContent: ViewStyle;
  filterItemHeader: ViewStyle;
  filterItemText: TextStyle;
  selectedFilterItem: ViewStyle;
  selectedFilterItemText: TextStyle;
  filterItemActions: ViewStyle;
  actionButton: ViewStyle;
  filterDetails: ViewStyle;
  filterFieldText: TextStyle;
  accessibilityText: TextStyle;
  
  // Drawer Styles
  drawerOverlay: ViewStyle;
  drawerContent: ViewStyle;
  drawerHeader: ViewStyle;
  drawerTitle: TextStyle;
  filterSearchContainer: ViewStyle;
  filterSearchInput: TextStyle;
  filterList: ViewStyle;
  emptySearchContainer: ViewStyle;
  emptySearchText: TextStyle;
  searchInputContainer: ViewStyle;
  searchIcon: ViewStyle;
  
  // List Styles
  distributorItem: ViewStyle;
  distributorAvatar: ViewStyle;
  distributorInitial: TextStyle;
  distributorInfo: ViewStyle;
  distributorName: TextStyle;
  distributorContact: TextStyle;
  daysAgo: TextStyle;
  separator: ViewStyle;
  
  // Search Styles
  searchHeader: ViewStyle;
  searchInput: TextStyle;
  removeButton: ViewStyle;
  removeButtonText: TextStyle;
  
  // FAB Styles
  fab: ViewStyle;
  
  // Empty State
  flatList: ViewStyle;
  emptyContentContainer: ViewStyle;
  emptyContainer: ViewStyle;
  emptyIcon: ViewStyle;
  emptyText: TextStyle;
  emptySubText: TextStyle;
  
  searchIconContainer: ViewStyle;
  
  iconWrapper: ViewStyle;
  
  clearFilterButton: ViewStyle;
};

export default function DistributorsScreen() {
  const [distributors, setDistributors] = useState<DisplayAccount[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [addOptionsVisible, setAddOptionsVisible] = useState(false);
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredDistributors, setFilteredDistributors] = useState<DisplayAccount[]>([]);
  const { user } = useAuth();
  const router = useRouter();
  const colors = useColors();
  const styles = useMemo(() => createStyles(colors), [colors]);
  
  // Side Navigation Drawer states
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [drawerSearchQuery, setDrawerSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<SavedFilter | null>(null);
  const [savedFilters, setSavedFilters] = useState<SavedFilter[]>([]);
  const [filteredSavedFilters, setFilteredSavedFilters] = useState<SavedFilter[]>([]);
  const slideAnim = useRef(new Animated.Value(-Dimensions.get('window').width * 0.8)).current;
  const [accessScope, setAccessScope] = useState<any>(null);

  useFocusEffect(
  useCallback(() => {
    const fetchAccessScope = async () => {
      const scope = await readUserScope();
      console.log("accessScope:distributors  " + JSON.stringify(scope));
      setAccessScope(scope);
    };
    fetchAccessScope();
  }, [])
);

interface FilterPayload {
  title: string;
  filter: Array<{
    field_name: string;
    field_label: string;
    value_id: string | string[];
    contains: string;
  }>;
  share_with: string;
  selected_users: string[];
  accessibility: string;
  prevent_sharing: boolean;
}
  // Add capitalize function
  const capitalizeWords = (str: string): string => {
    return str
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  // Add functions for filter persistence
  const saveFilterToStorage = async (filter: SavedFilter | null) => {
    try {
      if (filter) {
        console.log('📱 Saving filter to storage:', filter);
        await AsyncStorage.setItem(DISTRIBUTORS_FILTER_STORAGE_KEY, JSON.stringify(filter));
      } else {
        console.log('📱 Clearing filter from storage');
        await AsyncStorage.removeItem(DISTRIBUTORS_FILTER_STORAGE_KEY);
      }
    } catch (error) {
      console.error('❌ Error saving filter to storage:', error);
    }
  };

  const loadFilterFromStorage = async () => {
    try {
      const savedFilterStr = await AsyncStorage.getItem(DISTRIBUTORS_FILTER_STORAGE_KEY);
      console.log('📱 Raw saved filter from storage:', savedFilterStr);
      
      if (savedFilterStr) {
        const filter = JSON.parse(savedFilterStr) as SavedFilter;
        console.log('📱 Parsed saved filter:', filter);
        
        // Validate the filter has required fields
        if (filter && filter.id && filter.name) {
          setSelectedFilter(filter);
          return filter;
        } else {
          console.log('⚠️ Invalid filter data in storage, clearing...');
          await AsyncStorage.removeItem(DISTRIBUTORS_FILTER_STORAGE_KEY);
        }
      }
    } catch (error) {
      console.error('❌ Error loading filter from storage:', error);
      // Clear invalid storage data
      await AsyncStorage.removeItem(DISTRIBUTORS_FILTER_STORAGE_KEY);
    }
    return null;
  };

  // Update useFocusEffect to load saved filter
  useFocusEffect(
    React.useCallback(() => {
      console.log('📱 Distributors screen focused - loading saved filter');
      const loadData = async () => {
        try {
          // First load the saved filter
          const savedFilter = await loadFilterFromStorage();
          console.log('📱 Loaded saved filter:', savedFilter);
          
          // Then fetch distributors with the filter if it exists
          if (savedFilter) {
            const filterPayload = {
              title: savedFilter.name,
              filter: savedFilter.filters.map((f: any) => ({
                field_name: f.field_id,
                field_label: f.name,
                value_id: (f.contains.toLowerCase().includes('contain') || f.contains.toLowerCase().includes('between')) 
                  ? Array.isArray(f.filter) ? f.filter : [f.filter] 
                  : f.filter,
                contains: f.contains || "contains"
              })),
              share_with: savedFilter.share_with || "selected_users",
              selected_users: savedFilter.users || ["1"],
              accessibility: savedFilter.accessibility || "Public",
              prevent_sharing: false
            };
            console.log('📱 Applying saved filter:', filterPayload);
            await fetchDistributors(filterPayload);
          } else {
            console.log('📱 No saved filter found, fetching all distributors');
            await fetchDistributors();
          }
          
          // Finally fetch available filters
          await fetchSavedFilters();
        } catch (error) {
          console.error('❌ Error loading data:', error);
          const errorMessage = error instanceof Error 
            ? error.message 
            : 'Failed to load distributors data';
          setError(errorMessage);
          Alert.alert('Error', errorMessage);
        }
      };
      loadData();
    }, [])
  );

  const fetchDistributors = async (filterPayload: any) => {
    try {
      setIsLoading(true);
      setError(null);
  console.log('🔄 filterPayload:', JSON.stringify(filterPayload, null, 2));
       var response = await apiService.post<AccountsResponse>(API_ENDPOINTS.ACCOUNT_LIST_WITH_FILTERS,filterPayload);
      
      if (!response.data || !response.data.sales_accounts) {
        console.error('❌ No data in response:', response);
        throw new Error('No data received from the server');
      }

      // Transform the data to include display fields
      const transformedData: DisplayAccount[] = response.data.sales_accounts
        .map((account: Account) => {
          const capitalizedName = account.name ? capitalizeWords(account.name) : 'A';
          return {
            ...account,
            name: capitalizedName,
            initial: capitalizedName[0],
          };
        });

      // Sort the data by created_at date (newest first)
      const sortedData = [...transformedData].sort((a, b) => {
        if (!a.created_at && !b.created_at) return 0;
        if (!a.created_at) return -1;
        if (!b.created_at) return 1;
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      });

      setDistributors(transformedData);
      setFilteredDistributors(transformedData);
    } catch (err) {
      console.error('Error fetching distributors:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch accounts');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDaysAgo = (dateStr: string): string => {
    //console.log('🔄 Formatting date:', dateStr);
    // First, ensure we have a valid date string
    if (!dateStr) return 'New';
    
    try {
      // Parse the date string - handle different formats
      const date = new Date(dateStr);
      
      // Check if date is valid
      if (isNaN(date.getTime())) {
        console.error('Invalid date format:', dateStr);
        return 'New';
      }
      
      const now = new Date();
      
      // Calculate difference in milliseconds
      const diffTime = Math.abs(now.getTime() - date.getTime());
      
      // Convert to days
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
      
      // Format based on time difference
      if (diffDays === 0) {
        // If it's today, we can be more specific with hours
        const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
        if (diffHours === 0) {
          const diffMinutes = Math.floor(diffTime / (1000 * 60));
          return diffMinutes <= 1 ? 'Just now' : `${diffMinutes} minutes ago`;
        }
        return diffHours === 1 ? '1 hour ago' : `${diffHours} hours ago`;
      }
      
      if (diffDays === 1) return 'Yesterday';
      if (diffDays < 7) return `${diffDays} days ago`;
      if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
      
      // Calculate months difference more accurately
      const monthDiff = (now.getFullYear() - date.getFullYear()) * 12 + (now.getMonth() - date.getMonth());
      if (monthDiff < 12) return monthDiff === 1 ? '1 month ago' : `${monthDiff} months ago`;
      
      // Calculate years difference
      const yearDiff = Math.floor(monthDiff / 12);
      return yearDiff === 1 ? '1 year ago' : `${yearDiff} years ago`;
    } catch (error) {
      //console.error('Error formatting date:', error);
      return 'New';
    }
  };

  const handleDistributorPress = (distributor: DisplayAccount) => {
    const customFieldsArray = JSON.parse(distributor.custom_field || '[]');
    console.log('Navigating to account detail:', customFieldsArray);
    router.push({
      pathname: '/accountDetail' as any,
      params: {
        id: distributor.id.toString(),
        name: distributor.name || '',
        phone: distributor.phone || '',
        website: distributor.website || '',
        created_at: distributor.created_at,
        item: JSON.stringify(distributor)
      }
    });
  };

  // Filter distributors when search query changes
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredDistributors(distributors);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = distributors.filter(distributor => 
        (distributor.name && distributor.name.toLowerCase().includes(query)) ||
        (distributor.phone && distributor.phone.toLowerCase().includes(query)) ||
        (distributor.website && distributor.website.toLowerCase().includes(query))
      );
      setFilteredDistributors(filtered);
    }
  }, [searchQuery, distributors]);

  // Drawer animation functions
  const handleOpenDrawer = () => {
    setIsDrawerOpen(true);
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const handleCloseDrawer = () => {
    Animated.timing(slideAnim, {
      toValue: -Dimensions.get('window').width * 0.8,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setIsDrawerOpen(false);
    });
  };

  // Fetch saved filters from API
  const fetchSavedFilters = async () => {
    try {
      const response = await apiService.get<FiltersResponse>(API_ENDPOINTS.SALES_ACCOUNTS_FILTERS);
      console.log('🔄 Saved filters response:', response.data.filters.length);
      
      if (response.data?.filters && response.data.filters.length > 0) {
        setSavedFilters(response.data.filters);
        setFilteredSavedFilters(response.data.filters);
      }
    } catch (error) {
      console.error('Error fetching filters:', error);
      Alert.alert('Error', 'Failed to fetch filters');
    }
  };

  // Update handleFilterSelect to save filter
  const handleFilterSelect = async (filter: SavedFilter) => {
    try {
      console.log('🎯 Filter Selected:', {
        id: filter.id,
        name: filter.name,
        filters: filter.filters
      });

      setSelectedFilter(filter);
      await saveFilterToStorage(filter);
      setIsDrawerOpen(false);

      const filterPayload = {
        title: filter.name,
        filter: filter.filters.map((f: any) => ({
          field_name: f.field_id,
          field_label: f.name,
          value_id: (f.contains.toLowerCase().includes('contain') || f.contains.toLowerCase().includes('between')) 
            ? Array.isArray(f.filter) ? f.filter : [f.filter] 
            : f.filter,
          contains: f.contains || "contains"
        })),
        share_with: filter.share_with || "selected_users",
        selected_users: filter.users || ["1"],
        accessibility: filter.accessibility || "Public",
        prevent_sharing: false
      };

      console.log('📱 Applying filter payload:', filterPayload);
      await fetchDistributors(filterPayload);
    } catch (error) {
      console.error('❌ Error in handleFilterSelect:', error);
      Alert.alert('Error', 'Failed to apply filter');
    }
  };

  // Update handleClearFilter to use the new signature
  const handleClearFilter = async () => {
    try {
      console.log('🧹 Clearing selected filter');
      setSelectedFilter(null);
      await saveFilterToStorage(null);
      // Fetch all distributors without filter
      await fetchDistributors();
      handleCloseDrawer();
    } catch (error) {
      console.error('❌ Error clearing filter:', error);
      Alert.alert('Error', 'Failed to clear filter');
    }
  };

  // Update handleDeleteFilter to clear storage if deleting selected filter
  const handleDeleteFilter = async (filterId: number) => {
    try {
      await apiService.delete(`${API_ENDPOINTS.SALES_ACCOUNTS_FILTERS}/${filterId}`);
      if (selectedFilter?.id === filterId) {
        setSelectedFilter(null);
        await saveFilterToStorage(null);
      }
      await fetchSavedFilters();
      await fetchDistributors();
      Alert.alert('Success', 'Filter deleted successfully');
    } catch (error) {
      console.error('❌ Error deleting filter:', error);
      Alert.alert('Error', 'Failed to delete filter');
    }
  };

  // Handle filter editing
  const handleEditFilter = (filter: SavedFilter, event: any) => {
    router.push({
      pathname: '/ui/product/save-view' as any,
      params: { 
        callFrom: CALL_FROM.ACCOUNT_LIST,
        filterData: JSON.stringify(filter),
        isEdit: "true"
      }
    });
    handleCloseDrawer();
  };

  // Filter saved filters when search query changes
  useEffect(() => {
    const searchFilters = () => {
      try {
        console.log('🔍 Search query:', drawerSearchQuery);
        console.log('📋 Total filters before search:', savedFilters.length);

        if (!drawerSearchQuery.trim()) {
          console.log('📋 Showing all filters - empty search');
          setFilteredSavedFilters(savedFilters);
          return;
        }

        const query = drawerSearchQuery.toLowerCase().trim();
        const filtered = savedFilters.filter(filter => {
          if (!filter) return false;

          const nameMatch = filter.name?.toLowerCase().includes(query);
          const filterConditionsMatch = filter.filters?.some(f => 
            f.name?.toLowerCase().includes(query) ||
            f.filter?.toString().toLowerCase().includes(query) ||
            f.contains?.toLowerCase().includes(query)
          );
          const accessibilityMatch = filter.accessibility?.toLowerCase().includes(query);
          const shareWithMatch = filter.share_with?.toLowerCase().includes(query);

          const isMatch = nameMatch || filterConditionsMatch || accessibilityMatch || shareWithMatch;
          
          if (isMatch) {
            console.log('✅ Match found:', filter.name);
          }

          return isMatch;
        });

        setFilteredSavedFilters(filtered);
      } catch (error) {
        console.error('❌ Error in search:', error);
        setFilteredSavedFilters(savedFilters);
      }
    };

    const debounceTimeout = setTimeout(searchFilters, 300);
    return () => clearTimeout(debounceTimeout);
  }, [drawerSearchQuery, savedFilters]);

  const renderFilterItem = (filter: SavedFilter, index: number) => {
    const handlePress = () => {
      console.log('👆 Filter item pressed:', filter.name);
      handleFilterSelect(filter);
    };

    const handleEditPress = (event: any) => {
      event.stopPropagation();
      console.log('✏️ Edit pressed for filter:', filter.name);
      handleEditFilter(filter, event);
    };

    const handleDeletePress = (event: any) => {
      event.stopPropagation();
      console.log('🗑️ Delete pressed for filter:', filter.name);
      Alert.alert(
        'Delete Filter',
        `Are you sure you want to delete "${filter.name}"?`,
        [
          {
            text: 'Cancel',
            style: 'cancel'
          },
          {
            text: 'Delete',
            style: 'destructive',
            onPress: () => handleDeleteFilter(filter.id)
          }
        ]
      );
    };

    return (
      <TouchableOpacity
        key={filter.id || index}
        style={[
          styles.filterItem,
          selectedFilter?.id === filter.id && styles.selectedFilterItem
        ]}
        onPress={handlePress}
        activeOpacity={0.7}
      >
        <View style={styles.filterItemContent}>
          <View style={styles.filterItemHeader}>
            <Text style={[
              styles.filterItemText,
              selectedFilter?.id === filter.id && styles.selectedFilterItemText
            ]}>
              {filter.name}
            </Text>
            <View style={styles.filterItemActions}>
              <TouchableOpacity
                onPress={handleEditPress}
                style={styles.actionButton}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Ionicons name="pencil" size={18} color={colors.primary} />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={handleDeletePress}
                style={styles.actionButton}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Ionicons name="trash-outline" size={18} color="#FF4444" />
              </TouchableOpacity>
            </View>
          </View>
          {filter.filters && filter.filters.length > 0 && (
            <View style={styles.filterDetails}>
              {filter.filters.map((field: FilterField, fieldIndex: number) => (
                <Text key={fieldIndex} style={styles.filterFieldText}>
                  {field.name}: {field.filter} ({field.contains})
                </Text>
              ))}
            </View>
          )}
          <Text style={styles.accessibilityText}>
            {filter.accessibility || 'Private'} • {filter.share_with || 'Not shared'}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  // Render side navigation drawer
  const renderSideNavigationView = () => (
    <Modal
      visible={isDrawerOpen}
      transparent={true}
      onRequestClose={handleCloseDrawer}
      animationType="none"
    >
      <TouchableOpacity
        style={styles.drawerOverlay}
        activeOpacity={1}
        onPress={handleCloseDrawer}
      >
        <Animated.View 
          style={[
            styles.drawerContent,
            {
              transform: [{ translateX: slideAnim }]
            }
          ]}
        >
          <View style={styles.drawerHeader}>
            <Text style={styles.drawerTitle}>
              Saved Filters {filteredSavedFilters.length > 0 && `(${filteredSavedFilters.length})`}
            </Text>
            <TouchableOpacity 
              onPress={handleCloseDrawer}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Ionicons name="close" size={24} color="#fff" />
            </TouchableOpacity>
          </View>

          <View style={styles.filterSearchContainer}>
            <View style={styles.searchInputContainer}>
              <View style={styles.iconWrapper}>
                <Ionicons 
                  name="search" 
                  size={20} 
                  color="#999"
                />
              </View>
              <TextInput
                style={styles.filterSearchInput}
                placeholder="Search filters..."
                value={drawerSearchQuery}
                onChangeText={setDrawerSearchQuery}
                placeholderTextColor="#999"
                autoCapitalize="none"
                autoCorrect={false}
              />
              {drawerSearchQuery.length > 0 && (
                <TouchableOpacity 
                  onPress={() => {
                    setDrawerSearchQuery('');
                    setFilteredSavedFilters(savedFilters);
                  }}
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                >
                  <View style={styles.iconWrapper}>
                    <Ionicons name="close-circle" size={20} color="#999" />
                  </View>
                </TouchableOpacity>
              )}
            </View>
          </View>

          <ScrollView style={styles.filterList}>
            {filteredSavedFilters.map((filter, index) => renderFilterItem(filter, index))}
            {filteredSavedFilters.length === 0 && (
              <View style={styles.emptySearchContainer}>
                <Text style={styles.emptySearchText}>
                  {drawerSearchQuery 
                    ? `No filters found matching "${drawerSearchQuery}"`
                    : 'No saved filters available'}
                </Text>
              </View>
            )}
          </ScrollView>
        </Animated.View>
      </TouchableOpacity>
    </Modal>
  );

  const renderContactItem = ({ item }: { item: DisplayAccount }) => {
    const avatarBgColor = getAvatarColor(item.name);
    const textColor = getContrastingTextColor(avatarBgColor);
    return (
      <TouchableOpacity 
        style={styles.distributorItem}
        onPress={() => handleDistributorPress(item)}
      >
        <View style={[styles.distributorAvatar, { backgroundColor: avatarBgColor }]}>
          <Text style={[styles.distributorInitial, { color: textColor }]}>{item.initial}</Text>
        </View>
        <View style={styles.distributorInfo}>
          <Text style={styles.distributorName}>{item.name}</Text>
          <Text style={styles.distributorContact}>{item.phone || 'No phone'}</Text>
        </View>
        <Text style={styles.daysAgo}>{formatDaysAgo(item.created_at)}</Text>
      </TouchableOpacity>
    );
  };

  // Modify header title container to include clear filter option
  const renderHeaderTitleContainer = () => (
    <TouchableOpacity 
      style={styles.headerTitleContainer}
      onPress={handleOpenDrawer}
    >
      <Text style={[styles.headerTitle, { color: '#FFFFFF' }]}>
        {selectedFilter?.name || "Accounts"}
      </Text>
      <Ionicons 
        name={isDrawerOpen ? "chevron-up" : "chevron-down"} 
        size={20} 
        color="#FFFFFF" 
      />
      {selectedFilter && (
        <TouchableOpacity 
          onPress={handleClearFilter}
          style={styles.clearFilterButton}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons name="close-circle" size={16} color="#FFFFFF" />
        </TouchableOpacity>
      )}
    </TouchableOpacity>
  );

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0F96BB" />
          <Text style={styles.loadingText}>Loading accounts...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: '#0F96BB' }]}>
          <TouchableOpacity 
            style={styles.headerLeft}
            onPress={handleOpenDrawer}
            activeOpacity={0.7}
          >
            {renderHeaderTitleContainer()}
          </TouchableOpacity>
          <View style={styles.headerRight}>
            <TouchableOpacity 
              style={styles.headerIcon}
              onPress={() => setIsSearchActive(true)}
            >
              <Ionicons name="search" size={24} color="#FFFFFF" />
            </TouchableOpacity>
            <Appbar.Action 
              icon="filter-outline"
              color="#fff" 
              onPress={() => {
                router.push({
                  pathname: '/ui/product/ProductFiltersPage',
                  params: { callFrom: CALL_FROM.ACCOUNT_LIST }
                } as any)
              }}
            />
          </View>
        </View>

        {/* Error Content */}
        <View style={styles.errorContent}>
          <View style={styles.errorContainer}>
            <Ionicons name="alert-circle-outline" size={48} color="#FF4444" style={styles.errorIcon} />
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity 
              style={styles.retryButton} 
              onPress={() => {
                if (selectedFilter) {
                  const filterPayload = {
                    title: selectedFilter.name,
                    filter: selectedFilter.filters.map((f: any) => ({
                      field_name: f.field_id,
                      field_label: f.name,
                      value_id: (f.contains.toLowerCase().includes('contain') || f.contains.toLowerCase().includes('between')) 
                        ? Array.isArray(f.filter) ? f.filter : [f.filter] 
                        : f.filter,
                      contains: f.contains || "contains"
                    })),
                    share_with: selectedFilter.share_with || "selected_users",
                    selected_users: selectedFilter.users || ["1"],
                    accessibility: selectedFilter.accessibility || "Public",
                    prevent_sharing: false
                  };
                  fetchDistributors(filterPayload);
                } else {
                  fetchDistributors();
                }
              }}
            >
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Keep the drawer available */}
        {renderSideNavigationView()}
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      
      {/* Header */}
      <View style={[styles.header, { backgroundColor: '#0F96BB' }]}>
        {isSearchActive ? (
          <View style={styles.searchHeader}>
            <TouchableOpacity onPress={() => {
                setIsSearchActive(false);
                setSearchQuery('');
            }}>
              <Ionicons name={Platform.OS === 'ios' ? 'chevron-back' : 'arrow-back'}  size={24} color="#FFFFFF" />
            </TouchableOpacity>
            <TextInput
              style={styles.searchInput}
              placeholder="Search accounts..."
              placeholderTextColor="rgba(255, 255, 255, 0.7)"
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoFocus
              
              selectionColor="#FFFFFF"
            />
            { searchQuery.length > 0 && (
              <TouchableOpacity 
                onPress={() => {
                  setIsSearchActive(false);
                  setSearchQuery('');
                }}
              >
                <Ionicons name="close-circle" size={24} color="#FFFFFF" />
              </TouchableOpacity>
            )}
          </View>
        ) : (
          <>
            <View style={styles.headerLeft}>
              {renderHeaderTitleContainer()}
            </View>
            <View style={styles.headerRight}>
              <TouchableOpacity 
                style={styles.headerIcon}
                onPress={() => setIsSearchActive(true)}
              >
                <Ionicons name="search" size={24} color="#FFFFFF" />
              </TouchableOpacity>
              <Appbar.Action 
                icon="filter-outline"
                color="#fff" 
                onPress={() => {
                  router.push({
                    pathname: '/ui/product/ProductFiltersPage',
                    params: { callFrom: CALL_FROM.ACCOUNT_LIST }
                  } as any)
                }}
              />
            </View>
          </>
        )}
      </View>
      
      {/* Distributors List */}
      <FlatList
        style={styles.flatList}
        contentContainerStyle={filteredDistributors.length === 0 ? styles.emptyContentContainer : undefined}
        data={filteredDistributors}
        keyExtractor={(item) => item.id.toString()}
        renderItem={renderContactItem}
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <Ionicons
              name="business-outline"
              size={64}
              color="#CCCCCC"
              style={styles.emptyIcon}
            />
            <Text style={styles.emptyText}>
              {searchQuery ? 'No accounts found matching your search' : 'No accounts found'}
            </Text>
            {searchQuery && (
              <Text style={styles.emptySubText}>
                Try adjusting your search terms or filters
              </Text>
            )}
          </View>
        )}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
      
      {/* Floating action button */}
       {accessScope?.contact?.create === true && accessScope?.task?.create === true &&
          accessScope?.appointment?.create === true && accessScope?.notes?.create === true && (
      <TouchableOpacity style={styles.fab} onPress={() => setAddOptionsVisible(true)}>
        <Ionicons name="add" size={32} color="#FFFFFF" />
      </TouchableOpacity>
      ) } 

      {/* Records popup */}
      <RecordsPopup 
        visible={addOptionsVisible}
        onClose={() => setAddOptionsVisible(false)}
        callFrom="distributors"
      />

      {renderSideNavigationView()}
    </SafeAreaView>
  );
}

const createStyles = (colors: ReturnType<typeof useColors>) => StyleSheet.create<DistributorScreenStyles>({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#EFEFEF',
    backgroundColor: '#0F96BB',
    height: 64,
    position: 'relative',
    zIndex: 1,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: '#FFFFFF',
    marginRight: 6,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: 48,
    justifyContent: 'flex-end',
  },
  headerIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  distributorItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  distributorAvatar: {
    width: 42,
    height: 42,
    borderRadius: 21,
    backgroundColor: '#0F96BB',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  distributorInitial: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  distributorInfo: {
    flex: 1,
  },
  distributorName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
  },
  distributorContact: {
    fontSize: 14,
    color: '#777777',
    marginTop: 2,
  },
  daysAgo: {
    fontSize: 14,
    color: '#777777',
  },
  separator: {
    height: 1,
    backgroundColor: '#EFEFEF',
    marginLeft: 72,
  },
  fab: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#0F96BB',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    fontWeight: '500',
    color: '#0F96BB',
    marginTop: 16,
  },
  errorContent: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFFFFF',
  },
  errorIcon: {
    marginBottom: 16,
  },
  errorText: {
    fontSize: 16,
    color: '#FF4444',
    textAlign: 'center',
    marginBottom: 16,
    paddingHorizontal: 24,
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    backgroundColor: '#0F96BB',
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FFFFFF',
  },
  flatList: {
    flex: 1,
  },
  emptyContentContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 60,
  },
  emptyIcon: {
    marginBottom: 20,
  },
  emptyText: {
    fontSize: 18,
    color: '#777777',
    textAlign: 'center',
    fontWeight: '500',
    marginBottom: 8,
  },
  emptySubText: {
    fontSize: 14,
    color: '#999999',
    textAlign: 'center',
    marginTop: 8,
  },
  searchHeader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    backgroundColor: '#0F96BB',
    zIndex: 2,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
    color: '#ffffff',
    marginHorizontal: 8,
    paddingHorizontal: 8,
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  drawerOverlay: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  drawerContent: {
    backgroundColor: colors.primary,
    height: '100%',
    width: '80%',
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    borderRightWidth: 1,
    borderRightColor: '#eee',
    shadowColor: '#000',
    shadowOffset: { width: 2, height: 0 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    paddingTop: Platform.OS === 'ios' ? 47 : 0,
  },
  drawerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 12,
    height: 56,
    zIndex: 1,
  },
  drawerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  filterItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#fff',
  },
  filterItemContent: {
    flex: 1,
  },
  filterItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  filterItemText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  accessibilityText: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  selectedFilterItem: {
    backgroundColor: '#f5f5f5',
  },
  selectedFilterItemText: {
    color: colors.primary,
    fontWeight: '500',
  },
  filterSearchContainer: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#fff',
  },
  filterSearchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    height: 40,
    paddingVertical: 0,
  },
  filterItemActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  actionButton: {
    padding: 4,
    borderRadius: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  filterList: {
    flex: 1,
    backgroundColor: '#fff',
  },
  emptySearchContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  emptySearchText: {
    fontSize: 16,
    color: '#777777',
    textAlign: 'center',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 40,
  },
  searchIcon: {
    marginRight: 8,
  },
  filterDetails: {
    marginTop: 8,
    paddingLeft: 8,
  },
  filterFieldText: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  removeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  removeButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  searchIconContainer: {
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconWrapper: {
    width: 24,
    height: 24,
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  clearFilterButton: {
    marginLeft: 8,
    padding: 4,
  },
}); 
