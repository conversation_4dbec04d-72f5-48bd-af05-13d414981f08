import { Tabs, router } from 'expo-router';
import React, { useState, useEffect } from 'react';
import { Platform, View, StyleSheet, TouchableOpacity, Text, Modal, Pressable, ActivityIndicator, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { useColors } from '@/hooks/useThemeColor';

import { convertDate, DATE_FORMAT, readUserScope } from '../ui/utils';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Activity, ActivitiesResponse } from '../models/home_page_model';
import { apiService } from '../../services/ApiService';
import { API_ENDPOINTS } from '../../config/api';
import { StatusBarConfig } from '@/components/StatusBarConfig';
// Define icon names as const for type safety
const ICONS = {
  PERSON: 'person-outline' as const,
  BUSINESS: 'business-outline' as const,
  DOCUMENT: 'document-text-outline' as const,
  CALENDAR: 'calendar-outline' as const,
  CASH: 'cash-outline' as const,
  CLIPBOARD: 'clipboard-outline' as const,
  MIC: 'mic-outline' as const,
  ADD: 'add' as const,
  HOME: 'home-outline' as const,
  PEOPLE: 'people-outline' as const,
} as const;

type IconType = typeof ICONS[keyof typeof ICONS];


// Custom action menu for the plus button
function ActionMenu({ visible, onClose }: { visible: boolean, onClose: () => void }) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [salesActivities, setSalesActivities] = useState<Activity[]>([]);
  const [records, setRecord] = useState<Activity[]>([]);
  const [accessScope, setAccessScope] = useState<any>(null);

const userScope=async()=>{
  const accessScope=await readUserScope();
  console.log("Access_Scope___sdjk: " + JSON.stringify(accessScope));
  setAccessScope(accessScope);
}

  const fetchActivities = async () => {
    try {
      setLoading(true);
      setError(null);
      const now = new Date();
    
      // Format date as YYYY-MM-DD using the conversion function
        const date = convertDate(now.toISOString(), DATE_FORMAT.SYSTEM_FORMAT, DATE_FORMAT.DATE_FORMAT);
        console.log("date: " + date);
      const { data } = await apiService.post<ActivitiesResponse>(
        API_ENDPOINTS.HOME_PAGE, 
        { date: date }
      );
    console.log("homnwPageFetchActivity: " + JSON.stringify(data));
      // Validate response structure
      if (!data || typeof data !== 'object') {
        throw new Error('Invalid response format');
      }

      // Validate response.response exists and is an object
      if (!data.response || typeof data.response !== 'object') {
        throw new Error('Invalid response structure');
      }

      // Check status and message
      if (!data.status) {
        throw new Error(data.message || 'Operation failed');
      }

      // Type guard for salesactivities
      if (data.response.salesactivities) {
        if (!Array.isArray(data.response.salesactivities)) {
          throw new Error('Sales activities data is not in the expected format');
        }
        setSalesActivities(data.response.salesactivities);
      } else {
        setSalesActivities([]);
      }

      // Type guard for records
      if (data.response.records) {
        if (!Array.isArray(data.response.records)) {
          throw new Error('Records data is not in the expected format');
        }
        setRecord(data.response.records);
      } else {
        setRecord([]);
      }

    } catch (err) {
      console.error('Error fetching activities:', err);
      let errorMessage = 'Failed to load activities';
      
      if (err instanceof Error) {
        // Handle specific error types
        if (err.message.includes('Network Error')) {
          errorMessage = 'Network error. Please check your connection.';
        } else if (err.message.includes('timeout')) {
          errorMessage = 'Request timed out. Please try again.';
        } else if (err.message.includes('401')) {
          errorMessage = 'Session expired. Please login again.';
        } else if (err.message.includes('403')) {
          errorMessage = 'You don\'t have permission to access this feature.';
        } else {
          // Use the error message if it exists
          errorMessage = err.message || errorMessage;
        }
      }
      
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Fetch data when modal becomes visible
  useEffect(() => {
    if (visible) {
      fetchActivities();
      userScope();
    }
  }, [visible]);

  if (!visible) return null;

  const getIconForActivity = (activity: Activity): IconType => {
    // Map activity names to icons
    const iconMap: Record<string, IconType> = {
      'Task': 'checkmark-circle-outline' as IconType    ,
      'Meeting': 'calendar-outline' as IconType,
      'Voice Notes': 'mic-outline' as IconType,
      'Contact': 'people-outline' as IconType,
      'Account': 'business-outline' as IconType,
      'Opportunity': 'trending-up-outline' as IconType,
      'Product': 'cube-outline' as IconType,
      'Quotation': 'document-text-outline' as IconType,
      'Call Log': 'call-outline' as IconType,
      'Note': 'pencil' as IconType,
      'Email': 'mail-outline' as IconType,
      'SMS': 'chatbubble-outline' as IconType,
      'Visit': 'location-outline' as IconType,
      'Survey': 'clipboard-outline' as IconType,
      'Complaint': 'alert-circle-outline' as IconType,
      'Feedback': 'chatbubble-ellipses-outline' as IconType,
      'Order': 'cart-outline' as IconType,
      'Invoice': 'receipt-outline' as IconType,
      'Payment': 'wallet-outline' as IconType,
      'Expense': 'cash-outline' as IconType,
      'Report': 'bar-chart-outline' as IconType,
      'Campaign': 'megaphone-outline' as IconType,
      'Lead': 'person-add-outline' as IconType,
      'Opportunity': 'trophy-outline' as IconType,
      'Contract': 'document-outline' as IconType,
      'Proposal': 'document-text-outline' as IconType,
      'Quote': 'pricetag-outline' as IconType,
      'Follow Up': 'refresh-outline' as IconType,
      'Reminder': 'notifications-outline' as IconType,
      'Schedule': 'time-outline' as IconType
    };
    
    return iconMap[activity.activity_name] || ICONS.CLIPBOARD;
  };

  const renderErrorState = () => (
    <View style={styles.errorContainer}>
      <Text style={styles.errorText}>{error}</Text>
      <TouchableOpacity 
        style={styles.retryButton} 
        onPress={fetchActivities}
      >
        <Text style={styles.retryButtonText}>Retry</Text>
      </TouchableOpacity>
    </View>
  );

  const renderLoadingState = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color="#0F96BB" />
      <Text style={styles.loadingText}>Loading activities...</Text>
    </View>
  );
  const renderRecords = () => {
    console.log("Access_Scope___renderRecords: " + JSON.stringify(accessScope));
    const contactAccess=accessScope?.contact;
    return  records.map((record) => {
                      console.log("Recordsdsd: "+record.activity_name);
                      return <TouchableOpacity
                        key={record.id}
                        style={styles.recordItem}
                        onPress={() => {
                          // Handle record press
                          console.log('Record pressed:', record.activity_name);
                          console.log('Current accessScope:', JSON.stringify(accessScope));

                          // If accessScope is not loaded yet, don't show coming soon message
                          if (!accessScope) {
                            console.log('Access scope not loaded yet, ignoring click');
                            return;
                          }

                          // Navigate based on the record type
                          if (record.activity_name === 'Task' || record.activity_name.toLowerCase().includes('task') && accessScope?.task?.create) {
                            router.push('/tasks/create');
                            onClose();
                          } else if (record.activity_name === 'Meeting' || record.activity_name.toLowerCase().includes('meeting') && accessScope?.meeting?.create) {
                            router.push('/meetings/createMeeting');
                            onClose();
                          } //else if (record.activity_name === 'Product' || record.activity_name.toLowerCase().includes('product')) {
                          //router.push('/ui/helpers/AddNotesPage');
                          //onClose();
                          //}
                            else if ((record.activity_name === 'Contact' || record.activity_name.toLowerCase().includes('contact')) && accessScope?.contact?.create) {
                            router.push('/contact');
                            onClose();
                          } else if ((record.activity_name === 'Account' || record.activity_name.toLowerCase().includes('account')) && accessScope?.sales_account?.create) {
                            router.push('/CreateAccounts');
                            onClose();
                          }
                          else if (record.activity_name === 'Opportunity' || record.activity_name.toLowerCase().includes('opportunity')) {
                            console.log('Opportunity clicked - Deal create permission:', accessScope?.deal?.create);
                            if (accessScope?.deal?.create) {
                              console.log('Navigating to CreateDeals');
                              router.push('/CreateDeals');
                              onClose();
                            } else {
                              console.log('No permission to create deals');
                              Alert.alert('Permission Denied', 'Opportunity creation is not available for your account.');
                            }
                          } else if (record.activity_name === 'Product' || record.activity_name.toLowerCase().includes('product')) {
                            router.push('/DealsAddProduct');
                            onClose();
                          }
                          else if (record.activity_name === 'Quotation' || record.activity_name.toLowerCase().includes('quotation')) {
                            router.push('/DealsAddQuote');
                            onClose();
                          }
                          
                          else {
                            // For other record types
                            Alert.alert('Coming Soon', `${record.activity_name} creation will be available soon!`);
                          }
                        }}
                      >
                        <View style={styles.recordIcon}>
                          <Ionicons name={getIconForActivity(record)} size={28} color="#0F96BB" />
                        </View>
                        <Text style={styles.recordText}>{record.activity_name}</Text>
                      </TouchableOpacity>
                    })
  };

  return (
    <Modal
      transparent={true}
      visible={visible}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.popupOverlay}>
        <TouchableOpacity 
          style={styles.closePopupArea}
          onPress={onClose}
        />
        
        <View style={styles.popup}>
          <Text style={styles.popupTitle}>RECORDS</Text>
          
          <View style={styles.popupRecords}>
            {loading ? renderLoadingState() : 
             error ? renderErrorState() : 
             records.length === 0 ? (
               <View style={styles.emptyContainer}>
                 <Text style={styles.emptyText}>No records available</Text>
               </View>
             ) : (
                renderRecords()
             )}
          </View>
          
          <Text style={styles.popupTitle}>SALES ACTIVITIES</Text>
          
          <View style={styles.popupActivities}>
            {loading ? renderLoadingState() : 
             error ? renderErrorState() : 
             salesActivities.length === 0 ? (
               <View style={styles.emptyContainer}>
                 <Text style={styles.emptyText}>No activities available</Text>
               </View>
             ) : (
               salesActivities.map((activity) => (
                 <TouchableOpacity 
                   key={activity.id} 
                   style={styles.activityItem}
                   onPress={() => {
                     // Handle activity press
                     console.log('Activity pressed:', activity.activity_name);
                     // Navigate based on the activity type
                     if (activity.activity_name === 'Task' || activity.activity_name.toLowerCase().includes('task')) {
                       router.push('/tasks/create');
                       onClose();
                     } else if (activity.activity_name === 'Meeting' || activity.activity_name.toLowerCase().includes('meeting')) {
                      router.push('/meetings/createMeeting');
                      onClose();
                    } else if(activity.activity_name === 'Voice Notes' || activity.activity_name.toLowerCase().includes('voice notes')){
                      //router.push('/ui/helpers/AddNotesPage');
                      router.push({
                        pathname: '/ui/helpers/AddNotesPage',
                        params: { id: '0' , type: "accounts"}
                       })
                      onClose();
                     }
                     else if(activity.activity_name === 'Voice Note' || activity.activity_name.toLowerCase().includes('voice note')){
                      router.push({
                        pathname: '/ui/helpers/AddNotesPage',
                        params: { id: '0' , type: "accounts"}
                       })
                      onClose();
                     }
                     else {
                       // For other activity types
                       Alert.alert('Coming Soon', `${activity.activity_name} creation will be available soon!`);
                     }
                   }}
                 >
                   <View style={styles.activityIcon}>
                     <Ionicons name={getIconForActivity(activity)} size={28} color="#0F96BB" />
                   </View>
                   <Text style={styles.activityText}>{activity.activity_name || activity.activity_name}</Text>
                 </TouchableOpacity>
               ))
             )}
          </View>
        </View>
      </View>
    </Modal>
  );
}

// Custom tab button component for the center "plus" button
function PlusButton({ onPress }: { onPress: () => void }) {
  return (
    <TouchableOpacity
      style={styles.plusButton}
      onPress={onPress}
    >
      <View style={styles.plusButtonInner}>
        <Ionicons name={ICONS.ADD} size={32} color="#fff" />
      </View>
    </TouchableOpacity>
  );
}

export default function TabLayout() {
  const [actionMenuVisible, setActionMenuVisible] = useState(false);
  const insets = useSafeAreaInsets();
  const colors = useColors();

  const showActionMenu = () => setActionMenuVisible(true);
  const hideActionMenu = () => setActionMenuVisible(false);

  const handleNotification = () => {
    console.log('Notification pressed');
  };

  const handleMenu = () => {
    console.log('Menu pressed');
  };

  return (
    <View style={{ flex: 1 }}>
      <StatusBarConfig />
      <View 
        style={{ 
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: 100 + insets.top,
          backgroundColor: '#0F96BB',
          zIndex: -1,
        }} 
      />
      <Tabs
        screenOptions={{
          headerShown: false,
          tabBarStyle: {
            backgroundColor: colors.background.primary,
            borderTopColor: colors.background.secondary,
            height: 60 + insets.bottom,
            paddingBottom: insets.bottom,
          },
          tabBarActiveTintColor: colors.tint,
          tabBarInactiveTintColor: colors.text.secondary,
          headerLeftContainerStyle: {
            paddingLeft: 16,
            paddingTop: insets.top,
          },
          headerRightContainerStyle: {
            paddingRight: 16,
            paddingTop: insets.top,
          },
          headerTitleContainerStyle: {
            paddingTop: insets.top,
          },
          headerShadowVisible: false,
          headerLeft: () => (
            <TouchableOpacity onPress={handleMenu} style={{ padding: 8 }}>
              <Ionicons name="menu-outline" size={24} color="#fff" />
            </TouchableOpacity>
          ),
        }}
      >
        <Tabs.Screen
          name="index"
          options={{
            title: 'Home',
            headerShown: false,
            tabBarIcon: ({ color }) => <Ionicons name={ICONS.HOME} size={24} color={color} />,
          }}
        />
        <Tabs.Screen
          name="contacts"
          options={{
            title: 'Contacts',
            //headerTitle: 'Contacts',
            tabBarIcon: ({ color }) => <Ionicons name={ICONS.PEOPLE} size={24} color={color} />,
            headerRight: () => (
              <TouchableOpacity style={{ padding: 8 }}>
                <Ionicons name="search-outline" size={24} color="#fff" />
              </TouchableOpacity>
            ),
          }}
        />
        <Tabs.Screen
          name="plus"
          options={{
            title: '',
            headerShown: false,
            tabBarIcon: () => <View />,
            tabBarButton: (props) => (
              <PlusButton
                onPress={showActionMenu}
              />
            ),
          }}
        />
        <Tabs.Screen
          name="distributors"
          options={{
            title: 'Accounts',
            //headerTitle: 'Accounts',
            tabBarIcon: ({ color }) => <Ionicons name={ICONS.BUSINESS} size={24} color={color} />,
            headerRight: () => (
              <TouchableOpacity style={{ padding: 8 }}>
                <Ionicons name="search-outline" size={24} color="#fff" />
              </TouchableOpacity>
            ),
          }}
        />
        <Tabs.Screen
          name="orders"
          options={{
            title: 'Opportunities',
            //headerTitle: 'Opportunities',
            tabBarIcon: ({ color }) => <Ionicons name={ICONS.DOCUMENT} size={24} color={color} />,
            headerRight: () => (
              <TouchableOpacity style={{ padding: 8 }}>
                <Ionicons name="search-outline" size={24} color="#fff" />
              </TouchableOpacity>
            ),
          }}
        />
      </Tabs>
      <ActionMenu visible={actionMenuVisible} onClose={hideActionMenu} />
    </View>
  );
}

const styles = StyleSheet.create({
  plusButton: {
    top: -20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  plusButtonInner: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#0F96BB',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  popupOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 1000,
  },
  closePopupArea: {
    flex: 1,
  },
  popup: {
    backgroundColor: '#C4E8F5',
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  popupTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  popupRecords: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  recordItem: {
    alignItems: 'center',
    width: '33%',
    marginBottom: 16,
  },
  recordIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  recordText: {
    fontSize: 12,
    color: '#333',
    textAlign: 'center',
  },
  popupActivities: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  activityItem: {
    alignItems: 'center',
    width: '33%',
    marginBottom: 16,
  },
  activityIcon: {
    width: 60,
    height: 60,
    borderRadius: 10,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  activityText: {
    fontSize: 12,
    color: '#777',
    textAlign: 'center',
  },
  loadingContainer: {
    width: '100%',
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    color: '#666',
    fontSize: 14,
  },
  errorContainer: {
    width: '100%',
    padding: 20,
    alignItems: 'center',
  },
  errorText: {
    color: '#FF3B30',
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 12,
  },
  retryButton: {
    backgroundColor: '#0F96BB',
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  emptyContainer: {
    width: '100%',
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    color: '#666',
    fontSize: 14,
    textAlign: 'center',
  },
});
