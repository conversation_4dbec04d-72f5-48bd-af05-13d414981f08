import React, { useState, useEffect, useMemo } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, ScrollView, FlatList, Pressable, TouchableWithoutFeedback, Switch, ActivityIndicator, Alert, Linking, Modal } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { SideMenu } from '../../components/SideMenu';
import { router } from 'expo-router';
import { useColors } from '@/hooks/useThemeColor';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from '../../context/AuthContext';
import { apiService } from '../../services/ApiService';
import { API_ENDPOINTS } from '../../config/api';
import { ActivitiesResponse, Activity, OverDueTask, Task } from '../models/home_page_model';
import * as Location from 'expo-location';
import { convertDate, DATE_FORMAT, readUserScope, RECORD_TYPES, SALES_ACTIVITIES, updateUserScope } from '../ui/utils';
import { useFocusEffect } from '@react-navigation/native';
import { useAppData } from '../ui/utils';
import { attendanceStorageService } from '../../services/AttendanceStorageService';
import useAttendanceValidation from '../../hooks/useAttendanceValidation';

// Constants - must match the ones in AuthService
const TOKEN_STORAGE_KEY = 'auth_token';
const USER_STORAGE_KEY = 'user_data';

var dateSelected = new Date();

// Sample data for calendar
const WEEKDAYS = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];

// Generate dates for current month
const generateMonthDates = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = today.getMonth();
  const currentDate = today.getDate();

  console.log("Today's date from generateMonthDates:", currentDate);

  // Get the number of days in the month
  const daysInMonth = new Date(year, month + 1, 0).getDate();

  // Create dates array
  const dates = [];

  for (let day = 1; day <= daysInMonth; day++) {
    // Create date with proper time set to start of day
    const date = new Date(year, month, day);
    date.setHours(0, 0, 0, 0);

    // Format the date in YYYY-MM-DD format
    const formattedDate = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;

    dates.push({
      day: WEEKDAYS[date.getDay()],
      date: day,
      isToday: day === currentDate,
      fullDate: formattedDate, // Store as YYYY-MM-DD string directly
    });
  }

  return dates;
};

// Get current month and year as string
const getCurrentMonthYear = () => {
  const today = new Date();
  return today.toLocaleString('default', { month: 'long', year: 'numeric' });
};

// Utility function to capitalize first letter
const capitalizeFirstLetter = (string: string) => {
  if (!string) return string;
  return string.charAt(0).toUpperCase() + string.slice(1);
};

// Format date to dd-mm-yyyy and format time to include AM/PM
const formatDateToDMY = (dateString: string, timeString?: string): string => {
  if (!dateString) return '';
  try {
    // First convert to standard format using the existing utility
    const standardDate = convertDate(dateString, DATE_FORMAT.SYSTEM_FORMAT, DATE_FORMAT.DATE_FORMAT);
    // Then restructure from yyyy-mm-dd to dd-mm-yyyy
    const [year, month, day] = standardDate.split('-');

    // If time is provided, format it with AM/PM
    if (timeString) {
      // Check if time already contains AM/PM
      if (timeString.toLowerCase().includes('am') || timeString.toLowerCase().includes('pm')) {
        return `${day}-${month}-${year} ${timeString}`;
      }

      // Parse the time string
      if (timeString.includes(':')) {
        // Time is in 24-hour format, convert to 12-hour with AM/PM
        const [hourStr, minuteStr] = timeString.split(':');
        const hour = parseInt(hourStr, 10);
        const minute = minuteStr || '00';

        const period = hour >= 12 ? 'PM' : 'AM';
        const hour12 = hour % 12 || 12; // Convert 0 to 12 for 12 AM

        return `${day}-${month}-${year} ${hour12}:${minute} ${period}`;
      }

      return `${day}-${month}-${year} ${timeString}`;
    }

    return `${day}-${month}-${year}`;
  } catch (error) {
   // console.error('Error formatting date to DMY:', error);
    return dateString;
  }
};

// Format date to dd/mm day format for tasks list
const formatDateForTaskList = (dateString: string): string => {
  if (!dateString) return '';
  try {
    // First convert to standard format using the existing utility
    const standardDate = convertDate(dateString, DATE_FORMAT.SYSTEM_FORMAT, DATE_FORMAT.DATE_FORMAT);
    // Parse the YYYY-MM-DD format correctly
    const [year, month, day] = standardDate.split('-').map(num => parseInt(num));
    // Create date with local timezone (month is 0-indexed in JS Date)
    const date = new Date(year, month - 1, day);

    // Format for table: "dd/mm day"
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return `${String(day).padStart(2, '0')}/${String(month).padStart(2, '0')} ${days[date.getDay()]}`;
  } catch (error) {
    console.error('Error formatting date for task list:', error);
    return dateString;
  }
};

// Format time to 12-hour format with AM/PM
const formatTimeForTaskList = (timeString: string): string => {
  if (!timeString) return '';
  try {
    // Check if time already contains AM/PM
    if (timeString.toLowerCase().includes('am') || timeString.toLowerCase().includes('pm')) {
      return timeString;
    }

    // Parse the time string
    if (timeString.includes(':')) {
      // Time is in 24-hour format, convert to 12-hour with AM/PM
      const [hourStr, minuteStr] = timeString.split(':');
      const hour = parseInt(hourStr, 10);
      const minute = minuteStr || '00';

      const period = hour >= 12 ? 'PM' : 'AM';
      const hour12 = hour % 12 || 12; // Convert 0 to 12 for 12 AM

      return `${hour12}:${minute} ${period}`;
    }

    return timeString;
  } catch (error) {
    console.error('Error formatting time for task list:', error);
    return timeString;
  }
};

export default function HomeScreen() {
  const [isSideMenuVisible, setIsSideMenuVisible] = useState(false);
  // Get the current date number directly
  const todayDate = new Date().getDate();
  const [selectedDate, setSelectedDate] = useState(todayDate);
  const [currentSelectedDate, setCurrentSelectedDate] = useState(new Date());
  const [isCheckedIn, setIsCheckedIn] = useState(false);
  const [checkedInTime, setCheckedInTime] = useState<string | null>(null);
  const [dates] = useState(generateMonthDates());
  const [currentMonthYear] = useState(getCurrentMonthYear());
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [homeData, setHomeData] = useState<ActivitiesResponse | null>(null);
  const colors = useColors();
  const styles = useMemo(() => createStyles(colors), [colors]);
  const { user } = useAuth(); // Get user from auth context
  const [tokenValue, setTokenValue] = useState<string | null>(null);
  const [userData, setUserData] = useState<string | null>(null);
  const [isAttendanceLoading, setIsAttendanceLoading] = useState(false);
  const calendarRef = React.useRef<FlatList>(null);
  const [showMoreOptions, setShowMoreOptions] = useState(false);
  const { updateRecordTypes, updateSalesActivities } = useAppData();

  // Attendance validation hook
  const { attendanceStatus } = useAttendanceValidation();

  // Helper function to get current attendance status (from API data or stored data)
  const getCurrentAttendanceStatus = () => {
    // First check API data
    if (homeData?.response?.attendance?.status) {
      return homeData.response.attendance.status;
    }
    // Fallback to stored attendance data
    if (attendanceStatus?.status) {
      return attendanceStatus.status;
    }
    return null;
  };

  const menuItems = [
    { id: 'products', icon: 'cube-outline', label: 'Products' },
    { id: 'settings', icon: 'settings-outline', label: 'Settings' },
    // { id: 'privacy', icon: 'shield-outline', label: 'Privacy Policy' },
    // { id: 'analytics', icon: 'bar-chart-outline', label: 'Analytics' },
    // { id: 'more', icon: 'ellipsis-horizontal-outline', label: 'More' },
  ];

  const handleMenuItemPress = (id: string) => {
    switch (id) {
      case 'products':
        router.push('/ui/product/ProductListPage' as any);
        break;
      case 'settings':
        router.push('/ui/settings/SettingsPage' as any);
        break;
      case 'privacy':
        router.push('/ui/privacy/PrivacyPolicyPage' as any);
        break;
      case 'analytics':
        router.push('/ui/analytics/AnalyticsPage' as any);
        break;
      case 'more':
        // Handle more options
        break;
    }
    setShowMoreOptions(false);
  };

  // Add layout effect to scroll to current date
  React.useLayoutEffect(() => {
    // Get today's date directly to avoid any timezone issues
    const today = new Date();
    const currentDay = today.getDate();
    console.log(`Today's actual date in layout effect: ${currentDay}, Month: ${today.getMonth() + 1}`);

    // Find current date in dates array
    const currentIndex = dates.findIndex(item => item.date === currentDay);
    console.log(`Found today at index: ${currentIndex}, Date value: ${currentIndex !== -1 ? dates[currentIndex].date : 'not found'}`);

    // Explicitly set the selected date to today
    setSelectedDate(currentDay);

    if (currentIndex !== -1 && calendarRef.current) {
      // Add a small delay to ensure the FlatList is properly laid out
      setTimeout(() => {
        calendarRef.current?.scrollToIndex({
          index: Math.max(0, currentIndex - 2), // Show 2 items before current date
          animated: true,
          viewPosition: 0.3
        });
      }, 100);
    }
  }, [dates]);

  // Load attendance data from storage on component mount
  useEffect(() => {
    const loadInitialAttendanceData = async () => {
      const now = new Date();
      const currentDate = convertDate(now.toISOString(), DATE_FORMAT.SYSTEM_FORMAT, DATE_FORMAT.DATE_FORMAT);

      console.log('🔄 Loading initial attendance data from storage...');
      await loadAttendanceFromStorage(currentDate);
    };

    loadInitialAttendanceData();
  }, []);

  // Fetch home page data
  useEffect(() => {
    // Get current date and explicitly select it
    const now = new Date();
    const today = now.getDate();

    console.log(`Component mounted, setting today's date: ${today}, ${now.toISOString()}`);

    // Update the selected date in the UI
    setSelectedDate(today);

    // Update our global reference
    dateSelected = now;

    // Fetch data for today
    fetchHomeData(now);

  }, []);

  // Add useFocusEffect to refresh data when screen is focused
  useFocusEffect(
    React.useCallback(() => {
      console.log('Home screen focused, refreshing data...');
      const now = new Date();
      if(dateSelected!=now){
        fetchHomeData(dateSelected);
      }else{
        fetchHomeData(now);
      }

      return () => {
        // Cleanup if needed
      };
    }, [dateSelected])
  );

  // Add useEffect to log token when component mounts
  useEffect(() => {
    const checkAuthData = async () => {
      try {
        // Check token

        const token = await AsyncStorage.getItem(TOKEN_STORAGE_KEY);
        console.log('HomeScreen - Token:', token);

        // Check user data
        const userDataString = await AsyncStorage.getItem(USER_STORAGE_KEY);
        console.log('HomeScreen - User Data:', userDataString);
        // setUserData(userDataString ? 'Found' : 'Not found');

        // Log everything together for easier debugging
        console.log('HomeScreen - Complete Auth State:', {
          token: token ? 'Present' : 'Missing',
          userData: userDataString ? 'Present' : 'Missing',
          contextUser: user ? 'Present' : 'Missing',
          contextUserId: user?.id || 'None'
        });
      } catch (error) {
        console.error('Error checking auth data:', error);
        // setTokenValue('Error');
        // setUserData('Error');
      }
    };

    checkAuthData();
  }, [user]);

  const toggleSideMenu = async () => {
    console.log('🔑 toggleSideMenu');
    setIsSideMenuVisible(!isSideMenuVisible);
  };


  const meetingDetails = async (activity: Activity) => {
    console.log('Opening meeting details for:', activity.activity_title);

      router.push({
      pathname: '/meetings/detailedMeeting' as any,
      params: { data: JSON.stringify(activity.id)  }
    });

  };

  const handleMonthlyViewPress = () => {
    // Navigate to calendar page
    router.push("/MonthlyCalendarView" as any);
  };

  // Add navigation to notifications screen
  const handleNotificationPress = () => {
    router.push('/notifications' as any);
  };

  // Handle attendance check-in/out toggle
  const toggleAttendance = async (value: boolean) => {
    try {
      setIsAttendanceLoading(true);

      // Request location permissions
      let { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission Denied',
          'Location permission is required for attendance marking.',
          [{ text: 'OK' }]
        );
        return;
      }

      // Get current location
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High
      });

      // Get address from coordinates
      const [address] = await Location.reverseGeocodeAsync({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude
      });

      // Format address
      const formattedAddress = [
        address.street,
        address.district,
        address.city,
        address.region,
        address.postalCode,
        address.country
      ].filter(Boolean).join(', ');

      // Show confirmation dialog with location
      Alert.alert(
        value ? 'Check In Confirmation' : 'Check Out Confirmation',
        `Are you sure you want to ${value ? 'check in' : 'check out'}?\n\nLocation:\n${formattedAddress}`,
        [
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => setIsAttendanceLoading(false)
          },
          {
            text: 'Confirm',
            onPress: async () => {
              try {
                const now = new Date();
                const date = convertDate(now.toISOString(), DATE_FORMAT.SYSTEM_FORMAT, DATE_FORMAT.DATE_TIME_FORMAT);

                const map = {
                  latitude: location.coords.latitude,
                  longitude: location.coords.longitude,
                  location: formattedAddress
                };

                console.log('Attendance request data:', map);
                var attendanceId = homeData?.response?.attendance?.id;

                const response = await apiService.put(API_ENDPOINTS.MEETING_ADD+"/"+attendanceId+"/status", map);

                if (response.status === 200) {
                  const timeString = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                  const newStatus = value ? 'Inprogress' : 'Completed';
                  const currentDate = convertDate(now.toISOString(), DATE_FORMAT.SYSTEM_FORMAT, DATE_FORMAT.DATE_FORMAT);

                  // Update local state
                  if (value) {
                    setCheckedInTime(timeString);
                  } else {
                    setCheckedInTime(null);
                  }
                  setIsCheckedIn(value);

                  // Save updated attendance status to local storage
                  try {
                    await attendanceStorageService.updateAttendanceStatus(currentDate, newStatus, {
                      checkin_time: value ? timeString : homeData?.response?.attendance?.checkin_time || null,
                      checkout_time: value ? null : timeString,
                      location: formattedAddress
                    });
                    console.log('✅ Attendance status saved to local storage');
                  } catch (storageError) {
                    console.error('❌ Error saving attendance to storage:', storageError);
                  }

                  // Refresh data from API
                  fetchHomeData(new Date());

                  // Show success message
                  Alert.alert(
                    value ? 'Checked In Successfully' : 'Checked Out Successfully',
                    `Location: ${formattedAddress}`,
                    [{ text: 'OK' }]
                  );
                } else {
                  handleError('Failed to update attendance');
                }
              } catch (error) {
                console.error("API error:", error);
                handleError('Failed to update attendance status');
              } finally {
                setIsAttendanceLoading(false);
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error("Attendance error:", error);
      handleError('Failed to update attendance status');
      setIsAttendanceLoading(false);
    }
  };
const showAttendanceView = () => {
  if(selectedDate===new Date().getDate()){
    if(homeData?.response?.attendance!=null){
      console.log("Attendance Status: "+homeData?.response?.attendance!.status);
      if(homeData?.response?.attendance.status=="Pending"||homeData?.response?.attendance.status=="Inprogress"){
        return 'flex';
      }
    }
  }
  return 'none';
}
  // Render meetings from API data
  const renderMeetings = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      );
    }

    if (error) {
      return (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      );
    }

    const activities = homeData?.response?.activities || [];
    if (activities.length === 0) {
      return (
        <View style={styles.noDataContainer}>
          <Text style={styles.noDataText}>No meetings scheduled</Text>
          <TouchableOpacity
            style={styles.addDataButton}
            onPress={() => router.push('/meetings/createMeeting' as any)}
          >
            <Ionicons name="add-circle-outline" size={20} color={colors.primary} style={styles.addButtonIcon} />
            <Text style={styles.addButtonText}>Add Meeting</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return activities.map((activity: any) => {
      console.log("Activity: "+activity.activity_title);
      return <TouchableOpacity key={activity.id} style={styles.menuButton} onPress={() => meetingDetails(activity)}>
        <View style={styles.meetingItem}>
          <View style={styles.meetingMainContent}>
            <View style={styles.meetingLeftContent}>
              <Text style={styles.meetingTitle}>{capitalizeFirstLetter(activity.activity_title)}</Text>
              {activity.description && (
                <Text style={styles.meetingDescription} numberOfLines={2} ellipsizeMode="tail">
                  {activity.description}
                </Text>
              )}
            </View>
            <View style={styles.meetingRightContent}>
              <View style={styles.dateTimeRow}>
                <Ionicons name="calendar-outline" size={14} color={colors.primary} style={styles.dateTimeIcon} />
                <Text style={styles.meetingDateTime}>
                  {activity.start_date ? formatDateForTaskList(activity.start_date) : 'No date'}
                </Text>
              </View>
              {activity.start_time && (
                <View style={styles.dateTimeRow}>
                  <Ionicons name="time-outline" size={14} color={colors.primary} style={styles.dateTimeIcon} />
                  <Text style={styles.meetingDateTime}>
                    {formatTimeForTaskList(activity.start_time)}
                  </Text>
                </View>
              )}
            </View>
          </View>
        </View>
      </TouchableOpacity>
    }  );

  };


  const fetchActivities = async () => {

   
    try {
      setLoading(true);
      setError(null);
      const now = new Date();

      // Format date as YYYY-MM-DD using the conversion function
      const date = convertDate(now.toISOString(), DATE_FORMAT.SYSTEM_FORMAT, DATE_FORMAT.DATE_FORMAT);
      console.log("datePassing: " + date);

      const { data } = await apiService.post<ActivitiesResponse>(
        API_ENDPOINTS.HOME_PAGE,
        { date: date }
      );
      console.log("Home page API Response:", {
        status: data.status,
        message: data.message,
        records: data.response?.records,
        sales_activities: data.response?.sales_activities,
        salesactivities: data.response?.salesactivities // Check both versions
      });

      // Validate response structure
      if (!data || typeof data !== 'object') {
        throw new Error('Invalid response format');
      }

      // Validate response.response exists and is an object
      if (!data.response || typeof data.response !== 'object') {
        throw new Error('Invalid response structure');
      }

      // Check status and message
      if (!data.status) {
        throw new Error(data.message || 'Operation failed');
      }

      // Type guard for salesactivities
      if (data.response.sales_activities) {
        if (!Array.isArray(data.response.sales_activities)) {
          throw new Error('Sales activities data is not in the expected format');
        }
        console.log("Storing sales_activities:", data.response.sales_activities);
        await updateSalesActivities(data.response.sales_activities);
      } else if (data.response.salesactivities) {
        // Handle alternate key name
        if (!Array.isArray(data.response.salesactivities)) {
          throw new Error('Sales activities data is not in the expected format');
        }
        console.log("Storing salesactivities (alternate key):", data.response.salesactivities);
        await updateSalesActivities(data.response.salesactivities);
      } else {
        console.log("No sales activities found in response, storing empty array");
        await updateSalesActivities([]);
      }

      // Type guard for records
      if (data.response.records) {
        if (!Array.isArray(data.response.records)) {
          throw new Error('Records data is not in the expected format');
        }
        console.log("Storing records:", data.response.records);
        await updateRecordTypes(data.response.records);
      } else {
        console.log("No records found in response, storing empty array");
        await updateRecordTypes([]);
      }

    } catch (err) {
      console.error('Error fetching activities:', err);
      let errorMessage = 'Failed to load activities';

      if (err instanceof Error) {
        // Handle specific error types
        if (err.message.includes('Network Error')) {
          errorMessage = 'Network error. Please check your connection.';
        } else if (err.message.includes('timeout')) {
          errorMessage = 'Request timed out. Please try again.';
        } else if (err.message.includes('401')) {
          errorMessage = 'Session expired. Please login again.';
        } else if (err.message.includes('403')) {
          errorMessage = 'You don\'t have permission to access this feature.';
        } else {
          errorMessage = err.message || errorMessage;
        }
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Load attendance data from local storage
  const loadAttendanceFromStorage = async (date: string) => {
    try {
      console.log('📖 Loading attendance from storage for date:', date);
      const storedAttendance = await attendanceStorageService.getAttendanceForDate(date);

      if (storedAttendance) {
        console.log('✅ Found stored attendance data:', storedAttendance);
        setIsCheckedIn(storedAttendance.status === "Inprogress");
        setCheckedInTime(storedAttendance.checkin_time);

        // Create a mock homeData structure with stored attendance
        const mockHomeData = {
          ...homeData,
          response: {
            ...homeData?.response,
            attendance: {
              id: storedAttendance.id,
              status: storedAttendance.status,
              checkin_time: storedAttendance.checkin_time,
              checkout_time: storedAttendance.checkout_time,
              location: storedAttendance.location
            }
          }
        };
        setHomeData(mockHomeData as ActivitiesResponse);
        return true;
      }
      return false;
    } catch (error) {
      console.error('❌ Error loading attendance from storage:', error);
      return false;
    }
  };

  const fetchHomeData = async (dateString: Date) => {
     fetchActivities();
      try {
        setLoading(true);
        setError(null);

         const userDataString = await AsyncStorage.getItem(USER_STORAGE_KEY);
    console.log('HomeScreen - User Data:', userDataString);
        console.log("Fetching data for date:", dateString);
        const date = convertDate(dateString.toISOString(), DATE_FORMAT.SYSTEM_FORMAT, DATE_FORMAT.DATE_FORMAT);
        const map = { "date": date };

        // First, try to load attendance from local storage
        console.log('🔄 Loading attendance from storage first...');
        await loadAttendanceFromStorage(date);

        try {
          const { data } = await apiService.post<ActivitiesResponse>(API_ENDPOINTS.HOME_PAGE, map);
          setHomeData(data);
          if (data.response.access_scope) {
            //console.log("access_scope_fetch: " + JSON.stringify(data.response.access_scope));
            updateUserScope(data.response.access_scope);
            //const scope=await readUserScope();
            //console.log("afterUpdateUserScope: " + JSON.stringify(scope));
          }
          // Save/update attendance data to local storage if available from API
          if (data.response.attendance) {
            console.log('💾 Saving/updating attendance data to storage:', data.response.attendance);
            await attendanceStorageService.saveAttendanceForDate(date, data.response.attendance);

            setIsCheckedIn(data.response.attendance.status === "Inprogress");
            setCheckedInTime(data.response.attendance.checkin_time);
          }
        } catch (apiError) {
          console.error('API Error, using stored data if available:', apiError);

          // Check if we have stored data to use as fallback
          const storedAttendance = await attendanceStorageService.getAttendanceForDate(date);
          if (!storedAttendance) {
            throw apiError; // Re-throw if no stored data available
          } else {
            console.log('✅ Using stored attendance data as fallback');
          }
        }
      } catch (err) {
        console.error('Error fetching home data:', err);
        setError('Failed to load home page data');
        handleError('Failed to load home page data');
      } finally {
        setLoading(false);
      }
    };
  // Function to fetch data for selected date
  const fetchDataForDate = async (selectedDay: number, fullDate: Date) => {
    try {
      setLoading(true);
      setError(null);

      // Format date as YYYY-MM-DD using the conversion function
      const formattedDate = convertDate(fullDate.toISOString(), DATE_FORMAT.SYSTEM_FORMAT, DATE_FORMAT.DATE_FORMAT);
      console.log("Selected date: " + formattedDate);

      try {
        const requestData = { date: formattedDate };
        const { data } = await apiService.post<ActivitiesResponse>(API_ENDPOINTS.HOME_PAGE, requestData);
        setHomeData(data);

        // Save attendance data to local storage if available
        if (data.response.attendance) {
          console.log('💾 Saving attendance data for selected date:', data.response.attendance);
          await attendanceStorageService.saveAttendanceForDate(formattedDate, data.response.attendance);

          const hasCheckedIn = data.response.attendance.checkin_time !== null;
          setCheckedInTime(hasCheckedIn ? data.response.attendance.checkin_time : null);
          setIsCheckedIn(data.response.attendance.status === "Inprogress");
        }
      } catch (apiError) {
        console.error('API Error for selected date, trying to load from storage:', apiError);

        // Try to load attendance from local storage as fallback
        const hasStoredData = await loadAttendanceFromStorage(formattedDate);

        if (!hasStoredData) {
          throw apiError; // Re-throw if no stored data available
        } else {
          console.log('✅ Successfully loaded attendance from storage for selected date');
        }
      }
    } catch (err) {
      console.error('Error fetching data for date:', err);
      setError('Failed to load data for selected date');
    } finally {
      setLoading(false);
    }
  };

  // Handle date selection
  const handleDateSelect = (day: number, dateString: string) => {
    try {
      console.log(`Selecting date: day=${day}, dateString=${dateString}`);
      // Create a proper date object from the date string
      const newDate = new Date(dateString);

      if (!isNaN(newDate.getTime())) {
        console.log(`Valid date selected: ${newDate.toISOString()}, day: ${newDate.getDate()}`);
        // Update UI state
        setSelectedDate(day);
        setCurrentSelectedDate(newDate);
        // Update global reference for selected date
        dateSelected = newDate;
        // Fetch data for the selected date
        fetchHomeData(newDate);
      } else {
        console.error('Invalid date:', dateString);
        handleError('Invalid date selected');
      }
    } catch (error) {
      console.error('Error in date selection:', error);
      handleError('Failed to process selected date');
    }
  };

  // Update error handling to use Alert instead of Toast
  const handleError = (message: string) => {
    Alert.alert(
      'Error',
      message,
      [{ text: 'OK', onPress: () => console.log('OK Pressed') }]
    );
  };

  const handleLinkPress = async (url: string) => {
    console.log("usdsdsdrl: "+url);
    try {
      // Check if the URL is supported
      const supported = await Linking.canOpenURL(url);

      if (supported) {
        // Open the URL in the default browser
        await Linking.openURL(url);
      } else {
        Alert.alert('Error', 'Cannot open this URL');
      }
    } catch (error) {
      console.error('Error opening URL:', error);
      Alert.alert('Error', 'Failed to open the link');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" />

      {/* Fixed Header */}
      <View style={styles.headerContainer}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.menuButton} onPress={toggleSideMenu}>
            <Ionicons name="menu" size={24} color="white" />
          </TouchableOpacity>

          <Text style={styles.headerTitle}>Home</Text>

          <View style={styles.headerRight}>
            <TouchableOpacity style={styles.notificationButton} onPress={handleNotificationPress}>
              <Ionicons name="notifications-outline" size={24} color="white" />
            </TouchableOpacity>
            {/*
            <TouchableOpacity style={styles.notificationButton} onPress={() => setShowMoreOptions(true)}>
              <Ionicons name="ellipsis-vertical" size={24} color="white" />
            </TouchableOpacity>
            */}
            <TouchableOpacity style={[styles.menuButton, {display:showAttendanceView()}] }>
              <View style={[ { backgroundColor: getCurrentAttendanceStatus() === "Inprogress" ? 'rgba(255, 255, 255, 0.2)' : 'rgba(255, 255, 255, 0.1)' }]}>
                {/* Only show text if status is not Pending */}
                {getCurrentAttendanceStatus() !== "Pending" && (
                  <Text style={[styles.attendanceLabel, { color: '#ffffff' }]}>
                    {getCurrentAttendanceStatus() === "Inprogress" ? "In" : "Out"}
                  </Text>
                )}
                {isAttendanceLoading ? (
                  <ActivityIndicator size="small" color="#ffffff" style={styles.attendanceLoader} />
                ) : (
                  <Switch
                    value={getCurrentAttendanceStatus() === "Inprogress"}
                    onValueChange={toggleAttendance}
                    trackColor={{ false: '#rgba(255, 255, 255, 0.3)', true: '#4CD964' }}
                    thumbColor={'#ffffff'}
                    ios_backgroundColor={'rgba(255, 255, 255, 0.3)'}
                    style={[styles.attendanceSwitch, { transform: [{ scaleX: 0.9 }, { scaleY: 0.9 }] }]}
                    disabled={isAttendanceLoading}
                  />
                )}
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Scrollable Content */}
      <ScrollView
        style={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Calendar */}
        <View style={styles.calendarContainer}>
          <View style={styles.monthHeader}>
            <Text style={styles.monthText}>{currentMonthYear}</Text>
            <TouchableOpacity style={styles.monthlyViewButton} onPress={handleMonthlyViewPress}>
              <Text style={styles.viewText}>Monthly view</Text>
              <View style={styles.arrowContainer}>
                <Ionicons name="chevron-forward" size={14} color={colors.primary} />
              </View>
            </TouchableOpacity>
          </View>

          <FlatList
            ref={calendarRef}
            data={dates}
            horizontal
            showsHorizontalScrollIndicator={false}
            keyExtractor={(item, index) => `date-${item.date}-${index}`}
            initialNumToRender={31}
            maxToRenderPerBatch={31}
            windowSize={5}
            getItemLayout={(data, index) => ({
              length: 53,
              offset: 53 * index,
              index,
            })}
            contentContainerStyle={[styles.dateList, { paddingHorizontal: 16 }]}
            style={{ marginLeft: -16 }}
            onScrollToIndexFailed={info => {
              const wait = new Promise(resolve => setTimeout(resolve, 100));
              wait.then(() => {
                if (calendarRef.current) {
                  calendarRef.current.scrollToIndex({
                    index: info.index,
                    animated: true,
                    viewPosition: 0.3
                  });
                }
              });
            }}
            renderItem={({ item }) => (
              <Pressable
                style={[
                  styles.dateItem,
                  selectedDate === item.date && styles.selectedDateItem,
                ]}
                onPress={() => handleDateSelect(item.date, item.fullDate)}>
                <Text style={[
                  styles.dayText,
                  selectedDate === item.date && styles.selectedDayText
                ]}>
                  {item.day}
                </Text>
                <Text
                  style={[
                    styles.dateText,
                    selectedDate === item.date && styles.selectedDateText,
                    item.isToday && styles.todayDateText
                  ]}>
                  {item.date}
                </Text>
                {item.isToday && <View style={styles.todayDot} />}
              </Pressable>
            )}
          />
        </View>

        {/* Meetings Section */}
        <View style={styles.sectionContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Meetings</Text>
            <TouchableOpacity style={styles.viewAllHeaderButton} onPress={() => router.push({
              pathname: '/meetings/meetingList'
            } as any)}>
              <Text style={styles.viewAllHeaderText}>View all</Text>
              <View style={styles.arrowContainer}>
                <Ionicons name="chevron-forward" size={14} color={colors.primary} />
              </View>
            </TouchableOpacity>
          </View>

          {renderMeetings()}
        </View>

        {/* Tasks Section */}
        <View style={styles.sectionContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Tasks</Text>
            <TouchableOpacity style={styles.viewAllHeaderButton} onPress={() => router.push({
              pathname: '/TaskListScreen'
            } as any)}>
              <Text style={styles.viewAllHeaderText}>View all</Text>
              <View style={styles.arrowContainer}>
                <Ionicons name="chevron-forward" size={14} color={colors.primary} />
              </View>
            </TouchableOpacity>
          </View>

          {/* {homeData?.response?.overduetasks && homeData.response.overduetasks.length > 0 ? ( */}
          {homeData?.response?.tasks && homeData.response.tasks.length > 0 ? (
            <>
              {homeData.response.tasks.map((task: Task) => (
                <TouchableOpacity
                  key={task.id}
                  style={styles.taskItem}
                  onPress={() => {
                    // First fetch the complete task data
                    const fetchAndShowTaskDetail = async () => {
                      try {
                        const endpoint = API_ENDPOINTS.TASK_DETAILS.replace(':id', String(task.id));
                        const response = await apiService.get<{ status: number; message: string; task: Task }>(endpoint);

                        if (response.data && response.data.task) {
                          // Navigate with full task object
                          router.push({
                            pathname: '/TaskDetailScreen' as any,
                            params: { task: JSON.stringify(response.data.task) }
                          });
                        } else {
                          // Fallback to ID-based navigation if fetch fails
                          router.push({
                            pathname: '/TaskDetailScreen' as any,
                            params: { taskId: task.id }
                          });
                        }
                      } catch (error) {
                        console.error('Error fetching task data:', error);
                        // Fallback to ID-based navigation
                        router.push({
                          pathname: '/TaskDetailScreen' as any,
                          params: { taskId: task.id }
                        });
                      }
                    };

                    fetchAndShowTaskDetail();
                  }}
                >
                  <View style={styles.taskContent}>
                    <View style={styles.taskMainContent}>
                      <View style={styles.taskLeftContent}>
                        <Text style={styles.taskTitle}>{capitalizeFirstLetter(task.title)}</Text>
                        {task.description && (
                          <Text style={styles.taskDescription} numberOfLines={2} ellipsizeMode="tail">
                            {task.description}
                          </Text>
                        )}
                        <View style={styles.taskRelatedContainer}>
                          {/* Display icon based on related_type */}
                          {(() => {
                            const relatedType = (task.related_type || '').toLowerCase();
                            let iconName: any = 'link-outline';

                            if (relatedType.includes('account')) {
                              iconName = 'business-outline';
                            } else if (relatedType.includes('contact')) {
                              iconName = 'person-outline';
                            } else if (relatedType.includes('deal')) {
                              iconName = 'trending-up-outline';
                            }

                            return <Ionicons name={iconName} size={16} color={colors.primary} style={styles.relationIcon} />;
                          })()}
                          <Text style={styles.taskRelatedLink} numberOfLines={1} ellipsizeMode="tail">
                            {task.related_to || ''}
                          </Text>
                        </View>
                      </View>
                      <View style={styles.taskRightContent}>
                        <View style={styles.dateTimeRow}>
                          <Ionicons name="calendar-outline" size={14} color={colors.primary} style={styles.dateTimeIcon} />
                          <Text style={styles.taskDateTime}>
                            {task.due_date ? formatDateForTaskList(task.due_date) : 'No date'}
                          </Text>
                        </View>
                        {task.due_time && (
                          <View style={styles.dateTimeRow}>
                            <Ionicons name="time-outline" size={14} color={colors.primary} style={styles.dateTimeIcon} />
                            <Text style={styles.taskDateTime}>
                              {formatTimeForTaskList(task.due_time)}
                            </Text>
                          </View>
                        )}
                      </View>
                    </View>
                  </View>
                </TouchableOpacity>
              ))}
            </>
          ) : (
            <View style={styles.noDataContainer}>
              <Text style={styles.noDataText}>No tasks for this day</Text>
              <TouchableOpacity
                style={styles.addDataButton}
                onPress={() => router.push('/tasks/create' as any)}
              >
                <Ionicons name="add-circle-outline" size={20} color={colors.primary} style={styles.addButtonIcon} />
                <Text style={styles.addButtonText}>Add Task</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Side Menu and Overlay - rendered outside the main ScrollView */}
      {isSideMenuVisible && (
        <>
          <TouchableWithoutFeedback onPress={toggleSideMenu}>
            <View style={styles.overlay} />
          </TouchableWithoutFeedback>
          <SideMenu isVisible={isSideMenuVisible} onClose={toggleSideMenu} />
        </>
      )}

      <Modal
        visible={showMoreOptions}
        transparent
        animationType="slide"
        onRequestClose={() => setShowMoreOptions(false)}
      >
        <TouchableWithoutFeedback onPress={() => setShowMoreOptions(false)}>
          <View style={styles.modalOverlay}>
            <TouchableWithoutFeedback>
              <View style={styles.bottomSheet}>
                <View style={styles.bottomSheetHeader}>
                  <Text style={styles.bottomSheetTitle}>More Options</Text>
                  <TouchableOpacity
                    onPress={() => setShowMoreOptions(false)}
                    style={styles.closeButton}
                  >
                    <Ionicons name="close" size={24} color="#666" />
                  </TouchableOpacity>
                </View>
                <View style={styles.menuItems}>
                  {menuItems.map((item) => (
                    <TouchableOpacity
                      key={item.id}
                      style={styles.menuItem}
                      onPress={() => handleMenuItemPress(item.id)}
                    >
                      <Ionicons name={item.icon as any} size={24} color={colors.text.primary} />
                      <Text style={styles.menuItemText}>{item.label}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </SafeAreaView>
  );
}


const createStyles = (colors: ReturnType<typeof useColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  headerContainer: {
    backgroundColor: "#0F96BB",
    paddingHorizontal: 16,
    paddingVertical: 10,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  scrollContent: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  menuButton: {
    padding: 0,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  notificationButton: {
    padding: 8,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 1,
  },
  calendarContainer: {
    backgroundColor: colors.background.primary,
    borderRadius: 4,
    paddingVertical: 8,
    marginBottom: 6,
  },
  monthHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  monthText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
  },
  viewText: {
    color: colors.primary,
    fontSize: 14,
  },
  dateList: {
    paddingHorizontal: 12,
  },
  dateItem: {
    width: 45,
    height: 70,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 4,
    borderRadius: 8,
  },
  selectedDateItem: {
    backgroundColor: colors.primary,
  },
  dayText: {
    fontSize: 12,
    color: colors.text.tertiary,
    marginBottom: 4,
  },
  selectedDayText: {
    color: colors.text.inverse,
  },
  dateText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text.primary,
  },
  selectedDateText: {
    color: colors.text.inverse,
  },
  todayDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: colors.primary,
    marginTop: 4,
  },
  sectionContainer: {
    backgroundColor: colors.background.primary,
    marginBottom: 2,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 4,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
  },
  addButton: {
    padding: 8,
  },
  meetingItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
  },
  meetingContent: {
    flex: 1,
    flexDirection: 'column',
  },
  meetingMainContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    flex: 1,
  },
  meetingLeftContent: {
    flex: 1,
    marginRight: 12,
  },
  meetingRightContent: {
    alignItems: 'flex-end',
    justifyContent: 'flex-start',
    minWidth: 100,
  },
  meetingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 2,
  },
  meetingDescription: {
    fontSize: 14,
    color: colors.text.secondary,
    marginBottom: 4,
    lineHeight: 18,
  },
  meetingDateTimeContainer: {
    flexDirection: 'column',
    gap: 6,
  },
  meetingDateTime: {
    fontSize: 12,
    color: colors.text.secondary,
    marginLeft: 4,
  },
  meetingTime: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.text.primary,
    width: 90,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
    marginTop: 10,
  },

  noTasksContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  noTasksText: {
    color: colors.text.tertiary,
    fontSize: 14,
  },
  noDataContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  noDataText: {
    color: colors.text.tertiary,
    fontSize: 14,
    marginBottom: 16,
  },
  addDataButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary + '10', // 10% opacity
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.primary + '30', // 30% opacity
  },
  addButtonIcon: {
    marginRight: 8,
  },
  addButtonText: {
    color: colors.primary,
    fontSize: 16,
    fontWeight: '600',
  },
  attendanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
    paddingVertical: 5,
    paddingHorizontal: 8,
    marginRight: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  attendanceLabel: {
    fontSize: 13,
    fontWeight: '600',
    marginRight: 0,
  },
  attendanceSwitch: {
    marginLeft: 2,
  },
  attendanceLoader: {
    marginLeft: 4,
    transform: [{ scale: 0.8 }]
  },
  debugContainer: {
    backgroundColor: 'rgba(0,0,0,0.7)',
    padding: 10,
    marginTop: 10,
    marginHorizontal: 10,
    borderRadius: 5,
  },
  debugText: {
    color: 'white',
    fontSize: 12,
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
  },
  errorContainer: {
    padding: 20,
    alignItems: 'center',
  },
  errorText: {
    color: 'red',
    fontSize: 14,
  },
  linkItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
  },
  linkContent: {
    flex: 1,
    marginRight: 12,
  },
  linkTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.text.primary,
    marginBottom: 4,
  },
  linkUrl: {
    fontSize: 16,
    color: colors.primary,
    textDecorationLine: 'underline',
  },
  taskItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
  },
  checkboxContainer: {
    marginRight: 12,
    marginTop: 2,
  },
  taskContent: {
    flex: 1,
  },
  taskMainContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    flex: 1,
  },
  taskLeftContent: {
    flex: 1,
    marginRight: 12,
  },
  taskRightContent: {
    alignItems: 'flex-end',
    justifyContent: 'flex-start',
    minWidth: 100,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 2,
  },
  taskDescription: {
    fontSize: 14,
    color: colors.text.secondary,
    marginBottom: 4,
    lineHeight: 18,
  },
  taskRelatedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  relationIcon: {
    marginRight: 6,
  },
  taskRelatedLink: {
    fontSize: 13,
    color: colors.primary,
    flex: 1,
  },
  taskDateTime: {
    fontSize: 12,
    color: colors.text.secondary,
    marginLeft: 4,
  },
  taskDateTimeContainer: {
    flexDirection: 'column',
    gap: 6,
    marginTop: 2,
  },
  dateTimeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 2,
  },
  dateTimeIcon: {
    marginRight: 6,
  },

  viewAllText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '500',
  },
  todayDateText: {
    fontWeight: 'bold',
  },
  viewAllHeaderButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  viewAllHeaderText: {
    color: colors.primary,
    fontSize: 14,
    marginRight: 6,
  },
  viewAllArrow: {
    marginLeft: 2,
  },
  arrowContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.primary + '15', // 15% opacity
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 4,
  },
  monthlyViewButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  moreButton: {
    padding: 8,
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  bottomSheet: {
    backgroundColor: colors.background.primary,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  bottomSheetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  bottomSheetTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
  },
  closeButton: {
    padding: 4,
  },
  menuItems: {
    padding: 16,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  menuItemText: {
    fontSize: 16,
    color: colors.text.primary,
    marginLeft: 16,
  },
});

//AsyncStorage.getItem(TOKEN_STORAGE_KEY);