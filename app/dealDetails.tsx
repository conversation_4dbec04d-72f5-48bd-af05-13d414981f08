import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Switch,
  ActivityIndicator,
  StatusBar,
  Modal,
  Alert,
  Platform,
  Linking,
  LogBox,
  RefreshControl,
} from 'react-native';
import { SafeAreaView, Edge } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { showSnackbar } from './ui/utils';
import { readUserScope, validateForEditAndDeleteActs } from './ui/utils';

// Define interfaces at the top of the file
interface PipelineStage {
  id: number;
  name: string;
  probability: string;
  index: string;
  active: number;
  is_current: boolean;
}
interface DealField {
  id: number;
  field_label: string;
  field_name: string;
  field_type: string;
  required: string | null;
  choices: DropdownChoice[];
  field_value: string | null;
  active?: number;
}
interface DropdownChoice {
  id: number;
  dealfield_id: number;
  custom_option: string;
}
interface ContactInfo {
  id: number;
  name: string;
  mobile_number: string;
}
// Define the response type structure
interface DealDetailsResponseData {
    deals: DealField[];
    tasks: any[];
    sales_account: any;
    contact: ContactInfo | null;
    task_count: number;
    meeting_count: number;
    notes_count: number;
  pipeline: {
    id: number;
    name: string;
  };
  pipeline_stages: PipelineStage[];
  current_stage: PipelineStage | null;
}
interface DealDetailsResponse {
  success: boolean;
  data: DealDetailsResponseData;
  message: string;
}
// Add AccessScope interface
interface AccessScope {
  deal?: {
    create?: boolean | 'global' | 'restricted';
    view?: boolean | 'global' | 'restricted';
    edit?: boolean | 'global' | 'restricted';
    delete?: boolean | 'global' | 'restricted';
  };
  // ... other access scopes ...
}
// Ignore specific LogBox warnings if needed
LogBox.ignoreLogs(['Warning: ...']); // Add specific warnings to ignore
const DealDetails: React.FC = () => {
  const [activeSegment, setActiveSegment] = useState('Deal details');
  const [showEmptyFields, setShowEmptyFields] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [dealDetails, setDealDetails] = useState<DealDetailsResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [accessScope, setAccessScope] = useState<AccessScope | null>(null);
  const [canEdit, setCanEdit] = useState(false);
  const [canDelete, setCanDelete] = useState(false);

  const [showStageDropdown, setShowStageDropdown] = useState(false);
  const router = useRouter();
  const params = useLocalSearchParams();
  const dealId = params.dealId as string;
  // Add logging to debug parameters
  console.log('DealDetails - All params:', JSON.stringify(params));
  console.log('DealDetails - dealId:', dealId);
  // Check if dealId exists on component mount
  useEffect(() => {
    if (!dealId) {
      console.error('No dealId found in navigation params:', params);
      Alert.alert(
        'Navigation Error',
        'No deal ID was provided. Please go back and try again.',
        [{ text: 'Go Back', onPress: () => router.back() }]
      );
    }
  }, []);
  // Fetch deal details from API - using useFocusEffect to refresh on navigation
  const fetchDealDetails = useCallback(async () => {
    if (!dealId) {
      console.error('No dealId provided in navigation params');
      setError('No deal ID provided. Please go back and try again.');
      setIsLoading(false);
      return;
    }
    console.log('Fetching deal details for ID:', dealId);
    setIsLoading(true);
    setError(null); // Clear any previous errors
    try {
      // Use the dynamic deal ID from the navigation params
      const endpointUrl = `${API_ENDPOINTS.DEALS}/${dealId}`;
      console.log('Making API request to:', endpointUrl);
      const response = await apiService.get<DealDetailsResponse>(endpointUrl);
      console.log('API response received:', response.data.success);
      if (response.data.success) {
        setDealDetails(response.data);
      } else {
        setError(response.data.message || 'Failed to load deal details');
      }
    } catch (err) {
      console.error('Error fetching deal details:', err);
      setError('Failed to load deal details. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [dealId]);

  // Use useFocusEffect to refresh data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      fetchDealDetails();
    }, [fetchDealDetails])
  );

  // Manual refresh function for pull-to-refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchDealDetails();
    setRefreshing(false);
  }, [fetchDealDetails]);
  // Log for debugging
  console.log('Rendering DealDetails component');
  // Define handlers with console logging for debugging
  const handleBack = () => {
    console.log('Back button pressed');
    router.back();
  };
  const handleDelete = () => {
    if (!canDelete) {
      showSnackbar('You do not have permission to delete this deal');
      return;
    }
    console.log('Delete button pressed');
    Alert.alert(
      'Delete Deal',
      'Are you sure you want to delete this deal? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: handleDeleteDeal,
        },
      ]
    );
  };
  const handleSegmentPress = (segment: string) => {
    setActiveSegment(segment);
  };

  const handleDeleteDeal = async () => {
    try {
      setIsLoading(true);
      const response = await apiService.delete(`${API_ENDPOINTS.DEALS}/${dealId}`);
      if (response.status === 200 || response.status === 204) {
        showSnackbar('Deal deleted successfully');
        router.back();
      } else {
        throw new Error(response.data?.message || 'Failed to delete deal');
      }
    } catch (error) {
      console.error('Error deleting deal:', error);
      showSnackbar('Failed to delete deal. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  // Get deal name from details if available
  const getDealName = (): string => {
    if (!dealDetails?.data?.deals) return 'Deal Details';
    const nameField = dealDetails.data.deals.find(field => field.field_name === 'name');
    return nameField?.field_value || 'Deal Details';
  };
  // Filter fields based on search query and empty fields toggle
  const filterFields = (fields: DealField[]): DealField[] => {
    if (!fields) return [];
    return fields.filter(field => {
      // Skip fields that should be hidden
      if (field.field_name === 'sales_account_id' && field.active === 0) return false;

      // Filter by search query if provided
      const matchesSearch = searchQuery
        ? field.field_label.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (field.field_value && field.field_value.toLowerCase().includes(searchQuery.toLowerCase()))
        : true;

      // Filter empty fields based on toggle
      const hasValue = field.field_value !== null &&
                      field.field_value !== '' &&
                      field.field_value !== undefined &&
                      field.field_value.toString().trim() !== '';
      const showField = showEmptyFields || hasValue;

      return matchesSearch && showField;
    });
  };
  const getDealValue = (): string => {
    if (!dealDetails?.data?.deals) return '$0.00';
    if (!dealDetails?.data?.deals) return '₹0.00';
    const amountField = dealDetails.data.deals.find(field => field.field_name === 'amount');
    const currencyField = dealDetails.data.deals.find(field => field.field_name === 'currency_id');
    const amount = amountField?.field_value || '0';
    const currency = currencyField?.field_value || 'INR';
    const currencySymbol = currency === 'USD' ? '$' : '₹';
    return `${currencySymbol} ${amount}`;
  };
  const getCurrentStage = (): { name: string; id: number | null } => {
    if (!dealDetails?.data) return { name: 'Select Stage', id: null };
    
    // If there's a current stage, use it
    if (dealDetails.data.current_stage) {
      return { 
        name: dealDetails.data.current_stage.name, 
        id: dealDetails.data.current_stage.id 
      };
    }
    
    // If no current stage but we have pipeline stages, use the first one
    if (dealDetails.data.pipeline_stages?.length > 0) {
      const firstStage = dealDetails.data.pipeline_stages[0];
      return { 
        name: firstStage.name, 
        id: firstStage.id 
      };
    }
    
    return { name: 'Select Stage', id: null };
  };
  // Add handleStageChange function
  const handleStageChange = async (stageId: number) => {
    try {
      setIsLoading(true);
      
      // Call the new stage update API endpoint
      const response = await apiService.put(`${API_ENDPOINTS.DEALS}/stage/update`, {
        deal_id: dealId,
        stage_id: stageId.toString()
      });
      
      if (response.data.status) {
        // Refresh deal details to get updated stage
        await fetchDealDetails();

        // Find the selected stage name for the snackbar message
        const stages = dealDetails?.data?.pipeline_stages || [];
        const selectedStage = stages.find((stage: PipelineStage) => stage.id === stageId);
        showSnackbar(`Deal stage updated to ${selectedStage?.name || 'new stage'}`);
      } else {
        throw new Error(response.data.message || 'Failed to update deal stage');
      }
    } catch (error) {
      console.error('Error updating deal stage:', error);
      showSnackbar('Failed to update deal stage');
    } finally {
      setIsLoading(false);
      setShowStageDropdown(false);
    }
  };
  const renderDealHeader = () => {
    const dealValue = getDealValue();
    const { name: stageName } = getCurrentStage();
    const stages = dealDetails?.data?.pipeline_stages || [];
    return (
      <View style={styles.dealHeaderContainer}>
        <ScrollView 
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.dealHeaderScrollContent}
        >
          {/* Deal Value Card */}
          <View style={styles.dealCard}>
            <Text style={styles.dealCardLabel}>Deal value</Text>
            <Text style={styles.dealValueAmount}>{dealValue}</Text>
          </View>
          
          {/* Deal Stage Card */}
          <View style={styles.dealCard}>
            <Text style={styles.dealCardLabel}>Deal stage</Text>
            <TouchableOpacity
              style={styles.stageDropdownButton}
              onPress={() => setShowStageDropdown(true)}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator size="small" color="#0F96BB" />
              ) : (
                <>
                  <Text style={styles.stageDropdownText} numberOfLines={1}>
                    {stageName}
                  </Text>
                  <Ionicons name="chevron-down" size={20} color="#333" />
                </>
              )}
            </TouchableOpacity>
          </View>
        </ScrollView>
        {/* Stage Dropdown Modal */}
        <Modal
          visible={showStageDropdown}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowStageDropdown(false)}
        >
          <TouchableOpacity
            style={styles.modalOverlay}
            activeOpacity={1}
            onPress={() => setShowStageDropdown(false)}
          >
            <View style={styles.stageDropdownContainer}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Select Stage</Text>
                <TouchableOpacity 
                  onPress={() => setShowStageDropdown(false)}
                  style={styles.closeButton}
                >
                  <Ionicons name="close" size={24} color="#666" />
                </TouchableOpacity>
              </View>
              
              <ScrollView style={styles.stageListContainer}>
                {stages.map((stage) => {
                  const isCurrentStage = dealDetails?.data?.current_stage?.id === stage.id;
                  return (
                    <TouchableOpacity
                      key={stage.id}
                      style={[
                        styles.stageDropdownItem,
                        isCurrentStage && styles.stageDropdownItemSelected
                      ]}
                      onPress={() => handleStageChange(stage.id)}
                      disabled={isLoading}
                    >
                      <Text style={[
                        styles.stageDropdownItemText,
                        isCurrentStage && styles.stageDropdownItemTextSelected
                      ]}>
                        {stage.name}
                      </Text>
                      <Text style={[
                        styles.stageProbability,
                        isCurrentStage && styles.stageProbabilitySelected
                      ]}>
                        {stage.probability}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
              </ScrollView>
            </View>
          </TouchableOpacity>
        </Modal>
      </View>
    );
  };
  const renderDealDetails = () => {
    if (!dealDetails?.data?.deals) return null;
    // Filter out deal value and stage fields from the details section
    const baseFilteredDeals = dealDetails.data.deals.filter(field =>
      !['amount', 'currency_id', 'deal_stage_id'].includes(field.field_name) &&
      field.active !== 0
    );

    // Apply search and empty field filters
    const filteredDeals = filterFields(baseFilteredDeals);

    return (
      <ScrollView
        style={styles.contentContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={['#0F96BB']}
            tintColor="#0F96BB"
          />
        }
      >
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color="#888" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search fields"
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity
              onPress={() => setSearchQuery('')}
              style={styles.clearSearchButton}
            >
              <Ionicons name="close-circle" size={20} color="#888" />
            </TouchableOpacity>
          )}
        </View>
        <View style={styles.toggleContainer}>
          <Text style={styles.toggleLabel}>Show empty fields</Text>
          <Switch
            value={showEmptyFields}
            onValueChange={setShowEmptyFields}
            trackColor={{ false: '#f0f0f0', true: '#d1e0ff' }}
            thumbColor={showEmptyFields ? '#0F6EBB' : '#fff'}
          />
        </View>
        {/* Basic Information */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionHeader}>BASIC INFORMATION</Text>
          {filteredDeals.length === 0 ? (
            <View style={styles.noResultsContainer}>
              <Text style={styles.noResultsText}>
                {searchQuery ? 'No fields match your search' : 'No fields to display'}
              </Text>
            </View>
          ) : (
            filteredDeals.map(field => renderField(field))
          )}
        </View>
        {/* Contact Information */}
        {dealDetails.data.contact && (
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionHeader}>RELATED CONTACT</Text>
            <TouchableOpacity
              style={styles.contactCard}
              onPress={() => {
                if (dealDetails.data.contact?.id) {
                  router.push({
                    pathname: '/contactDetail' as any,
                    params: { id: dealDetails.data.contact.id.toString() }
                  });
                }
              }}
            >
              <View style={styles.contactInitials}>
                <Text style={styles.initialsText}>
                  {dealDetails.data.contact.name ? 
                    dealDetails.data.contact.name.substring(0, 2).toUpperCase() : 
                    'NA'}
                </Text>
              </View>
              <View style={styles.contactDetails}>
                <Text style={styles.contactName}>
                  {dealDetails.data.contact.name || 'No Name'}
                </Text>
                <Text style={styles.contactLink}>View contact</Text>
              </View>
              <Ionicons name="chevron-forward" size={24} color="#888" />
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    );
  };
  const renderField = (field: DealField) => {
    // Format value based on field type
    let displayValue = field.field_value || 'Not provided';
    // Handle dropdown fields
    const isDropdown = ['dropdown', 'lookup'].includes(field.field_type.toLowerCase());
    if (isDropdown && field.choices && field.field_value) {
      const choice = field.choices.find(c => c.id.toString() === field.field_value?.toString());
      if (choice) {
        displayValue = choice.custom_option;
      }
    }
    // Format currency display
    if (field.field_name === 'amount' && field.field_value) {
      const currencyField = dealDetails?.data.deals.find(f => f.field_name === 'currency_id');
      const currencyValue = currencyField?.field_value;
      const currencySymbol = currencyValue === 'USD' ? '$' : currencyValue || '₹';
      displayValue = `${currencySymbol} ${field.field_value}`;
    }
    return (
      <View key={field.id} style={styles.fieldRow}>
        <Text style={styles.fieldLabel}>{field.field_label}</Text>
        <Text style={[
          styles.fieldValue,
          !field.field_value && { color: '#aaa' }
        ]}>
          {displayValue}
        </Text>
      </View>
    );
  };
  const renderActivities = () => {
    // Get task and meeting counts from API response
    const taskCount = dealDetails?.data?.task_count || 0;
    const meetingCount = dealDetails?.data?.meeting_count || 0;
    const notesCount = dealDetails?.data?.notes_count || 0;
    const handleNotesPress = () => {
      // For now, just log that notes was pressed since we don't have a notes screen yet
      console.log('Notes pressed for deal ID:', dealId);
      //Alert.alert('Feature Coming Soon', 'Notes functionality will be available in a future update.');
      router.push({
                pathname: '/ui/screens/NotesListScreen' as any,
                params: { id: dealId , type: "deals"}
               })
    };
    const navigateToTasks = () => {
      console.log('Navigating to tasks list for deal ID:', dealId);
      router.push({
        pathname: '/TaskListScreen' as any,
        params: { deal_id: dealId }
      });
    };
    const navigateToMeetings = () => {
      console.log('Navigating to meetings list for deal ID:', dealId);
      router.push({
        pathname: '/meetings/meetingList' as any,
        params: { deal_id: dealId }
      });
    };
    const handleCall = async () => {
      console.log('handleCall', dealDetails);
      if (!dealDetails?.data?.contact) {
        console.log('No contact details available');
        showSnackbar('No contact details available');
        return;
      }
      
      // Find phone number from account fields
      console.log('Accounts fields:', dealDetails.data.contact);
     
      
      console.log('Found phone field:', dealDetails.data.contact.mobile_number);
      const phoneNumber = dealDetails.data.contact.mobile_number;
          
      if (!phoneNumber) {
        console.log('No phone number found in account');
        showSnackbar('No phone number available for this contact.');
        return;
      }
      
      console.log('Found phone number:', phoneNumber);
      
      // Format the number - keep only digits and plus sign at the start
      let formattedNumber = phoneNumber.replace(/[^\d+]/g, '');
      if (!formattedNumber.startsWith('+')) {
        formattedNumber = formattedNumber.replace(/^0+/, ''); // Remove leading zeros
      }
      
      console.log('Formatted phone number:', formattedNumber);
      
      const phoneUrl = Platform.select({
        ios: `telprompt:${formattedNumber}`,
        android: `tel:${formattedNumber}`,
        default: `tel:${formattedNumber}`
      });
  
      try {
        const supported = await Linking.canOpenURL(phoneUrl);
        console.log('Phone URL:', phoneUrl, 'Supported:', supported);
       // if (supported) {
          await Linking.openURL(phoneUrl);
        //} else {
          //showSnackbar('Phone calls are not supported on this device');
        //}
      } catch (error) {
        console.error('Phone call error:', error);
        Alert.alert('Error', 'Failed to make phone call');
      }
    };
  
    return (
      <ScrollView
        style={styles.contentContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={['#0F96BB']}
            tintColor="#0F96BB"
          />
        }
      >
        {/* Quick Action Buttons */}
        <View style={styles.quickActionsRow}>
          <View style={styles.quickActionItem}>
            <TouchableOpacity style={styles.actionIconContainer} onPress={handleCall}>
              <Ionicons name="call-outline" size={24} color="#000" />
            </TouchableOpacity>
            <Text style={styles.actionText}>Call phone</Text>
          </View>
          <View style={styles.quickActionItem}>
            <TouchableOpacity style={styles.actionIconContainer} onPress={() =>
               router.push({
                pathname: '/ui/helpers/AddNotesPage' as any,
                params: { id: dealId , name: "deals"}
               })}
               >
              <Ionicons name="mic-outline" size={24} color="#000" />
            </TouchableOpacity>
            <Text style={styles.actionText}>Add note</Text>
          </View>
        </View>
        {/* Activity List */}
        <View style={styles.listContainer}>
          <TouchableOpacity
            style={[styles.listItem, (notesCount === 0) && styles.disabledItem]}
            onPress={() => {
              //if (notesCount > 0) {
                handleNotesPress();
              //} else {
                //showSnackbar('No notes available for this deal');
              //}
            }}
          >
            <Ionicons name="document-text-outline" size={22} color="#333" />
            <Text style={styles.listItemText}>Notes ({notesCount})</Text>
            <Ionicons name="chevron-forward" size={22} color="#888" style={styles.chevron} />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.listItem, (taskCount === 0) && styles.disabledItem]}
            onPress={() => {
              //if (taskCount > 0) {
                navigateToTasks();
              //} else {
                //showSnackbar('No tasks available for this deal');
              //}
            }}
          >
            <Ionicons name="calendar-outline" size={22} color="#333" />
            <Text style={styles.listItemText}>Tasks ({taskCount})</Text>
            <Ionicons name="chevron-forward" size={22} color="#888" style={styles.chevron} />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.listItem, (meetingCount === 0) && styles.disabledItem]}
            onPress={() => {
              //if (meetingCount > 0) {
                navigateToMeetings();
              //} else {
                //showSnackbar('No meetings available for this deal');
              //}
            }}
          >
           
            <Ionicons name="people-outline" size={22} color="#333" />
            <Text style={styles.listItemText}>Meetings ({meetingCount})</Text>
            <Ionicons name="chevron-forward" size={22} color="#888" style={styles.chevron} />
          </TouchableOpacity>
        </View>
      </ScrollView>
    );
  };
  const renderMore = () => {
    // Get contact count - if there's a contact, count is 1, otherwise 0
    const contactCount = dealDetails?.data?.contact ? 1 : 0;
    
    return (
      <ScrollView
        style={styles.contentContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={['#0F96BB']}
            tintColor="#0F96BB"
          />
        }
      >
        {/* Simplified More tab with additional options */}
        <View style={styles.listContainer}>
          <TouchableOpacity 
            style={styles.listItem}
            onPress={() => {
              router.push({
                pathname: '/RelatedContactsList' as any,
                params: { dealId: dealId }
              });
            }}
          >
            <Ionicons name="people-outline" size={22} color="#333" />
            { /* ({contactCount})*/}
            <Text style={styles.listItemText}>Contacts</Text> 
            <Ionicons name="chevron-forward" size={22} color="#888" style={styles.chevron} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.listItem} onPress={() => {
            router.push({
              pathname: '/files',
              params: { dealId: dealId }
            });
          }}>
            <Ionicons name="document-outline" size={22} color="#333" />
            <Text style={styles.listItemText}>Files</Text>
            <Ionicons name="chevron-forward" size={22} color="#888" style={styles.chevron} />
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.listItem}
            onPress={() => {
              console.log('Navigate to products list for deal ID:', dealId);
              router.push({
                pathname: '/DealsProductList' as any,
                params: { dealId: dealId }
              });
            }}
          >
            <Ionicons name="cube-outline" size={22} color="#333" />
            <Text style={styles.listItemText}>Products</Text>
            <Ionicons name="chevron-forward" size={22} color="#888" style={styles.chevron} />
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.listItem}
            onPress={() => {
              console.log('Navigate to quotes list for deal ID:', dealId);
              router.push({
                pathname: '/DealsQuoteList' as any,
                params: { dealId: dealId }
              });
            }}
          >
            <Ionicons name="receipt-outline" size={22} color="#333" />
            <Text style={styles.listItemText}>Quotes</Text>
            <Ionicons name="chevron-forward" size={22} color="#888" style={styles.chevron} />
          </TouchableOpacity>
        </View>
      </ScrollView>
    );
  };
  const renderContent = () => {
    switch (activeSegment) {
      case 'Deal details':
        return renderDealDetails();
      case 'Activities':
        return renderActivities();
      case 'More':
        return renderMore();
      default:
        return renderDealDetails();
    }
  };
  // Add useEffect for access scope
  useEffect(() => {
    const fetchAccessScope = async () => {
      try {
        const scope = await readUserScope();
        console.log("Deal access scope:", JSON.stringify(scope));
        setAccessScope(scope);
      } catch (error) {
        console.error("Error fetching access scope:", error);
        setAccessScope(null);
      }
    };
    fetchAccessScope();
  }, []);

  // Add refreshAccessScope function
  const refreshAccessScope = async (type: string) => {
    console.log("refreshAccessScope deal:", JSON.stringify(accessScope));
    const ownerId = dealDetails?.data?.deals?.find(field => field.field_name === 'owner_id')?.field_value;
    const territoryId = dealDetails?.data?.deals?.find(field => field.field_name === 'territory_id')?.field_value;
    console.log("ownerId:", ownerId, "territoryId:", territoryId);

    const permission = type === 'delete' ? accessScope?.deal?.delete?.toString() : accessScope?.deal?.edit?.toString();
    console.log(`${type} permission:`, permission);

    const canAccess = await validateForEditAndDeleteActs(territoryId!, permission!, ownerId!);
    console.log(`can${type}:`, canAccess);

    if (type === 'delete') {
      setCanDelete(canAccess);
    } else {
      setCanEdit(canAccess);
    }
  };

  // Add useEffect to check permissions when deal details or access scope changes
  useEffect(() => {
    if (accessScope && dealDetails?.data?.deals) {
      refreshAccessScope('delete');
      refreshAccessScope('edit');
    }
  }, [accessScope, dealDetails?.data?.deals]);

  // Update handleEdit to check permissions
  const handleEdit = () => {
    if (!canEdit) {
      showSnackbar('You do not have permission to edit this deal');
      return;
    }
    // Navigate to edit page
    console.log('Navigate to edit deal page ');//+JSON.stringify(dealDetails?.data));
    router.push({
      pathname: '/EditDeal' as any,
      params: { dealDetails: dealDetails?.data ,dealId : dealId }
    });
  };

  if (isLoading) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '#f5f5f5' }}>
        <StatusBar barStyle="light-content" backgroundColor="#0F96BB" />
        {/* Custom Header instead of Appbar.Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Deal Details</Text>
          <View style={styles.headerRight}></View>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0F96BB" />
          <Text style={styles.loadingText}>Loading Opportunity details...</Text>
        </View>
      </SafeAreaView>
    );
  }
  if (error) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '#f5f5f5' }}>
        <StatusBar barStyle="light-content" backgroundColor="#0F96BB" />
        {/* Custom Header instead of Appbar.Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Deal Details</Text>
          <View style={styles.headerRight}></View>
        </View>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => {
              setError(null);
              fetchDealDetails();
            }}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }
  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#0F96BB' }} edges={['top']}>
      <StatusBar barStyle="light-content" backgroundColor="#0F96BB" />
      <Stack.Screen options={{ headerShown: false }} />
      
      {/* Custom Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{getDealName()}</Text>
        </View>
        <View style={styles.headerActions}>
          {canEdit && (
          <TouchableOpacity
            style={styles.headerButton}
            onPress={handleEdit}
            disabled={isLoading}
          >
            <Ionicons name="pencil" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          )}
          {canDelete && (
            <TouchableOpacity
              style={styles.optionsButton}
              onPress={handleDelete}
            disabled={isLoading}
          >
            <Ionicons name="trash-outline" size={24} color={isLoading ? "#CCCCCC" : "#FFFFFF"} />
          </TouchableOpacity>
          )}
        </View>
      </View>
      
      <View style={styles.container}>
        {/* Deal Header (Value and Stage) */}
        {renderDealHeader()}
      
      {/* Segments */}
      <View style={styles.segmentContainer}>
        {['Deal details', 'Activities', 'More'].map((segment) => {
          const isActive = activeSegment === segment;
          return (
            <TouchableOpacity
              key={segment}
              style={[
                styles.segmentButton,
                isActive && styles.activeSegment,
              ]}
              onPress={() => handleSegmentPress(segment)}
              activeOpacity={0.7}
            >
              <Text
                style={[
                  styles.segmentText,
                  isActive && styles.activeSegmentText,
                ]}
              >
                {segment}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
      
      {/* Content */}
      <View style={{ flex: 1 }}>
        {renderContent()}
      </View>

      </View>
    </SafeAreaView>
  );
};
const styles = StyleSheet.create({
  safeAreaContainer: {
    flex: 1,
    backgroundColor: '#0F96BB',
  },
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    borderTopLeftRadius: Platform.OS === 'ios' ? 15 : 0,
    borderTopRightRadius: Platform.OS === 'ios' ? 15 : 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#0F96BB',
    paddingVertical: 10,
    paddingHorizontal: 16,
    zIndex: 10,
    elevation: 4,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'left',
    flex: 1,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    padding: 8,
    marginRight: 8,
  },
  optionsButton: {
    padding: 8,
  },
  headerRight: {
    width: 24, // To balance the header layout when no actions are displayed
  },
  segmentContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
  },
  segmentButton: {
    flex: 1,
    paddingVertical: 15,
    alignItems: 'center',
    borderBottomWidth: 3,
    borderBottomColor: 'transparent',
  },
  segmentText: {
    fontSize: 15,
    color: '#999',
  },
  activeSegment: {
    borderBottomWidth: 3,
    borderBottomColor: '#0F96BB',
    backgroundColor: 'rgba(15, 150, 187, 0.05)', // Light tint of the main color
  },
  activeSegmentText: {
    color: '#0F96BB',
    fontWeight: 'bold',
    fontSize: 16,
  },
  contentContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 8,
    margin: 15,
    paddingHorizontal: 10,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 10,
    fontSize: 15,
  },
  toggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'white',
    padding: 15,
    marginHorizontal: 15,
    marginBottom: 15,
    borderRadius: 8,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  toggleLabel: {
    fontSize: 15,
    color: '#333',
  },
  sectionContainer: {
    backgroundColor: 'white',
    marginHorizontal: 15,
    marginBottom: 15,
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  sectionHeader: {
    fontSize: 12,
    fontWeight: '600',
    color: '#0F96BB',
    backgroundColor: '#f8f8f8',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
  },
  fieldRow: {
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  fieldLabel: {
    fontSize: 13,
    color: '#888',
    marginBottom: 4,
  },
  fieldValue: {
    fontSize: 15,
    color: '#333',
  },
  contactCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
  },
  contactInitials: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#0F96BB',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  initialsText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  contactDetails: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    color: '#333',
    marginBottom: 4,
  },
  contactLink: {
    fontSize: 14,
    color: '#0F96BB',
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#888',
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#d32f2f',
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#0F96BB',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 4,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'flex-end',
  } as const,

  listContainer: {
    backgroundColor: 'white',
  },
  disabledItem: {
    opacity: 0.7,
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  listItemText: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    marginLeft: 15,
  },
  chevron: {
    marginLeft: 'auto',
  },
  quickActionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: 'white',
    paddingVertical: 25,
    marginBottom: 15,
  },
  quickActionItem: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  actionIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#f0f0f0',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 10,
  },
  actionText: {
    fontSize: 14,
    color: '#333',
    textAlign: 'center',
  },
  dealHeaderContainer: {
    backgroundColor: 'white',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
  } as const,
  
  dealHeaderScrollContent: {
    paddingHorizontal: 15,
    gap: 15,
    flexDirection: 'row',
  } as const,
  
  dealCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 15,
    minWidth: 120,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  } as const,
  
  dealCardLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  } as const,
  
  dealValueAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  } as const,
  
  stageDropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'white',
    paddingVertical: 4,
    paddingHorizontal: 0,
    borderRadius: 8,
    minHeight: 32,
  } as const,
  
  stageDropdownText: {
    fontSize: 16,
    color: '#333',
    fontWeight: '600',
    flex: 1,
    marginRight: 8,
  } as const,
  
  stageDropdownItemSelected: {
    backgroundColor: '#e8f4f8',
  } as const,
  
  stageDropdownItemTextSelected: {
    color: '#0F96BB',
    fontWeight: '600',
  } as const,
  
  stageProbabilitySelected: {
    color: '#0F96BB',
  } as const,
  
  stageDropdownContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginHorizontal: 20,
    marginTop: 'auto',
    marginBottom: 'auto',
    maxHeight: '80%',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    overflow: 'hidden',
  } as const,
  
  stageDropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  } as const,
  
  stageProbability: {
    fontSize: 14,
    color: '#666',
  } as const,
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
  } as const,
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  } as const,
  closeButton: {
    padding: 4,
  } as const,
  stageListContainer: {
    maxHeight: '80%',
  } as const,
  stageDropdownItemText: {
    fontSize: 16,
    color: '#333',
  } as const,
  noResultsContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  } as const,
  noResultsText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  } as const,
  clearSearchButton: {
    padding: 4,
    marginLeft: 8,
  } as const,
});
export default DealDetails; 