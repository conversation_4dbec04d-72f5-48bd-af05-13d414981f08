import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  ScrollView,
  ActivityIndicator,
  Alert,
  Platform,
  KeyboardAvoidingView,
  Modal,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { DealField, DealFieldsResponse, DealFieldChoice } from './models/DealField';
import { useColors } from '@/hooks/useThemeColor';
import DateTimePicker from '@react-native-community/datetimepicker';

// Update the DealFormData type to support both string and number values
type DealFormData = {
  [key: string]: string | number | null;
};

const UpdateDeals = () => {
  const [fields, setFields] = useState<DealField[]>([]);
  const [formData, setFormData] = useState<DealFormData>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [currentDropdownField, setCurrentDropdownField] = useState<DealField | null>(null);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [currentDateField, setCurrentDateField] = useState<string>('');
  const [tempDate, setTempDate] = useState(new Date());
  const [dateError, setDateError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  const router = useRouter();
  const colors = useColors();
  const params = useLocalSearchParams();
  const dealId = params.dealId as string;

  // Add isDateValid function
  const isDateValid = (selectedDate: Date): boolean => {
    const now = new Date();
    now.setHours(0, 0, 0, 0);
    const selected = new Date(selectedDate);
    selected.setHours(0, 0, 0, 0);
    return selected >= now;
  };

  // First, fetch the current deal details
  useEffect(() => {
    if (!dealId) {
      setError('No deal ID provided.');
      setLoading(false);
      return;
    }
    
    fetchDealDetails();
  }, [dealId]);

  // Fetch current deal details
  const fetchDealDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('Fetching deal details for ID:', dealId);
      const endpointUrl = `${API_ENDPOINTS.DEALS}/${dealId}`;
      const response = await apiService.get(endpointUrl);
      
      if (!response.data?.success) {
        throw new Error(response.data?.message || 'Failed to fetch deal details');
      }
      
      console.log('Deal details fetched successfully');
      // After fetching the deal details, fetch the deal fields
      await fetchDealFields(response.data.data.deals);
      
    } catch (err) {
      console.error('Error fetching deal details:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
      setLoading(false);
    }
  };

  // Fetch deal fields from API and pre-populate with existing values
  const fetchDealFields = async (existingFields: DealField[] = []) => {
    try {
      console.log('Fetching all deal fields...');
      const response = await apiService.get<DealFieldsResponse>(API_ENDPOINTS.DEAL_FIELDS);
      
      if (!response.data || !response.data.dealFields) {
        throw new Error('Failed to fetch deal fields');
      }

      // Create a map of existing fields for easier lookup
      const existingFieldsMap = new Map();
      existingFields.forEach(field => {
        existingFieldsMap.set(field.field_name, field);
      });

      // Merge all available fields with existing values
      const mergedFields = response.data.dealFields.map(field => {
        const existingField = existingFieldsMap.get(field.field_name);
        return {
          ...field,
          field_value: existingField ? existingField.field_value : field.field_value,
          // Preserve the active state from the API field, not the existing field
        };
      });

      // Sort fields by order property
      const sortedFields = [...mergedFields].sort((a, b) => {
        if (a.order === null) return 1;
        if (b.order === null) return -1;
        return a.order - b.order;
      });

      console.log(`Fetched ${sortedFields.length} deal fields`);
      setFields(sortedFields);
      
      // Initialize form data with existing values
      const initialFormData: DealFormData = {};
      sortedFields.forEach(field => {
        // Handle different field types appropriately
        if (field.field_type.toLowerCase() === 'dropdown' || field.field_type.toLowerCase() === 'lookup') {
          // For dropdown/lookup fields, set both the display value and the ID
          if (field.field_value) {
            const choice = field.choices.find(c => c.id.toString() === field.field_value);
            if (choice) {
              initialFormData[field.field_name] = choice.custom_option;
              initialFormData[`${field.field_name}_id`] = field.field_value;
            } else {
              initialFormData[field.field_name] = null;
              initialFormData[`${field.field_name}_id`] = null;
            }
          } else {
            initialFormData[field.field_name] = null;
            initialFormData[`${field.field_name}_id`] = null;
          }
        } else {
          // For other field types, just set the value directly
          initialFormData[field.field_name] = field.field_value;
        }
      });
      
      setFormData(initialFormData);
    } catch (err) {
      console.error('Error setting up deal fields:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Handle text input changes
  const handleInputChange = (fieldName: string, value: string) => {
    setFormData(prevData => ({
      ...prevData,
      [fieldName]: value
    }));
  };

  // Handle dropdown selection
  const handleDropdownSelect = (field: DealField, choice: DealFieldChoice) => {
    setFormData(prevData => ({
      ...prevData,
      [`${field.field_name}_id`]: choice.id,
      [field.field_name]: choice.custom_option
    }));
    setShowDropdown(false);
  };

  // Handle date selection
  const onDateChange = (event: any, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      // On Android, the picker closes immediately after selection
      setShowDatePicker(false);
      
      // Only update if a date was actually selected (not cancelled)
      if (event.type !== 'dismissed' && selectedDate) {
        if (isDateValid(selectedDate)) {
          handleInputChange(currentDateField, selectedDate.toISOString().split('T')[0]);
          setDateError(null);
        } else {
          setDateError("Please select a future date");
          Alert.alert("Invalid Date", "Please select a future date");
        }
      }
    } else {
      // On iOS, just update the temp date (user will press Done to confirm)
      if (selectedDate) {
        setTempDate(selectedDate);
      }
    }
  };

  const handleDateConfirm = () => {
    if (isDateValid(tempDate)) {
      handleInputChange(currentDateField, tempDate.toISOString().split('T')[0]);
      setShowDatePicker(false);
      setDateError(null);
    } else {
      setDateError("Please select a future date");
      Alert.alert("Invalid Date", "Please select a future date");
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      // Validate required fields
      const requiredFields = fields.filter(field => field.required === 'on');
      const missingFields = requiredFields.filter(field => !formData[field.field_name]);
      
      if (missingFields.length > 0) {
        Alert.alert('Validation Error', `Please fill in the following required fields: ${missingFields.map(f => f.field_label).join(', ')}`);
        return;
      }

      setSubmitting(true);

      // Prepare data for submission - include all fields, even inactive ones
      const deal = fields.map(field => {
        const isDropdownOrLookup = ['dropdown', 'lookup'].includes(field.field_type.toLowerCase());
        return {
          ...field,
          field_value: isDropdownOrLookup ? formData[`${field.field_name}_id`] || null : formData[field.field_name] || null
        };
      });

      const requestData = {
        deal: deal
      };

      console.log('Updating deal data:', requestData);

      // Use PUT request to update the deal
      const response = await apiService.put(`${API_ENDPOINTS.DEALS}/${dealId}`, requestData);
      
      // Log the entire response for debugging
      console.log('API Response:', JSON.stringify(response.data, null, 2));

      if (response.data?.success === true) {
        // Use router.back() instead of replace to avoid stacking pages
        router.back();
        return;
      }
      
      // If we get here, it means there was an error
      throw new Error(response.data?.message || 'Failed to update deal');
      
    } catch (err) {
      console.error('Error updating deal:', err);
      Alert.alert(
        'Error',
        'Failed to update deal. Please try again.'
      );
    } finally {
      setSubmitting(false);
    }
  };

  // Add filtered choices computation
  const filteredChoices = React.useMemo(() => {
    if (!currentDropdownField?.choices) return [];
    if (!searchQuery) return currentDropdownField.choices;
    
    return currentDropdownField.choices.filter(choice => 
      choice.custom_option.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [currentDropdownField?.choices, searchQuery]);

  // Render dropdown options
  const renderDropdownOptions = () => {
    if (!currentDropdownField) return null;
    
    const isLookup = currentDropdownField.field_type.toLowerCase() === 'lookup';
    
    return (
      <Modal
        visible={showDropdown}
        transparent={true}
        animationType="slide"
        onRequestClose={() => {
          setShowDropdown(false);
          setSearchQuery('');
        }}
      >
        <TouchableOpacity 
          style={styles.modalContainer} 
          activeOpacity={1} 
          onPress={() => {
            setShowDropdown(false);
            setSearchQuery('');
          }}
        >
          <View style={styles.dropdownContainer}>
            <View style={styles.dropdownHeader}>
              <Text style={styles.dropdownTitle}>{currentDropdownField.field_label}</Text>
              <TouchableOpacity onPress={() => {
                setShowDropdown(false);
                setSearchQuery('');
              }}>
                <Ionicons name="close" size={24} color={colors.text.primary} />
              </TouchableOpacity>
            </View>

            {isLookup && (
              <View style={styles.searchContainer}>
                <Ionicons name="search" size={20} color={colors.text.secondary} style={styles.searchIcon} />
                <TextInput
                  style={styles.searchInput}
                  placeholder="Search..."
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                  autoCapitalize="none"
                />
              </View>
            )}
            
            <FlatList
              data={filteredChoices}
              keyExtractor={(item) => item.id.toString()}
              renderItem={({ item }) => (
                <TouchableOpacity 
                  style={styles.dropdownItem}
                  onPress={() => handleDropdownSelect(currentDropdownField, item)}
                >
                  <Text style={[
                    styles.dropdownOptionText,
                    formData[currentDropdownField.field_name] === item.custom_option && styles.selectedOptionText
                  ]}>
                    {item.custom_option}
                  </Text>
                  {formData[currentDropdownField.field_name] === item.custom_option && (
                    <Ionicons name="checkmark" size={20} color={colors.primary} />
                  )}
                </TouchableOpacity>
              )}
              ItemSeparatorComponent={() => <View style={styles.separator} />}
              ListEmptyComponent={() => (
                <View style={styles.emptyContainer}>
                  <Text style={styles.emptyText}>No options available</Text>
                </View>
              )}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    );
  };

  // Render date picker
  const renderDatePicker = () => {
    if (!showDatePicker) return null;
    
    // For Android, render the picker directly (it shows as a dialog)
    if (Platform.OS === 'android') {
      return (
        <DateTimePicker
          testID="dateTimePicker"
          value={tempDate}
          mode="date"
          display="default"
          onChange={onDateChange}
          minimumDate={new Date()}
        />
      );
    }
    
    // For iOS, render in a modal
    return (
      <Modal
        visible={showDatePicker}
        transparent={true}
        animationType="slide"
      >
        <View style={styles.modalContainer}>
          <View style={styles.pickerModalContent}>
            <View style={styles.pickerHeader}>
              <TouchableOpacity 
                onPress={() => setShowDatePicker(false)}
                style={styles.pickerHeaderButton}
              >
                <Text style={styles.pickerHeaderButtonText}>Cancel</Text>
              </TouchableOpacity>
              <Text style={styles.pickerHeaderTitle}>Select Date</Text>
              <TouchableOpacity 
                onPress={handleDateConfirm}
                style={styles.pickerHeaderButton}
              >
                <Text style={[styles.pickerHeaderButtonText, styles.doneButton]}>Done</Text>
              </TouchableOpacity>
            </View>
            <DateTimePicker
              testID="dateTimePicker"
              value={tempDate}
              mode="date"
              display="spinner"
              onChange={onDateChange}
              minimumDate={new Date()}
              style={styles.picker}
            />
          </View>
        </View>
      </Modal>
    );
  };

  // Render form fields based on field type
  const renderFormField = (field: DealField) => {
    const isRequired = field.required === 'on';
    
    switch (field.field_type.toLowerCase()) {
      case 'lookup':
        return (
          <View key={field.id} style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>
              {field.field_label} {isRequired && <Text style={styles.requiredStar}>*</Text>}
            </Text>
            <TouchableOpacity
              style={styles.dropdownField}
              onPress={() => {
                setCurrentDropdownField(field);
                setShowDropdown(true);
              }}
            >
              <Text style={[
                styles.dropdownText,
                !formData[field.field_name] && styles.placeholderText
              ]}>
                {formData[field.field_name] || field.placeholder}
              </Text>
              <Ionicons name="chevron-down" size={20} color={colors.text.secondary} />
            </TouchableOpacity>
          </View>
        );
        
      case 'dropdown':
        return (
          <View key={field.id} style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>
              {field.field_label} {isRequired && <Text style={styles.requiredStar}>*</Text>}
            </Text>
            <TouchableOpacity
              style={styles.dropdownField}
              onPress={() => {
                setCurrentDropdownField(field);
                setShowDropdown(true);
              }}
            >
              <Text style={[
                styles.dropdownText,
                !formData[field.field_name] && styles.placeholderText
              ]}>
                {formData[field.field_name] || field.placeholder}
              </Text>
              <Ionicons name="chevron-down" size={20} color={colors.text.secondary} />
            </TouchableOpacity>
          </View>
        );
        
      case 'text field':
        return (
          <View key={field.id} style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>
              {field.field_label} {isRequired && <Text style={styles.requiredStar}>*</Text>}
            </Text>
            <TextInput
              style={styles.textInput}
              placeholder={field.placeholder}
              value={formData[field.field_name]?.toString() || ''}
              onChangeText={(text) => handleInputChange(field.field_name, text)}
            />
          </View>
        );
        
      case 'number':
        return (
          <View key={field.id} style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>
              {field.field_label} {isRequired && <Text style={styles.requiredStar}>*</Text>}
            </Text>
            <TextInput
              style={styles.textInput}
              placeholder={field.placeholder}
              value={formData[field.field_name]?.toString() || ''}
              onChangeText={(text) => handleInputChange(field.field_name, text)}
              keyboardType="numeric"
            />
          </View>
        );
        
      case 'datepicker':
        return (
          <View key={field.id} style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>
              {field.field_label} {isRequired && <Text style={styles.requiredStar}>*</Text>}
            </Text>
            <TouchableOpacity
              style={[styles.dropdownField, dateError ? styles.errorBorder : null]}
              onPress={() => {
                setCurrentDateField(field.field_name);
                // Set the initial date to the current form value or today
                let initialDate = new Date();
                if (formData[field.field_name]) {
                  try {
                    initialDate = new Date(formData[field.field_name] as string);
                    // If the date is invalid, use today's date
                    if (isNaN(initialDate.getTime())) {
                      initialDate = new Date();
                    }
                  } catch (e) {
                    console.error('Error parsing date:', e);
                    initialDate = new Date();
                  }
                }
                setTempDate(initialDate);
                setShowDatePicker(true);
                setDateError(null); // Clear any previous errors
              }}
            >
              <Text style={[
                styles.dropdownText,
                !formData[field.field_name] && styles.placeholderText,
                dateError && styles.errorMessage
              ]}>
                {formData[field.field_name] || field.placeholder}
              </Text>
              <Ionicons name="calendar-outline" size={20} color={dateError ? "#dc3545" : "#777"} />
            </TouchableOpacity>
            {dateError && <Text style={styles.errorMessage}>{dateError}</Text>}
            {showDatePicker && renderDatePicker()}
          </View>
        );
        
      default:
        return (
          <View key={field.id} style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>
              {field.field_label} {isRequired && <Text style={styles.requiredStar}>*</Text>}
            </Text>
            <TextInput
              style={styles.textInput}
              placeholder={field.placeholder}
              value={formData[field.field_name]?.toString() || ''}
              onChangeText={(text) => handleInputChange(field.field_name, text)}
            />
          </View>
        );
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0F96BB" />
        <Text style={styles.loadingText}>Loading Opportunity data...</Text>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchDealDetails}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color={colors.text.inverse} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Update Deal</Text>
          <View style={{ width: 40 }} />
        </View>
        
        {/* Form Content */}
        <View style={styles.mainContainer}>
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.contentContainer}
            keyboardShouldPersistTaps="handled"
          >
            {fields.map(field => renderFormField(field))}
          </ScrollView>
          
          {/* Fixed Update Deal Button */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.submitButton, submitting && styles.submittingButton]}
              onPress={handleSubmit}
              disabled={submitting}
            >
              {submitting ? (
                <ActivityIndicator color="#fff" size="small" />
              ) : (
                <Text style={styles.submitButtonText}>Update Deal</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
        
        {/* Dropdown Options Modal */}
        {renderDropdownOptions()}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  mainContainer: {
    flex: 1,
    position: 'relative',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#555',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#dc3545',
    marginBottom: 20,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#0F96BB',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#0F96BB',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 80,
  },
  fieldContainer: {
    marginBottom: 20,
  },
  fieldLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    color: '#333',
  },
  requiredStar: {
    color: 'red',
  },
  textInput: {
    backgroundColor: '#fff',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#ddd',
    fontSize: 16,
  },
  dropdownField: {
    backgroundColor: '#fff',
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#ddd',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dropdownText: {
    fontSize: 16,
    color: '#333',
  },
  placeholderText: {
    color: '#999',
  },
  submitButton: {
    backgroundColor: '#0F96BB',
    paddingVertical: 15,
    borderRadius: 5,
    alignItems: 'center',
    width: '100%',
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#f5f5f5',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderTopWidth: 1,
    borderTopColor: '#ddd',
  },
  submittingButton: {
    backgroundColor: '#0F96BB',
    opacity: 0.7,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  dropdownContainer: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    maxHeight: '80%',
  },
  dropdownHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  dropdownTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  dropdownItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  dropdownOptionText: {
    fontSize: 16,
    color: '#333',
  },
  selectedOptionText: {
    color: '#0F96BB',
    fontWeight: '500',
  },
  separator: {
    height: 1,
    backgroundColor: '#eee',
  },
  pickerModalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: 'auto',
  },
  pickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#f8f9fa',
  },
  pickerHeaderButton: {
    padding: 8,
  },
  pickerHeaderButtonText: {
    fontSize: 16,
    color: '#0077B6',
  },
  doneButton: {
    fontWeight: '600',
  },
  pickerHeaderTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  picker: {
    height: 200,
    backgroundColor: '#fff',
  },
  errorBorder: {
    borderWidth: 1,
    borderColor: '#dc3545',
  },
  errorMessage: {
    color: '#dc3545',
    fontSize: 12,
    marginTop: 4,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#fff',
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
    color: '#333',
  },
  emptyContainer: {
    padding: 16,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
  },
});

export default UpdateDeals; 