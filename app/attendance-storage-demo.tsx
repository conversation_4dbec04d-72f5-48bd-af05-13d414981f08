/**
 * Attendance Storage Demo Page
 * Demonstrates the attendance storage functionality
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  RefreshControl
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useColors } from '../hooks/useThemeColor';
import { attendanceStorageService, StoredAttendanceData } from '../services/AttendanceStorageService';
import AttendanceStatusCard from '../components/AttendanceStatusCard';
import { convertDate, DATE_FORMAT } from './ui/utils';

export default function AttendanceStorageDemoScreen() {
  const [attendanceData, setAttendanceData] = useState<{ [date: string]: StoredAttendanceData }>({});
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [summary, setSummary] = useState({
    totalDays: 0,
    checkedInDays: 0,
    pendingDays: 0,
    completedDays: 0
  });
  
  const colors = useColors();
  const router = useRouter();

  useEffect(() => {
    loadAttendanceData();
  }, []);

  const loadAttendanceData = async () => {
    try {
      setLoading(true);
      const allData = await attendanceStorageService.getAllAttendanceData();
      setAttendanceData(allData);
      
      // Get summary for the past 30 days
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30);
      
      const summaryData = await attendanceStorageService.getAttendanceSummary(
        startDate.toISOString().split('T')[0],
        endDate.toISOString().split('T')[0]
      );
      setSummary(summaryData);
    } catch (error) {
      console.error('Error loading attendance data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadAttendanceData();
  };

  const addSampleData = async () => {
    try {
      const today = new Date();
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      
      const todayStr = convertDate(today.toISOString(), DATE_FORMAT.SYSTEM_FORMAT, DATE_FORMAT.DATE_FORMAT);
      const yesterdayStr = convertDate(yesterday.toISOString(), DATE_FORMAT.SYSTEM_FORMAT, DATE_FORMAT.DATE_FORMAT);
      
      // Add sample data for today
      await attendanceStorageService.saveAttendanceForDate(todayStr, {
        id: 1,
        status: 'Inprogress',
        checkin_time: '09:00 AM',
        checkout_time: null,
        location: 'Office Building, Main Street, City'
      });
      
      // Add sample data for yesterday
      await attendanceStorageService.saveAttendanceForDate(yesterdayStr, {
        id: 2,
        status: 'Completed',
        checkin_time: '08:45 AM',
        checkout_time: '05:30 PM',
        location: 'Client Office, Business District'
      });
      
      Alert.alert('Success', 'Sample attendance data added!');
      loadAttendanceData();
    } catch (error) {
      console.error('Error adding sample data:', error);
      Alert.alert('Error', 'Failed to add sample data');
    }
  };

  const clearAllData = () => {
    Alert.alert(
      'Clear All Data',
      'Are you sure you want to clear all stored attendance data? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: async () => {
            try {
              await attendanceStorageService.clearAllAttendanceData();
              setAttendanceData({});
              setSummary({ totalDays: 0, checkedInDays: 0, pendingDays: 0, completedDays: 0 });
              Alert.alert('Success', 'All attendance data cleared!');
            } catch (error) {
              console.error('Error clearing data:', error);
              Alert.alert('Error', 'Failed to clear data');
            }
          }
        }
      ]
    );
  };

  const attendanceDates = Object.keys(attendanceData).sort().reverse(); // Most recent first

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <Stack.Screen 
        options={{ 
          title: 'Attendance Storage Demo',
          headerShown: true,
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: 'white',
          headerTitleStyle: { fontWeight: 'bold' }
        }} 
      />
      
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Summary Card */}
        <View style={[styles.summaryCard, { backgroundColor: colors.background.secondary }]}>
          <Text style={[styles.summaryTitle, { color: colors.text.primary }]}>
            Attendance Summary (Last 30 Days)
          </Text>
          <View style={styles.summaryGrid}>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryValue, { color: colors.primary }]}>{summary.totalDays}</Text>
              <Text style={[styles.summaryLabel, { color: colors.text.secondary }]}>Total Days</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryValue, { color: '#4CD964' }]}>{summary.checkedInDays}</Text>
              <Text style={[styles.summaryLabel, { color: colors.text.secondary }]}>Checked In</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryValue, { color: '#FF9500' }]}>{summary.pendingDays}</Text>
              <Text style={[styles.summaryLabel, { color: colors.text.secondary }]}>Pending</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryValue, { color: '#007AFF' }]}>{summary.completedDays}</Text>
              <Text style={[styles.summaryLabel, { color: colors.text.secondary }]}>Completed</Text>
            </View>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionContainer}>
          <TouchableOpacity 
            style={[styles.actionButton, { backgroundColor: colors.primary }]} 
            onPress={addSampleData}
          >
            <Ionicons name="add-circle-outline" size={20} color="white" />
            <Text style={styles.actionButtonText}>Add Sample Data</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.actionButton, { backgroundColor: '#FF3B30' }]} 
            onPress={clearAllData}
          >
            <Ionicons name="trash-outline" size={20} color="white" />
            <Text style={styles.actionButtonText}>Clear All Data</Text>
          </TouchableOpacity>
        </View>

        {/* Attendance Data List */}
        <View style={styles.dataContainer}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            Stored Attendance Data ({attendanceDates.length} days)
          </Text>
          
          {attendanceDates.length === 0 ? (
            <View style={[styles.emptyContainer, { backgroundColor: colors.background.secondary }]}>
              <Ionicons name="calendar-outline" size={48} color={colors.text.tertiary} />
              <Text style={[styles.emptyText, { color: colors.text.tertiary }]}>
                No attendance data stored
              </Text>
              <Text style={[styles.emptySubtext, { color: colors.text.tertiary }]}>
                Use the home page to check in/out or add sample data above
              </Text>
            </View>
          ) : (
            attendanceDates.map((date) => (
              <AttendanceStatusCard
                key={date}
                date={date}
                onRefresh={loadAttendanceData}
                showDetails={true}
              />
            ))
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  summaryCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  summaryGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  summaryItem: {
    alignItems: 'center',
    flex: 1,
  },
  summaryValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  summaryLabel: {
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
  },
  actionContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  dataContainer: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
    borderRadius: 12,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 12,
    marginBottom: 4,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
});
