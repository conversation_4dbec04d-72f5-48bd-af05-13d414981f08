import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  RefreshControl,
  StatusBar,
  SafeAreaView,
  Platform,
  Alert,
  TextInput,
} from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { showSnackbar } from './ui/utils';

interface Deal {
  id: number;
  name?: string;
  amount?: string;
  stage?: {
    id?: string | null;
    name?: string | null;
  } | null;
  contact_name?: string | null;
  account_name?: string | null;
  created_at?: string | null;
}

interface DealsResponse {
  success: boolean;
  data: Deal[];
  message: string;
}

const STATUS_BAR_CONFIG = {
  barStyle: "light-content" as const,
  backgroundColor: "#0F96BB",
  translucent: Platform.OS === 'android',
};

export default function SelectDealForContact() {
  const [deals, setDeals] = useState<Deal[]>([]);
  const [filteredDeals, setFilteredDeals] = useState<Deal[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);

  const router = useRouter();
  const params = useLocalSearchParams();
  const contactId = params.contact_id;
  const accountId = params.account_id;
  const insets = useSafeAreaInsets();

  useEffect(() => {
    fetchDeals();
  }, []);

  // Filter opportunities when search query changes
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredDeals(deals);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = deals.filter(opportunity => {
        // Add safety checks for all searchable properties
        const opportunityName = opportunity?.name?.toLowerCase() || '';
        const contactName = opportunity?.contact_name?.toLowerCase() || '';
        const accountName = opportunity?.account_name?.toLowerCase() || '';
        const stageName = opportunity?.stage?.name?.toLowerCase() || '';

        return opportunityName.includes(query) ||
               contactName.includes(query) ||
               accountName.includes(query) ||
               stageName.includes(query);
      });
      setFilteredDeals(filtered);
    }
  }, [searchQuery, deals]);

  const fetchDeals = async () => {
    try {
      setError(null);
      
      // Use the deals list API with filters to get all deals
      const filterPayload = {
        title: "All Deals",
        filter: [],
        share_with: "selected_users",
        selected_users: ["1"],
        accessibility: "Public",
        prevent_sharing: false
      };

      console.log('Fetching deals with payload:', JSON.stringify(filterPayload, null, 2));
      console.log('Deals API endpoint:', API_ENDPOINTS.DEAL_LIST_WITH_FILTERS);

      const response = await apiService.post<DealsResponse>(
        API_ENDPOINTS.DEAL_LIST_WITH_FILTERS,
        filterPayload
      );

      console.log('Deals API response:', JSON.stringify(response.data, null, 2));

      // Handle different possible response structures
      let dealsData = [];
      if (response.data?.data) {
        dealsData = response.data.data;
      } else if (response.data?.deals) {
        dealsData = response.data.deals;
      } else if (Array.isArray(response.data)) {
        dealsData = response.data;
      } else {
        console.warn('Unexpected API response structure:', response.data);
        throw new Error('No deals data received from the server');
      }

      // Ensure we have an array and filter out any invalid items
      const validDeals = Array.isArray(dealsData) ? dealsData.filter(deal => deal && deal.id) : [];

      // Transform and sort the data
      const sortedData = validDeals.sort((a, b) =>
        (a.name || '').localeCompare(b.name || '')
      );

      console.log(`Successfully loaded ${sortedData.length} deals`);
      console.log('First few deals:', sortedData.slice(0, 3).map(d => ({ id: d.id, name: d.name })));

      setDeals(sortedData);
      setFilteredDeals(sortedData);
    } catch (err) {
      console.error('Error fetching deals:', err);
      setError('Failed to fetch deals. Please try again.');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setIsRefreshing(true);
    fetchDeals();
  };

  const handleBack = () => {
    router.back();
  };

  const handleDealSelect = async (deal: Deal) => {
    if (!contactId && !accountId) {
      showSnackbar('Contact or Account ID is missing');
      return;
    }

    if (!deal || !deal.id) {
      showSnackbar('Invalid deal selected');
      return;
    }

    try {
      setIsUpdating(true);

      // We should update the DEAL with the contact/account information, not the other way around
      const entityId = contactId || accountId;
      const entityType = contactId ? 'contact' : 'account';
      const fieldName = contactId ? 'contacts' : 'sales_accounts';

      console.log(`Updating deal ${deal.id} with ${entityType}:`, {
        dealId: deal.id,
        dealName: deal.name,
        entityId,
        entityType,
        fieldName
      });

      // Fetch deal details to get the current field structure
      console.log('Fetching deal details from:', `${API_ENDPOINTS.DEALS}/${deal.id}`);
      const dealResponse = await apiService.get(`${API_ENDPOINTS.DEALS}/${deal.id}`);

      console.log('Deal details API response:', JSON.stringify(dealResponse.data, null, 2));

      // Handle the correct API response structure for deals
      let dealFields = null;
      if (dealResponse.data?.data?.deals) {
        dealFields = dealResponse.data.data.deals;
      } else if (dealResponse.data?.deal?.deals) {
        dealFields = dealResponse.data.deal.deals;
      } else if (dealResponse.data?.deals) {
        dealFields = dealResponse.data.deals;
      }

      if (!dealFields) {
        console.error('Invalid deal response structure:', dealResponse.data);
        throw new Error('Failed to get deal details');
      }

      console.log('Deal fields:', dealFields.map((f: any) => ({ name: f.field_name, value: f.field_value })));

      // Find the appropriate field (contacts or sales_accounts) and update it
      let targetFieldFound = false;
      const updatedFields = dealFields.map((field: any) => {
        if (field.field_name === fieldName) {
          targetFieldFound = true;
          let newFieldValue;
          let newValue;

          if (field.field_type === 'Dropdown' && field.choices) {
            // For dropdown fields, find the choice that matches our entity
            const entityChoice = field.choices.find((choice: any) =>
              choice.id.toString() === entityId.toString()
            );

            if (entityChoice) {
              newFieldValue = entityChoice.custom_option;
              newValue = entityChoice.custom_option;
              console.log(`Setting dropdown field to choice: ${entityChoice.custom_option} (ID: ${entityChoice.id})`);
            } else {
              // If no matching choice found, use entity ID as fallback
              newFieldValue = entityId.toString();
              newValue = entityId.toString();
              console.log(`No matching choice found, using ${entityType} ID: ${entityId}`);
            }
          } else {
            // For other field types (text, number, etc.), use entity ID
            newFieldValue = entityId.toString();
            newValue = entityId.toString();
            console.log(`Setting non-dropdown field to ${entityType} ID: ${entityId}`);
          }

          console.log(`Updating from "${field.field_value}" to "${newFieldValue}"`);

          return {
            ...field,
            field_value: newFieldValue,
            value: newValue // Include both field_value and value like the edit page does
          };
        }
        return field;
      });

      if (!targetFieldFound) {
        console.warn(`${fieldName} field not found in deal fields!`);
        console.log('Available field names:', dealFields.map((f: any) => f.field_name));
      }

      // Prepare the update payload (same structure as deal edit page)
      const updatePayload = {
        deal: updatedFields
      };

      console.log('Deal update API request body:', JSON.stringify(updatePayload, null, 2));
      console.log('Deal update API endpoint:', `${API_ENDPOINTS.DEALS}/${deal.id}`);

      // Update the deal
      const updateResponse = await apiService.put(
        `${API_ENDPOINTS.DEALS}/${deal.id}`,
        updatePayload
      );

      console.log('Deal update API response:', JSON.stringify(updateResponse.data, null, 2));

      if (updateResponse.status === 200 || updateResponse.status === 201) {
        console.log('✅ Deal updated successfully!');
        console.log(`${entityType} association completed:`, {
          dealId: deal.id,
          dealName: deal.name,
          entityId: entityId,
          entityType: entityType
        });

        showSnackbar(`Deal "${deal.name}" has been tagged with the ${entityType} successfully`);
        router.back(); // Go back to RelatedDealsList
      } else {
        throw new Error(updateResponse.data?.message || 'Failed to update deal');
      }
    } catch (err) {
      console.error(`Error updating ${contactId ? 'contact' : 'account'} with deal:`, err);
      const errorMessage = err instanceof Error ? err.message : `Failed to add deal to ${contactId ? 'contact' : 'account'}`;
      showSnackbar(errorMessage);
      Alert.alert('Error', errorMessage);
    } finally {
      setIsUpdating(false);
    }
  };

  const renderDealItem = ({ item }: { item: Deal }) => {
    // Add safety checks for all properties
    const dealName = item?.name || 'Unnamed Deal';
    const stageName = item?.stage?.name || 'No stage';
    const dealAmount = item?.amount ? Number(item.amount).toLocaleString() : '0';
    const contactName = item?.contact_name;
    const accountName = item?.account_name;

    return (
      <TouchableOpacity
        style={styles.dealContainer}
        onPress={() => handleDealSelect(item)}
        disabled={isUpdating}
      >
        <View style={styles.dealContent}>
          <View style={styles.dealInfo}>
            <Text style={styles.dealName}>{dealName}</Text>
            <Text style={styles.dealStage}>{stageName}</Text>
            <Text style={styles.dealAmount}>₹{dealAmount}</Text>
            {contactName && <Text style={styles.dealDetail}>👤 {contactName}</Text>}
            {accountName && <Text style={styles.dealDetail}>🏢 {accountName}</Text>}
          </View>
          <Ionicons name="chevron-forward" size={20} color="#666" />
        </View>
      </TouchableOpacity>
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={[styles.safeAreaContainer, { paddingTop: insets.top }]}>
        <StatusBar {...STATUS_BAR_CONFIG} />
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Ionicons name={Platform.OS === 'ios' ? 'chevron-back' : 'arrow-back'}  size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <View style={styles.headerContent}>
            <Text style={styles.headerTitleText}>Select Opportunity</Text>
          </View>
          <View style={styles.headerRight} />
        </View>
        <View style={styles.mainContainer}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#0F96BB" />
            <Text style={styles.loadingText}>Loading opportunities...</Text>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.safeAreaContainer, { paddingTop: insets.top }]}>
      <StatusBar {...STATUS_BAR_CONFIG} />
      <Stack.Screen options={{ headerShown: false }} />

      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Ionicons name={Platform.OS === 'ios' ? 'chevron-back' : 'arrow-back'}  size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitleText}>Select Opportunity</Text>
        </View>
        <View style={styles.headerRight} />
      </View>

      <View style={styles.mainContainer}>
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search opportunities..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor="#999"
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery('')} style={styles.clearButton}>
                <Ionicons name="close-circle" size={20} color="#666" />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {error ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity style={styles.retryButton} onPress={fetchDeals}>
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <FlatList
            data={filteredDeals}
            renderItem={renderDealItem}
            keyExtractor={(item) => item.id.toString()}
            refreshControl={
              <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
            }
            contentContainerStyle={styles.listContent}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="briefcase-outline" size={80} color="#ccc" />
                <Text style={styles.emptyText}>
                  {searchQuery ? 'No opportunities found matching your search' : 'No opportunities available'}
                </Text>
              </View>
            }
          />
        )}

        {isUpdating && (
          <View style={styles.updatingOverlay}>
            <View style={styles.updatingContainer}>
              <ActivityIndicator size="large" color="#0F96BB" />
              <Text style={styles.updatingText}>Adding opportunity...</Text>
            </View>
          </View>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeAreaContainer: {
    flex: 1,
    backgroundColor: '#0F96BB',
  },
  mainContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#0F96BB',
    height: Platform.OS === 'ios' ? 44 : 56,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
    zIndex: 1000,
    paddingHorizontal: Platform.OS === 'ios' ? 8 : 0,
  },
  backButton: {
    width: Platform.OS === 'ios' ? 44 : 56,
    height: Platform.OS === 'ios' ? 44 : 56,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: Platform.OS === 'ios' ? -8 : 0,
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: Platform.OS === 'ios' ? 4 : 16,
  },
  headerRight: {
    width: Platform.OS === 'ios' ? 44 : 56,
    height: Platform.OS === 'ios' ? 44 : 56,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Platform.OS === 'ios' ? -8 : 0,
  },
  headerTitleText: {
    color: '#FFFFFF',
    fontSize: Platform.OS === 'ios' ? 17 : 20,
    fontWeight: Platform.OS === 'ios' ? '600' : '500',
    textAlign: 'center',
  },
  searchContainer: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 40,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  clearButton: {
    marginLeft: 8,
  },
  listContent: {
    flexGrow: 1,
    paddingTop: 8,
  },
  dealContainer: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginVertical: 4,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  dealContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  dealInfo: {
    flex: 1,
  },
  dealName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  dealStage: {
    fontSize: 14,
    color: '#0F96BB',
    fontWeight: '500',
    marginBottom: 4,
  },
  dealAmount: {
    fontSize: 16,
    color: '#4CAF50',
    fontWeight: '600',
    marginBottom: 4,
  },
  dealDetail: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#666',
    fontSize: 16,
    marginTop: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#ff0000',
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#0F96BB',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 16,
  },
  updatingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  updatingContainer: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  updatingText: {
    color: '#333',
    fontSize: 16,
    marginTop: 10,
  },
});
