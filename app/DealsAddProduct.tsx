import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Platform,
  TextInput,
  ScrollView,
  KeyboardAvoidingView,
  ViewStyle,
  TextStyle,
  Alert,
  Modal,
  Pressable,
  Animated,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { Appbar } from 'react-native-paper';

interface Product {
  id: string;  // This will be the catalog product ID
  dealProductId?: string;  // This will store the deal product ID when editing
  name: string;
  price: string;
  quantity: string;
  currency?: string;
  pricing_type?: string;
  setup_fee?: string;
}

interface ProductCurrency {
  id: number;
  currency_name: string;
  base_currency_amount: string;
  setup_fee: string | null;
}

interface CategoryProduct {
  id: string;
  product_name: string;
  owner_name: string | null;
  base_currency_amount: string | null;
  currency: string | null;
  pricing_type: string | null;
  created_at: string | null;
  product_image: string;
  productcurrencies: ProductCurrency[];
}

interface Category {
  id: number;
  category: string;
  category_name: string;
  product_count: number;
  products: CategoryProduct[];
}

interface ApiResponse {
  status: boolean;
  message: string;
  response: Category[];
}

interface ProductWithCurrencies extends Product {
  availableCurrencies?: ProductCurrency[];
}

// Add new interface for API request
interface ProductRequest {
  id: number | null;
  product_id: string;
  unitprice: number;
  quantity: number;
  discount_type: 'percentage' | 'flat';
  discount: number;
  setup_fee?: number;
  billing_cycle?: string;
  no_of_billing_cycle?: number;
}

const STATUS_BAR_CONFIG = {
  barStyle: "light-content" as const,
  backgroundColor: "#0F96BB",
  translucent: Platform.OS === 'android'
};

export default function DealsAddProduct() {
  const insets = useSafeAreaInsets();
  const [products, setProducts] = useState<Product[]>([]);
  const [showProductSearch, setShowProductSearch] = useState(false);
  const [showCategoryPicker, setShowCategoryPicker] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('All categories');
  const [searchQuery, setSearchQuery] = useState('');
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCurrencies, setSelectedCurrencies] = useState<{[key: string]: ProductCurrency}>({});
  const [productDiscounts, setProductDiscounts] = useState<{[key: string]: string}>({});
  const [expandedProducts, setExpandedProducts] = useState<{[key: string]: boolean}>({});
  const [discountTypes, setDiscountTypes] = useState<{[key: string]: 'percentage' | 'flat'}>({});
  const [showDiscountTypePicker, setShowDiscountTypePicker] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [showSummary, setShowSummary] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editProductId, setEditProductId] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  
  const router = useRouter();
  const params = useLocalSearchParams();
  const dealId = params.dealId as string;
  const editMode = params.editMode === 'true';
  const productId = params.productId as string;
  
  // Load categories first
  useEffect(() => {
    const loadInitialData = async () => {
      await fetchCategories();
      setIsInitialized(true);
    };
    
    loadInitialData();
  }, []);
  
  // Handle edit mode after categories are loaded
  useEffect(() => {
    if (!isInitialized) return;
    
    if (editMode && productId) {
      setIsEditMode(true);
      setEditProductId(productId);
      setShowProductSearch(false);
      fetchProductForEdit();
    }
  }, [isInitialized, editMode, productId]);
  
  const fetchProductForEdit = async () => {
    if (!dealId || !productId) return;
    
    try {
      setIsLoading(true);
      
      // Create endpoint to fetch single product details
      const response = await apiService.post(`${API_ENDPOINTS.DEALS_PRODUCTS_LIST}`, {
        dealid: parseInt(dealId)
      });
      
      console.log('API response received:', response.data.status);
      
      if (response.data.status) {
        const dealProducts = response.data.response.dealproducts || [];
        const productToEdit = dealProducts.find((p: any) => p.id.toString() === productId);
        
        if (productToEdit) {
          // Create a product object from the deal product data
          const editProduct: Product = {
            id: productToEdit.product_id.toString(), // Use product_id from catalog
            dealProductId: productToEdit.id.toString(), // Store the deal product ID
            name: productToEdit.product_name,
            price: productToEdit.unitprice,
            quantity: productToEdit.quantity.toString(),
            currency: 'INR', // Default or from productData if available
            pricing_type: productToEdit.billing_cycle ? 'Subscription Pricing' : 'One-time pricing',
            setup_fee: productToEdit.setupfree || productToEdit.setup_fee
          };
          
          // Set products array with the product to edit
          setProducts([editProduct]);
          
          // Set discount information
          setProductDiscounts({
            [editProduct.id]: productToEdit.discount?.toString() || '0'
          });
          
          // Set discount type
          setDiscountTypes({
            [editProduct.id]: productToEdit.discount_type || 'percentage'
          });
          
          // Expand the product card by default in edit mode
          setExpandedProducts({
            [editProduct.id]: true
          });
        } else {
          setError('Product not found');
          console.error('Product not found:', productId);
        }
      } else {
        setError(response.data.message || 'Failed to load product data');
        console.error('API error:', response.data.message);
      }
    } catch (error) {
      console.error('Error fetching product data:', error);
      setError('Failed to load product data for editing');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await apiService.get<ApiResponse>(API_ENDPOINTS.PRODUCT_CATEGORIES);
      if (response.data.status) {
        setCategories(response.data.response);
        if (response.data.response.length > 0) {
          setSelectedCategory('All categories');
        }
      } else {
        setError(response.data.message);
      }
    } catch (err) {
      setError('Failed to fetch categories. Please try again.');
      console.error('Error fetching categories:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    if (showProductSearch) {
      setShowProductSearch(false);
      router.back();
    } else {
      router.back();
    }
  };

  const toggleSummary = () => {
    setShowSummary(!showSummary);
  };

  const calculateDealValue = (products: Product[], discounts: {[key: string]: string}, discountTypes: {[key: string]: 'percentage' | 'flat'}) => {
    return products.reduce((total, product) => {
      const price = parseFloat(product.price) || 0;
      const quantity = parseInt(product.quantity) || 0;
      const discount = parseFloat(discounts[product.id]) || 0;
      const discountType = discountTypes[product.id] || 'percentage';
      
      let itemTotal = price * quantity;
      
      if (discountType === 'percentage') {
        itemTotal = itemTotal * (1 - discount / 100);
      } else {
        itemTotal = itemTotal - discount;
      }
      
      if (product.setup_fee) {
        itemTotal += parseFloat(product.setup_fee);
      }
      
      return total + itemTotal;
    }, 0);
  };

  const handleSave = async () => {
    if (products.length === 0) {
      Alert.alert('Error', 'Please add at least one product');
      return;
    }

    try {
      setIsSaving(true);

      // Transform products into the required format
      const productsPayload: ProductRequest[] = products.map(product => ({
        id: isEditMode && product.dealProductId ? parseInt(product.dealProductId) : null,
        product_id: product.id,
        unitprice: parseFloat(product.price),
        quantity: parseInt(product.quantity),
        discount_type: discountTypes[product.id] || 'percentage',
        discount: parseFloat(productDiscounts[product.id] || '0'),
        setup_fee: product.setup_fee ? parseFloat(product.setup_fee) : undefined,
        billing_cycle: product.pricing_type === 'Subscription Pricing' ? 'monthly' : undefined,
        no_of_billing_cycle: product.pricing_type === 'Subscription Pricing' ? 12 : undefined
      }));

      const dealValue = calculateDealValue(products, productDiscounts, discountTypes);

      // Choose the appropriate endpoint based on whether we're editing or adding
      const endpoint = isEditMode ? API_ENDPOINTS.DEAL_PRODUCTS_UPDATE : API_ENDPOINTS.DEAL_PRODUCTS;
      
      const response = await apiService.post(endpoint, {
        dealid: parseInt(dealId),
        products: productsPayload,
        deal_value: dealValue
      });

      if (response.data.status) {
        const successMessage = isEditMode ? 'Product updated successfully' : 'Products added successfully';
        Alert.alert('Success', successMessage, [
          {
            text: 'OK',
            onPress: () => router.back()
          }
        ]);
      } else {
        Alert.alert('Error', response.data.message || `Failed to ${isEditMode ? 'update' : 'add'} products`);
      }
    } catch (error) {
      console.error(`Error ${isEditMode ? 'updating' : 'saving'} products:`, error);
      Alert.alert('Error', `Failed to ${isEditMode ? 'update' : 'save'} products. Please try again.`);
    } finally {
      setIsSaving(false);
    }
  };

  const handleAddProduct = () => {
    // Only show product search if not in edit mode
    if (!isEditMode) {
      // Clear search query and reset category when opening the modal
      setSearchQuery('');
      setSelectedCategory('All categories');
      setShowCategoryPicker(false);
      setShowProductSearch(true);
    }
  };

  const handleUpdateProduct = (id: string, field: keyof Product, value: string) => {
    setProducts(products.map(product => 
      product.id === id ? { ...product, [field]: value } : product
    ));
    
    // If updating price, also update the selected currency's base_currency_amount
    if (field === 'price' && selectedCurrencies[id]) {
      setSelectedCurrencies({
        ...selectedCurrencies,
        [id]: {
          ...selectedCurrencies[id],
          base_currency_amount: value
        }
      });
    }
    
    // If updating setup_fee, also update the selected currency's setup_fee
    if (field === 'setup_fee' && selectedCurrencies[id]) {
      setSelectedCurrencies({
        ...selectedCurrencies,
        [id]: {
          ...selectedCurrencies[id],
          setup_fee: value
        }
      });
    }
  };

  const handleRemoveProduct = (id: string) => {
    Alert.alert(
      'Remove Product',
      'Are you sure you want to remove this product?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Remove', 
          style: 'destructive',
          onPress: () => {
            setProducts(products.filter(product => product.id !== id));
          }
        }
      ]
    );
  };

  const handleSelectCategory = (category: string) => {
    setSelectedCategory(category);
    setShowCategoryPicker(false);
    // Clear search query when changing categories
    setSearchQuery('');
  };

  const getAllProducts = () => {
    return categories.reduce((allProducts: CategoryProduct[], category) => {
      return [...allProducts, ...category.products];
    }, []);
  };

  const getFilteredProducts = () => {
    const allProducts = selectedCategory === 'All categories' 
      ? getAllProducts()
      : categories.find(cat => cat.category_name === selectedCategory)?.products || [];

    return allProducts.filter(product => 
      product.product_name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  const getProductPrice = (product: CategoryProduct): string => {
    if (product.productcurrencies && product.productcurrencies.length > 0) {
      return product.productcurrencies[0].base_currency_amount;
    }
    return product.base_currency_amount || '0';
  };

  const getProductCurrency = (product: CategoryProduct): string => {
    if (product.productcurrencies && product.productcurrencies.length > 0) {
      return product.productcurrencies[0].currency_name;
    }
    return product.currency || 'INR';
  };

  const getProductSetupFee = (product: CategoryProduct): string | undefined => {
    if (product.productcurrencies && product.productcurrencies.length > 0) {
      return product.productcurrencies[0].setup_fee || undefined;
    }
    return undefined;
  };

  const toggleProductExpansion = (productId: string) => {
    setExpandedProducts(prev => ({
      ...prev,
      [productId]: !prev[productId]
    }));
  };

  const handleCloseProductSearch = () => {
    // Clear search state when closing the modal
    setSearchQuery('');
    setSelectedCategory('All categories');
    setShowCategoryPicker(false);
    setShowProductSearch(false);
  };

  const handleProductSelect = (product: CategoryProduct) => {
    const newProduct: ProductWithCurrencies = {
      id: product.id,
      name: product.product_name,
      price: getProductPrice(product),
      quantity: '1',
      currency: getProductCurrency(product),
      pricing_type: product.pricing_type || 'One-time pricing',
      setup_fee: getProductSetupFee(product),
      availableCurrencies: product.productcurrencies
    };
    setProducts([...products, newProduct]);
    setSelectedCurrencies({
      ...selectedCurrencies,
      [product.id]: product.productcurrencies[0]
    });
    setProductDiscounts({
      ...productDiscounts,
      [product.id]: '0'
    });
    handleCloseProductSearch(); // Use the new close function that clears search state
  };

  const handleDiscountTypeSelect = (productId: string, type: 'percentage' | 'flat') => {
    setDiscountTypes({
      ...discountTypes,
      [productId]: type
    });
    setShowDiscountTypePicker(null);
  };

  const renderProduct = (product: ProductWithCurrencies) => {
    const isExpanded = expandedProducts[product.id];
    const selectedCurrency = selectedCurrencies[product.id];
    const isSubscription = product.pricing_type === 'Subscription Pricing';
    const discountType = discountTypes[product.id] || 'percentage';

    return (
      <View key={product.id} style={styles.productCard}>
        <TouchableOpacity 
          style={styles.productHeader}
          onPress={() => toggleProductExpansion(product.id)}
        >
          <Text style={styles.productName}>{product.name}</Text>
          <View style={styles.productHeaderRight}>
            <TouchableOpacity
              onPress={() => handleRemoveProduct(product.id)}
              hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
              style={styles.removeButton}
            >
              <Ionicons name="trash-outline" size={24} color="#666" />
            </TouchableOpacity>
            <Ionicons 
              name={isExpanded ? "chevron-up" : "chevron-down"} 
              size={24} 
              color="#666" 
            />
          </View>
        </TouchableOpacity>

        {isExpanded ? (
          <View style={styles.productExpandedContent}>
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Unit price</Text>
              <TextInput
                style={styles.input}
                value={product.price}
                onChangeText={(value) => handleUpdateProduct(product.id, 'price', value)}
                keyboardType="decimal-pad"
                placeholder="0.00"
                placeholderTextColor="#999"
              />
            </View>

            {isSubscription && (
              <View style={styles.inputGroup}>
                <Text style={styles.label}>Setup fee</Text>
                <TextInput
                  style={styles.input}
                  value={product.setup_fee || '0'}
                  onChangeText={(value) => handleUpdateProduct(product.id, 'setup_fee', value)}
                  keyboardType="decimal-pad"
                  placeholder="0.00"
                  placeholderTextColor="#999"
                />
              </View>
            )}

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Quantity</Text>
              <TextInput
                style={styles.input}
                value={product.quantity}
                onChangeText={(value) => handleUpdateProduct(product.id, 'quantity', value)}
                keyboardType="number-pad"
                placeholder="1"
                placeholderTextColor="#999"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Discount</Text>
              <View style={styles.discountContainer}>
                <View style={styles.discountTypeSelectorContainer}>
                  <TouchableOpacity
                    style={styles.discountTypeSelector}
                    onPress={() => setShowDiscountTypePicker(product.id)}
                  >
                    <Text style={styles.discountTypeText}>
                      {discountType === 'percentage' ? 'Percentage (%)' : `Flat value (${selectedCurrency?.currency_name || product.currency || '₹'})`}
                    </Text>
                    <Ionicons name="chevron-down" size={20} color="#666" />
                  </TouchableOpacity>
                  {showDiscountTypePicker === product.id && (
                    <View style={styles.discountTypePopup}>
                      <TouchableOpacity
                        style={[
                          styles.discountTypeOption,
                          discountType === 'flat' && styles.selectedDiscountType
                        ]}
                        onPress={() => handleDiscountTypeSelect(product.id, 'flat')}
                      >
                        <Text style={[
                          styles.discountTypeOptionText,
                          discountType === 'flat' && styles.selectedDiscountTypeText
                        ]}>
                          Flat value ({selectedCurrency?.currency_name || product.currency || '₹'})
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={[
                          styles.discountTypeOption,
                          discountType === 'percentage' && styles.selectedDiscountType
                        ]}
                        onPress={() => handleDiscountTypeSelect(product.id, 'percentage')}
                      >
                        <Text style={[
                          styles.discountTypeOptionText,
                          discountType === 'percentage' && styles.selectedDiscountTypeText
                        ]}>
                          Percentage (%)
                        </Text>
                      </TouchableOpacity>
                    </View>
                  )}
                </View>
                <TextInput
                  style={[styles.input, styles.discountInput]}
                  value={productDiscounts[product.id]}
                  onChangeText={(value) => {
                    setProductDiscounts({
                      ...productDiscounts,
                      [product.id]: value
                    });
                  }}
                  keyboardType="decimal-pad"
                  placeholder="0"
                  placeholderTextColor="#999"
                />
              </View>
            </View>
          </View>
        ) : (
          <View style={styles.productSummary}>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Unit price : </Text>
              <Text style={styles.summaryValue}>₹ {product.price}</Text>
            </View>
            {isSubscription && (
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Setup fee : </Text>
                <Text style={styles.summaryValue}>₹ {product.setup_fee || '0'}</Text>
              </View>
            )}
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Quantity : </Text>
              <Text style={styles.summaryValue}>{product.quantity}</Text>
              <View style={styles.discountSummary}>
                <Text style={styles.summaryLabel}>Discount : </Text>
                <Text style={styles.summaryValue}>{productDiscounts[product.id] || '0'}%</Text>
              </View>
            </View>
          </View>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { paddingTop: insets.top }]}>
      <StatusBar {...STATUS_BAR_CONFIG} />
      <Stack.Screen 
        options={{ 
          headerShown: false,
        }} 
      />
      
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitleText}>Add Product</Text>
        </View>
        <View style={styles.headerRight}>
          {isSaving ? (
            <ActivityIndicator color="#FFFFFF" style={{ marginRight: 10 }} />
          ) : (
            <TouchableOpacity onPress={handleSave} style={styles.saveButton}>
              <Ionicons name="checkmark" size={28} color="#FFFFFF" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      <View style={styles.mainContainer}>
        {isLoading && isEditMode ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#0F96BB" />
            <Text style={styles.loadingText}>Loading product data...</Text>
          </View>
        ) : (
          <KeyboardAvoidingView 
            style={styles.contentContainer} 
            behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          >
            {/* Header title */}
            <View style={styles.headerContainer}>
              <Text style={styles.headerTitle}>
                {isEditMode ? 'EDIT PRODUCT' : `PRODUCTS ( ${products.length} )`}
              </Text>
            </View>

            <ScrollView style={styles.scrollView}>
              <View style={styles.content}>
                {products.map(renderProduct)}

                {/* Add product button - only show in add mode, not edit mode */}
                {!isEditMode && (
                  <TouchableOpacity 
                    style={styles.addProductButton}
                    onPress={handleAddProduct}
                  >
                    <Text style={styles.addProductLinkText}>Add product</Text>
                  </TouchableOpacity>
                )}
              </View>
            </ScrollView>

            {/* Total Deal Value Footer */}
            <View style={styles.totalValueContainer}>
              {showSummary && (
                <View style={styles.summaryContent}>
                  <View style={styles.summaryRow}>
                    <Text style={styles.summaryLabel}>Sub total</Text>
                    <Text style={styles.summaryValue}>₹ {calculateDealValue(products, productDiscounts, discountTypes).toFixed(2)}</Text>
                  </View>
                  
                  {/* Could add more summary items here like taxes, etc. */}
                </View>
              )}
              
              <TouchableOpacity 
                style={styles.totalValueContent}
                onPress={toggleSummary}
                activeOpacity={0.7}
              >
                <Text style={styles.totalValueLabel}>Total Deal value</Text>
                <View style={styles.totalValueAmountContainer}>
                  <Text style={styles.totalValueAmount}>
                    ₹ {calculateDealValue(products, productDiscounts, discountTypes).toFixed(2)}
                  </Text>
                  <Ionicons 
                    name={showSummary ? "chevron-down" : "chevron-up"} 
                    size={24} 
                    color="#333" 
                  />
                </View>
              </TouchableOpacity>
            </View>
          </KeyboardAvoidingView>
        )}

        {/* Product Search Modal - only show in add mode, not edit mode */}
        <Modal
          visible={showProductSearch && !isEditMode}
          animationType="slide"
          transparent={true}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalView}>
              <SafeAreaView style={styles.modalContainer}>
                <View style={styles.modalHeader}>
                  <TouchableOpacity
                    onPress={handleCloseProductSearch}
                    hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
                  >
                    <Text style={styles.modalCancelButton}>Cancel</Text>
                  </TouchableOpacity>
                  <Text style={styles.modalTitle}>Add Product</Text>
                  <View style={styles.modalHeaderRight} />
                </View>

                {/* Categories Selector */}
                <View style={styles.categoryWrapper}>
                  <TouchableOpacity 
                    style={styles.categorySelector}
                    onPress={() => setShowCategoryPicker(!showCategoryPicker)}
                  >
                    <Text style={styles.categoryText}>{selectedCategory}</Text>
                    <Ionicons 
                      name={showCategoryPicker ? "chevron-up" : "chevron-down"} 
                      size={24} 
                      color="#333" 
                    />
                  </TouchableOpacity>

                  {/* Inline Category Picker */}
                  {showCategoryPicker && (
                    <View style={styles.categoryPickerContainer}>
                      <TouchableOpacity
                        style={[
                          styles.categoryItem,
                          selectedCategory === 'All categories' && styles.selectedCategoryItem
                        ]}
                        onPress={() => handleSelectCategory('All categories')}
                      >
                        <Text style={[
                          styles.categoryItemText,
                          selectedCategory === 'All categories' && styles.selectedCategoryItemText
                        ]}>
                          All categories
                        </Text>
                      </TouchableOpacity>
                      {categories.map((category) => (
                        <TouchableOpacity
                          key={category.id}
                          style={[
                            styles.categoryItem,
                            selectedCategory === category.category_name && styles.selectedCategoryItem
                          ]}
                          onPress={() => handleSelectCategory(category.category_name)}
                        >
                          <Text style={[
                            styles.categoryItemText,
                            selectedCategory === category.category_name && styles.selectedCategoryItemText
                          ]}>
                            {category.category_name}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  )}
                </View>

                {/* Search Input */}
                <View style={styles.searchContainer}>
                  <Ionicons name="search" size={20} color="#999" style={styles.searchIcon} />
                  <TextInput
                    style={styles.searchInput}
                    placeholder="Search for product to add"
                    placeholderTextColor="#999"
                    value={searchQuery}
                    onChangeText={setSearchQuery}
                  />
                </View>

                {/* Product List */}
                <ScrollView 
                  style={styles.productList}
                  keyboardShouldPersistTaps="handled"
                >
                  {isLoading ? (
                    <View style={styles.loadingContainer}>
                      <ActivityIndicator size="large" color="#0F96BB" />
                    </View>
                  ) : error ? (
                    <View style={styles.errorContainer}>
                      <Text style={styles.errorText}>{error}</Text>
                      <TouchableOpacity 
                        style={styles.retryButton}
                        onPress={fetchCategories}
                      >
                        <Text style={styles.retryButtonText}>Retry</Text>
                      </TouchableOpacity>
                    </View>
                  ) : (
                    getFilteredProducts().map((product) => (
                      <TouchableOpacity 
                        key={product.id}
                        style={styles.productListItem}
                        onPress={() => {
                          handleProductSelect(product);
                        }}
                      >
                        <Text style={styles.productListItemText}>{product.product_name}</Text>
                      </TouchableOpacity>
                    ))
                  )}
                </ScrollView>
              </SafeAreaView>
            </View>
          </View>
        </Modal>
      </View>
    </SafeAreaView>
  );
}

// Define styles type
type Styles = {
  container: ViewStyle;
  mainContainer: ViewStyle;
  contentContainer: ViewStyle;
  header: ViewStyle;
  backButton: ViewStyle;
  headerContent: ViewStyle;
  headerRight: ViewStyle;
  headerTitleText: TextStyle;
  saveButton: ViewStyle;
  scrollView: ViewStyle;
  content: ViewStyle;
  productCard: ViewStyle;
  productHeader: ViewStyle;
  productHeaderRight: ViewStyle;
  productName: TextStyle;
  removeButton: ViewStyle;
  productSummary: ViewStyle;
  summaryRow: ViewStyle;
  summaryLabel: TextStyle;
  summaryValue: TextStyle;
  productExpandedContent: ViewStyle;
  inputGroup: ViewStyle;
  label: TextStyle;
  input: TextStyle;
  discountContainer: ViewStyle;
  discountInput: TextStyle;
  addProductLink: ViewStyle;
  addProductLinkText: TextStyle;
  modalOverlay: ViewStyle;
  modalView: ViewStyle;
  modalContainer: ViewStyle;
  modalHeader: ViewStyle;
  modalCancelButton: TextStyle;
  modalTitle: TextStyle;
  modalHeaderRight: ViewStyle;
  categoryWrapper: ViewStyle;
  categorySelector: ViewStyle;
  categoryText: TextStyle;
  searchContainer: ViewStyle;
  searchIcon: TextStyle;
  searchInput: TextStyle;
  productList: ViewStyle;
  productListItem: ViewStyle;
  productListItemText: TextStyle;
  categoryPickerContainer: ViewStyle;
  categoryItem: ViewStyle;
  selectedCategoryItem: ViewStyle;
  categoryItemText: TextStyle;
  selectedCategoryItemText: TextStyle;
  loadingContainer: ViewStyle;
  loadingText: TextStyle;
  errorContainer: ViewStyle;
  errorText: TextStyle;
  retryButton: ViewStyle;
  retryButtonText: TextStyle;
  summaryContent: ViewStyle;
  headerContainer: ViewStyle;
  headerTitle: TextStyle;
  totalValueContainer: ViewStyle;
  totalValueContent: ViewStyle;
  totalValueLabel: TextStyle;
  totalValueAmountContainer: ViewStyle;
  totalValueAmount: TextStyle;
  discountTypeSelectorContainer: ViewStyle;
  discountTypeSelector: ViewStyle;
  discountTypeText: TextStyle;
  discountTypePopup: ViewStyle;
  discountTypeOption: ViewStyle;
  selectedDiscountType: ViewStyle;
  discountTypeOptionText: TextStyle;
  selectedDiscountTypeText: TextStyle;
  addProductButton: ViewStyle;
  discountSummary: ViewStyle;
  iosHeader: ViewStyle;
};

const styles = StyleSheet.create<Styles>({
  container: {
    flex: 1,
    backgroundColor: '#0F96BB',
  },
  mainContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  contentContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#0F96BB',
    height: Platform.OS === 'ios' ? 44 : 56,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
    zIndex: 1000,
    paddingHorizontal: Platform.OS === 'ios' ? 8 : 0,
  },
  backButton: {
    width: Platform.OS === 'ios' ? 44 : 56,
    height: Platform.OS === 'ios' ? 44 : 56,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: Platform.OS === 'ios' ? -8 : 0,
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: Platform.OS === 'ios' ? 4 : 16,
  },
  headerRight: {
    width: Platform.OS === 'ios' ? 44 : 56,
    height: Platform.OS === 'ios' ? 44 : 56,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Platform.OS === 'ios' ? -8 : 0,
  },
  headerTitleText: {
    color: '#FFFFFF',
    fontSize: Platform.OS === 'ios' ? 17 : 20,
    fontWeight: Platform.OS === 'ios' ? '600' : '500',
    textAlign: 'center',
  },
  saveButton: {
    width: Platform.OS === 'ios' ? 44 : 56,
    height: Platform.OS === 'ios' ? 44 : 56,
    alignItems: 'center',
    justifyContent: 'center',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  productCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    marginBottom: 16,
    overflow: 'hidden',
    padding: 16,
  },
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  productHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  productName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    flex: 1,
  },
  removeButton: {
    padding: 4,
  },
  productSummary: {
    marginTop: 4,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  summaryLabel: {
    fontSize: 16,
    color: '#333',
    fontWeight: '400',
  },
  summaryValue: {
    fontSize: 16,
    color: '#333',
    fontWeight: '400',
  },
  productExpandedContent: {
    marginTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    padding: 12,
    fontSize: 16,
    color: '#333',
    backgroundColor: '#fff',
  },
  discountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  discountTypeSelectorContainer: {
    position: 'relative',
    zIndex: 1,
    width: 250,
    flex: 2,
  },
  discountTypeSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 12,
    backgroundColor: '#fff',
    width: '100%',
  },
  discountTypeText: {
    fontSize: 14,
    color: '#333',
    flex: 1,
    marginRight: 8,
    flexShrink: 1,
    flexWrap: 'nowrap',
  },
  discountInput: {
    flex: 1,
    minWidth: 80,
    maxWidth: 120,
  },
  discountTypePopup: {
    position: 'absolute',
    bottom: '100%',
    marginBottom: 4,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderRadius: 6,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    zIndex: 1000,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  discountTypeOption: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  selectedDiscountType: {
    backgroundColor: '#f0f0f0',
  },
  discountTypeOptionText: {
    fontSize: 16,
    color: '#333',
  },
  selectedDiscountTypeText: {
    color: '#007AFF',
    fontWeight: '500',
  },
  addProductLink: {
    paddingVertical: 12,
  },
  addProductLinkText: {
    fontSize: 17,
    color: '#007AFF',
    fontWeight: '400',
  },
  addProductButton: {
    paddingVertical: 12,
    marginTop: 10,
    marginBottom: 80, // Add bottom margin to account for total value container
  },
  headerContainer: {
    backgroundColor: '#f5f5f5',
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
    marginTop: 0,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  totalValueContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopWidth: 1,
    borderTopColor: '#ddd',
    backgroundColor: 'white',
  },
  totalValueContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  totalValueLabel: {
    fontSize: 18,
    fontWeight: '500',
    color: '#333',
  },
  totalValueAmountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  totalValueAmount: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginRight: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalView: {
    height: '60%',
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalCancelButton: {
    fontSize: 17,
    color: '#007AFF',
    fontWeight: '400',
  },
  modalTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: '#000',
  },
  modalHeaderRight: {
    width: 70, // Match cancel button width for centering
  },
  categoryWrapper: {
    backgroundColor: 'white',
    zIndex: 1,
  },
  categorySelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  categoryText: {
    fontSize: 17,
    color: '#333',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#eee',
    margin: 16,
    paddingHorizontal: 12,
    borderRadius: 10,
  },
  searchIcon: {
    marginRight: 8,
  } as TextStyle,
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 17,
    color: '#333',
  },
  productList: {
    flex: 1,
    backgroundColor: 'white',
  },
  productListItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  productListItemText: {
    fontSize: 17,
    color: '#333',
  },
  categoryPickerContainer: {
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#eee',
    maxHeight: 300,
  },
  categoryItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  selectedCategoryItem: {
    backgroundColor: '#f0f0f0',
  },
  categoryItemText: {
    fontSize: 17,
    color: '#333',
  },
  selectedCategoryItemText: {
    color: '#007AFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#777',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  errorText: {
    color: '#ff4444',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 12,
  },
  retryButton: {
    backgroundColor: '#0F96BB',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 6,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  summaryContent: {
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  discountSummary: {
    flexDirection: 'row',
    marginLeft: 16,
  },
  iosHeader: {
    height: 44,
  },
}); 