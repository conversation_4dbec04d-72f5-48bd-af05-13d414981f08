import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  ScrollView,
  ActivityIndicator,
  Alert,
  Platform,
  Keyboard,
  KeyboardAvoidingView,
  Modal,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { AccountField, AccountFieldsResponse, AccountFormData, AccountFieldChoice } from './models/AccountField';
import { useColors } from '@/hooks/useThemeColor';
import { DynamicFormField } from './dynamic_fields/DynamicFormField';
import { FIELD_TYPES } from './models/contact_fields_model';
import { useAuth } from '../context/AuthContext';

const CreateAccounts = () => {
  const [fields, setFields] = useState<AccountField[]>([]);
  const [formData, setFormData] = useState<AccountFormData>({} as AccountFormData);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [currentDropdownField, setCurrentDropdownField] = useState<AccountField | null>(null);
  const [showAllFields, setShowAllFields] = useState(false); // Toggle for showing quick_add "off" fields
  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false);
  const router = useRouter();
  const colors = useColors();
  const { user } = useAuth(); // Get the current logged-in user
  
  // Fetch account fields on component mount
  useEffect(() => {
    fetchAccountFields();
  }, []);

  // Set default owner when form fields are loaded
  useEffect(() => {
    if (fields.length > 0 && user) {
      // Find the owner field
      const ownerField = fields.find(field =>
        field.field_name.toLowerCase().includes('owner') ||
        field.field_label.toLowerCase().includes('owner') ||
        field.lookup_type === 'users'
      );

      if (ownerField && ownerField.choices) {
        // Find the choice that matches the current user's ID
        const userChoice = ownerField.choices.find(choice => choice.id === user.id);

        if (userChoice) {
          // Set the owner to the current user
          setFormData(prevData => ({
            ...prevData,
            [ownerField.field_name]: user.id.toString(), // Store ID for API
            [`${ownerField.field_name}_display`]: userChoice.custom_option // Store display text for UI
          }));
          console.log('Set default owner to current user:', user.id, userChoice.custom_option);
        }
      }
    }
  }, [fields, user]);

  // Fetch account fields from API
  const fetchAccountFields = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch fields from the fields endpoint
      console.log('Fetching account fields...');
      const response = await apiService.get<AccountFieldsResponse>(API_ENDPOINTS.ACCOUNT_FIELDS);
      
      console.log('Fields response status:', response.data?.status);
      
      if (!response.data || !response.data.accountFields) {
        throw new Error('Failed to fetch account fields');
      }

      // Sort fields by order property, with null values at the end
      const sortedFields = [...response.data.accountFields].sort((a, b) => {
        if (a.order === null) return 1;
        if (b.order === null) return -1;
        return a.order - b.order;
      });

      console.log(`Fetched ${sortedFields.length} account fields`);
      setFields(sortedFields);
      
      // Initialize form data with empty values
      const initialFormData: AccountFormData = { name: '' };
      sortedFields.forEach(field => {
        initialFormData[field.field_name] = field.field_value || '';
      });
      
      setFormData(initialFormData);
    } catch (err) {
      console.error('Error fetching account fields:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Handle text input changes
  const handleInputChange = (fieldName: string, value: string) => {
    setFormData(prevData => ({
      ...prevData,
      [fieldName]: value
    }));
  };

  const handleChange = (fieldId: string, value: string) => {
    // Find the field by ID to get the field_name
    const field = fields.find(f => f.id.toString() === fieldId);
    if (field) {
      setFormData(prev => ({
        ...prev,
        [field.field_name]: value
      }));

      // If user starts typing and there was a validation attempt,
      // check if this field is now valid and reset validation state if all required fields are filled
      if (hasAttemptedSubmit) {
        const updatedFormData = { ...formData, [field.field_name]: value };

        // Get fields to validate based on current visibility
        const fieldsToValidate = showAllFields
          ? fields.filter(field => field.active === 1 && field.required === 'on')
          : fields.filter(field => field.active === 1 && field.quick_add === "on" && field.required === 'on');

        const hasAllRequiredFields = fieldsToValidate
          .every(field => updatedFormData[field.field_name] && updatedFormData[field.field_name].trim() !== '');

        if (hasAllRequiredFields) {
          setHasAttemptedSubmit(false);
        }
      }
    }
  };
  // Handle dropdown selection
  const handleDropdownSelect = (field: AccountField, choice: AccountFieldChoice) => {
    // For owner fields, we need to store the ID, not the email
    const isOwnerField = field.field_name.toLowerCase().includes('owner') ||
                        field.field_label.toLowerCase().includes('owner') ||
                        field.lookup_type === 'users';

    if (isOwnerField) {
      // Store both the ID for API submission and display text for UI
      setFormData(prevData => ({
        ...prevData,
        [field.field_name]: choice.id.toString(), // Store ID for API
        [`${field.field_name}_display`]: choice.custom_option // Store display text for UI
      }));
    } else {
      // For other fields, store the custom_option as before
      setFormData(prevData => ({
        ...prevData,
        [field.field_name]: choice.custom_option
      }));
    }

    // Close dropdown after selection
    setShowDropdown(false);
  };

  // Function to get quick_add "on" fields
  const getQuickAddFields = () => {
    return fields
      .filter(field => field.active === 1 && field.quick_add === "on")
      .sort((a, b) => (a.order || 0) - (b.order || 0));
  };

  // Function to get quick_add "off" fields
  const getQuickAddOffFields = () => {
    return fields
      .filter(field => field.active === 1 && field.quick_add === "off")
      .sort((a, b) => (a.order || 0) - (b.order || 0));
  };

  // Function to render the toggle for showing all fields
  const renderFieldToggle = () => {
    const quickAddOffFields = getQuickAddOffFields();

    if (quickAddOffFields.length === 0) {
      return null; // Don't show toggle if there are no quick_add "off" fields
    }

    return (
      <View style={styles.toggleContainer}>
        <Text style={styles.toggleLabel}>Show all fields</Text>
        <TouchableOpacity
          style={[styles.toggleButton, showAllFields && styles.toggleButtonActive]}
          onPress={() => setShowAllFields(!showAllFields)}
        >
          <View style={[styles.toggleSlider, showAllFields && styles.toggleSliderActive]} />
        </TouchableOpacity>
      </View>
    );
  };

  const validateForm = () => {
    const errors: string[] = [];

    // Get fields to validate based on current visibility
    const fieldsToValidate = showAllFields
      ? fields.filter(field => field.active === 1)
      : fields.filter(field => field.active === 1 && field.quick_add === "on");

    fieldsToValidate.forEach(field => {
      if (field.required === 'on' && (!formData[field.field_name] || formData[field.field_name].trim() === '')) {
        errors.push(`${field.field_label} is required`);
      }
    });
    return errors;
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      // Set hasAttemptedSubmit to true to show validation errors
      setHasAttemptedSubmit(true);

      const errors = validateForm();
      if (errors.length > 0) {
        Alert.alert('Validation Error', errors[0]);
        return;
      }
      setSubmitting(true);

      // Prepare data for submission
      // Update field values with user input
      const sales_account = fields
        .filter(field => field.active === 1)
        .map(field => {
          let value = formData[field.field_name] || '';

          // For dropdown and lookup fields, we need to handle the value differently
          if ((field.field_type === FIELD_TYPES.DROPDOWN || field.field_type === FIELD_TYPES.LOOKUP) && value) {
            const selectedChoice = field.choices?.find(c => c.id.toString() === value);
            return {
              ...field,
              field_value: selectedChoice ? selectedChoice.id.toString() : value, // Pass the ID for API
            };
          }

          return {
            ...field,
            field_value: value,
          };
        });

      // Create the request payload with the sales_account array
      const requestData = {
        sales_account: sales_account
      };

      console.log('Submitting account data with structure: sales_account array ' + requestData);

      // Submit the form
      const response = await apiService.post(API_ENDPOINTS.SALES_ACCOUNTS, requestData);
      console.log('Response from API:', response.data);
      if (!response.data) {
        throw new Error('No response data received');
      }
      if (response.data.status === 201 || response.data.status === 200) {
        console.log('Account created successfully:', response.data);
        Alert.alert(
          'Success',
          response.data.message,
          [{ text: 'OK', onPress: () => { router.replace('/distributors'); } }]
        );
      } else {
        throw new Error(response.data.message);
      }
    } catch (err) {
      console.error('Error creating account:', err);

      // Extract error message from the nested structure
      let errorMessage = 'An error occurred';

      if (err && typeof err === 'object') {
        // Check for nested error structure: err.data.message
        if ((err as any).data?.message) {
          errorMessage = (err as any).data.message;
        }
        // Check for direct message property
        else if ((err as any).message) {
          errorMessage = (err as any).message;
        }
      }

      Alert.alert('Error', errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  // Render dropdown options
  const renderDropdownOptions = () => {
    if (!currentDropdownField) return null;
    
    return (
      <Modal
        visible={showDropdown}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowDropdown(false)}
      >
        <TouchableOpacity 
          style={styles.modalOverlay} 
          activeOpacity={1} 
          onPress={() => setShowDropdown(false)}
        >
          <View style={styles.dropdownContainer}>
            <View style={styles.dropdownHeader}>
              <Text style={styles.dropdownTitle}>{currentDropdownField.field_label}</Text>
              <TouchableOpacity onPress={() => setShowDropdown(false)}>
                <Ionicons name="close" size={24} color={colors.text.primary} />
              </TouchableOpacity>
            </View>
            
            <FlatList
              data={currentDropdownField.choices}
              keyExtractor={(item) => item.id.toString()}
              renderItem={({ item }) => {
                // Check if this is an owner field
                const isOwnerField = currentDropdownField.field_name.toLowerCase().includes('owner') ||
                                    currentDropdownField.field_label.toLowerCase().includes('owner') ||
                                    currentDropdownField.lookup_type === 'users';

                // Determine if this item is selected
                const isSelected = isOwnerField
                  ? formData[currentDropdownField.field_name] === item.id.toString()
                  : formData[currentDropdownField.field_name] === item.custom_option;

                return (
                  <TouchableOpacity
                    style={styles.dropdownItem}
                    onPress={() => handleDropdownSelect(currentDropdownField, item)}
                  >
                    <Text style={[
                      styles.dropdownOptionText,
                      isSelected && styles.selectedOptionText
                    ]}>
                      {item.custom_option}
                    </Text>
                    {isSelected && (
                      <Ionicons name="checkmark" size={20} color={colors.primary} />
                    )}
                  </TouchableOpacity>
                );
              }}
              ItemSeparatorComponent={() => <View style={styles.separator} />}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    );
  };

  // Render form fields based on field type
  const renderFormField = (field: AccountField) => {
    const isRequired = field.required === 'on';
    
    switch (field.field_type.toLowerCase()) {
      case 'dropdown':
           case 'lookup':
        return (
          <View key={field.id} style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>
              {field.field_label} {isRequired && <Text style={styles.requiredStar}>*</Text>}
            </Text>
            <TouchableOpacity
             activeOpacity={1}
              style={styles.dropdownField}
              onPress={() => {
                setCurrentDropdownField(field);
                setShowDropdown(true);
              }}
            >
              <Text style={[
                styles.dropdownText,
                !formData[field.field_name] && styles.placeholderText
              ]}>
                {(() => {
                  // For owner fields, show the display text instead of the ID
                  const isOwnerField = field.field_name.toLowerCase().includes('owner') ||
                                      field.field_label.toLowerCase().includes('owner') ||
                                      field.lookup_type === 'users';

                  if (isOwnerField && formData[`${field.field_name}_display`]) {
                    return formData[`${field.field_name}_display`];
                  }

                  return formData[field.field_name] || field.placeholder;
                })()}
              </Text>
              <Ionicons name="chevron-down" size={20} color={colors.text.secondary} />
            </TouchableOpacity>
          </View>
        );
        
      case 'text field':
        return (
          <View key={field.id} style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>
              {field.field_label} {isRequired && <Text style={styles.requiredStar}>*</Text>}
            </Text>
            <TextInput
              style={styles.textInput}
              placeholder={field.placeholder}
              value={formData[field.field_name]}
              onChangeText={(text) => handleInputChange(field.field_name, text)}
            />
          </View>
        );
        
      case 'number':
        return (
          <TouchableOpacity  style={[ {display:field.active === 1 ? 'flex' : 'none'}]}>
          <View key={field.id} style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>
              {field.field_label} {isRequired && <Text style={styles.requiredStar}>*</Text>}
            </Text>
            <TextInput
              style={styles.textInput}
              maxLength={getFieldMaxLength(field)}
              placeholder={field.placeholder}
              value={formData[field.field_name]}
              onChangeText={(text) => handleInputChange(field.field_name, text)}
              keyboardType="numeric"
            />
          </View>
          </TouchableOpacity>
        );
        

      default:
        return (
          <View key={field.id} style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>
              {field.field_label} {isRequired && <Text style={styles.requiredStar}>*</Text>}
            </Text>
            <TextInput
              style={styles.textInput}
              placeholder={field.placeholder}
              value={formData[field.field_name]}
              onChangeText={(text) => handleInputChange(field.field_name, text)}
            />
          </View>
        );
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0F96BB" />
        <Text style={styles.loadingText}>Loading form fields...</Text>
      </SafeAreaView>
    );
  }

  
const getFieldMaxLength = (field: AccountField): number => {
  switch (field.field_type) {
    case FIELD_TYPES.NUMBER:
      if(field.field_label.includes("Telephone")||field.field_label.includes("Mobile")||field.field_label.includes("Phone")||field.field_label.includes("contact")){
        return 10;
      }else{
        return 255;
      };
    case FIELD_TYPES.TEXTAREA:
      return 1000;
    case FIELD_TYPES.TEXT:
    default:
      return 255;
  }
};
  if (error) {
    return (
      <SafeAreaView style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchAccountFields}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color={colors.text.inverse} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Create Account</Text>
          <View style={{ width: 40 }} />
        </View>
        
        {/* Form Content */}
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.contentContainer}
          keyboardShouldPersistTaps="handled"
        >
          {Array.isArray(fields) && fields.length > 0 ? (
            fields.some(item => item.active === 1) ? (
              <>
                {/* Render toggle for showing all fields */}
                {renderFieldToggle()}

                {/* Render quick_add "on" fields first */}
                {getQuickAddFields().map((item) => (
                  <View key={item.id}>
                    <DynamicFormField
                      field={item}
                      value={formData[item.field_name] || ''}
                      onChangeText={(value) => handleChange(item.id.toString(), value)}
                      showRequired={hasAttemptedSubmit}
                      showAllFields={showAllFields}
                    />
                  </View>
                ))}

                {/* Render quick_add "off" fields if toggle is enabled */}
                {showAllFields && getQuickAddOffFields().map((item) => (
                  <View key={item.id}>
                    <DynamicFormField
                      field={item}
                      value={formData[item.field_name] || ''}
                      onChangeText={(value) => handleChange(item.id.toString(), value)}
                      showRequired={hasAttemptedSubmit}
                      showAllFields={showAllFields}
                    />
                  </View>
                ))}
              </>
            ) : (
              <View style={styles.centerContent}>
                <Text style={styles.noFieldsText}>No active fields available</Text>
                <Text style={styles.noFieldsSubText}>All fields are currently inactive</Text>
              </View>
            )
          ) : (
            <View style={styles.centerContent}>
              <Text style={styles.noFieldsText}>No form fields available</Text>
            </View>
          )}
        </ScrollView>
         <View style={styles.buttonContainer}>
        <TouchableOpacity
            style={[styles.submitButton, submitting && styles.submittingButton]}
            onPress={handleSubmit}
            disabled={submitting}
          >
            {submitting ? (
              <ActivityIndicator color="#fff" size="small" />
            ) : (
              <Text style={styles.submitButtonText}>Create Account</Text>
            )}
        </TouchableOpacity>
        </View>
        {/* Dropdown Options Modal */}
        {renderDropdownOptions()}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#555',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: 'red',
    marginBottom: 20,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#0F96BB',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#0F96BB',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 100,
  },
  fieldContainer: {
    marginBottom: 20,
  },
  fieldLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    color: '#333',
  },
  requiredStar: {
    color: 'red',
  },
  textInput: {
    backgroundColor: '#fff',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#ddd',
    fontSize: 16,
  },
  dropdownField: {
    backgroundColor: '#fff',
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#ddd',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dropdownText: {
    fontSize: 16,
    color: '#333',
  },
  placeholderText: {
    color: '#999',
  },
  
  buttonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  submitButton: {
    backgroundColor: '#0F96BB',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
  },
  submittingButton: {
    backgroundColor: '#0F96BB',
    opacity: 0.7,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  dropdownContainer: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    maxHeight: '80%',
  },
  dropdownHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  dropdownTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  dropdownItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  dropdownOptionText: {
    fontSize: 16,
    color: '#333',
  },
  selectedOptionText: {
    color: '#0F96BB',
    fontWeight: '500',
  },
  separator: {
    height: 1,
    backgroundColor: '#eee',
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  noFieldsText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 8,
  },
  noFieldsSubText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    opacity: 0.8,
  },
  toggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 16,
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  toggleLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  toggleButton: {
    width: 50,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#ddd',
    justifyContent: 'center',
    paddingHorizontal: 2,
  },
  toggleButtonActive: {
    backgroundColor: '#0F96BB',
  },
  toggleSlider: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  toggleSliderActive: {
    transform: [{ translateX: 22 }],
  },
});

export default CreateAccounts; 