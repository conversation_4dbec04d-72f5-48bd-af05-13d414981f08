import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, SafeAreaView, StatusBar, Platform, ActivityIndicator, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import { Task, isTaskOverdue } from './models/Task';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { showSnackbar, readUserScope, validateForEditAndDeleteActs } from './ui/utils';
import { LinearGradient } from 'expo-linear-gradient';
import useAttendanceValidation from '../hooks/useAttendanceValidation';

// Types for task fields response
interface TaskOutcome {
  id: number;
  outcome: string;
  salesactivities_id: number;
  created_at: string;
  updated_at: string;
}

interface TaskType {
  id: number;
  task_type: string;
  active: number;
  created_at: string | null;
  updated_at: string | null;
}

interface Owner {
  id: number;
  name: string;
  email: string;
  mobile: string;
}

interface RelatedEntity {
  id: number;
  name: string;
  email?: string;
  mobile?: string | null;
}

interface TaskFieldsResponse {
  status: number;
  message: string;
  tasks: {
    outcomes: TaskOutcome[];
    task_type: TaskType[];
    owners: Owner[];
    related_to: {
      contacts: RelatedEntity[];
      accounts: RelatedEntity[];
      deals: RelatedEntity[];
    };
  };
}

// Default task data structure (only used if no task data is passed)
const DEFAULT_TASK_DATA = {
  id: 0,
  task_type: '',
  title: 'No Title',
  description: '',
  due_date: '',
  due_time: '',
  outcome: null,
  mark_as_complete: 'false',
  completed_date: null,
  completed_time: null,
  user_id: 0,
  related_type: '',
  related_to: 0,
  collaboratoes: '',
  snooze_date: null,
  snooze_time: null,
  create_at: '',
  created_at: '',
  updated_at: ''
};

// Helper function to get first character for avatar
const getAvatarLetter = (name: string) => {
  return name && name.length > 0 ? name[0].toUpperCase() : '?';
};

export default function TaskDetailScreen() {
  const params = useLocalSearchParams();

  const [task, setTask] = useState<Task>(DEFAULT_TASK_DATA);
  const [loading, setLoading] = useState(true);
  const [taskFields, setTaskFields] = useState<TaskFieldsResponse['tasks'] | null>(null);
  const [isInitialized, setIsInitialized] = useState(false); // Flag to track initialization
  const [accessScope, setAccessScope] = useState<any>(null);
  const [canEdit, setCanEdit] = useState(false);
  const [canDelete, setCanDelete] = useState(false);
  const styles = createStyles();

  // Attendance validation hook
  const { validateAndExecute, canPerformActions, attendanceStatus } = useAttendanceValidation();

  // Add useEffect for access scope
  useEffect(() => {
    const fetchAccessScope = async () => {
      try {
        const scope = await readUserScope();
        setAccessScope(scope);
      } catch (error) {
        console.error("Error fetching access scope:", error);
        setAccessScope(null);
      }
    };
    fetchAccessScope();
  }, []);

  // Add refreshAccessScope function
  const refreshAccessScope = async (type: string) => {
    console.log("refreshAccessScope task:", JSON.stringify(accessScope));
    const ownerId = task.owner_id;
    const territoryId = task.territory_id;
    console.log("ownerId:", ownerId, "territoryId:", territoryId);

    const permission = type === 'delete' ? accessScope?.task?.delete?.toString() : accessScope?.task?.edit?.toString();
    console.log(`sdfdsfsdf${type} permission:`, permission);

    if (type === 'delete') {
      setCanDelete(permission);
    } else {
      setCanEdit(permission);
    }
  };

  // Update useEffect to check permissions when task or access scope changes
  useEffect(() => {
    if (accessScope && task.id) {
      refreshAccessScope('delete');
      refreshAccessScope('edit');
    }
  }, [accessScope, task]);

  // Parse task data from params only once during initialization
  useEffect(() => {
    if (isInitialized) return; // Skip if already initialized

    const fetchTaskById = async (id: string | number) => {
      try {
        console.log('Fetching task by ID:', id);
        setLoading(true);

        // Build the endpoint with the ID
        const endpoint = API_ENDPOINTS.TASK_DETAILS.replace(':id', String(id));
        console.log('Using endpoint:', endpoint);

        const response = await apiService.get<{ status: number; message: string; task: Task }>(endpoint);
        console.log('Task API response:', JSON.stringify(response.data));

        if (response.data && response.data.task) {
          console.log('Successfully fetched task:', response.data.task.title);
          setTask(response.data.task);
        } else {
          console.error('Task not found or invalid response format');
          Alert.alert('Error', 'Failed to load task details');
        }
      } catch (error) {
        console.error('Error fetching task by ID:', error);
        Alert.alert('Error', 'Could not load task details. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    console.log('TaskDetailScreen params:', params);

    if (params.task && typeof params.task === 'string') {
      try {
        console.log('Using serialized task data');
        const taskData = JSON.parse(params.task) as Task;
        setTask(taskData);
        setLoading(false);
      } catch (error) {
        console.error('Error parsing task data:', error);
        Alert.alert('Error', 'Invalid task data format');
      }
    } else if (params.taskId) {
      // If taskId is provided, fetch the task by ID
      console.log('Using taskId to fetch data:', params.taskId);
      // Convert array to string if needed
      const taskId = Array.isArray(params.taskId) ? params.taskId[0] : params.taskId;
      fetchTaskById(taskId);
    } else {
      console.error('No task data or taskId provided');
      Alert.alert('Error', 'No task information provided');
      setLoading(false);
    }

    setIsInitialized(true);
  }, [params, isInitialized]);

  // Separate useEffect to fetch task fields only once
  useEffect(() => {
    let isMounted = true; // Track if component is mounted

    const fetchData = async () => {
      try {
        const response = await apiService.get<TaskFieldsResponse>(`${API_ENDPOINTS.TASK_TYPE}`);
        if (isMounted && response.data?.tasks) {
          setTaskFields(response.data.tasks);
        }
      } catch (error) {
        console.error('Error fetching task fields:', error);
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchData();

    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, []);

  // Get task type name based on task_type ID
  const getTaskTypeName = (): string => {
    if (!taskFields || task.task_type === null || task.task_type === undefined) return '';

    const taskTypeStr = String(task.task_type);

    // First try to find by ID (if task_type is a number or string representation of a number)
    const taskTypeById = taskFields.task_type.find(
      type => type.id === parseInt(taskTypeStr, 10)
    );

    if (taskTypeById) {
      return taskTypeById.task_type;
    }

    // If not found by ID, try to find by name
    const taskTypeByName = taskFields.task_type.find(
      type => type.task_type.toLowerCase() === taskTypeStr.toLowerCase()
    );

    return taskTypeByName ? taskTypeByName.task_type : taskTypeStr;
  };

  // Get related entity name
  const getRelatedEntityName = (): string => {
    if (!taskFields || !task.related_type || !task.related_to) return '';

    // Map related_type to the corresponding key in the API response
    let relatedTypeKey = '';
    switch (task.related_type.toLowerCase()) {
      case 'contact':
      case 'contacts':
        relatedTypeKey = 'contacts';
        break;
      case 'account':
      case 'accounts':
        relatedTypeKey = 'accounts';
        break;
      case 'deal':
      case 'deals':
        relatedTypeKey = 'deals';
        break;
      default:
        relatedTypeKey = task.related_type;
        break;
    }

    // Check if the related_to object has the key we're looking for
    if (!taskFields.related_to[relatedTypeKey as keyof typeof taskFields.related_to]) {
      return `${task.related_type} #${task.related_to}`;
    }

    // Find the entity with the matching ID
    const entities = taskFields.related_to[relatedTypeKey as keyof typeof taskFields.related_to] as RelatedEntity[];
    const entity = entities.find(item => item.id === task.related_to);

    return entity ? entity.name : `${task.related_type} #${task.related_to}`;
  };

  // Get collaborator IDs as an array
  const getCollaboratorIds = (): number[] => {
    if (!task.collaboratoes) return [];

    try {
      // Handle different formats of collaborator data
      const collaboratorsStr = String(task.collaboratoes).trim();
      if (!collaboratorsStr) return [];

      // Case 1: Single ID (e.g., "143")
      if (!collaboratorsStr.includes(',') && !collaboratorsStr.includes('[')) {
        const id = parseInt(collaboratorsStr, 10);
        if (!isNaN(id)) {
          return [id];
        }
      }

      // Case 2: JSON array (e.g., "[143,145]")
      try {
        const parsed = JSON.parse(collaboratorsStr);
        if (Array.isArray(parsed)) {
          return parsed.map(id => typeof id === 'number' ? id : parseInt(String(id), 10))
            .filter(id => !isNaN(id));
        }
      } catch {
        // Case 3: Comma-separated string (e.g., "143,145")
        return collaboratorsStr.split(',')
          .map(id => id.trim())
          .filter(id => id)
          .map(id => parseInt(id, 10))
          .filter(id => !isNaN(id));
      }

      return [];
    } catch (e) {
      console.error('Error parsing collaborators:', e);
      return [];
    }
  };

  // Get a single collaborator's name by ID
  const getCollaboratorName = (id: number): string => {
    if (!taskFields) return `User #${id}`;

    const owner = taskFields.owners.find(owner => owner.id === id);
    return owner ? owner.name : `User #${id}`;
  };

  const handleBack = () => {
    // Check if we came from task creation screen
    const fromCreate = params.from === 'create';

    if (fromCreate) {
      // If coming from task creation, navigate to TaskListScreen
      console.log('Navigating to TaskListScreen after task creation');
      router.replace('/TaskListScreen' as any); // Use replace instead of push to prevent navigation stack issues
    } else {
      // Otherwise use normal back behavior
      console.log('Using normal back navigation');
      router.back();
    }
  };

  const handleEdit = () => {
    if (!canEdit) {
      showSnackbar('You do not have permission to edit this task');
      return;
    }

    console.log('Edit task');
    router.push({
      pathname: '/tasks/create' as any,
      params: { task: JSON.stringify(task), mode: 'edit' }
    });
  };

  const handleMarkComplete = async () => {
    const actionName = task.mark_as_complete === "true" ? "mark task as incomplete" : "mark task as complete";

    await validateAndExecute(async () => {
      try {
        setLoading(true);
        const newStatus = task.mark_as_complete === "true" ? "false" : "true";

        // Use API_ENDPOINTS and apiService for consistency
        const endpoint = API_ENDPOINTS.TASK_UPDATE.replace(':id', task.id.toString());
        const response = await apiService.put(endpoint, {
          mark_as_complete: newStatus
        });

        if (response.data && response.data.status === 200) {
          // Update local state
          setTask({
            ...task,
            mark_as_complete: newStatus,
            completed_date: newStatus === "true" ? new Date().toISOString().split('T')[0] : null,
            completed_time: newStatus === "true" ? new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true }) : null
          });

          // Show success message
          showSnackbar(`Task ${newStatus === "true" ? "completed" : "marked as incomplete"} successfully`);
        } else {
          Alert.alert('Error', 'Failed to update task status');
        }
      } catch (error) {
        console.error('Error updating task status:', error);
        Alert.alert('Error', 'Could not update task status. Please try again.');
      } finally {
        setLoading(false);
      }
    }, actionName);
  };

  const handleDelete = () => {
    if (!canDelete) {
      showSnackbar('You do not have permission to delete this task');
      return;
    }

    Alert.alert(
      'Delete Task',
      'Are you sure you want to delete this task? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => deleteTask()
        },
      ]
    );
  };

  // Add function to delete account
  const deleteTask = async () => {
    if (!task.id) {
      showSnackbar('Task ID is missing');
      return;
    }

    try {
      // Show loading indicator or disable UI interactions here if needed
      const endpoint = API_ENDPOINTS.TASK_DETAILS.replace(':id', task.id.toString());
      console.log('endpointsdsd', endpoint);
      const response = await apiService.delete(endpoint);
      console.log('DeleteTaskResponse', response);
      if (response.status === 200 || response.status === 204) {
        //showSnackbar('Task deleted successfully');
        // Navigate back to the Task list
        //router.replace('/TaskListScreen');
        router.back();
      } else {
        throw new Error(response.data?.message || 'Failed to delete task');
      }
    } catch (error) {
      console.error('Error deleting account:', error);
      showSnackbar('Failed to delete task. Please try again.');
    }
  };

  // Helper function to format time in 12-hour format
  const formatTimeTo12Hour = (timeString: string): string => {
    if (!timeString) return 'No time';

    try {
      // Check if time already contains AM/PM
      if (timeString.toLowerCase().includes('am') || timeString.toLowerCase().includes('pm')) {
        return timeString;
      }

      // Parse the time string (expected format: HH:MM:SS or HH:MM)
      const timeParts = timeString.split(':');
      if (timeParts.length >= 2) {
        const hour = parseInt(timeParts[0], 10);
        const minute = parseInt(timeParts[1], 10);

        if (!isNaN(hour) && !isNaN(minute)) {
          const isPM = hour >= 12;
          const hour12 = hour % 12 || 12; // Convert 0 to 12 for 12 AM
          return `${hour12}:${minute.toString().padStart(2, '0')} ${isPM ? 'PM' : 'AM'}`;
        }
      }

      return timeString; // Fallback to original if parsing fails
    } catch (error) {
      console.error('Error formatting time:', error);
      return timeString;
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor="#0F96BB" barStyle="light-content" />
      <SafeAreaView style={{ flex: 1 }}>
        {/* Modern Header with Gradient */}
        <LinearGradient
          colors={['#0F96BB', '#0a7ea4']}
          style={styles.header}
        >
          <TouchableOpacity
            onPress={handleBack}
            style={styles.backButton}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>

          <Text style={styles.headerTitle}>Task Details</Text>

          <View style={styles.headerActions}>
            {task.mark_as_complete !== "true" && canEdit && (
              <TouchableOpacity
                onPress={handleEdit}
                style={styles.headerButton}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Ionicons name="pencil" size={22} color="#FFFFFF" />
              </TouchableOpacity>
            )}

            {task.mark_as_complete === "true" || canDelete && (
              <TouchableOpacity
                onPress={handleDelete}
                style={styles.headerButton}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Ionicons name="trash-outline" size={24} color="#FFFFFF" />
              </TouchableOpacity>
            )}
          </View>
        </LinearGradient>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#0F96BB" />
            <Text style={styles.loadingText}>Loading task details...</Text>
          </View>
        ) : (
          <>
            <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
              {/* Task Title Card */}
              <View style={styles.titleCard}>
                <View style={styles.titleHeader}>
                  <View style={styles.taskIconContainer}>
                    <Ionicons
                      name={task.mark_as_complete === "true" ? "checkmark-circle" : "calendar"}
                      size={28}
                      color={task.mark_as_complete === "true" ? "#4CAF50" : "#0F96BB"}
                    />
                  </View>
                  <View style={styles.titleContent}>
                    <Text style={styles.title}>{task.title}</Text>

                    {/* Status Badges */}
                    <View style={styles.statusRow}>
                      <View style={[
                        styles.statusBadge,
                        isTaskOverdue(task) ? styles.overdueBadge :
                        task.mark_as_complete === "true" ? styles.completedBadge : styles.pendingBadge
                      ]}>
                        <Ionicons
                          name={
                            task.mark_as_complete === "true" ? "checkmark-circle" :
                            isTaskOverdue(task) ? "alert-circle" : "time"
                          }
                          size={14}
                          color="white"
                        />
                        <Text style={styles.statusText}>
                          {task.mark_as_complete === "true" ? "Completed" :
                           isTaskOverdue(task) ? "Overdue" : "Pending"}
                        </Text>
                      </View>

                      {task.task_type && (
                        <View style={styles.taskTypeChip}>
                          <Text style={styles.taskTypeText} numberOfLines={1}>
                            {getTaskTypeName()}
                          </Text>
                        </View>
                      )}
                    </View>


                  </View>
                </View>
              </View>

              {/* Details Cards */}
              <View style={styles.detailsContainer}>
                {/* Time Details Card */}
                {(task.due_date || task.due_time || task.created_at) && (
                  <View style={styles.detailCard}>
                    <View style={styles.detailCardHeader}>
                      <View style={styles.detailIconWrapper}>
                        <Ionicons name="time-outline" size={20} color="#0F96BB" />
                      </View>
                      <Text style={styles.detailCardTitle}>Time Information</Text>
                    </View>

                    <View style={styles.detailCardContent}>
                      {task.due_date && (
                        <View style={styles.timeDetailRow}>
                          <Ionicons name="calendar-outline" size={16} color="#666" />
                          <Text style={styles.timeDetailLabel}>Due Date:</Text>
                          <Text style={[
                            styles.timeDetailValue,
                            isTaskOverdue(task) && styles.overdueText
                          ]}>
                            {new Date(task.due_date).toLocaleDateString('en-US', {
                              weekday: 'short',
                              month: 'short',
                              day: 'numeric',
                              year: 'numeric'
                            })}
                          </Text>
                        </View>
                      )}

                      {task.due_time && (
                        <View style={styles.timeDetailRow}>
                          <Ionicons name="time-outline" size={16} color="#666" />
                          <Text style={styles.timeDetailLabel}>Due Time:</Text>
                          <Text style={[
                            styles.timeDetailValue,
                            isTaskOverdue(task) && styles.overdueText
                          ]}>
                            {formatTimeTo12Hour(task.due_time)}
                          </Text>
                        </View>
                      )}

                      {task.created_at && (
                        <View style={styles.timeDetailRow}>
                          <Ionicons name="add-circle-outline" size={16} color="#666" />
                          <Text style={styles.timeDetailLabel}>Created:</Text>
                          <Text style={styles.timeDetailValue}>
                            {new Date(task.created_at).toLocaleDateString('en-US', {
                              month: 'short',
                              day: 'numeric',
                              year: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit',
                              hour12: true
                            })}
                          </Text>
                        </View>
                      )}

                      {task.mark_as_complete === "true" && task.completed_date && (
                        <View style={styles.timeDetailRow}>
                          <Ionicons name="checkmark-circle-outline" size={16} color="#4CAF50" />
                          <Text style={styles.timeDetailLabel}>Completed:</Text>
                          <Text style={[styles.timeDetailValue, { color: '#4CAF50' }]}>
                            {new Date(task.completed_date).toLocaleDateString('en-US', {
                              month: 'short',
                              day: 'numeric',
                              year: 'numeric'
                            })}
                            {task.completed_time && ` at ${formatTimeTo12Hour(task.completed_time)}`}
                          </Text>
                        </View>
                      )}
                    </View>
                  </View>
                )}

                {task.related_type && (
                  <View style={styles.detailCard}>
                    <View style={styles.detailCardHeader}>
                      <View style={styles.detailIconWrapper}>
                        <Ionicons name="person-outline" size={20} color="#0F96BB" />
                      </View>
                      <Text style={styles.detailCardTitle}>Related to</Text>
                    </View>

                    <View style={styles.detailCardContent}>
                      <Text style={styles.detailSubtitle}>
                        {task.related_type.charAt(0).toUpperCase() + task.related_type.slice(1)}
                      </Text>

                      <View style={styles.contactRow}>
                        <View style={styles.contactAvatar}>
                          <Text style={styles.contactAvatarText}>
                            {getAvatarLetter(getRelatedEntityName())}
                          </Text>
                        </View>
                        <Text style={styles.contactName}>{getRelatedEntityName()}</Text>
                      </View>
                    </View>
                  </View>
                )}

                {task.task_type && (
                  <View style={styles.detailCard}>
                    <View style={styles.detailCardHeader}>
                      <View style={styles.detailIconWrapper}>
                        <Ionicons name="document-text-outline" size={20} color="#0F96BB" />
                      </View>
                      <Text style={styles.detailCardTitle}>Task Type</Text>
                    </View>

                    <View style={styles.detailCardContent}>
                      <Text style={styles.detailValue}>{getTaskTypeName()}</Text>
                    </View>
                  </View>
                )}

                {task.collaboratoes && (
                  <View style={styles.detailCard}>
                    <View style={styles.detailCardHeader}>
                      <View style={styles.detailIconWrapper}>
                        <Ionicons name="people-outline" size={20} color="#0F96BB" />
                      </View>
                      <Text style={styles.detailCardTitle}>Collaborators</Text>
                    </View>

                    <View style={styles.detailCardContent}>
                      {loading ? (
                        <ActivityIndicator size="small" color="#0F96BB" />
                      ) : getCollaboratorIds().length > 0 ? (
                        getCollaboratorIds().map((id, index) => (
                          <View key={index} style={styles.collaboratorRow}>
                            <View style={styles.collaboratorAvatar}>
                              <Text style={styles.collaboratorAvatarText}>
                                {getAvatarLetter(getCollaboratorName(id))}
                              </Text>
                            </View>
                            <Text style={styles.collaboratorName}>{getCollaboratorName(id)}</Text>
                          </View>
                        ))
                      ) : (
                        <Text style={styles.detailValue}>{String(task.collaboratoes)}</Text>
                      )}
                    </View>
                  </View>
                )}

                {task.description && (
                  <View style={styles.detailCard}>
                    <View style={styles.detailCardHeader}>
                      <View style={styles.detailIconWrapper}>
                        <Ionicons name="document-text-outline" size={20} color="#0F96BB" />
                      </View>
                      <Text style={styles.detailCardTitle}>Description</Text>
                    </View>

                    <View style={styles.detailCardContent}>
                      <Text style={styles.descriptionText}>{task.description}</Text>
                    </View>
                  </View>
                )}
              </View>
            </ScrollView>

            {/* Modern Action Button */}
            <View style={styles.actionContainer}>
              <TouchableOpacity
                onPress={handleMarkComplete}
                style={[
                  styles.actionButton,
                  task.mark_as_complete === "true" ? styles.incompleteButton : styles.completeButton
                ]}
                activeOpacity={0.8}
              >
                <Ionicons
                  name={task.mark_as_complete === "true" ? "close-circle" : "checkmark-circle"}
                  size={22}
                  color="white"
                />
                <Text style={styles.actionButtonText}>
                  {task.mark_as_complete === "true" ? "Mark Incomplete" : "Mark Complete"}
                </Text>
              </TouchableOpacity>
            </View>


          </>
        )}
      </SafeAreaView>
    </View>
  );
}const createStyles = () => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    ...Platform.select({
      android: {
        paddingTop: StatusBar.currentHeight
      }
    })
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    height: 56, // Match TaskListScreen app bar height
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#FFFFFF',
    position: 'absolute',
    left: 0,
    right: 0,
    textAlign: 'center',
    zIndex: 1,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
    minWidth: 40, // Fixed width for consistent spacing
    zIndex: 2, // Ensure button is above the title
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 0, // Remove right padding to bring more button closer to edge
    zIndex: 2, // Ensure buttons are above the title
  },
  headerButton: {
    padding: 8,
    marginLeft: 2, // Further reduced spacing between edit and more buttons
  },
  content: {
    flex: 1,
    paddingTop: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },

  // Title Card Styles
  titleCard: {
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 16,
    paddingHorizontal: 20,
    paddingVertical: 12,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  titleHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  taskIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#f0f8ff',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  titleContent: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1a1a1a',
    marginBottom: 12,
    lineHeight: 26,
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    flexWrap: 'wrap',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginRight: 8,
    marginBottom: 4,
  },
  completedBadge: {
    backgroundColor: '#4CAF50',
  },
  overdueBadge: {
    backgroundColor: '#F44336',
  },
  pendingBadge: {
    backgroundColor: '#FF9800',
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  taskTypeChip: {
    backgroundColor: '#e3f2fd',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginBottom: 4,
  },
  taskTypeText: {
    fontSize: 12,
    color: '#1976d2',
    fontWeight: '600',
  },
  dateTimeSection: {
    marginTop: 4,
  },
  dueDateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  dueDateText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 6,
    fontWeight: '500',
  },
  dueTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 22, // Align with date text
  },
  dueTimeText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 6,
    fontWeight: '500',
  },
  completionTimeSection: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  completionTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8fff8',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#4CAF50',
  },
  completionTimeLabel: {
    fontSize: 12,
    color: '#4CAF50',
    fontWeight: '600',
    marginLeft: 6,
    marginRight: 8,
  },
  completionTimeText: {
    fontSize: 12,
    color: '#4CAF50',
    fontWeight: '500',
    marginLeft: 4,
  },
  overdueText: {
    color: '#F44336',
    fontWeight: '600',
  },

  // Detail Cards Styles
  detailsContainer: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  detailCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
  },
  detailCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
  },
  detailIconWrapper: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#f0f8ff',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  detailCardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  detailCardContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  detailSubtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  detailValue: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  contactRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  contactAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#0F96BB',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  contactAvatarText: {
    fontSize: 14,
    fontWeight: '600',
    color: 'white',
  },
  contactName: {
    fontSize: 16,
    color: '#0F96BB',
    fontWeight: '500',
    flex: 1,
  },
  collaboratorRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  collaboratorAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#0F96BB',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  collaboratorAvatarText: {
    fontSize: 12,
    fontWeight: '600',
    color: 'white',
  },
  collaboratorName: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  descriptionText: {
    fontSize: 15,
    color: '#333',
    lineHeight: 22,
  },

  // Time Detail Styles
  timeDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    paddingVertical: 4,
  },
  timeDetailLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
    marginLeft: 8,
    marginRight: 8,
    minWidth: 80,
  },
  timeDetailValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
    flex: 1,
  },

  // Action Button Styles
  actionContainer: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  completeButton: {
    backgroundColor: '#4CAF50',
  },
  incompleteButton: {
    backgroundColor: '#FF9800',
  },
  actionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  optionsContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  optionsBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  optionsMenu: {
    width: '80%',
    backgroundColor: '#fff',
    borderRadius: 4,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  optionItem: {
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  optionText: {
    fontSize: 16,
    color: '#333',
  },
});

