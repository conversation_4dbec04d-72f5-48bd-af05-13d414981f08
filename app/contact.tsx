import React, { useState, useMemo, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  StatusBar,
  Button
} from 'react-native';
import { SafeAreaView, Edge } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useLocalSearchParams, useRouter, useGlobalSearchParams } from 'expo-router';
import { Contact } from './models/Contact';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { useColors } from '@/hooks/useThemeColor';
import { DynamicFormField } from './dynamic_fields/DynamicFormField';
import { ContactField, FIELD_TYPES } from './models/contact_fields_model';
import { ContactFieldsResponse } from './models/contact_fields_model';
import { showSnackbar } from './ui/utils';
import { getFieldValue } from './models/model_single_contact_details';
import { useAuth } from '../context/AuthContext';


export default function ContactScreen() {
  const [loading, setLoading] = useState(false);
  const [formFields, setFormFields] = useState<ContactField[]>([]);
  const [formData, setFormData] = useState<Record<string, string>>({});
  const [loadingFields, setLoadingFields] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false);
  const [showAllFields, setShowAllFields] = useState(false); // Toggle for showing quick_add "off" fields
  const { user } = useAuth(); // Get the current logged-in user

  const params = useGlobalSearchParams();
  const router = useRouter();
  const colors = useColors();
  const styles = useMemo(() => createStyles(colors), [colors]);

  // Add state for account details
  const [accountDetails, setAccountDetails] = useState<any>(null);
  const [callFrom, setCallFrom] = useState<string | null>(null);
  const [accountId, setAccountId] = useState<string | null>(null);

  // Add state to track if account was auto-selected
  const [autoSelectedAccount, setAutoSelectedAccount] = useState<boolean>(false);
  const [autoSelectedAccountName, setAutoSelectedAccountName] = useState<string>('');

  useEffect(() => {
    fetchDynamicFields();
  }, []);

  // Set owner_id when form fields are loaded
  useEffect(() => {
    if (formFields.length > 0 && user) {
      // Find the owner_id field
      const ownerField = formFields.find(field => field.field_name === 'owner_id');

      if (ownerField && !isEditMode) {
        // Set the owner_id to the current user's ID for new contacts
        setFormData(prev => ({
          ...prev,
          [ownerField.id]: user.id.toString()
        }));
        console.log('Set owner_id to current user:', user.id);
      }
    }
  }, [formFields, user, isEditMode]);

  useEffect(() => {
    if (params.contact) {
      try {
        const contactData = JSON.parse(params.contact as string);
        console.log('Received contact data:', contactData);
        setIsEditMode(true);

        // Wait for form fields to be loaded before setting values
        if (formFields.length > 0) {
          const initialData: Record<string, string> = {};

          // Map contact data to form fields
          formFields.forEach(field => {
            // Get the field_name from the field
            const fieldName = field.field_name;
            const fName = getFieldValue(contactData, fieldName);
            // Check if contact data has a value for this field
            if (fName !== null && fName !== undefined) {
              // For dropdown and lookup fields, we need to handle the value differently
              if (field.field_type === FIELD_TYPES.DROPDOWN || field.field_type === FIELD_TYPES.LOOKUP) {
                // Find the choice that matches the contact data value
                const choice = field.choices?.find(c =>
                  c.custom_option === fName ||
                  c.id.toString() === fName.toString()
                );
                if (choice) {
                  initialData[field.id] = choice.id.toString();
                } else {
                  // If no choice found but we have a value, it might be an ID already
                  initialData[field.id] = fName.toString();
                }
              } else {
                // For other field types, just use the value directly
                initialData[field.id] = fName.toString();
              }
            }
          });

          console.log('Setting initial form data:', initialData);
          setFormData(initialData);
        }
      } catch (error) {
        console.error('Error parsing contact data:', error);
        showSnackbar('Error loading contact data');
      }
    }
  }, [params.contact, formFields]); // Add formFields as dependency

  // Update the header title based on mode and reset validation state
  useEffect(() => {
    router.setParams({
      title: isEditMode ? 'Edit Contact' : 'Add Contact'
    });
    // Reset validation state when switching modes
    setHasAttemptedSubmit(false);
  }, [isEditMode]);

  // Extract navigation parameters with more detailed logging
  useEffect(() => {
    console.log('=== CONTACT SCREEN NAVIGATION PARAMS ===');
    console.log('All params received:', JSON.stringify(params, null, 2));
    console.log('Navigation parameters received:', {
      callFrom: params.callFrom,
      id: params.id,
      accountId: params.accountId,
      source: params.source,
      hasAccountDetails: params.accountDetails ? 'yes' : 'no'
    });

    if (params.callFrom) {
      setCallFrom(params.callFrom as string);
      console.log('✅ Set callFrom to:', params.callFrom);
    }

    // Check both params.id and params.accountId
    const accountIdFromParams = params.id || params.accountId;
    if (accountIdFromParams) {
      setAccountId(accountIdFromParams as string);
      console.log('✅ Set accountId to:', accountIdFromParams);
    }

    if (params.accountDetails) {
      try {
        // Handle both string and object formats
        let parsedAccountDetails;
        if (typeof params.accountDetails === 'string') {
          parsedAccountDetails = JSON.parse(params.accountDetails);
        } else {
          parsedAccountDetails = params.accountDetails;
        }

        setAccountDetails(parsedAccountDetails);
        console.log('✅ Received account details:', JSON.stringify(parsedAccountDetails, null, 2));

        // Try to extract account info immediately for debugging
        const accountInfo = getAccountInfo(parsedAccountDetails);
        console.log('✅ Extracted account info:', accountInfo);
      } catch (error) {
        console.error('❌ Error parsing account details:', error);
      }
    } else {
      console.log('⚠️ No accountDetails parameter received');
    }
    console.log('=== END NAVIGATION PARAMS ===');

    // Temporary alert for debugging
    if (params.callFrom === 'accountDetail') {
      setTimeout(() => {
        // Alert.alert(
        //   'Debug: Navigation Params',
        //   `callFrom: ${params.callFrom}\naccountId: ${params.accountId || params.id}\nsource: ${params.source}\nhasAccountDetails: ${params.accountDetails ? 'Yes' : 'No'}`,
        //   [{ text: 'OK' }]
        // );
      }, 1000);
    }
  }, [params.callFrom, params.id, params.accountId, params.accountDetails, params.source]);

  // Enhanced helper function to identify the account field with more logging
  const findAccountField = (fields: ContactField[]): ContactField | undefined => {
    console.log('Searching for account field among', fields.length, 'fields');

    // Log all field names and labels for debugging
    fields.forEach(field => {
      console.log(`Field ${field.id}: ${field.field_name} (${field.field_label}) - Type: ${field.field_type}`);
      if (field.field_type === FIELD_TYPES.DROPDOWN && field.choices) {
        console.log(`  Has ${field.choices.length} choices`);
      }
    });

    // First, try to find by exact field name match
    const exactNameMatches = ['account_id', 'sales_account_id', 'account', 'accountId', 'salesAccountId'];
    let accountField = fields.find(field =>
      exactNameMatches.includes(field.field_name)
    );

    if (accountField) {
      console.log('Found account field by exact name match:', accountField.field_name);
      return accountField;
    }

    // If not found, try by field label
    const labelKeywords = ['account', 'company', 'organization', 'business'];
    accountField = fields.find(field =>
      labelKeywords.some(keyword => field.field_label.toLowerCase().includes(keyword))
    );

    if (accountField) {
      console.log('Found account field by label match:', accountField.field_label);
      return accountField;
    }

    // If still not found, try by dropdown choices
    accountField = fields.find(field =>
      field.field_type === FIELD_TYPES.DROPDOWN &&
      field.choices?.some(choice =>
        choice.custom_option && labelKeywords.some(keyword =>
          choice.custom_option.toLowerCase().includes(keyword)
        )
      )
    );

    if (accountField) {
      console.log('Found account field by dropdown choices');
      return accountField;
    }

    console.log('No account field found');
    return undefined;
  };

  // Extract account information from accountDetails with format flexibility
  const getAccountInfo = (accountDetails: any) => {
    if (!accountDetails) return null;

    // Try different possible structures
    if (accountDetails.account) {
      return {
        id: accountDetails.account.id?.toString() || '',
        name: accountDetails.account.name || ''
      };
    } else if (accountDetails.id && (accountDetails.name || accountDetails.account_name)) {
      return {
        id: accountDetails.id.toString(),
        name: accountDetails.name || accountDetails.account_name || ''
      };
    } else if (typeof accountDetails === 'object') {
      // Look for id and name properties at any level
      const findProperty = (obj: any, propName: string): any => {
        if (!obj || typeof obj !== 'object') return null;

        if (obj[propName] !== undefined) return obj[propName];

        for (const key in obj) {
          if (typeof obj[key] === 'object') {
            const result = findProperty(obj[key], propName);
            if (result !== null) return result;
          }
        }

        return null;
      };

      const id = findProperty(accountDetails, 'id');
      const name = findProperty(accountDetails, 'name') ||
        findProperty(accountDetails, 'account_name') ||
        findProperty(accountDetails, 'accountName');

      if (id || name) {
        return {
          id: id?.toString() || '',
          name: name || ''
        };
      }
    }

    return null;
  };

  // Add a function to handle setting account value based on field type
  const setAccountValueByFieldType = (field: ContactField, accountId: string | null, accountInfo: any | null) => {
    console.log('=== SETTING ACCOUNT VALUE ===');
    console.log('Field:', field.field_name, '- Type:', field.field_type);
    console.log('Account ID:', accountId);
    console.log('Account Info:', accountInfo);

    // For dropdown fields
    if (field.field_type === FIELD_TYPES.DROPDOWN && field.choices) {
      console.log('Processing dropdown field with', field.choices.length, 'choices');

      // Log all choices for debugging
      field.choices.forEach((choice, index) => {
        console.log(`Choice ${index}: ID=${choice.id}, Option="${choice.custom_option}"`);
      });

      // Try to find by ID first
      let accountChoice;

      if (accountId) {
        console.log('Searching by account ID:', accountId);
        accountChoice = field.choices.find(choice => choice.id.toString() === accountId);
        console.log('Found by ID:', accountChoice ? accountChoice.custom_option : 'not found');
      }

      // Then try by account info
      if (!accountChoice && accountInfo && accountInfo.id) {
        console.log('Searching by account info ID:', accountInfo.id);
        accountChoice = field.choices.find(choice => choice.id.toString() === accountInfo.id);
        console.log('Found by info ID:', accountChoice ? accountChoice.custom_option : 'not found');
      }

      // Then try by name
      if (!accountChoice && accountInfo && accountInfo.name) {
        console.log('Searching by account name:', accountInfo.name);
        accountChoice = field.choices.find(choice =>
          choice.custom_option &&
          (choice.custom_option.toLowerCase() === accountInfo.name.toLowerCase() ||
            choice.custom_option.toLowerCase().includes(accountInfo.name.toLowerCase()))
        );
        console.log('Found by name:', accountChoice ? accountChoice.custom_option : 'not found');
      }

      if (accountChoice) {
        console.log('✅ Setting dropdown account to:', accountChoice.custom_option);
        setFormData(prev => ({
          ...prev,
          [field.id]: accountChoice.id.toString()
        }));
        setAutoSelectedAccount(true);
        setAutoSelectedAccountName(accountChoice.custom_option);
        console.log('✅ Account value set successfully');
        return true;
      } else {
        console.log('❌ Could not find matching account in dropdown choices');
        return false;
      }
    }
    // For text fields
    else if (field.field_type === FIELD_TYPES.TEXT || field.field_type === FIELD_TYPES.NUMBER) {
      const valueToSet = accountId || (accountInfo && accountInfo.id ? accountInfo.id : '');

      if (valueToSet) {
        console.log('Setting text/number field to:', valueToSet);
        setFormData(prev => ({
          ...prev,
          [field.id]: valueToSet
        }));
        setAutoSelectedAccount(true);
        setAutoSelectedAccountName(accountInfo && accountInfo.name ? accountInfo.name : 'Selected Account');
        return true;
      }
      return false;
    }
    // For other field types
    else {
      console.log('Unsupported field type for account:', field.field_type);

      // Try setting the ID anyway as a fallback
      if (accountId) {
        console.log('Setting fallback value for unsupported field type:', accountId);
        setFormData(prev => ({
          ...prev,
          [field.id]: accountId
        }));
        setAutoSelectedAccount(true);
        setAutoSelectedAccountName(accountInfo && accountInfo.name ? accountInfo.name : 'Selected Account');
        return true;
      }
      return false;
    }
  };

  // Improved account selection logic with better debugging
  useEffect(() => {
    console.log('=== ACCOUNT SELECTION EFFECT ===');
    console.log('Account selection effect triggered with:', {
      callFrom,
      hasAccountDetails: accountDetails ? 'yes' : 'no',
      formFieldsLoaded: formFields.length > 0,
      isEditMode,
      accountId
    });

    if (callFrom === 'accountDetail' && formFields.length > 0 && !isEditMode) {
      console.log('✅ Conditions met - attempting to set account from accountDetail page');

      // Find the account field using our helper function
      const accountField = findAccountField(formFields);
      console.log('Account field found:', accountField ?
        `${accountField.id}: ${accountField.field_name} (${accountField.field_label}) - Type: ${accountField.field_type}` : 'none');

      // Get account info with format flexibility
      const accountInfo = accountDetails ? getAccountInfo(accountDetails) : null;
      console.log('Account info extracted:', accountInfo);

      if (accountField && (accountInfo || accountId)) {
        console.log('✅ Proceeding with account selection...');
        // Use the helper function to set the account value
        const success = setAccountValueByFieldType(accountField, accountId, accountInfo);
        console.log('Account selection result:', success ? 'SUCCESS' : 'FAILED');
      } else {
        if (!accountField) {
          console.log('❌ No suitable account field found. Available fields:',
            formFields.map(f => `${f.id}: ${f.field_name} (${f.field_label}) - Type: ${f.field_type}`).join(', '));
        }
        if (!accountInfo && !accountId) {
          console.log('❌ No account info or ID available');
        }
      }
    } else {
      console.log('⚠️ Conditions not met for account selection:', {
        callFromMatch: callFrom === 'accountDetail',
        hasFormFields: formFields.length > 0,
        notEditMode: !isEditMode
      });
    }
    console.log('=== END ACCOUNT SELECTION EFFECT ===');
  }, [callFrom, accountDetails, formFields, isEditMode, accountId]);

  // Monitor formData changes to verify account selection
  useEffect(() => {
    if (callFrom === 'accountDetail' && formFields.length > 0) {
      const accountField = findAccountField(formFields);
      if (accountField) {
        const accountValue = formData[accountField.id];
        console.log('Current account field value:', accountValue);

        if (accountValue) {
          // If it's a dropdown, get the display name
          if (accountField.field_type === FIELD_TYPES.DROPDOWN && accountField.choices) {
            const selectedChoice = accountField.choices.find(c => c.id.toString() === accountValue);
            if (selectedChoice) {
              console.log('Selected account:', selectedChoice.custom_option);
            }
          }
        }
      }
    }
  }, [formData, callFrom, formFields]);

  // Add a direct account selection function that can be called from other places
  const setAccountDirectly = () => {
    if (callFrom === 'accountDetail' && formFields.length > 0 && !isEditMode) {
      console.log('Manually setting account...');

      const accountField = findAccountField(formFields);
      if (!accountField) {
        console.log('No account field found');
        return;
      }

      // Try to get account info
      const accountInfo = accountDetails ? getAccountInfo(accountDetails) : null;

      // Use the helper function to set the account value
      const success = setAccountValueByFieldType(accountField, accountId, accountInfo);

      if (success) {
        console.log('Successfully set account value');
      } else {
        console.log('Failed to set account value');
      }
    }
  };

  // Call the direct account selection function after fields are loaded
  useEffect(() => {
    if (formFields.length > 0 && callFrom === 'accountDetail' && !isEditMode) {
      // Small delay to ensure all state is properly updated
      const timer = setTimeout(() => {
        setAccountDirectly();
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [formFields, callFrom, isEditMode]);

  // Add a debug button to manually trigger account selection
  const renderDebugButton = () => {
    if (__DEV__ && callFrom === 'accountDetail') {
      return (
        <View style={{ padding: 10, backgroundColor: '#f0f0f0', marginBottom: 10 }}>
          <Text style={{ fontSize: 12, color: '#666', marginBottom: 5 }}>
            Debug: Account Selection Status
          </Text>
          <Text style={{ fontSize: 12, color: '#666', marginBottom: 5 }}>
            Account ID: {accountId || 'Not set'}
          </Text>
          <Text style={{ fontSize: 12, color: '#666', marginBottom: 5 }}>
            Account Details: {accountDetails ? 'Present' : 'Not present'}
          </Text>
          <Text style={{ fontSize: 12, color: '#666', marginBottom: 5 }}>
            Auto Selected: {autoSelectedAccount ? 'Yes' : 'No'}
          </Text>
          <TouchableOpacity
            style={{
              backgroundColor: '#007bff',
              padding: 8,
              borderRadius: 4,
              alignItems: 'center'
            }}
            onPress={setAccountDirectly}
          >
            <Text style={{ color: 'white', fontSize: 12 }}>
              Manually Set Account
            </Text>
          </TouchableOpacity>
        </View>
      );
    }
    return null;
  };

  const fetchDynamicFields = async () => {
    try {
      setLoadingFields(true);
      setError(null);
      console.log('Fetching dynamic fields for contact form');

      const response = await apiService.get<ContactFieldsResponse>(API_ENDPOINTS.CONTACTS_DYNAMIC_FIELD);
      console.log('Dynamic fields response:', response);

      if (response.status === 200) {
        setFormFields(response.data.contactFields);

        // Check if we need to set the account field immediately
        if (callFrom === 'accountDetail' && !isEditMode) {
          console.log('Attempting to set account field immediately after fetching fields');

          const accountField = findAccountField(response.data.contactFields);
          if (accountField) {
            console.log('Found account field in newly loaded fields:', accountField.field_name);

            // We'll set a flag to trigger the account selection in the next render cycle
            setTimeout(() => {
              setAccountDirectly();
            }, 100);
          }
        }

        setLoadingFields(false);
      } else {
        throw new Error('Failed to fetch form fields');
      }
    } catch (err) {
      console.error('Error fetching dynamic fields:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch form fields');
    } finally {
      setLoadingFields(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  const handleChange = (fieldId: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [fieldId]: value
    }));

    // If user starts typing and there was a validation attempt,
    // check if this field is now valid and reset validation state if all required fields are filled
    if (hasAttemptedSubmit) {
      const updatedFormData = { ...formData, [fieldId]: value };

      // Get fields to validate based on current visibility
      const fieldsToValidate = showAllFields
        ? formFields.filter(field => field.active === 1 && field.required === 'on')
        : formFields.filter(field => field.active === 1 && field.quick_add === "on" && field.required === 'on');

      const hasAllRequiredFields = fieldsToValidate
        .every(field => updatedFormData[field.id] && updatedFormData[field.id].trim() !== '');

      if (hasAllRequiredFields) {
        setHasAttemptedSubmit(false);
      }
    }
  };

  const validateForm = () => {
    const errors: string[] = [];

    // Get fields to validate based on current visibility
    const fieldsToValidate = showAllFields
      ? formFields.filter(field => field.active === 1)
      : formFields.filter(field => field.active === 1 && field.quick_add === "on");

    fieldsToValidate.forEach(field => {
      if (field.required === 'on' && (!formData[field.id] || formData[field.id].trim() === '')) {
        errors.push(`${field.field_label} is required`);
      }
    });
    return errors;
  };
const renderFormField = (field: ContactField) => {
    return (
      <View key={field.id}>
        <DynamicFormField
          field={field}
          value={formData[field.id] || ''}
          onChangeText={(value) => handleChange(field.id.toString(), value)}
          showRequired={hasAttemptedSubmit}
          showAllFields={showAllFields}
        />

      </View>
    )
  }

  // Function to get quick_add "on" fields
  const getQuickAddFields = () => {
    return formFields
      .filter(field => field.active === 1 && field.quick_add === "on")
      .sort((a, b) => (a.order || 0) - (b.order || 0));
  };

  // Function to get quick_add "off" fields
  const getQuickAddOffFields = () => {
    return formFields
      .filter(field => field.active === 1 && field.quick_add === "off")
      .sort((a, b) => (a.order || 0) - (b.order || 0));
  };

  // Function to render the toggle for showing all fields
  const renderFieldToggle = () => {
    const quickAddOffFields = getQuickAddOffFields();

    if (quickAddOffFields.length === 0) {
      return null; // Don't show toggle if there are no quick_add "off" fields
    }

    return (
      <View style={styles.toggleContainer}>
        <Text style={styles.toggleLabel}>Show all fields</Text>
        <TouchableOpacity
          style={[styles.toggleButton, showAllFields && styles.toggleButtonActive]}
          onPress={() => setShowAllFields(!showAllFields)}
        >
          <View style={[styles.toggleSlider, showAllFields && styles.toggleSliderActive]} />
        </TouchableOpacity>
      </View>
    );
  };
  const handleSubmit = async () => {
    try {
      // Set hasAttemptedSubmit to true to show validation errors
      setHasAttemptedSubmit(true);

      const errors = validateForm();
      if (errors.length > 0) {
        showSnackbar(errors[0]);
        return;
      }
      console.log('valuesdsd', "submit");
      setLoading(true);

      // Prepare the data for API with entire model
      const prepareApiData = () => {
        console.log('formFieldssdsd', formFields);
        const activeFields = formFields
          .filter(field => field.active === 1)
          .map(field => {
            let value = formData[field.id] || '';

            // If this is the owner_id field and it's empty, set it to the current user's ID
            if (field.field_name === 'owner_id' && (!value || value.trim() === '') && user) {
              value = user.id.toString();
            }

            console.log('valuesdsd', value);
            // For dropdown and lookup fields, we need to handle the value differently
            if ((field.field_type === FIELD_TYPES.DROPDOWN || field.field_type === FIELD_TYPES.LOOKUP) && value) {
              const selectedChoice = field.choices?.find(c => c.id.toString() === value);
              return {
                ...field, // Include all field properties
                field_value: selectedChoice ? selectedChoice.id.toString() : value, // Pass the ID for API
                value: selectedChoice ? selectedChoice.id.toString() : value // Keep original value as ID
              };
            }

            return {
              ...field, // Include all field properties
              field_value: value,
              value: value // Keep original value
            };
          });

        return { contact: activeFields };
      };

      const apiData = prepareApiData();
      console.log('Submitting data:', apiData);

      let response;
      if (isEditMode && params.contact) {
        // Update existing contact
        const id = JSON.parse(params.id as string);
        console.log('contactDatasdsd', id);
        //const id=getFieldValue(contactData, "id")
        const updateEndpoint = `${API_ENDPOINTS.CONTACTS}/${id}`;
        console.log('Updating contact at:', updateEndpoint);
        response = await apiService.put(updateEndpoint, apiData);
        console.log('responseDataContactId', response);
      } else {
        // Create new contact
        console.log('Creating new contact');
        response = await apiService.post(API_ENDPOINTS.CONTACTS, apiData);
      }

      if (response.status === 200 || response.status === 201) {
        console.log('responseDataContactId', response.data.contact);

        // Show response message from API
        const responseMessage = response.data?.message;

        Alert.alert(
          'Success',
          responseMessage,
          [{
            text: 'OK', onPress: () => {
              router.back()
              if (isEditMode && params.contact) {
                router.push({
                  pathname: '/contactDetail',
                  params: { id: response.data.contact }
                });

              } else {
                router.push({
                  pathname: '/contactDetail',
                  params: { id: response.data.contact }
                });

              }
            }
          }]
        );
        setFormData({}); // Reset form after successful submission
        setHasAttemptedSubmit(false); // Reset validation state
      } else {
        // Show error message from API
        const errorMessage = response.data?.message;
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error(`Error ${isEditMode ? 'updating' : 'creating'} contact:`, error);

      // Extract error message from the nested structure
      let errorMessage = 'An error occurred';

      if (error && typeof error === 'object') {
        // Check for nested error structure: error.data.message
        if ((error as any).data?.message) {
          errorMessage = (error as any).data.message;
        }
        // Check for direct message property
        else if ((error as any).message) {
          errorMessage = (error as any).message;
        }
      }

      Alert.alert(
        'Error',
        errorMessage,
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  if (loadingFields) {
    return (
      <SafeAreaView style={styles.safeArea} edges={['top'] as Edge[]}>
        <View style={styles.centerContent}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.safeArea} edges={['top'] as Edge[]}>
        <View style={styles.centerContent}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={fetchDynamicFields}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea} edges={['top'] as Edge[]}>
      <StatusBar backgroundColor="#0F96BB" barStyle="light-content" />
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      {/* Custom Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {isEditMode ? 'Edit Contact' : 'Add Contact'}
        </Text>
        <View style={styles.placeholder} />
      </View>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={[styles.container, { backgroundColor: colors.background.secondary }]}
      >
        <ScrollView
          style={[styles.container, styles.scrollContainer]}
          contentContainerStyle={styles.scrollContentContainer}
        >
          {/* {renderDebugButton()} */}

          {Array.isArray(formFields) && formFields.length > 0 ? (
            formFields.some(item => item.active === 1) ? (
              <>
                {/* Render toggle for showing all fields */}
                {renderFieldToggle()}

                {/* Render quick_add "on" fields first */}
                {getQuickAddFields().map((item) => renderFormField(item))}

                {/* Render quick_add "off" fields if toggle is enabled */}
                {showAllFields && getQuickAddOffFields().map((item) => renderFormField(item))}
              </>
            ) : (
              <View style={styles.centerContent}>
                <Text style={styles.noFieldsText}>No active fields available</Text>
                <Text style={styles.noFieldsSubText}>All fields are currently inactive</Text>
              </View>
            )
          ) : (
            <View style={styles.centerContent}>
              <Text style={styles.noFieldsText}>No form fields available</Text>
            </View>
          )}
        </ScrollView>
      </KeyboardAvoidingView>

      {Array.isArray(formFields) && formFields.some(item => item.active === 1) && (
        <View style={styles.submitContainer}>
          <TouchableOpacity
            onPress={handleSubmit}
            disabled={loading}
            style={[
              styles.submitButton,
              loading && styles.submitButtonDisabled
            ]}
          >
            {loading ? (
              <ActivityIndicator color="#FFFFFF" size="small" />
            ) : (
              <Text style={styles.submitButtonText}>Submit</Text>
            )}
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );

  
  
}
const createStyles = (colors: ReturnType<typeof useColors>) => StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#0F96BB',
  },
  container: {
    flex: 1,
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#ffffff',
  },
  errorText: {
    fontSize: 16,
    color: 'red',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: colors.text.inverse,
    fontSize: 16,
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#0F96BB',
    paddingVertical: 15,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#0F8AAA',
    elevation: Platform.OS === 'android' ? 4 : 0,
    shadowColor: Platform.OS === 'ios' ? '#000' : 'transparent',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: Platform.OS === 'ios' ? 0.1 : 0,
    shadowRadius: Platform.OS === 'ios' ? 3 : 0,
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  placeholder: {
    width: 34,
  },
  content: {
    flex: 1,
  },
  formContainer: {
    padding: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  inputContainer: {
    marginBottom: 15,
  },
  label: {
    fontSize: 14,
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  messageInput: {
    height: 80,
    paddingTop: 12,
  },
  submitContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: colors.background.tertiary,
    backgroundColor: colors.background.primary,
  },
  submitButton: {
    backgroundColor: colors.primary,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 48,
  },
  submitButtonDisabled: {
    opacity: 0.7,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  noFieldsText: {
    fontSize: 16,
    color: colors.text.tertiary,
    textAlign: 'center',
    marginBottom: 8,
  },
  noFieldsSubText: {
    fontSize: 14,
    color: colors.text.tertiary,
    textAlign: 'center',
    opacity: 0.8,
  },
  scrollContainer: {
    flex: 1,
    width: '100%',
  },
  scrollContentContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  toggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 16,
    backgroundColor: colors.background.primary,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.background.tertiary,
  },
  toggleLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text.primary,
  },
  toggleButton: {
    width: 50,
    height: 28,
    borderRadius: 14,
    backgroundColor: colors.background.tertiary,
    justifyContent: 'center',
    paddingHorizontal: 2,
  },
  toggleButtonActive: {
    backgroundColor: colors.primary,
  },
  toggleSlider: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.background.primary,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  toggleSliderActive: {
    transform: [{ translateX: 22 }],
  },
});
