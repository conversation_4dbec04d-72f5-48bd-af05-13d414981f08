// import React, { useState, useMemo, useEffect } from 'react';
// import {
//   StyleSheet,
//   View,
//   Text,
//   TouchableOpacity,
//   FlatList,
//   StatusBar,
//   Platform,
//   ActivityIndicator,
//   Modal,
// } from 'react-native';
// import { Ionicons } from '@expo/vector-icons';
// import { Stack, useRouter } from 'expo-router';
// import { useColors } from '@/hooks/useThemeColor';
// import { SafeAreaView } from 'react-native-safe-area-context';
// import { apiService } from '../services/ApiService';
// import { API_ENDPOINTS } from '../config/api';
// import { ClaimHistoryResponse, ClaimHistoryItem } from '@/app/models/ClaimHistory';
// import { showSnackbar } from './ui/utils';
// import AsyncStorage from '@react-native-async-storage/async-storage';
// import Calendar from './components/Calendar';
// import MarqueeView from 'react-native-marquee-view';
// import { TransportModeModal } from './components/TransportModeModal';

// type TabType = 'pending' | 'completed' | 'rejected';

// // Claim cycle interface
// interface ClaimCycle {
//   id: string;
//   period: string;
//   startDate: string;
//   endDate: string;
// }

// // Custom ConditionalMarquee component that only scrolls text longer than 12 characters
// const ConditionalMarquee = ({
//   text,
//   style,
//   minLength = 12, // Only apply marquee if text length exceeds this value
//   speed = 0.1
// }: {
//   text: string,
//   style?: any,
//   minLength?: number,
//   speed?: number
// }) => {
//   // Check if text length exceeds the minimum length for scrolling
//   const shouldScroll = text.length > minLength;

//   if (!shouldScroll) {
//     // If text is short enough, just display it normally
//     return <Text style={style} numberOfLines={1} ellipsizeMode="tail">{text}</Text>;
//   }

//   // If text is long, use MarqueeView for scrolling
//   return (
//     <MarqueeView
//       style={{ flex: 1, overflow: 'hidden' }}
//       speed={speed}
//     >
//       <Text style={style}>{text}</Text>
//     </MarqueeView>
//   );
// };

// // Generate previous 4 completed claim cycles (excluding current cycle)
// // Using the same proven logic as Claims screen
// const generatePreviousClaimCycles = (): ClaimCycle[] => {
//   console.log('=== GENERATING CLAIM CYCLES (USING CLAIMS SCREEN LOGIC) ===');

//   const cycles: ClaimCycle[] = [];
//   let cycleIdCounter = 1;

//   // Helper function to format dates consistently
//   const formatDate = (date: Date): string => {
//     const formattedDate = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
//     console.log(`Formatting date: ${date.toISOString()} -> ${formattedDate}`);
//     return formattedDate;
//   };

//   const createCycle = (year: number, month: number, isFirstHalf: boolean) => {
//     let fromDay, toDay;
//     if (isFirstHalf) {
//       fromDay = 1;
//       toDay = 15;
//     } else {
//       fromDay = 16;
//       const lastDayOfMonth = new Date(year, month + 1, 0).getDate();
//       toDay = lastDayOfMonth;
//     }

//     const startDate = new Date(year, month, fromDay);
//     const endDate = new Date(year, month, toDay);

//     // Format dates for API
//     const startDateStr = formatDate(startDate);
//     const endDateStr = formatDate(endDate);

//     // Format label for display with short month name and year
//     const fromFormatted = `${fromDay.toString().padStart(2, '0')} ${startDate.toLocaleString('default', { month: 'short' })} ${year}`;
//     const toFormatted = `${toDay.toString().padStart(2, '0')} ${endDate.toLocaleString('default', { month: 'short' })} ${year}`;
//     const period = `${fromFormatted} to ${toFormatted}`;

//     const cycle = {
//       id: cycleIdCounter.toString(),
//       period,
//       startDate: startDateStr,
//       endDate: endDateStr
//     };
//     cycleIdCounter++;
//     return cycle;
//   };

//   try {
//     // Get current date
//     const today = new Date();
//     console.log('Current date:', today.toISOString());

//     // Determine the current cycle and generate previous 4 cycles
//     const currentYear = today.getFullYear();
//     const currentMonth = today.getMonth();
//     const currentDay = today.getDate();

//     console.log('Current year:', currentYear, 'month:', currentMonth, 'day:', currentDay);

//     // Start from the most recent completed cycle
//     let year = currentYear;
//     let month = currentMonth;
//     let isFirstHalf: boolean;

//     if (currentDay <= 15) {
//       // We're in first half, so last completed cycle is second half of previous month
//       console.log('Currently in first half, going to second half of previous month');
//       month = month - 1;
//       if (month < 0) {
//         month = 11;
//         year = year - 1;
//       }
//       isFirstHalf = false; // Second half of previous month
//     } else {
//       // We're in second half, so last completed cycle is first half of current month
//       console.log('Currently in second half, going to first half of current month');
//       isFirstHalf = true; // First half of current month
//     }

//     console.log('Starting from year:', year, 'month:', month, 'isFirstHalf:', isFirstHalf);

//     // Generate previous 4 cycles
//     for (let i = 0; i < 4; i++) {
//       const cycle = createCycle(year, month, isFirstHalf);
//       cycles.push(cycle);
//       console.log(`Cycle ${i + 1}:`, cycle.period);

//       // Move to previous cycle
//       if (isFirstHalf) {
//         // Was first half, now go to second half of previous month
//         isFirstHalf = false;
//         month = month - 1;
//         if (month < 0) {
//           month = 11;
//           year = year - 1;
//         }
//       } else {
//         // Was second half, now go to first half of same month
//         isFirstHalf = true;
//       }
//     }

//     console.log('Generated cycles:', JSON.stringify(cycles.map(c => c.period), null, 2));
//     console.log('=== END GENERATING CLAIM CYCLES ===');
//     return cycles;
//   } catch (error) {
//     console.error('Error generating claim cycles:', error);
//     // Return an empty array in case of error
//     return [];
//   }
// };

// // Add this common date formatting function after the imports
// const formatDate = (dateStr: string, format: 'display' | 'table' = 'display'): string => {
//   // Parse the YYYY-MM-DD format correctly
//   const [year, month, day] = dateStr.split('-').map(num => parseInt(num));
//   // Create date with local timezone (month is 0-indexed in JS Date)
//   const date = new Date(year, month - 1, day);

//   if (format === 'table') {
//     // Format for table: "dd/mm day"
//     const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
//     return `${String(day).padStart(2, '0')}/${String(month).padStart(2, '0')} ${days[date.getDay()]}`;
//   }

//   // Default display format: "dd MMM yyyy"
//   return date.toLocaleDateString('en-GB', {
//     day: '2-digit',
//     month: 'short',
//     year: 'numeric'
//   });
// };

// export default function ClaimHistoryScreen() {
//   const [activeTab, setActiveTab] = useState<TabType>('pending');
//   const [loading, setLoading] = useState(false);

//   // Generate claim cycles fresh each time and set default to most recent completed cycle
//   const [claimCycles] = useState<ClaimCycle[]>(() => {
//     const cycles = generatePreviousClaimCycles();
//     console.log('Initializing with cycles:', cycles.map(c => c.period));
//     return cycles;
//   });
//   const [selectedClaimCycle, setSelectedClaimCycle] = useState<ClaimCycle>(() => {
//     const cycles = generatePreviousClaimCycles();
//     console.log('Setting initial selected cycle:', cycles[0]?.period);
//     return cycles[0];
//   });

//   // Modal state for cycle selection
//   const [claimCycleModalVisible, setClaimCycleModalVisible] = useState(false);

//   // Store all statuses
//   const [allClaims, setAllClaims] = useState<{ pending: ClaimHistoryItem[]; completed: ClaimHistoryItem[]; rejected: ClaimHistoryItem[] }>({ pending: [], completed: [], rejected: [] });
//   const [refreshing, setRefreshing] = useState(false);

//   const router = useRouter();
//   const colors = useColors();
//   const styles = useMemo(() => createStyles(colors), [colors]);

//   const fetchAllClaims = async () => {
//     try {
//       setLoading(true);
//       const token = await AsyncStorage.getItem('auth_token');
//       const response = await apiService.get<ClaimHistoryResponse>(
//         API_ENDPOINTS.CLAIM_HISTORY,
//         { startDate: selectedClaimCycle.startDate, endDate: selectedClaimCycle.endDate },
//         { headers: { Authorization: `Bearer ${token}` } }
//       );
//       if (response.data?.success) {
//         setAllClaims({
//           pending: response.data.response.pending || [],
//           completed: response.data.response.completed || [],
//           rejected: response.data.response.rejected || [],
//         });
//       } else {
//         showSnackbar(response.data?.message || 'Failed to fetch claim history');
//       }
//     } catch (error: any) {
//       showSnackbar(error?.message || 'Error loading claim history');
//     } finally {
//       setLoading(false);
//       setRefreshing(false);
//     }
//   };

//   // Fetch once on cycle change
//   useEffect(() => {
//     fetchAllClaims();
//   }, [selectedClaimCycle]);

//   // Tab change just filters
//   const filteredData = useMemo(() => {
//     return allClaims[activeTab] || [];
//   }, [allClaims, activeTab]);

//   // Pull to refresh handler
//   const onRefresh = () => {
//     setRefreshing(true);
//     fetchAllClaims();
//   };

//   const handleBack = () => {
//     router.back();
//   };

//   const handleCycleSelect = (cycle: ClaimCycle) => {
//     setSelectedClaimCycle(cycle);
//     setClaimCycleModalVisible(false);
//   };

//   // Table Header Component
//   const TableHeader = () => (
//     <View style={styles.tableHeader}>
//       <View style={{ flex: 0.3, minWidth: 30, justifyContent: 'center', alignItems: 'center' }}>
//         <Text style={styles.headerCell}>#</Text>
//       </View>
//       <View style={{ flex: 1.8, minWidth: 60, justifyContent: 'center', alignItems: 'center' }}>
//         <Text style={styles.headerCell}>Task</Text>
//       </View>
//       <View style={{ flex: 1, minWidth: 50, justifyContent: 'center', alignItems: 'center' }}>
//         <Text style={styles.headerCell}>Date</Text>
//       </View>
//       <View style={{ flex: 1, minWidth: 60, justifyContent: 'center', alignItems: 'center' }}>
//         <Text style={styles.headerCell}>Distance</Text>
//       </View>
//       <View style={{ flex: 1, minWidth: 80, justifyContent: 'center', alignItems: 'center' }}>
//         <Text style={styles.headerCell}>Status</Text>
//       </View>
//       {activeTab === 'rejected' && (
//         <View style={{ flex: 0.4, minWidth: 40, justifyContent: 'center', alignItems: 'center' }}>
//           <Text style={[styles.headerCell, { fontSize: 12 }]}>Edit</Text>
//         </View>
//       )}
//     </View>
//   );

//   // Status display logic (Android logic)
//   const getStatusDisplay = (item: ClaimHistoryItem, tab: TabType) => {
//     // Colors
//     const green = '#4CAF50'; // light green
//     const orange = '#FF9800'; // peach
//     // Logic
//     if (tab === 'pending') {
//       if (item.claim_status === 'Approved') {
//         return { text: 'Waiting for Finance Approval', color: green };
//       } else {
//         return { text: 'Waiting for Manager Approval', color: orange };
//       }
//     } else if (tab === 'completed') {
//       return { text: item.claim_status || item.status, color: green };
//     } else if (tab === 'rejected') {
//       if (item.claim_status === 'Finance Rejected') {
//         return { text: 'Finance Rejected', color: green };
//       } else {
//         return { text: 'Manager Rejected', color: orange };
//       }
//     }
//     return { text: item.claim_status || item.status, color: colors.text.primary };
//   };

//   // State for edit modal
//   const [editModal, setEditModal] = useState<{ visible: boolean, remark: string, claimId: number | null }>({ visible: false, remark: '', claimId: null });
//   const [selectedClaim, setSelectedClaim] = useState<any>(null);
//   const [showTransportModeModal, setShowTransportModeModal] = useState(false);

//   // Table Row Component
//   const renderItem = ({ item, index }: { item: ClaimHistoryItem; index: number }) => {
//     const { text: statusText, color: statusColor } = getStatusDisplay(item, activeTab);

//     return (
//       <TouchableOpacity style={styles.tableRow}>
//         {/* # Column */}
//         <View style={{ flex: 0.3, minWidth: 30, justifyContent: 'center', alignItems: 'center' }}>
//           <Text style={[styles.cell, { fontWeight: 'bold' }]}>{index + 1}</Text>
//         </View>

//         {/* Task Column with ConditionalMarquee */}
//         <View style={{ flex: 1.8, minWidth: 60, justifyContent: 'center' }}>
//           <ConditionalMarquee
//             text={item.activity_name || 'N/A'}
//             style={styles.cell}
//             minLength={12}
//             speed={0.1}
//           />
//         </View>

//         {/* Date Column */}
//         <View style={{ flex: 1, minWidth: 50, justifyContent: 'center' }}>
//           <ConditionalMarquee
//             text={formatDate(item.start_date, 'table')}
//             style={styles.cell}
//             minLength={12}
//             speed={0.1}
//           />
//         </View>

//         {/* Distance Column */}
//         <View style={{ flex: 1, minWidth: 60, justifyContent: 'center' }}>
//           <ConditionalMarquee
//             text={item.distance ? `${Number(item.distance).toFixed(2)} km` : '-'}
//             style={styles.cell}
//             minLength={12}
//             speed={0.1}
//           />
//         </View>

//         {/* Status Column with ConditionalMarquee */}
//         <View style={{ flex: 1, minWidth: 80 }}>
//           <ConditionalMarquee
//             text={statusText}
//             style={[styles.cell, { color: statusColor, fontWeight: 'bold' }]}
//             minLength={12}
//             speed={0.1}
//           />
//         </View>

//         {/* Edit Button for Rejected Tab */}
//         {activeTab === 'rejected' && (
//           <TouchableOpacity
//             style={{ flex: 0.4, minWidth: 40, justifyContent: 'center', alignItems: 'center' }}
//             onPress={() => setEditModal({ visible: true, remark: item.claim_approved_remarks || '', claimId: item.id })}
//           >
//             <Ionicons name="pencil" size={18} color={colors.primary} />
//           </TouchableOpacity>
//         )}
//       </TouchableOpacity>
//     );
//   };

//   // Load data on component mount
//   useEffect(() => {
//     fetchAllClaims();
//   }, []); // Empty dependency array means this runs only once on component mount

//   // Add this function to handle transport mode selection
//   const handleTransportModeSelect = (_mode: string) => {
//     if (selectedClaim) {
//       // Just fetch claims after successful update
//       fetchAllClaims();
//     }
//   };

//   return (
//     <SafeAreaView style={styles.safeArea}>
//       <StatusBar
//         backgroundColor= "#0F96BB"  //{colors.primary}
//         barStyle="light-content"
//         translucent={true}
//       />
//       <View style={styles.container}>
//         <Stack.Screen
//           options={{
//             headerShown: false,
//           }}
//         />

//         {/* Custom Header */}
//         <View style={styles.header}>
//           <TouchableOpacity style={styles.backButton} onPress={handleBack}>
//             <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
//           </TouchableOpacity>
//           <Text style={styles.headerTitle}>Claim History</Text>
//           <View style={styles.headerActions}>
//             {/* You can add action buttons here if needed */}
//           </View>
//         </View>

//         <View style={styles.content}>
//           {/* Claim Cycle Selector */}
//           <View style={styles.cyclePickerContainer}>
//             <Text style={styles.cyclePickerLabel}>Claim Period</Text>
//             <TouchableOpacity
//               style={styles.cycleSelector}
//               onPress={() => setClaimCycleModalVisible(true)}
//             >
//               <Text style={styles.cycleSelectorText} numberOfLines={1} ellipsizeMode="tail">
//                 {selectedClaimCycle.period}
//               </Text>
//               <Ionicons
//                 name={claimCycleModalVisible ? "chevron-up" : "chevron-down"}
//                 size={24}
//                 color={colors.text.primary}
//               />
//             </TouchableOpacity>
//           </View>

//           {loading ? (
//             <View style={styles.loadingContainer}>
//               <ActivityIndicator size="large" color={colors.primary} />
//             </View>
//           ) : (
//             <>
//               {/* Tabs - Always visible */}
//               <View style={styles.tabContainer}>
//                 <TouchableOpacity style={[styles.tab, activeTab === 'pending' && styles.activeTab]} onPress={() => setActiveTab('pending')}>
//                   <Text style={[styles.tabText, activeTab === 'pending' && styles.activeTabText]}>Pending ({allClaims.pending.length})</Text>
//                 </TouchableOpacity>
//                 <TouchableOpacity style={[styles.tab, activeTab === 'completed' && styles.activeTab]} onPress={() => setActiveTab('completed')}>
//                   <Text style={[styles.tabText, activeTab === 'completed' && styles.activeTabText]}>Completed ({allClaims.completed.length})</Text>
//                 </TouchableOpacity>
//                 <TouchableOpacity style={[styles.tab, activeTab === 'rejected' && styles.activeTab]} onPress={() => setActiveTab('rejected')}>
//                   <Text style={[styles.tabText, activeTab === 'rejected' && styles.activeTabText]}>Rejected ({allClaims.rejected.length})</Text>
//                 </TouchableOpacity>
//               </View>

//               {filteredData.length > 0 ? (
//                 <FlatList
//                   data={filteredData}
//                   renderItem={renderItem}
//                   keyExtractor={item => item.id.toString()}
//                   ListHeaderComponent={TableHeader}
//                   stickyHeaderIndices={[0]}
//                   onRefresh={onRefresh}
//                   refreshing={refreshing}
//                 />
//               ) : (
//                 <View style={[styles.emptyContainer, { marginTop: 20 }]}>
//                   <Text style={styles.emptyText}>
//                     {activeTab === 'pending' && 'No pending claims found'}
//                     {activeTab === 'completed' && 'No completed claims found'}
//                     {activeTab === 'rejected' && 'No rejected claims found'}
//                   </Text>
//                 </View>
//               )}
//             </>
//           )}
//         </View>
//       </View>

//       {/* Edit Modal for Rejected Claims */}
//       <Modal visible={editModal.visible} transparent animationType="fade">
//         <View style={styles.modalOverlay}>
//           <View style={styles.editModalBox}>
//             <Text style={styles.modalTitle}>Do you want to edit the claim?</Text>
//             <Text style={styles.modalRemark}>Remark: {editModal.remark}</Text>
//             <View style={styles.editModalActions}>
//               <TouchableOpacity
//                 onPress={() => setEditModal({ visible: false, remark: '', claimId: null })}
//                 style={styles.cancelButton}
//               >
//                 <Text style={styles.cancelButtonText}>Cancel</Text>
//               </TouchableOpacity>
//               <TouchableOpacity
//                 onPress={() => {
//                   // Find the claim object using the claimId stored in editModal
//                   const claimToEdit = allClaims.rejected.find(claim => claim.id === editModal.claimId);
//                   if (claimToEdit) {
//                     setSelectedClaim(claimToEdit);
//                     setShowTransportModeModal(true);
//                   }
//                   setEditModal({ visible: false, remark: '', claimId: null });
//                 }}
//                 style={styles.confirmButton}
//               >
//                 <Text style={styles.confirmButtonText}>Yes</Text>
//               </TouchableOpacity>
//             </View>
//           </View>
//         </View>
//       </Modal>

//       {/* Transport Mode Modal */}
//       <TransportModeModal
//         isVisible={showTransportModeModal}
//         onClose={() => setShowTransportModeModal(false)}
//         onModeSelect={handleTransportModeSelect}
//         selectedMode={selectedClaim?.transport_mode}
//         selectedClaim={selectedClaim}
//       />

//       {/* Claim Cycle Selection Modal */}
//       <Modal visible={claimCycleModalVisible} transparent animationType="slide">
//         <View style={styles.modalOverlay}>
//           <View style={styles.cycleModalContent}>
//             <View style={styles.cycleModalHeader}>
//               <Text style={styles.cycleModalTitle}>Select Claim Period</Text>
//               <TouchableOpacity onPress={() => setClaimCycleModalVisible(false)}>
//                 <Ionicons name="close" size={24} color={colors.text.primary} />
//               </TouchableOpacity>
//             </View>

//             <View style={styles.cycleModalDivider} />

//             {claimCycles.map((cycle) => (
//               <TouchableOpacity
//                 key={cycle.id}
//                 style={[
//                   styles.cycleItem,
//                   selectedClaimCycle.id === cycle.id && styles.selectedCycleItem
//                 ]}
//                 onPress={() => handleCycleSelect(cycle)}
//               >
//                 <View style={styles.cycleItemContent}>
//                   <Text style={[
//                     styles.cycleText,
//                     selectedClaimCycle.id === cycle.id && styles.selectedCycleText
//                   ]}>
//                     {cycle.period}
//                   </Text>
//                   <View style={styles.checkmarkContainer}>
//                     {selectedClaimCycle.id === cycle.id && (
//                       <Ionicons
//                         name="checkmark"
//                         size={24}
//                         color={colors.primary}
//                       />
//                     )}
//                   </View>
//                 </View>
//               </TouchableOpacity>
//             ))}
//           </View>
//         </View>
//       </Modal>
//     </SafeAreaView>
//   );
// }

// const createStyles = (colors: ReturnType<typeof useColors>) => StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: colors.background.secondary,
//   },
//   safeArea: {
//     flex: 1,
//     backgroundColor: '#FFFFFF',
//     //paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
//   },
//   content: {
//     flex: 1,
//     backgroundColor: '#FFFFFF',
//   },
//   header: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     justifyContent: 'space-between',
//     paddingVertical: Platform.OS === 'android' ? 12 : 15,
//     paddingHorizontal: 15,
//     backgroundColor: '#0F96BB',
//     borderBottomWidth: 1,
//     borderBottomColor: colors.background.tertiary,
//     position: 'relative',
//     // marginTop: Platform.OS === 'android' ? 0 : 0,
//   },
//   headerTitle: {
//     fontSize: 18,
//     fontWeight: 'bold',
//     color: '#FFFFFF',
//     position: 'absolute',
//     left: 0,
//     right: 0,
//     textAlign: 'center',
//     zIndex: 1,
//   },
//   backButton: {
//     padding: 5,
//     zIndex: 2,
//   },
//   headerActions: {
//     flexDirection: 'row',
//     zIndex: 2,
//   },
//   iconButton: {
//     padding: 5,
//     marginLeft: 10,
//   },
//   cyclePickerContainer: {
//     paddingHorizontal: 15,
//     paddingVertical: 15,
//     backgroundColor: '#FFFFFF',
//     borderBottomWidth: 1,
//     borderBottomColor: colors.background.tertiary,
//   },
//   cyclePickerLabel: {
//     fontSize: 16,
//     fontWeight: '600',
//     color: colors.text.primary,
//     marginBottom: 10,
//   },
//   cycleSelector: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     justifyContent: 'space-between',
//     backgroundColor: colors.background.tertiary,
//     borderRadius: 8,
//     paddingHorizontal: 12,
//     paddingVertical: 12,
//     minHeight: 48,
//   },
//   cycleSelectorText: {
//     flex: 1,
//     fontSize: 14,
//     color: colors.text.primary,
//     marginRight: 8,
//   },
//   tabContainer: {
//     flexDirection: 'row',
//     backgroundColor: '#0F96BB',
//     marginTop: 0,
//     paddingHorizontal: 15,
//   },
//   tab: {
//     flex: 1,
//     paddingVertical: 15,
//     alignItems: 'center',
//   },
//   activeTab: {
//     borderBottomWidth: 3,
//     borderBottomColor: '#FFFFFF',
//   },
//   tabText: {
//     color: 'rgba(255, 255, 255, 0.7)',
//     fontWeight: '500',
//     fontSize: 14,
//   },
//   activeTabText: {
//     color: '#FFFFFF',
//     fontWeight: 'bold',
//   },
//   tableHeader: {
//     flexDirection: 'row',
//     backgroundColor: colors.background.primary,
//     paddingVertical: 12,
//     paddingHorizontal: 10,
//     borderBottomWidth: 1,
//     borderBottomColor: colors.background.tertiary,
//     alignItems: 'center',
//   },
//   headerCell: {
//     fontSize: 14,
//     fontWeight: 'bold',
//     color: colors.text.primary,
//     textAlign: 'center',
//   },
//   tableRow: {
//     flexDirection: 'row',
//     backgroundColor: colors.background.primary,
//     paddingVertical: 10,
//     paddingHorizontal: 10,
//     borderBottomWidth: 1,
//     borderBottomColor: colors.background.tertiary,
//     alignItems: 'center',
//     minHeight: 44,
//   },
//   cell: {
//     fontSize: 13,
//     color: colors.text.primary,
//     textAlign: 'center',
//     minHeight: 20,
//     width: '100%',
//   },
//   marqueeContainer: {
//     height: 20,
//     width: '100%',
//   },
//   statusColumn: {
//     flex: 1,
//     minWidth: 80,
//     justifyContent: 'center',
//     alignItems: 'center',
//     overflow: 'hidden',
//     paddingHorizontal: 5,
//   },
//   statusText: {
//     fontSize: 13,
//     fontWeight: 'bold',
//     color: colors.text.primary,
//   },
//   loadingContainer: {
//     flex: 1,
//     justifyContent: 'center',
//     alignItems: 'center',
//   },
//   emptyContainer: {
//     flex: 1,
//     justifyContent: 'center',
//     alignItems: 'center',
//     paddingVertical: 20,
//   },
//   emptyText: {
//     fontSize: 16,
//     color: colors.text.secondary,
//   },
//   // Modal styles
//   modalOverlay: {
//     flex: 1,
//     backgroundColor: 'rgba(0, 0, 0, 0.5)',
//     justifyContent: 'center',
//     alignItems: 'center',
//   },
//   editModalBox: {
//     backgroundColor: '#FFFFFF',
//     borderRadius: 10,
//     padding: 20,
//     width: '80%',
//     maxWidth: 400,
//     shadowColor: '#000',
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.25,
//     shadowRadius: 3.84,
//     elevation: 5,
//   },
//   modalTitle: {
//     fontSize: 18,
//     fontWeight: 'bold',
//     marginBottom: 10,
//     color: colors.text.primary,
//   },
//   modalRemark: {
//     fontSize: 14,
//     color: colors.text.secondary,
//     marginBottom: 20,
//   },
//   editModalActions: {
//     flexDirection: 'row',
//     justifyContent: 'space-between',
//     marginTop: 10,
//   },
//   cancelButton: {
//     backgroundColor: '#F5F5F5',
//     paddingVertical: 10,
//     paddingHorizontal: 20,
//     borderRadius: 5,
//     marginRight: 10,
//   },
//   cancelButtonText: {
//     color: colors.text.primary,
//     fontWeight: '500',
//   },
//   confirmButton: {
//     backgroundColor: colors.primary,
//     paddingVertical: 10,
//     paddingHorizontal: 20,
//     borderRadius: 5,
//   },
//   confirmButtonText: {
//     color: '#FFFFFF',
//     fontWeight: 'bold',
//   },
//   tickerBox: {
//     flex: 1,
//     overflow: 'hidden',
//   },
//   tickerText: {
//     fontSize: 13,
//     color: colors.text.primary,
//     width: '100%',
//   },
//   tickerStatus: {
//     fontSize: 13,
//     fontWeight: 'bold',
//     width: '100%',
//   },
//   tickerContainer: {
//     flex: 1,
//     justifyContent: 'center',
//     alignItems: 'center',
//     overflow: 'hidden',
//   },
//   // Cycle Modal Styles
//   cycleModalContent: {
//     backgroundColor: '#FFFFFF',
//     borderTopLeftRadius: 20,
//     borderTopRightRadius: 20,
//     padding: 0,
//     maxHeight: '70%',
//     minHeight: 300,
//   },
//   cycleModalHeader: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     justifyContent: 'space-between',
//     paddingHorizontal: 20,
//     paddingVertical: 16,
//     borderBottomWidth: 1,
//     borderBottomColor: colors.background.tertiary,
//   },
//   cycleModalTitle: {
//     fontSize: 18,
//     fontWeight: 'bold',
//     color: colors.text.primary,
//   },
//   cycleModalDivider: {
//     height: 1,
//     backgroundColor: colors.background.tertiary,
//   },
//   cycleItem: {
//     paddingHorizontal: 20,
//     paddingVertical: 16,
//     borderBottomWidth: 1,
//     borderBottomColor: colors.background.tertiary,
//   },
//   selectedCycleItem: {
//     backgroundColor: colors.background.tertiary,
//   },
//   cycleItemContent: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     justifyContent: 'space-between',
//   },
//   cycleText: {
//     fontSize: 16,
//     color: colors.text.primary,
//     flex: 1,
//   },
//   selectedCycleText: {
//     fontWeight: '600',
//     color: colors.primary,
//   },
//   checkmarkContainer: {
//     width: 24,
//     height: 24,
//     alignItems: 'center',
//     justifyContent: 'center',
//   },
// });

import React, { useState, useMemo, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  StatusBar,
  Platform,
  ActivityIndicator,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter } from 'expo-router';
import { useColors } from '@/hooks/useThemeColor';
import { SafeAreaView } from 'react-native-safe-area-context';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { ClaimHistoryResponse, ClaimHistoryItem } from '@/app/models/ClaimHistory';
import { showSnackbar } from './ui/utils';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Calendar from './components/Calendar';
import MarqueeView from 'react-native-marquee-view';
import { TransportModeModal } from './components/TransportModeModal';

type TabType = 'pending' | 'completed' | 'rejected';

// Custom ConditionalMarquee component that only scrolls text longer than 12 characters
const ConditionalMarquee = ({
  text,
  style,
  minLength = 12, // Only apply marquee if text length exceeds this value
  speed = 0.1
}: {
  text: string,
  style?: any,
  minLength?: number,
  speed?: number
}) => {
  // Check if text length exceeds the minimum length for scrolling
  const shouldScroll = text.length > minLength;

  if (!shouldScroll) {
    // If text is short enough, just display it normally
    return <Text style={style} numberOfLines={1} ellipsizeMode="tail">{text}</Text>;
  }

  // If text is long, use MarqueeView for scrolling
  return (
    <MarqueeView
      style={{ flex: 1, overflow: 'hidden' }}
      speed={speed}
    >
      <Text style={style}>{text}</Text>
    </MarqueeView>
  );
};

// Add this common date formatting function after the imports
const formatDate = (dateStr: string, format: 'display' | 'table' = 'display'): string => {
  // Parse the YYYY-MM-DD format correctly
  const [year, month, day] = dateStr.split('-').map(num => parseInt(num));
  // Create date with local timezone (month is 0-indexed in JS Date)
  const date = new Date(year, month - 1, day);

  if (format === 'table') {
    // Format for table: "dd/mm day"
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return `${String(day).padStart(2, '0')}/${String(month).padStart(2, '0')} ${days[date.getDay()]}`;
  }

  // Default display format: "dd MMM yyyy"
  return date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: 'short',
    year: 'numeric'
  });
};

export default function ClaimHistoryScreen() {
  // We're not using URL params anymore as we always use current month range
  const [activeTab, setActiveTab] = useState<TabType>('pending');

  // Get first day of current month for default start date
  const getFirstDayOfCurrentMonth = (): string => {
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    // Format as YYYY-MM-DD
    return `${firstDay.getFullYear()}-${String(firstDay.getMonth() + 1).padStart(2, '0')}-${String(firstDay.getDate()).padStart(2, '0')}`;
  };

  // Get current date as string for default end date
  const getCurrentDate = (): string => {
    const today = new Date();
    // Format as YYYY-MM-DD
    return `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
  };

  // Initialize with default dates (1st of current month to current date)
  const [startDate, setStartDate] = useState(getFirstDayOfCurrentMonth());
  const [endDate, setEndDate] = useState(getCurrentDate());
  const [loading, setLoading] = useState(false);

  // Calendar state
  const [showStartCalendar, setShowStartCalendar] = useState(false);
  const [showEndCalendar, setShowEndCalendar] = useState(false);

  // Store all statuses
  const [allClaims, setAllClaims] = useState<{ pending: ClaimHistoryItem[]; completed: ClaimHistoryItem[]; rejected: ClaimHistoryItem[] }>({ pending: [], completed: [], rejected: [] });
  const [refreshing, setRefreshing] = useState(false);

  const router = useRouter();
  const colors = useColors();
  const styles = useMemo(() => createStyles(colors), [colors]);

  const fetchAllClaims = async () => {
    try {
      setLoading(true);
      const token = await AsyncStorage.getItem('auth_token');
      const response = await apiService.get<ClaimHistoryResponse>(
        API_ENDPOINTS.CLAIM_HISTORY,
        { startDate, endDate },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      if (response.data?.success) {
        setAllClaims({
          pending: response.data.response.pending || [],
          completed: response.data.response.completed || [],
          rejected: response.data.response.rejected || [],
        });
      } else {
        showSnackbar(response.data?.message || 'Failed to fetch claim history');
      }
    } catch (error: any) {
      showSnackbar(error?.message || 'Error loading claim history');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Fetch once on date change
  useEffect(() => {
    fetchAllClaims();
  }, [startDate, endDate]);

  // Tab change just filters
  const filteredData = useMemo(() => {
    return allClaims[activeTab] || [];
  }, [allClaims, activeTab]);

  // Pull to refresh handler
  const onRefresh = () => {
    setRefreshing(true);
    fetchAllClaims();
  };

  const handleBack = () => {
    router.back();
  };

  const handleSubmit = () => {
    fetchAllClaims();
  };

  // Table Header Component
  const TableHeader = () => (
    <View style={styles.tableHeader}>
      <View style={{ flex: 0.3, minWidth: 30, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={styles.headerCell}>#</Text>
      </View>
      <View style={{ flex: 1.8, minWidth: 60, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={styles.headerCell}>Task</Text>
      </View>
      <View style={{ flex: 1, minWidth: 50, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={styles.headerCell}>Date</Text>
      </View>
      <View style={{ flex: 1, minWidth: 60, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={styles.headerCell}>Distance</Text>
      </View>
      <View style={{ flex: 1, minWidth: 80, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={styles.headerCell}>Status</Text>
      </View>
      {activeTab === 'rejected' && (
        <View style={{ flex: 0.4, minWidth: 40, justifyContent: 'center', alignItems: 'center' }}>
          <Text style={[styles.headerCell, { fontSize: 12 }]}>Edit</Text>
        </View>
      )}
    </View>
  );

  // Status display logic (Android logic)
  const getStatusDisplay = (item: ClaimHistoryItem, tab: TabType) => {
    // Colors
    const green = '#4CAF50'; // light green
    const orange = '#FF9800'; // peach
    // Logic
    if (tab === 'pending') {
      if (item.claim_status === 'Approved') {
        return { text: 'Waiting for Finance Approval', color: green };
      } else {
        return { text: 'Waiting for Manager Approval', color: orange };
      }
    } else if (tab === 'completed') {
      return { text: item.claim_status || item.status, color: green };
    } else if (tab === 'rejected') {
      if (item.claim_status === 'Finance Rejected') {
        return { text: 'Finance Rejected', color: green };
      } else {
        return { text: 'Manager Rejected', color: orange };
      }
    }
    return { text: item.claim_status || item.status, color: colors.text.primary };
  };

  // State for edit modal
  const [editModal, setEditModal] = useState<{ visible: boolean, remark: string, claimId: number | null }>({ visible: false, remark: '', claimId: null });
  const [selectedClaim, setSelectedClaim] = useState<any>(null);
  const [showTransportModeModal, setShowTransportModeModal] = useState(false);

  // Table Row Component
  const renderItem = ({ item, index }: { item: ClaimHistoryItem; index: number }) => {
    const { text: statusText, color: statusColor } = getStatusDisplay(item, activeTab);

    return (
      <TouchableOpacity style={styles.tableRow}>
        {/* # Column */}
        <View style={{ flex: 0.3, minWidth: 30, justifyContent: 'center', alignItems: 'center' }}>
          <Text style={[styles.cell, { fontWeight: 'bold' }]}>{index + 1}</Text>
        </View>

        {/* Task Column with ConditionalMarquee */}
        <View style={{ flex: 1.8, minWidth: 60, justifyContent: 'center' }}>
          <ConditionalMarquee
            text={item.activity_name || 'N/A'}
            style={styles.cell}
            minLength={12}
            speed={0.1}
          />
        </View>

        {/* Date Column */}
        <View style={{ flex: 1, minWidth: 50, justifyContent: 'center' }}>
          <ConditionalMarquee
            text={formatDate(item.start_date, 'table')}
            style={styles.cell}
            minLength={12}
            speed={0.1}
          />
        </View>

        {/* Distance Column */}
        <View style={{ flex: 1, minWidth: 60, justifyContent: 'center' }}>
          <ConditionalMarquee
            text={item.distance ? `${Number(item.distance).toFixed(2)} km` : '-'}
            style={styles.cell}
            minLength={12}
            speed={0.1}
          />
        </View>

        {/* Status Column with ConditionalMarquee */}
        <View style={{ flex: 1, minWidth: 80 }}>
          <ConditionalMarquee
            text={statusText}
            style={[styles.cell, { color: statusColor, fontWeight: 'bold' }]}
            minLength={12}
            speed={0.1}
          />
        </View>

        {/* Edit Button for Rejected Tab */}
        {activeTab === 'rejected' && (
          <TouchableOpacity
            style={{ flex: 0.4, minWidth: 40, justifyContent: 'center', alignItems: 'center' }}
            onPress={() => setEditModal({ visible: true, remark: item.claim_approved_remarks || '', claimId: item.id })}
          >
            <Ionicons name="create-outline" size={18} color={colors.primary} />
          </TouchableOpacity>
        )}
      </TouchableOpacity>
    );
  };

  // We're using allClaims directly for counts in the UI

  // Update the formatDisplayDate function to use the common function
  const formatDisplayDate = (dateStr: string) => {
    return formatDate(dateStr, 'display');
  };

  // Handle start date selection
  const handleStartDateSelect = (selectedDate: Date) => {
    // Fix timezone issue by creating a new date with local values
    const localDate = new Date(
      selectedDate.getFullYear(),
      selectedDate.getMonth(),
      selectedDate.getDate(),
      12, 0, 0 // Set to noon to avoid timezone issues
    );

    const today = new Date();
    today.setHours(23, 59, 59, 999);

    // Start date should not be in the future
    if (localDate > today) {
      showSnackbar('Please select a date not in the future');
      return;
    }

    // Start date should not be after end date
    const endDateObj = endDate ? new Date(endDate) : today;
    if (localDate > endDateObj) {
      showSnackbar('Start date cannot be after end date');
      return;
    }

    // Set the start date - format as YYYY-MM-DD
    const formattedDate = `${localDate.getFullYear()}-${String(localDate.getMonth() + 1).padStart(2, '0')}-${String(localDate.getDate()).padStart(2, '0')}`;
    setStartDate(formattedDate);
  };

  // Handle end date selection
  const handleEndDateSelect = (selectedDate: Date) => {
    // Fix timezone issue by creating a new date with local values
    const localDate = new Date(
      selectedDate.getFullYear(),
      selectedDate.getMonth(),
      selectedDate.getDate(),
      12, 0, 0 // Set to noon to avoid timezone issues
    );

    const today = new Date();
    today.setHours(23, 59, 59, 999);

    // End date should not be in the future
    if (localDate > today) {
      showSnackbar('Please select a date not in the future');
      return;
    }

    // End date should not be before start date
    const startDateObj = startDate ? new Date(startDate) : new Date(0);
    if (localDate < startDateObj) {
      showSnackbar('End date cannot be before start date');
      return;
    }

    // Set the end date - format as YYYY-MM-DD
    const formattedDate = `${localDate.getFullYear()}-${String(localDate.getMonth() + 1).padStart(2, '0')}-${String(localDate.getDate()).padStart(2, '0')}`;
    setEndDate(formattedDate);
  };

  // Set dates to current month range on initial load
  useEffect(() => {
    // Always use the current month range, ignoring URL parameters
    setStartDate(getFirstDayOfCurrentMonth());
    setEndDate(getCurrentDate());

    // Fetch data with the current month range
    fetchAllClaims();
  }, []); // Empty dependency array means this runs only once on component mount

  // Add this function to handle transport mode selection
  const handleTransportModeSelect = (_mode: string) => {
    if (selectedClaim) {
      // Just fetch claims after successful update
      fetchAllClaims();
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar
        backgroundColor= "#0F96BB"  //{colors.primary}
        barStyle="light-content"
        translucent={true}
      />
      <View style={styles.container}>
        <Stack.Screen
          options={{
            headerShown: false,
          }}
        />

        {/* Custom Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Claim History</Text>
          <View style={styles.headerActions}>
            {/* You can add action buttons here if needed */}
          </View>
        </View>

        <View style={styles.content}>
          {/* Date Range Picker - Always visible */}
          <View style={styles.datePickerContainer}>
            <View style={{flex: 0.45}}>
            <Text style={{ fontSize: 14, color: colors.text.secondary, marginBottom: 4 }}>
              From Date</Text>
            <TouchableOpacity
              style={styles.dateInputContainer}
              onPress={() => setShowStartCalendar(true)}
            >
              <Ionicons name="calendar-outline" size={20} color={colors.text.secondary} />
              <Text style={styles.dateInput}>
                {startDate ? formatDisplayDate(startDate) : 'Start Date'}
              </Text>
            </TouchableOpacity>
            </View>
            <View style={{flex: 0.45}}>
            <Text style={{ fontSize: 14, color: colors.text.secondary, marginBottom: 4 }}>
              To Date</Text>
            <TouchableOpacity
              style={styles.dateInputContainer}
              onPress={() => setShowEndCalendar(true)}
            >
              <Ionicons name="calendar-outline" size={20} color={colors.text.secondary} />
              <Text style={styles.dateInput}>
                {endDate ? formatDisplayDate(endDate) : 'End Date'}
              </Text>
            </TouchableOpacity>
            </View>
            <TouchableOpacity
              //style={styles.submitButton}
              style={[styles.submitButton, {display: 'none'}]}
              onPress={handleSubmit}

            >
              <Text style={styles.submitButtonText}>Submit</Text>
            </TouchableOpacity>
          </View>

          {/* Calendar Components */}
          <Calendar
            isVisible={showStartCalendar}
            onClose={() => setShowStartCalendar(false)}
            onDateSelected={handleStartDateSelect}
            selectedDate={startDate ? new Date(startDate) : new Date()}
            // No minimum date - allow selection of any past date
            maxDate={endDate ? new Date(endDate) : new Date()} // Max date is either end date or today
            title="Select Start Date"
            primaryColor={colors.primary}
          />

          <Calendar
            isVisible={showEndCalendar}
            onClose={() => setShowEndCalendar(false)}
            onDateSelected={handleEndDateSelect}
            selectedDate={endDate ? new Date(endDate) : new Date()}
            minDate={startDate ? new Date(startDate) : undefined} // Min date is start date
            maxDate={new Date()} // Max date is today
            title="Select End Date"
            primaryColor={colors.primary}
          />

          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
            </View>
          ) : (
            <>
              {/* Tabs - Always visible */}
              <View style={styles.tabContainer}>
                <TouchableOpacity style={[styles.tab, activeTab === 'pending' && styles.activeTab]} onPress={() => setActiveTab('pending')}>
                  <Text style={[styles.tabText, activeTab === 'pending' && styles.activeTabText]}>Pending ({allClaims.pending.length})</Text>
                </TouchableOpacity>
                <TouchableOpacity style={[styles.tab, activeTab === 'completed' && styles.activeTab]} onPress={() => setActiveTab('completed')}>
                  <Text style={[styles.tabText, activeTab === 'completed' && styles.activeTabText]}>Completed ({allClaims.completed.length})</Text>
                </TouchableOpacity>
                <TouchableOpacity style={[styles.tab, activeTab === 'rejected' && styles.activeTab]} onPress={() => setActiveTab('rejected')}>
                  <Text style={[styles.tabText, activeTab === 'rejected' && styles.activeTabText]}>Rejected ({allClaims.rejected.length})</Text>
                </TouchableOpacity>
              </View>

              {filteredData.length > 0 ? (
                <FlatList
                  data={filteredData}
                  renderItem={renderItem}
                  keyExtractor={item => item.id.toString()}
                  ListHeaderComponent={TableHeader}
                  stickyHeaderIndices={[0]}
                  onRefresh={onRefresh}
                  refreshing={refreshing}
                />
              ) : (
                <View style={[styles.emptyContainer, { marginTop: 20 }]}>
                  <Text style={styles.emptyText}>
                    {activeTab === 'pending' && 'No pending claims found'}
                    {activeTab === 'completed' && 'No completed claims found'}
                    {activeTab === 'rejected' && 'No rejected claims found'}
                  </Text>
                </View>
              )}
            </>
          )}
        </View>
      </View>

      {/* Edit Modal for Rejected Claims */}
      <Modal visible={editModal.visible} transparent animationType="fade">
        <View style={styles.modalOverlay}>
          <View style={styles.editModalBox}>
            <Text style={styles.modalTitle}>Do you want to edit the claim?</Text>
            <Text style={styles.modalRemark}>Remark: {editModal.remark}</Text>
            <View style={styles.editModalActions}>
              <TouchableOpacity
                onPress={() => setEditModal({ visible: false, remark: '', claimId: null })}
                style={styles.cancelButton}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  // Find the claim object using the claimId stored in editModal
                  const claimToEdit = allClaims.rejected.find(claim => claim.id === editModal.claimId);
                  if (claimToEdit) {
                    setSelectedClaim(claimToEdit);
                    setShowTransportModeModal(true);
                  }
                  setEditModal({ visible: false, remark: '', claimId: null });
                }}
                style={styles.confirmButton}
              >
                <Text style={styles.confirmButtonText}>Yes</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Transport Mode Modal */}
      <TransportModeModal
        isVisible={showTransportModeModal}
        onClose={() => setShowTransportModeModal(false)}
        onModeSelect={handleTransportModeSelect}
        selectedMode={selectedClaim?.transport_mode}
        selectedClaim={selectedClaim}
      />
    </SafeAreaView>
  );
}

const createStyles = (colors: ReturnType<typeof useColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.secondary,
  },
  safeArea: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    //paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  content: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: Platform.OS === 'android' ? 12 : 15,
    paddingHorizontal: 15,
    backgroundColor: '#0F96BB',
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
    position: 'relative',
    // marginTop: Platform.OS === 'android' ? 0 : 0,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    position: 'absolute',
    left: 0,
    right: 0,
    textAlign: 'center',
    zIndex: 1,
  },
  backButton: {
    padding: 5,
    zIndex: 2,
  },
  headerActions: {
    flexDirection: 'row',
    zIndex: 2,
  },
  iconButton: {
    padding: 5,
    marginLeft: 10,
  },
  datePickerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 0,
    backgroundColor: '#FFFFFF',
    marginTop: 0,
    height: 100,
  },
  dateInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.tertiary,
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 10,
    flex: 0.45,
  },
  dateInput: {
    flex: 1,
    paddingVertical: 4,
    paddingHorizontal: 4,
    color: colors.text.primary,
    fontSize: 13,
  },
  submitButton: {
    backgroundColor: '#0F96BB',
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 80,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 14,
    textAlign: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#0F96BB',
    marginTop: 0,
    paddingHorizontal: 15,
  },
  tab: {
    flex: 1,
    paddingVertical: 15,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 3,
    borderBottomColor: '#FFFFFF',
  },
  tabText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontWeight: '500',
    fontSize: 14,
  },
  activeTabText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: colors.background.primary,
    paddingVertical: 12,
    paddingHorizontal: 10,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
    alignItems: 'center',
  },
  headerCell: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.text.primary,
    textAlign: 'center',
  },
  tableRow: {
    flexDirection: 'row',
    backgroundColor: colors.background.primary,
    paddingVertical: 10,
    paddingHorizontal: 10,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
    alignItems: 'center',
    minHeight: 44,
  },
  cell: {
    fontSize: 13,
    color: colors.text.primary,
    textAlign: 'center',
    minHeight: 20,
    width: '100%',
  },
  marqueeContainer: {
    height: 20,
    width: '100%',
  },
  statusColumn: {
    flex: 1,
    minWidth: 80,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    paddingHorizontal: 5,
  },
  statusText: {
    fontSize: 13,
    fontWeight: 'bold',
    color: colors.text.primary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  emptyText: {
    fontSize: 16,
    color: colors.text.secondary,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  editModalBox: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    padding: 20,
    width: '80%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: colors.text.primary,
  },
  modalRemark: {
    fontSize: 14,
    color: colors.text.secondary,
    marginBottom: 20,
  },
  editModalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  cancelButton: {
    backgroundColor: '#F5F5F5',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    marginRight: 10,
  },
  cancelButtonText: {
    color: colors.text.primary,
    fontWeight: '500',
  },
  confirmButton: {
    backgroundColor: colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
  },
  confirmButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  tickerBox: {
    flex: 1,
    overflow: 'hidden',
  },
  tickerText: {
    fontSize: 13,
    color: colors.text.primary,
    width: '100%',
  },
  tickerStatus: {
    fontSize: 13,
    fontWeight: 'bold',
    width: '100%',
  },
  tickerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  }
});