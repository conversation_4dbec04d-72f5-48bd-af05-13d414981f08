import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  RefreshControl,
  StatusBar,
  SafeAreaView,
  Platform,
  Alert,
  TextInput,
} from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { showSnackbar } from './ui/utils';

interface Contact {
  id: number;
  first_name?: string;
  last_name?: string;
  emails?: string | null;
  mobile_number?: string | null;
  telephone_numbers?: string | null;
  work_number?: string | null;
  sales_accounts?: string | null;
  created_at?: string | null;
}

interface DisplayContact {
  id: number;
  name: string;
  email: string | null;
  phone: string | null;
  initial: string;
}

interface ContactsResponse {
  success: boolean;
  contacts: Contact[];
  message: string;
}

const STATUS_BAR_CONFIG = {
  barStyle: "light-content" as const,
  backgroundColor: "#0F96BB",
  translucent: Platform.OS === 'android',
};

export default function SelectContactForAccount() {
  const [contacts, setContacts] = useState<DisplayContact[]>([]);
  const [filteredContacts, setFilteredContacts] = useState<DisplayContact[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);

  const router = useRouter();
  const params = useLocalSearchParams();
  const accountId = params.account_id;
  const dealId = params.deal_id;
  const insets = useSafeAreaInsets();

  // Capitalize function
  const capitalizeWords = (str: string): string => {
    return str
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  useEffect(() => {
    fetchContacts();
  }, []);

  // Filter contacts when search query changes
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredContacts(contacts);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = contacts.filter(contact => {
        // Add safety checks for all searchable properties
        const contactName = contact?.name?.toLowerCase() || '';
        const contactEmail = contact?.email?.toLowerCase() || '';
        const contactPhone = contact?.phone?.toLowerCase() || '';
        
        return contactName.includes(query) ||
               contactEmail.includes(query) ||
               contactPhone.includes(query);
      });
      setFilteredContacts(filtered);
    }
  }, [searchQuery, contacts]);

  const fetchContacts = async () => {
    try {
      setError(null);
      
      // Use the contacts list API with filters to get all contacts
      const filterPayload = {
        title: "All Contacts",
        filter: [],
        share_with: "selected_users",
        selected_users: ["1"],
        accessibility: "Public",
        prevent_sharing: false
      };

      console.log('Fetching contacts with payload:', JSON.stringify(filterPayload, null, 2));
      console.log('Contacts API endpoint:', API_ENDPOINTS.CONTACTS_LIST);

      const response = await apiService.post<ContactsResponse>(
        API_ENDPOINTS.CONTACTS_LIST,
        filterPayload
      );

      console.log('Contacts API response:', JSON.stringify(response.data, null, 2));

      // Handle different possible response structures
      let contactsData = [];
      if (response.data?.contacts) {
        contactsData = response.data.contacts;
      } else if (response.data?.data) {
        contactsData = response.data.data;
      } else if (Array.isArray(response.data)) {
        contactsData = response.data;
      } else {
        console.warn('Unexpected API response structure:', response.data);
        throw new Error('No contacts data received from the server');
      }

      // Ensure we have an array and filter out any invalid items
      const validContacts = Array.isArray(contactsData) ? contactsData.filter(contact => contact && contact.id) : [];

      // Transform the data to include display fields
      const transformedData: DisplayContact[] = validContacts
        .map((contact: Contact) => {
          const firstName = contact.first_name || '';
          const lastName = contact.last_name || '';
          const fullName = `${firstName} ${lastName}`.trim() || 'Unknown Contact';
          const capitalizedName = capitalizeWords(fullName);
          
          return {
            id: contact.id,
            name: capitalizedName,
            email: contact.emails || null,
            phone: contact.mobile_number || contact.telephone_numbers || contact.work_number || null,
            initial: capitalizedName[0] || 'U',
          };
        });

      // Sort by name
      const sortedData = transformedData.sort((a, b) => 
        (a.name || '').localeCompare(b.name || '')
      );

      console.log(`Successfully loaded ${sortedData.length} contacts`);
      console.log('First few contacts:', sortedData.slice(0, 3).map(c => ({ id: c.id, name: c.name })));

      setContacts(sortedData);
      setFilteredContacts(sortedData);
    } catch (err) {
      console.error('Error fetching contacts:', err);
      setError('Failed to fetch contacts. Please try again.');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setIsRefreshing(true);
    fetchContacts();
  };

  const handleBack = () => {
    router.back();
  };

  const handleContactSelect = async (contact: DisplayContact) => {
    if (!accountId && !dealId) {
      showSnackbar('Account or Deal ID is missing');
      return;
    }

    if (!contact || !contact.id) {
      showSnackbar('Invalid contact selected');
      return;
    }

    try {
      setIsUpdating(true);

      // Determine which entity we're updating (account or deal)
      const entityId = accountId || dealId;
      const entityType = accountId ? 'account' : 'deal';
      const entityEndpoint = accountId ? API_ENDPOINTS.SALES_ACCOUNTS : API_ENDPOINTS.DEALS;

      console.log(`Updating ${entityType} with contact:`, {
        entityId,
        contactId: contact.id,
        contactName: contact.name
      });

      // For accounts, we need to update the contact's sales_accounts field
      // For deals, we need to update the deal's contact field
      if (accountId) {
        // Update contact's sales_accounts field to associate with account
        console.log('Fetching contact details from:', `${API_ENDPOINTS.CONTACTS}/${contact.id}`);
        const contactResponse = await apiService.get(`${API_ENDPOINTS.CONTACTS}/${contact.id}`);
        
        console.log('Contact details API response:', JSON.stringify(contactResponse.data, null, 2));
        
        if (!contactResponse.data?.contact?.contacts) {
          console.error('Invalid contact response structure:', contactResponse.data);
          throw new Error('Failed to get contact details');
        }

        const contactFields = contactResponse.data.contact.contacts;
        console.log('Contact fields found:', contactFields.length);
        
        // Find the sales_accounts field and update it with the selected account ID
        console.log('Looking for sales_accounts field in contact fields...');
        let salesAccountsFieldFound = false;
        
        const updatedFields = contactFields.map((field: any) => {
          console.log(`Field: ${field.field_name}, Type: ${field.field_type}, Current value: ${field.field_value}`);

          if (field.field_name === 'sales_accounts') {
            salesAccountsFieldFound = true;
            console.log(`Found sales_accounts field! Type: ${field.field_type}`);
            
            // Handle different field types properly
            let newFieldValue;
            let newValue;
            
            if (field.field_type === 'Dropdown' && field.choices) {
              // For dropdown fields, find the choice that matches our account
              const accountChoice = field.choices.find((choice: any) => 
                choice.id.toString() === accountId.toString()
              );
              
              if (accountChoice) {
                newFieldValue = accountChoice.custom_option;
                newValue = accountChoice.custom_option;
                console.log(`Setting dropdown field to choice: ${accountChoice.custom_option} (ID: ${accountChoice.id})`);
              } else {
                // If no matching choice found, use account ID as fallback
                newFieldValue = accountId.toString();
                newValue = accountId.toString();
                console.log(`No matching choice found, using account ID: ${accountId}`);
              }
            } else {
              // For other field types (text, number, etc.), use account ID
              newFieldValue = accountId.toString();
              newValue = accountId.toString();
              console.log(`Setting non-dropdown field to account ID: ${accountId}`);
            }
            
            console.log(`Updating from "${field.field_value}" to "${newFieldValue}"`);
            
            return {
              ...field,
              field_value: newFieldValue,
              value: newValue // Include both field_value and value like the edit page does
            };
          }
          return field;
        });

        if (!salesAccountsFieldFound) {
          console.warn('sales_accounts field not found in contact fields!');
          console.log('Available field names:', contactFields.map((f: any) => f.field_name));
        }

        // Prepare the update payload (same structure as contact edit page)
        const updatePayload = {
          contact: updatedFields
        };

        console.log('Contact update API request body:', JSON.stringify(updatePayload, null, 2));
        console.log('Contact update API endpoint:', `${API_ENDPOINTS.CONTACTS}/${contact.id}`);

        // Update the contact
        const updateResponse = await apiService.put(
          `${API_ENDPOINTS.CONTACTS}/${contact.id}`,
          updatePayload
        );

        console.log('Contact update API response:', JSON.stringify(updateResponse.data, null, 2));

        if (updateResponse.status === 200 || updateResponse.status === 201) {
          showSnackbar(`Contact "${contact.name}" has been added to the account successfully`);
          router.back(); // Go back to RelatedContactsList
        } else {
          throw new Error(updateResponse.data?.message || 'Failed to update contact');
        }
      } else {
        // For deals, update the deal's contacts field to associate with contact
        console.log('Fetching deal details from:', `${API_ENDPOINTS.DEALS}/${dealId}`);
        const dealResponse = await apiService.get(`${API_ENDPOINTS.DEALS}/${dealId}`);

        console.log('Deal details API response:', JSON.stringify(dealResponse.data, null, 2));

        // Handle the correct API response structure for deals
        let dealFields = null;
        if (dealResponse.data?.data?.deals) {
          dealFields = dealResponse.data.data.deals;
        } else if (dealResponse.data?.deal?.deals) {
          dealFields = dealResponse.data.deal.deals;
        } else if (dealResponse.data?.deals) {
          dealFields = dealResponse.data.deals;
        }

        if (!dealFields) {
          console.error('Invalid deal response structure:', dealResponse.data);
          console.error('Expected deals field not found in any of the expected paths');
          throw new Error('Failed to get deal details - deals field not found');
        }
        console.log('Deal fields found:', dealFields.length);

        // Find the contacts field and update it with the selected contact ID
        console.log('Looking for contacts field in deal fields...');
        let contactsFieldFound = false;

        const updatedFields = dealFields.map((field: any) => {
          console.log(`Field: ${field.field_name}, Type: ${field.field_type}, Current value: ${field.field_value}`);

          if (field.field_name === 'contacts') {
            contactsFieldFound = true;
            console.log(`Found contacts field! Type: ${field.field_type}`);

            // Handle different field types properly
            let newFieldValue;
            let newValue;

            if (field.field_type === 'Dropdown' && field.choices) {
              // For dropdown fields, find the choice that matches our contact
              const contactChoice = field.choices.find((choice: any) =>
                choice.custom_option === contact.name || choice.id.toString() === contact.id.toString()
              );

              if (contactChoice) {
                newFieldValue = contactChoice.custom_option;
                newValue = contactChoice.custom_option;
                console.log(`Setting dropdown field to choice: ${contactChoice.custom_option} (ID: ${contactChoice.id})`);
              } else {
                // If no matching choice found, use contact name as fallback
                newFieldValue = contact.name;
                newValue = contact.name;
                console.log(`No matching choice found, using contact name: ${contact.name}`);
              }
            } else {
              // For other field types (text, number, etc.), use contact ID
              newFieldValue = contact.id.toString();
              newValue = contact.id.toString();
              console.log(`Setting non-dropdown field to contact ID: ${contact.id}`);
            }

            console.log(`Updating from "${field.field_value}" to "${newFieldValue}"`);

            return {
              ...field,
              field_value: newFieldValue,
              value: newValue // Include both field_value and value like the edit page does
            };
          }
          return field;
        });

        if (!contactsFieldFound) {
          console.warn('contacts field not found in deal fields!');
          console.log('Available field names:', dealFields.map((f: any) => f.field_name));
        }

        // Prepare the update payload (same structure as deal edit page)
        const updatePayload = {
          deal: updatedFields
        };

        console.log('Deal update API request body:', JSON.stringify(updatePayload, null, 2));
        console.log('Deal update API endpoint:', `${API_ENDPOINTS.DEALS}/${dealId}`);

        // Update the deal
        const updateResponse = await apiService.put(
          `${API_ENDPOINTS.DEALS}/${dealId}`,
          updatePayload
        );

        console.log('Deal update API response:', JSON.stringify(updateResponse.data, null, 2));

        if (updateResponse.status === 200 || updateResponse.status === 201) {
          console.log('✅ Deal updated successfully!');
          console.log('Contact association completed:', {
            dealId: dealId,
            contactId: contact.id,
            contactName: contact.name
          });

          // Verify the update by fetching deal details again
          console.log('🔍 Verifying deal update by fetching updated deal details...');
          try {
            const verifyResponse = await apiService.get(`${API_ENDPOINTS.DEALS}/${dealId}`);
            console.log('📋 Updated deal details:', JSON.stringify(verifyResponse.data, null, 2));

            // Check if contact field was updated
            if (verifyResponse.data?.data?.deals) {
              const contactField = verifyResponse.data.data.deals.find((field: any) => field.field_name === 'contacts');
              if (contactField) {
                console.log('✅ Contact field in updated deal:', {
                  fieldName: contactField.field_name,
                  fieldValue: contactField.field_value,
                  fieldType: contactField.field_type
                });
              }
            }
          } catch (verifyErr) {
            console.warn('⚠️ Could not verify deal update:', verifyErr);
          }

          showSnackbar(`Contact "${contact.name}" has been added to the deal successfully`);
          router.back(); // Go back to RelatedContactsList
        } else {
          throw new Error(updateResponse.data?.message || 'Failed to update deal');
        }
      }
    } catch (err) {
      console.error(`Error updating ${accountId ? 'account' : 'deal'} with contact:`, err);
      const errorMessage = err instanceof Error ? err.message : `Failed to add contact to ${accountId ? 'account' : 'deal'}`;
      showSnackbar(errorMessage);
      Alert.alert('Error', errorMessage);
    } finally {
      setIsUpdating(false);
    }
  };

  const renderContactItem = ({ item }: { item: DisplayContact }) => {
    // Add safety checks for all properties
    const contactName = item?.name || 'Unknown Contact';
    const contactEmail = item?.email;
    const contactPhone = item?.phone;
    const contactInitial = item?.initial || 'U';

    return (
      <TouchableOpacity 
        style={styles.contactContainer}
        onPress={() => handleContactSelect(item)}
        disabled={isUpdating}
      >
        <View style={styles.contactContent}>
          <View style={[styles.contactAvatar, { backgroundColor: '#0F96BB' }]}>
            <Text style={styles.contactInitial}>{contactInitial}</Text>
          </View>
          <View style={styles.contactInfo}>
            <Text style={styles.contactName}>{contactName}</Text>
            {contactEmail && <Text style={styles.contactDetail}>📧 {contactEmail}</Text>}
            {contactPhone && <Text style={styles.contactDetail}>📞 {contactPhone}</Text>}
          </View>
          <Ionicons name="chevron-forward" size={20} color="#666" />
        </View>
      </TouchableOpacity>
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={[styles.safeAreaContainer, { paddingTop: insets.top }]}>
        <StatusBar {...STATUS_BAR_CONFIG} />
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Ionicons name={Platform.OS === 'ios' ? 'chevron-back' : 'arrow-back'}  size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <View style={styles.headerContent}>
            <Text style={styles.headerTitleText}>Select Contact</Text>
          </View>
          <View style={styles.headerRight} />
        </View>
        <View style={styles.mainContainer}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#0F96BB" />
            <Text style={styles.loadingText}>Loading contacts...</Text>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.safeAreaContainer, { paddingTop: insets.top }]}>
      <StatusBar {...STATUS_BAR_CONFIG} />
      <Stack.Screen options={{ headerShown: false }} />

      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Ionicons name={Platform.OS === 'ios' ? 'chevron-back' : 'arrow-back'}  size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitleText}>Select Contact</Text>
        </View>
        <View style={styles.headerRight} />
      </View>

      <View style={styles.mainContainer}>
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search contacts..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor="#999"
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery('')} style={styles.clearButton}>
                <Ionicons name="close-circle" size={20} color="#666" />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {error ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity style={styles.retryButton} onPress={fetchContacts}>
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <FlatList
            data={filteredContacts}
            renderItem={renderContactItem}
            keyExtractor={(item) => item.id.toString()}
            refreshControl={
              <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
            }
            contentContainerStyle={styles.listContent}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="people-outline" size={80} color="#ccc" />
                <Text style={styles.emptyText}>
                  {searchQuery ? 'No contacts found matching your search' : 'No contacts available'}
                </Text>
              </View>
            }
          />
        )}

        {isUpdating && (
          <View style={styles.updatingOverlay}>
            <View style={styles.updatingContainer}>
              <ActivityIndicator size="large" color="#0F96BB" />
              <Text style={styles.updatingText}>Adding contact...</Text>
            </View>
          </View>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeAreaContainer: {
    flex: 1,
    backgroundColor: '#0F96BB',
  },
  mainContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#0F96BB',
    height: Platform.OS === 'ios' ? 44 : 56,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
    zIndex: 1000,
    paddingHorizontal: Platform.OS === 'ios' ? 8 : 0,
  },
  backButton: {
    width: Platform.OS === 'ios' ? 44 : 56,
    height: Platform.OS === 'ios' ? 44 : 56,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: Platform.OS === 'ios' ? -8 : 0,
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: Platform.OS === 'ios' ? 4 : 16,
  },
  headerRight: {
    width: Platform.OS === 'ios' ? 44 : 56,
    height: Platform.OS === 'ios' ? 44 : 56,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Platform.OS === 'ios' ? -8 : 0,
  },
  headerTitleText: {
    color: '#FFFFFF',
    fontSize: Platform.OS === 'ios' ? 17 : 20,
    fontWeight: Platform.OS === 'ios' ? '600' : '500',
    textAlign: 'center',
  },
  searchContainer: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 40,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  clearButton: {
    marginLeft: 8,
  },
  listContent: {
    flexGrow: 1,
    paddingTop: 8,
  },
  contactContainer: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginVertical: 4,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  contactContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  contactAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  contactInitial: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  contactDetail: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#666',
    fontSize: 16,
    marginTop: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#ff0000',
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#0F96BB',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 16,
  },
  updatingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  updatingContainer: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  updatingText: {
    color: '#333',
    fontSize: 16,
    marginTop: 10,
  },
});
