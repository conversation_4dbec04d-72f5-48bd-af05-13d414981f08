import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, StatusBar, Platform, ViewStyle, TextStyle, ScrollView, ActivityIndicator, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useColors } from '@/hooks/useThemeColor';
import { useNavigation, router, useLocalSearchParams, useRouter, useFocusEffect } from 'expo-router';
import { Task, TasksResponse, isTaskOverdue, isTaskUpcoming, isTaskCompleted } from './models/Task';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { Appbar, Searchbar } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { readUserScope, showSnackbar } from './ui/utils';
import useAttendanceValidation from '../hooks/useAttendanceValidation';

// Task filter types
type TaskStatus = 'All' | 'Overdue' | 'Upcoming' | 'Completed';

// Add interfaces for task fields response
interface TaskOwner {
  id: number;
  name: string;
  email: string;
  mobile: string;
}

interface TaskFieldsResponse {
  status: number;
  message: string;
  tasks: {
    outcomes: any[];
    task_type: any[];
    owners: TaskOwner[];
    related_to: {
      contacts: any[];
      accounts: any[];
      deals: any[];
    };
  };
}

interface SingleContactResponse {
  status: number;
  message: string;
  contact: {
    id: number;
    name: string;
    email: string;
    mobile: string;
  };
}

export default function TaskListScreen() {
  const [activeTab, setActiveTab] = useState<TaskStatus>('All');
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [headerTitle, setHeaderTitle] = useState('All Tasks');
  const [showSearch, setShowSearch] = useState(false);
  const [searchText, setSearchText] = useState('');
  const colors = useColors();
  const params = useLocalSearchParams();
  const { contact_id, sales_account_id, deal_id } = params;
  const navigation = useRouter();
  const [taskOwners, setTaskOwners] = useState<TaskOwner[]>([]);
  const [ownersLoaded, setOwnersLoaded] = useState(false);

  const styles = createStyles(colors);

  // Attendance validation hook
  const { validateAndExecute } = useAttendanceValidation();

const [accessScope, setAccessScope] = useState<any>(null);

  useEffect(() => {
    const fetchAccessScope = async () => {
      const scope = await readUserScope();
      setAccessScope(scope);
    };
    fetchAccessScope();
  }, []);

  useEffect(() => {
    // Set appropriate header title based on context
    if (contact_id) {
      setHeaderTitle('Contact Tasks');
    } else if (sales_account_id) {
      setHeaderTitle('Account Tasks');
    } else if (deal_id) {
      setHeaderTitle('Deal Tasks');
    } else {
      setHeaderTitle('All Tasks');
    }
  }, [contact_id, sales_account_id, deal_id]);

  useFocusEffect(
    useCallback(() => {
      console.log('Task list screen focused, refreshing tasks...');
      fetchTasks();
    }, [contact_id, sales_account_id, deal_id])
  );

  useEffect(() => {
    // Fetch task fields data (including owners/collaborators) once when component mounts
    const fetchTaskFields = async () => {
      try {
        if (ownersLoaded) return; // Don't fetch if already loaded

        const response = await apiService.get<TaskFieldsResponse>(`${API_ENDPOINTS.TASK_TYPE}`);
        if (response.data?.tasks?.owners) {
          setTaskOwners(response.data.tasks.owners);
          setOwnersLoaded(true);
        }
      } catch (error) {
        console.error('Error fetching task fields:', error);
      }
    };

    fetchTaskFields();
  }, [ownersLoaded]);

  const fetchTasks = async () => {
    console.log("fetching tasks");
    try {
      setLoading(true);
      setError(null);

      let endpoint = API_ENDPOINTS.TASKS;

      // Determine which endpoint to use based on params
      if (contact_id) {
        console.log('Fetching tasks for contact ID:', contact_id);
        endpoint = API_ENDPOINTS.TASKS_BY_CONTACT.replace(':id', contact_id as string);
      } else if (sales_account_id) {
        console.log('Fetching tasks for account ID:', sales_account_id);
        endpoint = API_ENDPOINTS.TASKS_BY_ACCOUNT.replace(':id', sales_account_id as string);
      } else if (deal_id) {
        console.log('Fetching tasks for deal ID:', deal_id);
        endpoint = API_ENDPOINTS.TASKS_BY_DEAL.replace(':id', deal_id as string);
      } else {
        console.log('Fetching all tasks');
      }

      console.log('Fetching tasks from endpoint:', endpoint);
      const response = await apiService.get<TasksResponse>(endpoint);

      if (!response.data || !response.data.tasks) {
        throw new Error('Failed to fetch tasks');
      }
      console.log("response.data.tasks: " + JSON.stringify(response));
      console.log(`Fetched ${response.data.tasks.length} tasks`);

      // Filter out any invalid task objects (null, undefined, or missing required properties)
      const validTasks = response.data.tasks.filter(task =>
        task && typeof task.id !== 'undefined' && task.title
      );

      if (validTasks.length < response.data.tasks.length) {
        console.warn(`Filtered out ${response.data.tasks.length - validTasks.length} invalid task objects`);
      }

      // Sort tasks with newest first (based on created_at or due_date)
      const sortedTasks = sortTasksNewestFirst(validTasks);
      console.log('Tasks sorted with newest first');

      setTasks(sortedTasks);
    } catch (err) {
      console.error('Error fetching tasks:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Function to sort tasks with newest first
  const sortTasksNewestFirst = (taskList: Task[]): Task[] => {
    if (!taskList || !Array.isArray(taskList) || taskList.length === 0) {
      return [];
    }

    // Log a few tasks before sorting
    if (taskList.length > 0) {
      console.log('Sample tasks before sorting:');
      taskList.slice(0, Math.min(3, taskList.length)).forEach((task, index) => {
        console.log(`${index + 1}. ${task.title} - Created: ${task.created_at || 'N/A'}, Due: ${task.due_date || 'N/A'}`);
      });
    }

    const sorted = [...taskList].sort((a, b) => {
      // First, try to sort by created_at if available
      if (a.created_at && b.created_at) {
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      }

      // If created_at is not available, fall back to due_date
      if (a.due_date && b.due_date) {
        const dateA = new Date(a.due_date);
        const dateB = new Date(b.due_date);

        // If due_date is the same, use due_time if available
        if (dateA.getTime() === dateB.getTime() && a.due_time && b.due_time) {
          return a.due_time.localeCompare(b.due_time);
        }

        return dateB.getTime() - dateA.getTime();
      }

      // If neither created_at nor due_date is available, keep original order
      return 0;
    });

    // Log a few tasks after sorting
    if (sorted.length > 0) {
      console.log('Sample tasks after sorting (newest first):');
      sorted.slice(0, Math.min(3, sorted.length)).forEach((task, index) => {
        console.log(`${index + 1}. ${task.title} - Created: ${task.created_at || 'N/A'}, Due: ${task.due_date || 'N/A'}`);
      });
    }

    return sorted;
  };

  const filteredTasks = () => {
    // Ensure tasks is always an array even if it's null/undefined
    if (!tasks || !Array.isArray(tasks)) {
      console.warn('Tasks is not a valid array, returning empty array');
      return [];
    }

    let filtered = [...tasks];

    // Remove any null/undefined task objects
    filtered = filtered.filter(task => task && typeof task.id !== 'undefined');

    // First apply search filter if search text exists
    if (searchText.trim()) {
      filtered = filtered.filter(task =>
        (task.title && task.title.toLowerCase().includes(searchText.toLowerCase())) ||
        (task.description && task.description.toLowerCase().includes(searchText.toLowerCase()))
      );
    }

    // Then apply tab filter
    switch (activeTab) {
      case 'Overdue':
        filtered = filtered.filter(task => isTaskOverdue(task));
        break;
      case 'Upcoming':
        filtered = filtered.filter(task => isTaskUpcoming(task));
        break;
      case 'Completed':
        filtered = filtered.filter(task => isTaskCompleted(task));
        break;
      default:
        // All tasks - no additional filtering needed
        break;
    }

    return filtered;
  };

  const handleTaskPress = (task: Task) => {
    // Add null check
    if (!task || typeof task.id === 'undefined') {
      showSnackbar('Cannot view task: Invalid task data');
      return;
    }

    // Navigate to task details
    console.log('Task pressed:', task);
    router.push({
      pathname: '/TaskDetailScreen',
      params: { task: JSON.stringify(task) }
    });
  };

  const handleTaskLongPress = (task: Task) => {
    // Add null check
    if (!task || typeof task.id === 'undefined') {
      showSnackbar('Cannot access task: Invalid task data');
      return;
    }

    // Show task options
    console.log('Task long pressed:', task);
    Alert.alert(
      'Task Options',
      'What would you like to do?',
      [
        {
          text: task.mark_as_complete === "true" ? 'Mark as Incomplete' : 'Mark as Complete',
          onPress: () => console.log('Toggle completion'),
        },
        {
          text: 'Edit Task',
          onPress: () => {

            console.log("tasksdsdsd: " + JSON.stringify(task));
            // Navigate to edit task screen
            router.push({
              pathname: '/tasks/create' as any,
              params: {
                mode: 'edit',
                task: JSON.stringify(task),
                callFrom: contact_id ? 'contactDetail' :
                         sales_account_id ? 'accountDetail' :
                         deal_id ? 'dealDetail' : 'taskList'
              }
            });
          },
        },
        {
          text: 'Delete Task',
          style: 'destructive',
          onPress: () => console.log('Delete task'),
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  const handleOptionSelect = (option: TaskStatus) => {
    setActiveTab(option);
  };

  // Get collaborator names
  const getCollaboratorNames = (task: Task): string => {
    if (!task || !task.collaboratoes || !taskOwners.length) return '';

    try {
      // Handle different formats of collaborator data
      const collaboratorsStr = String(task.collaboratoes).trim();
      if (!collaboratorsStr) return '';

      // Case 1: Single ID (e.g., "143")
      if (!collaboratorsStr.includes(',') && !collaboratorsStr.includes('[')) {
        const id = parseInt(collaboratorsStr, 10);
        if (!isNaN(id)) {
          const owner = taskOwners.find(owner => owner.id === id);
          return owner ? owner.name : '';
        }
      }

      // Case 2: JSON array (e.g., "[143,145]")
      let collaboratorIds: number[] = [];
      try {
        const parsed = JSON.parse(collaboratorsStr);
        if (Array.isArray(parsed)) {
          collaboratorIds = parsed.map(id => typeof id === 'number' ? id : parseInt(String(id), 10));
        }
      } catch {
        // Case 3: Comma-separated string (e.g., "143,145")
        collaboratorIds = collaboratorsStr.split(',')
          .map(id => id.trim())
          .filter(id => id)
          .map(id => parseInt(id, 10))
          .filter(id => !isNaN(id));
      }

      // Map IDs to names
      if (collaboratorIds.length > 0) {
        return collaboratorIds
          .map(id => {
            const owner = taskOwners.find(owner => owner.id === id);
            return owner ? owner.name : '';
          })
          .filter(Boolean)
          .join(', ');
      }

      return '';
    } catch (e) {
      console.error('Error parsing collaborators:', e);
      return '';
    }
  };

  const handleToggleTaskStatus = async (task: Task) => {
    if (!task || typeof task.id === 'undefined') {
      showSnackbar('Cannot update task: Invalid task data');
      return;
    }

    const newStatus = task.mark_as_complete === "true" ? "false" : "true";
    const action = newStatus === "true" ? "complete" : "incomplete";
    const actionName = `mark task as ${action}`;

    await validateAndExecute(async () => {
      Alert.alert(
        'Confirm Status Change',
        `Are you sure you want to mark this task as ${action}?`,
        [
          {
            text: 'Cancel',
            style: 'cancel'
          },
          {
            text: 'Yes',
            onPress: async () => {
              try {
                const endpoint = API_ENDPOINTS.TASK_UPDATE.replace(':id', String(task.id));
                const response = await apiService.put(endpoint, {
                  mark_as_complete: newStatus
                });

                if (response.data && response.data.status === 200) {
                  // Refresh the task list
                  await fetchTasks();
                  showSnackbar(`Task marked as ${action}`);
                } else {
                  throw new Error('Failed to update task status');
                }
              } catch (error) {
                console.error('Error updating task status:', error);
                showSnackbar('Failed to update task status. Please try again.');
              }
            }
          }
        ]
      );
    }, actionName);
  };

  const renderTaskItem = ({ item }: { item: Task }) => {
    // Add safeguard against undefined items
    if (!item) {
      console.warn('Tried to render undefined task item');
      return null;
    }

    const isCompleted = item.mark_as_complete === "true";
    const collaborators = getCollaboratorNames(item);

    return (
      <TouchableOpacity
        style={styles.taskItem}
        onPress={() => handleTaskPress(item)}
        onLongPress={() => handleTaskLongPress(item)}
        delayLongPress={500}
      >
        <View style={styles.taskContent}>
          <View style={styles.taskHeader}>
            <Text style={[styles.taskTitle, isCompleted && styles.completedTaskText]} numberOfLines={1}>
              {item.title || 'Untitled Task'}
            </Text>
            <TouchableOpacity
              onPress={() => handleToggleTaskStatus(item)}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              {isCompleted ? (
                <Ionicons name="checkmark-circle" size={20} color={colors.primary} />
              ) : (
                <Ionicons name="ellipse-outline" size={20} color="#999" />
              )}
            </TouchableOpacity>
          </View>

          {/* Task Description - Dynamic height based on content */}
          {item.description && (
            <Text style={styles.taskDescription} numberOfLines={2}>
              {item.description}
            </Text>
          )}

          {/* Date and Time in same line */}
          <View style={styles.dateTimeContainer}>
            <View style={styles.dateTimeRow}>
              <Ionicons name="calendar-outline" size={14} color={isOverdue(item) ? '#e53935' : colors.primary} style={styles.dateTimeIcon} />
              <Text style={[styles.taskDate, isOverdue(item) && styles.overdueDate]}>
                {formatTaskDateOnly(item.due_date)}
              </Text>
              {item.due_time && (
                <>
                  <Text style={styles.dateSeparator}> • </Text>
                  <Ionicons name="time-outline" size={14} color={isOverdue(item) ? '#e53935' : colors.primary} style={styles.dateTimeIcon} />
                  <Text style={[styles.taskDate, isOverdue(item) && styles.overdueDate]}>
                    {formatTaskTimeOnly(item.due_time)}
                  </Text>
                </>
              )}
            </View>
          </View>

          {/* Horizontally scrollable collaborators */}
          {collaborators ? (
            <ScrollView
              horizontal={true}
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.scrollableCollaborators}
            >
              <View style={styles.collaboratorsContainer}>
                <Ionicons name="people-outline" size={14} color="#666" />
                <Text style={styles.collaboratorsText} numberOfLines={1}>
                  {collaborators}
                </Text>
              </View>
            </ScrollView>
          ) : null}
        </View>
      </TouchableOpacity>
    );
  };

  const isOverdue = (task: Task): boolean => {
    if (!task) return false;
    return !isTaskCompleted(task) && isTaskOverdue(task);
  };

  // Format date only for display (dd/mm day format)
  const formatTaskDateOnly = (dateString: string): string => {
    if (!dateString) return 'No date';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Invalid date';

      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
      const dayOfWeek = days[date.getDay()];

      return `${day}/${month} ${dayOfWeek}`;
    } catch (error) {
      console.error('Error formatting task date:', error);
      return 'Date error';
    }
  };

  // Format time only for display (12-hour format with AM/PM)
  const formatTaskTimeOnly = (timeString: string): string => {
    if (!timeString) return 'No time';
    try {
      // Check if time already contains AM/PM
      if (timeString.toLowerCase().includes('am') || timeString.toLowerCase().includes('pm')) {
        return timeString;
      }

      // Parse the time string (expected format: HH:MM:SS or HH:MM)
      const timeParts = timeString.split(':');
      if (timeParts.length >= 2) {
        const hour = parseInt(timeParts[0], 10);
        const minute = parseInt(timeParts[1], 10);

        if (!isNaN(hour) && !isNaN(minute)) {
          const isPM = hour >= 12;
          const hour12 = hour % 12 || 12; // Convert 0 to 12 for 12 AM
          return `${hour12}:${minute.toString().padStart(2, '0')} ${isPM ? 'PM' : 'AM'}`;
        }
      }

      return timeString; // Fallback to original if parsing fails
    } catch (error) {
      console.error('Error formatting task time:', error);
      return 'Time error';
    }
  };

  const renderTabBar = () => {
    const tabs: TaskStatus[] = ['All', 'Overdue', 'Upcoming', 'Completed'];

    return (
      <View style={styles.tabsOuterContainer}>
        <ScrollView
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.tabsScrollContainer}
        >
          <View style={styles.tabsContainer}>
            {tabs.map((tab) => {
              const isActive = activeTab === tab;
              const count = tab === 'All' ? tasks.length :
                           tab === 'Overdue' ? tasks.filter(t => isTaskOverdue(t)).length :
                           tab === 'Upcoming' ? tasks.filter(t => isTaskUpcoming(t)).length :
                           tasks.filter(t => isTaskCompleted(t)).length;

              return (
                <TouchableOpacity
                  key={tab}
                  style={[styles.tab, isActive && styles.activeTab]}
                  onPress={() => handleOptionSelect(tab)}
                >
                  <Text style={[styles.tabText, isActive && styles.activeTabText]}>
                    {`${tab} (${count})`}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
        </ScrollView>
      </View>
    );
  };

  const renderHeader = () => (
    <Appbar.Header style={styles.appbar}>
      {!showSearch ? (
        <>
          <Appbar.Action
            icon="arrow-left"
            onPress={() => {
              console.log('back_action', contact_id, sales_account_id, deal_id);
              router.back();
            }}
            color="#FFFFFF"
          />
          <Appbar.Content
            title={headerTitle}
            titleStyle={[styles.appbarTitle, { color: '#FFFFFF' }]}
          />
          <Appbar.Action
            icon="magnify"
            onPress={() => setShowSearch(true)}
            color="#FFFFFF"
          />
          {accessScope?.task?.create === true && (
          <TouchableOpacity
            style={styles.plusButton}
            onPress={() => {
              if (contact_id) {
                router.push({pathname: '/tasks/create' as any, params: { id: contact_id, callFrom: 'contactDetail' }});
              } else if (sales_account_id) {
                router.push({pathname: '/tasks/create' as any, params: { sales_account_id, callFrom: 'accountDetail' }});
              } else if (deal_id) {
                router.push({pathname: '/tasks/create' as any, params: { id: deal_id, callFrom: 'dealDetail' }});
              } else {
                router.push({pathname: '/tasks/create' as any});
              }
            }}
          >
            <Ionicons name="add" size={22} color="#FFFFFF" style={{ fontWeight: 'bold' }} />
          </TouchableOpacity>
          )}
        </>
      ) : (
        <>
          <Appbar.Action
            icon="arrow-left"
            onPress={() => {
              setShowSearch(false);
              setSearchText('');
            }}
            color="#FFFFFF"
          />
          <View style={styles.searchView}>
            <Searchbar
              placeholder="Search tasks"
              onChangeText={setSearchText}
              value={searchText}
              style={[styles.searchbar, { backgroundColor: colors.background.transparent }]}
              inputStyle={styles.searchInput}
              icon="magnify"
              iconColor="#FFFFFF"
              clearIcon={() => null}
              placeholderTextColor="#FFFFFF"
              selectionColor="#FFFFFF"
              autoFocus
              returnKeyType="search"
              onIconPress={() => {}}
              showDivider={false}
            />
          </View>
          <Appbar.Action
            icon="close"
            onPress={() => {
              setShowSearch(false);
              setSearchText('');
            }}
            color="#FFFFFF"
          />
        </>
      )}
    </Appbar.Header>
  );

  if (loading) {
    return (
      <View style={styles.container}>
        <StatusBar backgroundColor="transparent" barStyle="dark-content" translucent={true} />
        <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }} edges={['left', 'right', 'bottom']}>
          {renderHeader()}
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={styles.loadingText}>Loading tasks...</Text>
          </View>
        </SafeAreaView>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <StatusBar backgroundColor="transparent" barStyle="dark-content" translucent={true} />
        <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }} edges={['left', 'right', 'bottom']}>
          {renderHeader()}
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity style={styles.retryButton} onPress={fetchTasks}>
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.goBackButton}
              onPress={() => router.back()}
            >
              <Text style={styles.backButtonText}>Go Back</Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor="transparent" barStyle="dark-content" translucent={true} />
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }} edges={['left', 'right', 'bottom']}>
        {renderHeader()}
        {renderTabBar()}

        {filteredTasks().length === 0 ? (
          <View style={styles.emptyContainer}>
            <Ionicons name="calendar-outline" size={60} color="#ccc" />
            <Text style={styles.emptyText}>
              {searchText ? 'No tasks found matching your search' :
               `No ${activeTab !== 'All' ? activeTab.toLowerCase() + ' ' : ''}tasks${contact_id ? ' for this contact' : sales_account_id ? ' for this account' : deal_id ? ' for this deal' : ''}`}
            </Text>
          </View>
        ) : (
          <FlatList
            data={filteredTasks()}
            keyExtractor={(item) => (item && item.id) ? String(item.id) : `task-${Math.random()}`}
            renderItem={renderTaskItem}
            contentContainerStyle={styles.taskList}
            showsVerticalScrollIndicator={false}
          />
        )}
      </SafeAreaView>
    </View>
  );
}

const createStyles = (colors: ReturnType<typeof useColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5'
  },
  appbar: {
    backgroundColor: '#0F96BB',
    elevation: 2,
    justifyContent: 'center', // Center content vertically
    height: 56, // Use standard height, let SafeAreaView handle insets
    paddingHorizontal: 16, // Add horizontal padding for better spacing
    // Remove manual inset handling as SafeAreaView takes care of it
  },
  appbarTitle: {
    fontSize: 20,
    color: colors.text.primary,
    fontWeight: '600'
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
    backgroundColor: colors.background.primary,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  tabsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tab: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginRight: 12,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
    minWidth: 100,
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: colors.primary,
  },
  tabText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  activeTabText: {
    color: '#fff',
  },
  taskList: {
    paddingHorizontal: 12,
    paddingVertical: 16,
    backgroundColor: 'transparent',
  },
  taskItem: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 16,
    marginHorizontal: 4,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 12,
    elevation: 12,
    borderWidth: 0.5,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  taskContent: {
    flex: 1,
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  taskTitle: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: colors.text.primary,
    marginRight: 8,
  },
  completedTaskText: {
    textDecorationLine: 'line-through',
    color: colors.text.secondary,
  },
  taskDescription: {
    fontSize: 14,
    color: colors.text.secondary,
    marginBottom: 8,
    lineHeight: 20,
    // backgroundColor: colors.background.tertiary,
    // padding: 8,
    // borderRadius: 8,
  },
  taskDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    justifyContent: 'space-between',
    width: '100%',
  },
  taskRelated: {
    fontSize: 14,
    color: colors.primary,
    marginRight: 12,
    flex: 1,
  },
  dateTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.tertiary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 5,
    marginBottom: 4,
  },
  dateTimeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  dateTimeIcon: {
    marginRight: 4,
  },
  dateSeparator: {
    fontSize: 12,
    color: colors.text.secondary,
    marginHorizontal: 4,
  },
  timeIcon: {
    marginRight: 4,
  },
  taskDate: {
    fontSize: 13,
    color: colors.text.secondary,
  },
  overdueDate: {
    color: '#e53935',
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    paddingHorizontal: 40, // Add more horizontal padding for better text wrapping
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
    textAlign: 'center', // Center the text
    lineHeight: 24, // Better line spacing for multi-line text
    fontWeight: '400',
    maxWidth: '100%', // Ensure text doesn't exceed container width
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    paddingHorizontal: 40,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 12,
    textAlign: 'center',
    lineHeight: 24,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    paddingHorizontal: 40,
  },
  errorText: {
    fontSize: 16,
    color: colors.status.error,
    marginBottom: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 6,
  },
  retryButtonText: {
    color: colors.button.primary.text,
    fontSize: 16,
    fontWeight: '500',
  },
  goBackButton: {
    marginTop: 10,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 6,
    backgroundColor: colors.background.tertiary,
  },
  backButtonText: {
    fontSize: 16,
    color: colors.text.primary,
    fontWeight: '500',
  },

  searchView: {
    flex: 1,
    marginHorizontal: 16,
  },
  searchbar: {
    elevation: 0,
    backgroundColor: colors.background.transparent,
    borderRadius: 8,

  },
  searchInput: {
    fontSize: 16,
    color: '#FFFFFF',
  },
  plusButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  collaboratorsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 2,
  },
  collaboratorsText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  scrollableTaskDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 20, // Add some padding for better scrolling
  },
  scrollableCollaborators: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 20, // Add some padding for better scrolling
    marginTop: 4,
  },
  tabsOuterContainer: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tabsScrollContainer: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
});