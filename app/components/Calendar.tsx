import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Modal,
  ScrollView,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Platform } from 'react-native';

interface CalendarProps {
  isVisible: boolean;
  onClose: () => void;
  onDateSelected: (date: Date) => void;
  selectedDate?: Date;
  minDate?: Date;
  maxDate?: Date;
  title?: string;
  primaryColor?: string;
}

const Calendar: React.FC<CalendarProps> = ({
  isVisible,
  onClose,
  onDateSelected,
  selectedDate = new Date(),
  minDate,
  maxDate = new Date(),
  title = 'Select Date',
  primaryColor = '#0F96BB',
}) => {
  const [currentMonth, setCurrentMonth] = useState(new Date(selectedDate));
  const [selectedDay, setSelectedDay] = useState(selectedDate);

  useEffect(() => {
    if (selectedDate) {
      setSelectedDay(selectedDate);
      setCurrentMonth(new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 1));
    }
  }, [selectedDate]);

  // Get days in month
  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  // Get day of week for first day of month (0 = Sunday, 1 = Monday, etc.)
  const getFirstDayOfMonth = (year: number, month: number) => {
    return new Date(year, month, 1).getDay();
  };

  // Format date for display
  const formatMonthYear = (date: Date): string => {
    return date.toLocaleDateString('en-US', {
      month: 'long',
      year: 'numeric'
    });
  };

  // Check if a date is selectable (within min and max range)
  const isDateSelectable = (date: Date): boolean => {
    // If minDate is provided, date should be after or equal to minDate
    const isAfterMinDate = minDate ? date >= new Date(new Date(minDate).setHours(0, 0, 0, 0)) : true;

    // If maxDate is provided, date should be before or equal to maxDate
    const isBeforeMaxDate = maxDate ? date <= new Date(new Date(maxDate).setHours(23, 59, 59, 999)) : true;

    return isAfterMinDate && isBeforeMaxDate;
  };

  // Navigate to previous month
  const goToPreviousMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1));
  };

  // Navigate to next month
  const goToNextMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1));
  };

  // Handle date selection
  const handleDateSelect = (date: Date) => {
    setSelectedDay(date);
    onDateSelected(date);
    onClose();
  };

  // Render calendar grid
  const renderCalendarDays = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    const daysInMonth = getDaysInMonth(year, month);
    const firstDayOfMonth = getFirstDayOfMonth(year, month);

    const days = [];
    const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

    // Render week day headers
    weekDays.forEach((day, index) => {
      days.push(
        <View key={`header-${index}`} style={styles.dayHeader}>
          <Text style={styles.dayHeaderText}>{day}</Text>
        </View>
      );
    });

    // Add empty cells for days before the first day of month
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(<View key={`empty-${i}`} style={styles.emptyDay} />);
    }

    // Render days of the month
    for (let i = 1; i <= daysInMonth; i++) {
      const date = new Date(year, month, i);
      const isSelected = selectedDay &&
        date.getDate() === selectedDay.getDate() &&
        date.getMonth() === selectedDay.getMonth() &&
        date.getFullYear() === selectedDay.getFullYear();
      const isToday = new Date().toDateString() === date.toDateString();
      const selectable = isDateSelectable(date);

      days.push(
        <TouchableOpacity
          key={`day-${i}`}
          style={[
            styles.day,
            isSelected && { backgroundColor: primaryColor },
            isToday && styles.today,
            !selectable && styles.disabledDay
          ]}
          onPress={() => selectable && handleDateSelect(date)}
          disabled={!selectable}
        >
          <Text style={[
            styles.dayText,
            isSelected && styles.selectedDayText,
            !selectable && styles.disabledDayText
          ]}>
            {i}
          </Text>
        </TouchableOpacity>
      );
    }

    return days;
  };

  return (
    <Modal
      transparent={true}
      visible={isVisible}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.calendarContainer}>
          {/* Header */}
          <View style={[styles.header, { backgroundColor: primaryColor }]}>
            <Text style={styles.headerTitle}>{title}</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#FFFFFF" />
            </TouchableOpacity>
          </View>

          {/* Month Navigation */}
          <View style={styles.monthNavigation}>
            <TouchableOpacity onPress={goToPreviousMonth} style={styles.navButton}>
              <Ionicons name={Platform.OS === 'ios' ? 'chevron-back' : 'arrow-back'}  size={24} color="#333" />
            </TouchableOpacity>
            <Text style={styles.monthYearText}>{formatMonthYear(currentMonth)}</Text>
            <TouchableOpacity onPress={goToNextMonth} style={styles.navButton}>
              <Ionicons name={Platform.OS === 'ios' ? 'chevron-forward' : 'arrow-forward'}  size={24} color="#333" />
            </TouchableOpacity>
          </View>

          {/* Calendar Grid */}
          <ScrollView contentContainerStyle={styles.calendarGrid}>
            {renderCalendarDays()}
          </ScrollView>

          {/* No buttons at the bottom */}
        </View>
      </View>
    </Modal>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  calendarContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    width: width * 0.95,
    maxWidth: 450,
    height: 'auto',
    minHeight: 350, // Set a minimum height that's 50px more than before
    maxHeight: '55%',
    overflow: 'hidden',
    marginBottom: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 17,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  closeButton: {
    padding: 5,
  },
  monthNavigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  navButton: {
    padding: 6,
  },
  monthYearText: {
    fontSize: 15,
    fontWeight: 'bold',
    color: '#333333',
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 6,
  },
  dayHeader: {
    width: '14.28%',
    paddingVertical: 8,
    alignItems: 'center',
  },
  dayHeaderText: {
    fontSize: 13,
    fontWeight: 'bold',
    color: '#666666',
  },
  day: {
    width: '14.28%',
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 1,
    borderRadius: 100,
  },
  dayText: {
    fontSize: 14,
    color: '#333333',
  },
  emptyDay: {
    width: '14.28%',
    aspectRatio: 1,
  },
  selectedDayText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  today: {
    borderWidth: 1,
    borderColor: '#0F96BB',
  },
  disabledDay: {
    opacity: 0.3,
  },
  disabledDayText: {
    color: '#999999',
  },
});

export default Calendar;
