import React from 'react';
import { View, StyleSheet, StyleProp, TextStyle, ViewStyle } from 'react-native';
import TextTicker from 'react-native-text-ticker';

interface MarqueeTextProps {
  style?: StyleProp<TextStyle>;
  containerStyle?: StyleProp<ViewStyle>;
  speed?: number; // pixels per second
  delay?: number; // milliseconds before starting
  loop?: boolean;
  children: string;
  duration?: number; // Animation duration in ms

}

const MarqueeText: React.FC<MarqueeTextProps> = ({
  style,
  containerStyle,
  speed = 50,
  delay = 1000,
  loop = true,
  children,
  duration = 8000,
}) => {
  // Always use TextTicker for consistent behavior
  return (
    <View style={[styles.container, containerStyle]}>
      <TextTicker
        style={style}
        duration={duration}
        loop={loop}
        bounce={false}
        repeatSpacer={50}
        marqueeDelay={delay}
        scrollSpeed={speed}
        useNativeDriver
        isRTL={false}
        animationType="scroll"
      >
        {children}
      </TextTicker>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default MarqueeText;
