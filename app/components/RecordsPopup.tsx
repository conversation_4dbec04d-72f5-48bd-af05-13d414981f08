import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Modal, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useColors } from '@/hooks/useThemeColor';
import { useRecords } from '@/context/RecordsContext';

interface RecordsPopupProps {
  visible: boolean;
  onClose: () => void;
  callFrom?: string;
}

export default function RecordsPopup({ visible, onClose, callFrom = 'default' }: RecordsPopupProps) {
  const router = useRouter();
  const colors = useColors();
  const { recordTypes, salesActivities } = useRecords();

  const handleActivityPress = (activity: any) => {
    if (activity.activity_name === 'Contact' || activity.activity_name.toLowerCase().includes('contact')) {
      router.push({ 
        pathname: '/contact',
        params: { callFrom }
      } as any);
    } else if (activity.activity_name === 'Account' || activity.activity_name.toLowerCase().includes('account')) {
      router.push('/CreateAccounts' as any);
    } else if (activity.activity_name === 'Deal' || activity.activity_name.toLowerCase().includes('deal')) {
      router.push('/CreateDeals' as any);
    } else if (activity.activity_name === 'Task' || activity.activity_name.toLowerCase().includes('task')) {
      router.push({
        pathname: '/tasks/create',
        params: { callFrom }
      } as any);
    } else if (activity.activity_name === 'Meeting' || activity.activity_name.toLowerCase().includes('meeting')) {
      router.push({
        pathname: '/meetings/createMeeting',
        params: { callFrom }
      } as any);
    } else {
      // For other record types
      Alert.alert('Coming Soon', `${activity.activity_name} creation will be available soon!`);
    }
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableOpacity 
        style={styles.overlay}
        activeOpacity={1}
        onPress={onClose}
      >
        <View style={[styles.popup, { backgroundColor: colors.background.primary }]}>
          <Text style={[styles.popupTitle, { color: colors.text.primary }]}>Select Record Type</Text>
          
          <View style={styles.popupSection}>
            {Array.isArray(recordTypes) && recordTypes.length > 0 ? (
              recordTypes.map((type: any) => {
                const iconName = type.icon || 'document-outline';
                return (
                  <TouchableOpacity
                    key={type.id}
                    style={styles.popupItem}
                    onPress={() => handleActivityPress(type)}
                  >
                    <View style={[styles.popupIcon, { backgroundColor: colors.background.tertiary }]}>
                      <Ionicons 
                        name={iconName}
                        size={20} 
                        color={colors.text.primary} 
                      />
                    </View>
                    <Text style={[styles.popupItemText, { color: colors.text.primary }]}>
                      {type.activity_name}
                    </Text>
                  </TouchableOpacity>
                );
              })
            ) : null}
          </View>

          <View style={styles.popupSection}>
            <Text style={[styles.popupSectionTitle, { color: colors.text.primary }]}>Sales Activities</Text>
            {Array.isArray(salesActivities) && salesActivities.length > 0 ? (
              salesActivities.map((activity: any) => (
                <TouchableOpacity 
                  key={activity.id} 
                  style={styles.popupItem}
                  onPress={() => handleActivityPress(activity)}
                >
                  <View style={[styles.popupIcon, { backgroundColor: colors.background.tertiary }]}>
                    <Ionicons 
                      name={activity.activity_icon || 'document-outline'} 
                      size={20} 
                      color={colors.text.primary} 
                    />
                  </View>
                  <Text style={[styles.popupItemText, { color: colors.text.primary }]}>
                    {activity.activity_name}
                  </Text>
                </TouchableOpacity>
              ))
            ) : null}
          </View>
        </View>
      </TouchableOpacity>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  popup: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 16,
    paddingBottom: 30,
    maxHeight: '70%',
  },
  popupTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    paddingHorizontal: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EFEFEF',
  },
  popupSection: {
    marginTop: 16,
  },
  popupSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  popupItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  popupIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  popupItemText: {
    fontSize: 16,
  },
}); 