import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  TextInput,
  Image,
  Platform,
  ScrollView,
  Alert,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { apiService } from '../../services/ApiService';
import { API_ENDPOINTS } from '../../config/api';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { showSnackbar } from '../ui/utils';
import { EnhancedConveyanceResponse, TransportMode as TransportModeType } from '../models/Conveyance';
import * as ImagePicker from 'expo-image-picker';
import * as MediaLibrary from 'expo-media-library';

interface TransportMode {
  label: string;
  value: string;
  rate: number;
}

interface TransportModeModalProps {
  isVisible: boolean;
  onClose: () => void;
  onModeSelect: (mode: string) => void;
  selectedMode?: string | null;
  selectedClaim?: any;
}

export function TransportModeModal({ isVisible, onClose, onModeSelect, selectedMode, selectedClaim }: TransportModeModalProps) {
  const [transportModes, setTransportModes] = useState<TransportMode[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [mode, setMode] = useState(selectedMode || '');
  const [amount, setAmount] = useState(selectedClaim?.claim_amount ? String(selectedClaim.claim_amount) : '');
  const [remarks, setRemarks] = useState('');
  const [selectedImages, setSelectedImages] = useState<ImagePicker.ImagePickerAsset[]>([]);
  const [showDropdown, setShowDropdown] = useState(false);
  const [showImageOptions, setShowImageOptions] = useState(false);

  useEffect(() => {
    if (isVisible) {
      console.log('Selected Claim in TransportModeModal:', selectedClaim);
      fetchTransportModes();
      setMode(selectedMode || '');
      // Make sure amount is never initialized as '0' but as empty string or actual amount
      setAmount(selectedClaim?.claim_amount ? String(selectedClaim.claim_amount) : '');
      setRemarks('');
      setSelectedImages([]);
      setShowDropdown(false);
      setShowImageOptions(false);
    }
  }, [isVisible, selectedMode, selectedClaim]);

  const fetchTransportModes = async () => {
    try {
      setIsLoading(true);
      const token = await AsyncStorage.getItem('auth_token');
      const today = new Date();
      const date = today.toISOString().split('T')[0];
      const response = await apiService.post<EnhancedConveyanceResponse>(
        `${API_ENDPOINTS.CONVEYANCE}/view`,
        { date },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      if (response.data?.success && response.data.response) {
        const modes = response.data.response.transport_modes.map((mode: TransportModeType) => ({
          label: mode.mode,
          value: mode.mode,
          rate: mode.rate
        }));
        setTransportModes(modes);
      } else {
        showSnackbar(response.data?.message || 'Failed to fetch transport modes');
      }
    } catch (error: any) {
      showSnackbar(error?.message || 'Error loading transport modes');
    } finally {
      setIsLoading(false);
    }
  };

  const shouldShowAmountField = () => mode.toLowerCase() !== 'bike';
  const shouldShowImageUploadField = () => mode.toLowerCase() !== 'bike';

  const handleUpdate = async () => {
    if (!mode) {
      showSnackbar('Please select a transport mode');
      return;
    }

    if (mode.toLowerCase() !== 'bike') {
      if (!amount || parseFloat(amount) <= 0) {
        showSnackbar('Please enter a valid claim amount');
        return;
      }

      if (selectedImages.length === 0) {
        showSnackbar('Please upload at least one image');
        return;
      }
    }

    try {
      setIsLoading(true);
      const token = await AsyncStorage.getItem('auth_token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      // Add a small delay before creating FormData to ensure UI stability
      await new Promise(resolve => setTimeout(resolve, 300));

      const formData = new FormData();

      // Add basic form fields
      formData.append('transport_mode', mode);
      formData.append('amount', mode.toLowerCase() === 'bike' ? '0' : amount);
      formData.append('remarks', remarks || '');

      // Add images with improved error handling
      if (selectedImages.length > 0) {
        try {
          for (const image of selectedImages) {
            if (!image || !image.uri) continue;

            // Get file extension from URI
            const uriParts = image.uri.split('.');
            const fileType = uriParts[uriParts.length - 1] || 'jpg';

            // Create unique filename with fewer random characters
            const fileName = `image_${Date.now()}.${fileType}`;

            // Create a safe image object
            const imageObject: any = {
              uri: Platform.OS === 'ios' ? image.uri.replace('file://', '') : image.uri,
              name: fileName,
              type: `image/${fileType}`,
            };

            // Append each image to formData
            formData.append('claim_images', imageObject);
          }
        } catch (imageError) {
          console.error('Error processing images:', imageError);
          // Continue with the update even if image processing fails
        }
      }

      // Add another small delay before making the API call
      await new Promise(resolve => setTimeout(resolve, 300));

      // Make the API call
      const response = await fetch(`http://************:5000/api/conveyance/${selectedClaim.id}/reapply`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json',
          'Content-Type': 'multipart/form-data',
        },
        body: formData,
      });

      const responseData = await response.json();

      if (response.ok && responseData.success) {
        // Show snackbar
        showSnackbar('Claim updated successfully');

        // Use Promise to ensure proper sequencing with a longer delay
        await new Promise(resolve => {
          setTimeout(() => {
            try {
              onModeSelect(mode);
              onClose();
            } catch (callbackError) {
              console.error('Error in callbacks:', callbackError);
            }
            resolve(true);
          }, 1500); // Reduced from 5000ms but still longer than original
        });
      } else {
        throw new Error(responseData.message || 'Failed to update claim');
      }

    } catch (error: any) {
      console.error('Error updating claim:', error);
      showSnackbar(error.message || 'Failed to update claim');
    } finally {
      setIsLoading(false);
    }
  };

  const checkPermissions = async (type: 'camera' | 'gallery') => {
    try {
      if (type === 'camera') {
        const { status: cameraStatus } = await ImagePicker.getCameraPermissionsAsync();
        if (cameraStatus !== 'granted') {
          const { status: newCameraStatus } = await ImagePicker.requestCameraPermissionsAsync();
          if (newCameraStatus !== 'granted') {
            Alert.alert(
              "Camera Permission Required",
              "This app needs camera access to take photos. Please enable camera access in your device settings.",
              [
                { text: "Cancel", style: "cancel" },
                {
                  text: "Settings",
                  onPress: () => {
                    Platform.OS === 'ios'
                      ? Linking.openURL('app-settings:')
                      : Linking.openSettings();
                  }
                }
              ]
            );
            return false;
          }
        }
      }

      // Check media library permissions
      const { status: mediaStatus } = await MediaLibrary.getPermissionsAsync();
      if (mediaStatus !== 'granted') {
        const { status: newMediaStatus } = await MediaLibrary.requestPermissionsAsync();
        if (newMediaStatus !== 'granted') {
          Alert.alert(
            "Media Library Permission Required",
            "This app needs access to your photos. Please enable photo library access in your device settings.",
            [
              { text: "Cancel", style: "cancel" },
              {
                text: "Settings",
                onPress: () => {
                  Platform.OS === 'ios'
                    ? Linking.openURL('app-settings:')
                    : Linking.openSettings();
                }
              }
            ]
          );
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('Error checking permissions:', error);
      return false;
    }
  };

  const handleImagePicker = async (type: 'camera' | 'gallery') => {
    try {
      // Check permissions
      const hasPermission = await checkPermissions(type);
      if (!hasPermission) {
        return;
      }

      try {
        let result;
        if (type === 'camera') {
          result = await ImagePicker.launchCameraAsync({
            allowsEditing: false, // Disable editing to reduce complexity
            quality: 0.5, // Lower quality to reduce memory usage
            exif: false, // Disable EXIF data to reduce file size
          });
        } else {
          result = await ImagePicker.launchImageLibraryAsync({
            allowsMultipleSelection: true,
            quality: 0.5, // Lower quality to reduce memory usage
            exif: false, // Disable EXIF data to reduce file size
          });
        }

        // Safely handle the result
        if (!result.canceled && result.assets && result.assets.length > 0) {
          // Use the original assets but with a delay to ensure UI is ready
          setTimeout(() => {
            try {
              if (type === 'gallery') {
                setSelectedImages(prevImages => [...prevImages, ...result.assets]);
              } else if (type === 'camera' && result.assets[0]) {
                setSelectedImages(prevImages => [...prevImages, result.assets[0]]);
              }
            } catch (updateError) {
              console.error('Error updating selected images:', updateError);
            }
          }, 500);
        }
      } catch (error) {
        console.error('Error in image picker result handling:', error);
        // Don't show alert here as it might cause additional issues
        // Just log the error for debugging
      }
    } catch (error) {
      console.error('Error in image picker:', error);
      Alert.alert(
        "Error",
        "There was a problem accessing the camera or photo library. Please try again."
      );
    }
  };

  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <TouchableOpacity
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={() => {
          setShowDropdown(false);
        }}
      >
        <View style={styles.bottomSheet}>
          {/* Header */}
          <View style={styles.headerRow}>
            <Text style={styles.sheetTitle}>Update Claim</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeBtn}>
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>

          {/* Form */}
          <View style={styles.formSection}>
            {/* Mode of Transport */}
            <Text style={styles.label}>Mode of Transport<Text style={{ color: 'red' }}>*</Text></Text>
            <TouchableOpacity
              style={styles.dropdownButton}
              onPress={() => setShowDropdown(!showDropdown)}
              activeOpacity={1}
            >
              <Text style={styles.dropdownButtonText}>{mode || 'Select transport mode'}</Text>
              <Ionicons name={showDropdown ? 'chevron-up' : 'chevron-down'} size={22} color="#666" />
            </TouchableOpacity>
            {mode && (
              <Text style={styles.rateText}>
                Rate: ₹{transportModes.find(m => m.value === mode)?.rate || 0}/km
              </Text>
            )}
            {/* Dropdown options as popup */}
            {showDropdown && (
              <View style={styles.dropdownOptionsPopup}>
                {transportModes.map((m) => (
                  <TouchableOpacity
                    key={m.value}
                    style={[styles.dropdownOption, mode === m.value && styles.selectedDropdownOption]}
                    onPress={() => {
                      setMode(m.value);
                      setShowDropdown(false);
                    }}
                  >
                    <Text style={[styles.dropdownOptionText, mode === m.value && styles.selectedDropdownOptionText]}>{m.label}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}

            {/* Amount Claimable */}
            {shouldShowAmountField() && (
              <View style={{ marginTop: 16 }}>
                <Text style={styles.label}>Amount Claimable</Text>
                <TextInput
                  style={styles.textInput}
                  value={amount}
                  onChangeText={setAmount}
                  keyboardType="numeric"
                  placeholder="Enter amount"
                />
              </View>
            )}

            {/* Remarks */}
            <View style={{ marginTop: 16 }}>
              <Text style={styles.label}>Remarks</Text>
              <TextInput
                style={[styles.textInput, styles.remarksInput]}
                value={remarks}
                onChangeText={setRemarks}
                placeholder="Enter remarks"
                multiline
                numberOfLines={3}
              />
            </View>

            {/* Upload File */}
            {shouldShowImageUploadField() && (
              <View style={{ marginTop: 16 }}>
                <Text style={styles.label}>Upload file<Text style={{ color: 'red' }}>*</Text></Text>
                <View style={styles.imageUploadContainer}>
                  {/* Display selected images in a row */}
                  <View style={styles.scrollViewWrapper}>
                    <ScrollView
                      horizontal={true}
                      showsHorizontalScrollIndicator={false}
                      style={styles.selectedImagesScroll}
                      contentContainerStyle={styles.selectedImagesScrollContent}
                      nestedScrollEnabled={true}
                    >
                      {selectedImages.map((image, index) => (
                        <View key={index} style={styles.thumbnailContainer}>
                          <Image
                            source={{ uri: image.uri }}
                            style={styles.thumbnailImage}
                          />
                          <TouchableOpacity
                            style={styles.removeImageButton}
                            onPress={() => {
                              const newImages = [...selectedImages];
                              newImages.splice(index, 1);
                              setSelectedImages(newImages);
                            }}
                          >
                            <Ionicons name="close-circle" size={18} color="#FF3B30" />
                          </TouchableOpacity>
                        </View>
                      ))}

                      {/* Square Plus Button */}
                      <TouchableOpacity
                        style={styles.squareUploadButton}
                        onPress={() => setShowImageOptions(true)}
                      >
                        <Ionicons name="add" size={28} color="#0F96BB" />
                      </TouchableOpacity>
                    </ScrollView>
                  </View>
                </View>

                {/* Image Options Popup */}
                {showImageOptions && (
                  <Modal
                    transparent={true}
                    animationType="fade"
                    visible={showImageOptions}
                    onRequestClose={() => setShowImageOptions(false)}
                  >
                    <TouchableOpacity
                      style={styles.imageOptionsOverlay}
                      activeOpacity={1}
                      onPress={() => setShowImageOptions(false)}
                    >
                      <View style={styles.imageOptionsPopup}>
                        <TouchableOpacity
                          style={styles.imageOptionRow}
                          onPress={() => {
                            setShowImageOptions(false);
                            // Use setTimeout to ensure the modal is closed before opening camera
                            setTimeout(() => handleImagePicker('camera'), 300);
                          }}
                        >
                          <View style={[styles.imageOptionIconContainer, { backgroundColor: '#4CD964' }]}>
                            <Ionicons name="camera" size={22} color="#fff" />
                          </View>
                          <Text style={styles.imageOptionRowText}>Take Photo</Text>
                        </TouchableOpacity>

                        <View style={styles.optionDivider} />

                        <TouchableOpacity
                          style={styles.imageOptionRow}
                          onPress={() => {
                            setShowImageOptions(false);
                            // Use setTimeout to ensure the modal is closed before opening gallery
                            setTimeout(() => handleImagePicker('gallery'), 300);
                          }}
                        >
                          <View style={[styles.imageOptionIconContainer, { backgroundColor: '#007AFF' }]}>
                            <Ionicons name="images" size={22} color="#fff" />
                          </View>
                          <Text style={styles.imageOptionRowText}>Choose from Gallery</Text>
                        </TouchableOpacity>

                        <View style={styles.optionDivider} />

                        <TouchableOpacity
                          style={[styles.imageOptionRow, { justifyContent: 'center' }]}
                          onPress={() => setShowImageOptions(false)}
                        >
                          <Text style={[styles.imageOptionRowText, { color: '#FF3B30' }]}>Cancel</Text>
                        </TouchableOpacity>
                      </View>
                    </TouchableOpacity>
                  </Modal>
                )}
              </View>
            )}

            {/* Update Button */}
            <TouchableOpacity
              style={[styles.updateButton, isLoading && styles.disabledButton]}
              onPress={handleUpdate}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color="white" />
              ) : (
                <Text style={styles.updateButtonText}>Update</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  bottomSheet: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 32,
    minHeight: 400,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  sheetTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#222',
  },
  closeBtn: {
    padding: 4,
  },
  formSection: {
    marginTop: 8,
  },
  label: {
    fontSize: 15,
    color: '#333',
    fontWeight: '500',
    marginBottom: 6,
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 12,
    height: 48,
    marginBottom: 4,
  },
  dropdownButtonText: {
    fontSize: 16,
    color: '#333',
  },
  rateText: {
    fontSize: 13,
    color: '#666',
    marginTop: 2,
    marginBottom: 4,
    fontStyle: 'italic',
  },
  dropdownOptionsPopup: {
    position: 'absolute',
    top: 60,
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#eee',
    zIndex: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
    maxHeight: 180,
  },
  dropdownOption: {
    paddingVertical: 10,
    paddingHorizontal: 16,
  },
  selectedDropdownOption: {
    backgroundColor: '#0F96BB22',
  },
  dropdownOptionText: {
    fontSize: 15,
    color: '#333',
  },
  selectedDropdownOptionText: {
    color: '#0F96BB',
    fontWeight: 'bold',
  },
  textInput: {
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 12,
    fontSize: 16,
  },
  remarksInput: {
    height: 70,
    textAlignVertical: 'top',
  },
  // Keep the original upload button styles for backward compatibility
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 12,
    marginTop: 4,
  },
  uploadButtonText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#666',
  },
  // New image upload UI styles
  imageUploadContainer: {
    marginTop: 4,
  },
  scrollViewWrapper: {
    width: '100%',
    height: 80,
    overflow: 'hidden',
  },
  selectedImagesScroll: {
    flex: 1,
    marginVertical: 8,
  },
  selectedImagesScrollContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 10,
  },
  thumbnailContainer: {
    width: 60,
    height: 60,
    marginRight: 10,
    borderRadius: 8,
    overflow: 'hidden',
    position: 'relative',
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 2,
  },
  squareUploadButton: {
    width: 60,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    borderStyle: 'dashed',
  },
  selectedFilesText: {
    marginLeft: 12,
    fontSize: 14,
    color: '#666',
  },
  updateButton: {
    backgroundColor: '#0F96BB',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 24,
  },
  updateButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  // New image options UI styles
  imageOptionsOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  imageOptionsPopup: {
    backgroundColor: '#fff',
    width: '100%',
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    paddingVertical: 15,
    paddingHorizontal: 5,
  },
  imageOptionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 15,
  },
  imageOptionIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  imageOptionRowText: {
    fontSize: 16,
    color: '#333',
  },
  optionDivider: {
    height: 1,
    backgroundColor: '#E5E5E5',
    marginHorizontal: 15,
  },
  // Keep original styles for backward compatibility
  imageOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  imageOptionText: {
    marginLeft: 12,
    fontSize: 16,
    color: '#333',
  },
  selectedImagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 12,
    gap: 8,
  },
  imagePreviewContainer: {
    position: 'relative',
    width: 80,
    height: 80,
  },
  imagePreview: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  disabledButton: {
    opacity: 0.7,
  },
});
