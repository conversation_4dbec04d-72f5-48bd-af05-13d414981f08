/**
 * Attendance Empty State Demo Page
 * Demonstrates the improved centered "not found" view for Attendance History
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  FlatList
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useColors } from '../hooks/useThemeColor';

export default function AttendanceEmptyStateDemoScreen() {
  const [showEmptyState, setShowEmptyState] = useState(true);
  const [filterType, setFilterType] = useState<'today' | 'weekly' | 'monthly' | null>('today');
  const colors = useColors();
  const router = useRouter();

  // Mock data for demonstration
  const mockData = [
    { id: 1, date: '2024-01-15', check_in_time: '09:00 AM', check_out_time: '05:30 PM' },
    { id: 2, date: '2024-01-16', check_in_time: '08:45 AM', check_out_time: '05:45 PM' },
  ];

  const months = [
    { value: 1, label: 'January' },
    { value: 2, label: 'February' },
    { value: 3, label: 'March' },
    { value: 4, label: 'April' },
    { value: 5, label: 'May' },
    { value: 6, label: 'June' },
    { value: 7, label: 'July' },
    { value: 8, label: 'August' },
    { value: 9, label: 'September' },
    { value: 10, label: 'October' },
    { value: 11, label: 'November' },
    { value: 12, label: 'December' },
  ];

  const selectedMonth = 1;
  const selectedYear = 2024;

  const handleRefresh = () => {
    console.log('Refresh triggered');
    // In real app, this would fetch data
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="calendar-outline" size={64} color="#ccc" />
      <Text style={styles.emptyText}>No attendance records found</Text>
      <Text style={styles.emptySubtext}>
        {filterType === 'today' && 'No attendance data for today'}
        {filterType === 'weekly' && 'No attendance data for this week'}
        {filterType === 'monthly' && `No attendance data for ${months.find(m => m.value === selectedMonth)?.label} ${selectedYear}`}
        {!filterType && 'Try adjusting your filter settings'}
      </Text>
      <TouchableOpacity 
        style={styles.refreshButton} 
        onPress={handleRefresh}
      >
        <Ionicons name="refresh-outline" size={20} color="#0F96BB" />
        <Text style={styles.refreshButtonText}>Refresh</Text>
      </TouchableOpacity>
    </View>
  );

  const renderItem = ({ item }: any) => (
    <View style={styles.tableRow}>
      <Text style={styles.tableCell}>{item.id}</Text>
      <Text style={styles.tableCell}>{item.date}</Text>
      <Text style={styles.tableCell}>{item.check_in_time}</Text>
      <Text style={styles.tableCell}>{item.check_out_time}</Text>
    </View>
  );

  const TableHeader = () => (
    <View style={styles.tableHeader}>
      <Text style={styles.headerCell}>S.No</Text>
      <Text style={styles.headerCell}>Date</Text>
      <Text style={styles.headerCell}>Check In</Text>
      <Text style={styles.headerCell}>Check Out</Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen 
        options={{ 
          title: 'Attendance Empty State Demo',
          headerShown: true,
          headerStyle: { backgroundColor: '#0F96BB' },
          headerTintColor: 'white',
          headerTitleStyle: { fontWeight: 'bold' }
        }} 
      />
      
      <ScrollView style={styles.scrollView}>
        {/* Controls */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Demo Controls</Text>
          
          <View style={styles.controlRow}>
            <Text style={styles.controlLabel}>Show Data:</Text>
            <TouchableOpacity
              style={[styles.toggleButton, { backgroundColor: showEmptyState ? '#ccc' : '#0F96BB' }]}
              onPress={() => setShowEmptyState(!showEmptyState)}
            >
              <Text style={[styles.toggleButtonText, { color: showEmptyState ? '#666' : 'white' }]}>
                {showEmptyState ? 'Show Empty State' : 'Show Data'}
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.controlRow}>
            <Text style={styles.controlLabel}>Filter Type:</Text>
            <View style={styles.filterButtons}>
              {['today', 'weekly', 'monthly', null].map((type) => (
                <TouchableOpacity
                  key={type || 'none'}
                  style={[
                    styles.filterButton,
                    { backgroundColor: filterType === type ? '#0F96BB' : '#f0f0f0' }
                  ]}
                  onPress={() => setFilterType(type as any)}
                >
                  <Text style={[
                    styles.filterButtonText,
                    { color: filterType === type ? 'white' : '#666' }
                  ]}>
                    {type || 'None'}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>

        {/* Demo Attendance List */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Attendance History (Demo)</Text>
          <Text style={styles.cardSubtitle}>
            This shows how the empty state appears in the actual Attendance History page
          </Text>
          
          <View style={styles.demoContainer}>
            {mockData.length > 0 && !showEmptyState ? (
              <FlatList
                data={mockData}
                renderItem={renderItem}
                keyExtractor={item => item.id.toString()}
                ListHeaderComponent={TableHeader}
                stickyHeaderIndices={[0]}
                scrollEnabled={false}
                style={styles.demoList}
              />
            ) : (
              <ScrollView
                style={styles.emptyStateWrapper}
                contentContainerStyle={styles.emptyScrollContent}
              >
                {renderEmptyState()}
              </ScrollView>
            )}
          </View>
        </View>

        {/* Features Explanation */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Improvements Made</Text>
          
          <View style={styles.featureList}>
            <View style={styles.featureItem}>
              <Ionicons name="checkmark-circle" size={20} color="#4CD964" />
              <Text style={styles.featureText}>
                <Text style={styles.featureBold}>Centered Layout:</Text> Empty state is now properly centered in the page
              </Text>
            </View>

            <View style={styles.featureItem}>
              <Ionicons name="checkmark-circle" size={20} color="#4CD964" />
              <Text style={styles.featureText}>
                <Text style={styles.featureBold}>Visual Icon:</Text> Added calendar icon for better visual appeal
              </Text>
            </View>

            <View style={styles.featureItem}>
              <Ionicons name="checkmark-circle" size={20} color="#4CD964" />
              <Text style={styles.featureText}>
                <Text style={styles.featureBold}>Context-Aware Messages:</Text> Different messages based on filter type
              </Text>
            </View>

            <View style={styles.featureItem}>
              <Ionicons name="checkmark-circle" size={20} color="#4CD964" />
              <Text style={styles.featureText}>
                <Text style={styles.featureBold}>Refresh Action:</Text> Added refresh button for easy data reload
              </Text>
            </View>

            <View style={styles.featureItem}>
              <Ionicons name="checkmark-circle" size={20} color="#4CD964" />
              <Text style={styles.featureText}>
                <Text style={styles.featureBold}>Better Typography:</Text> Improved text hierarchy and spacing
              </Text>
            </View>
          </View>
        </View>

        {/* Navigation */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Navigation</Text>
          <TouchableOpacity
            style={styles.navButton}
            onPress={() => router.push('/attendance' as any)}
          >
            <Ionicons name="calendar-outline" size={20} color="white" />
            <Text style={styles.navButtonText}>Go to Attendance History</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  cardSubtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
    lineHeight: 20,
  },
  controlRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  controlLabel: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  toggleButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
  },
  toggleButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  filterButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  filterButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 6,
  },
  filterButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  demoContainer: {
    borderWidth: 1,
    borderColor: '#e1e1e1',
    borderRadius: 8,
    overflow: 'hidden',
  },
  demoList: {
    height: 300,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#f0f0f0',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
    paddingVertical: 12,
  },
  headerCell: {
    flex: 1,
    fontWeight: 'bold',
    textAlign: 'center',
    color: '#555',
    fontSize: 14,
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
    paddingVertical: 12,
    backgroundColor: 'white',
  },
  tableCell: {
    flex: 1,
    textAlign: 'center',
    color: '#333',
    fontSize: 13,
  },
  // Empty state styles (matching the actual attendance page)
  emptyStateWrapper: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  emptyScrollContent: {
    flexGrow: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 20,
    minHeight: 400,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#555',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#888',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  refreshButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f0f8ff',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#0F96BB',
    gap: 8,
  },
  refreshButtonText: {
    fontSize: 16,
    color: '#0F96BB',
    fontWeight: '500',
  },
  featureList: {
    gap: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
  },
  featureText: {
    flex: 1,
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
  },
  featureBold: {
    fontWeight: '600',
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#0F96BB',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
  },
  navButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
});
