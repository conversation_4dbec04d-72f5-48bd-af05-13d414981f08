import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Dimensions, Platform, StatusBar } from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import { SafeAreaView } from 'react-native-safe-area-context';

const { width, height } = Dimensions.get('window');
const ASPECT_RATIO = width / height;
const LATITUDE_DELTA = 0.0922;
const LONGITUDE_DELTA = LATITUDE_DELTA * ASPECT_RATIO;

export default function ConveyanceMapScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();

  const {
    checkin_latitude,
    checkin_longitude,
    checkout_latitude,
    checkout_longitude,
    activity_name,
    start_time,
    end_time
  } = params;

  // Convert string coordinates to numbers
  const checkInLocation = {
    latitude: parseFloat(checkin_latitude as string),
    longitude: parseFloat(checkin_longitude as string),
  };

  const checkOutLocation = {
    latitude: parseFloat(checkout_latitude as string),
    longitude: parseFloat(checkout_longitude as string),
  };

  // Calculate the center point between check-in and check-out locations
  const centerLocation = {
    latitude: (checkInLocation.latitude + checkOutLocation.latitude) / 2,
    longitude: (checkInLocation.longitude + checkOutLocation.longitude) / 2,
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Stack.Screen
        options={{
          headerShown: false
        }}
      />
      
      {/* Custom Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Location Map</Text>
        <View style={{ width: 40 }} />
      </View>

      <View style={styles.mapContainer}>
        <MapView
          provider={PROVIDER_GOOGLE}
          style={styles.map}
          initialRegion={{
            ...centerLocation,
            latitudeDelta: LATITUDE_DELTA,
            longitudeDelta: LONGITUDE_DELTA,
          }}
        >
          {/* Check-in Marker */}
          <Marker
            coordinate={checkInLocation}
            title="Check In"
            description={`${activity_name} - ${start_time}`}
            pinColor="green"
          />

          {/* Check-out Marker */}
          <Marker
            coordinate={checkOutLocation}
            title="Check Out"
            description={`${activity_name} - ${end_time}`}
            pinColor="red"
          />
        </MapView>
      </View>

      <View style={styles.legend}>
        <View style={styles.legendItem}>
          <View style={[styles.legendDot, { backgroundColor: '#00C853' }]} />
          <Text style={styles.legendText}>Check In Location</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendDot, { backgroundColor: '#FF3D00' }]} />
          <Text style={styles.legendText}>Check Out Location</Text>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#0F96BB',
    paddingVertical: 15,
    paddingHorizontal: 15,
    ...Platform.select({
      android: {
        paddingTop: StatusBar.currentHeight,
      },
    }),
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  backButton: {
    padding: 8,
  },
  mapContainer: {
    flex: 1,
    overflow: 'hidden',
  },
  map: {
    flex: 1,
  },
  legend: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 15,
    flexDirection: 'row',
    justifyContent: 'space-around',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  legendText: {
    fontSize: 14,
    color: '#333',
  },
}); 