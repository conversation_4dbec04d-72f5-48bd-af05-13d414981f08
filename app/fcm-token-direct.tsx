import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Button, ScrollView, SafeAreaView, Platform } from 'react-native';
import { Stack } from 'expo-router';
import firebase from '@react-native-firebase/app';
import messaging from '@react-native-firebase/messaging';

// Firebase config
const firebaseConfig = {
  apiKey: "AIzaSyCh5gE623XX2PvCGIBOxJpqzKBicZNHVek",
  projectId: "salescrm-f2511",
  appId: "1:880620206085:android:d6697f8f77f46b66b3c46c",
  messagingSenderId: "880620206085",
  databaseURL: "https://salescrm-f2511.firebaseio.com",
  storageBucket: "salescrm-f2511.appspot.com"
};

export default function FCMTokenDirectScreen() {
  const [logs, setLogs] = useState<string[]>([]);
  const [token, setToken] = useState<string | null>(null);

  const log = (message: string) => {
    console.log(message);
    setLogs(prev => [...prev, message]);
  };

  const initFirebaseDirect = async () => {
    try {
      log('Initializing Firebase directly...');
      
      // Delete existing apps first
      if (firebase.apps.length > 0) {
        await Promise.all(firebase.apps.map(app => app.delete()));
        log('Deleted existing Firebase apps');
      }
      
      // Initialize Firebase with direct config
      const app = await firebase.initializeApp(firebaseConfig);
      log(`Firebase initialized with app name: ${app.name}`);
      
      // Verify app has correct options
      if (app.options) {
        log(`Project ID: ${app.options.projectId}`);
        log(`API Key available: ${app.options.apiKey ? 'yes' : 'no'}`);
      }
      
      return true;
    } catch (error) {
      log(`Firebase init error: ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  };

  const getPermissions = async () => {
    try {
      log('Requesting FCM permissions...');
      
      if (Platform.OS === 'android') {
        const authStatus = await messaging().requestPermission();
        log(`Permission status: ${authStatus}`);
        return true;
      } else if (Platform.OS === 'ios') {
        await messaging().registerDeviceForRemoteMessages();
        const authStatus = await messaging().requestPermission();
        log(`iOS Permission status: ${authStatus}`);
        return true;
      }
      
      return false;
    } catch (error) {
      log(`Permission error: ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  };

  const getFCMTokenDirect = async () => {
    try {
      log('Requesting FCM token directly...');
      
      // Get token directly from messaging
      const fcmToken = await messaging().getToken();
      
      if (fcmToken) {
        log('✅ FCM token obtained!');
        setToken(fcmToken);
        return fcmToken;
      } else {
        log('❌ Empty FCM token result');
        return null;
      }
    } catch (error) {
      log(`❌ FCM token error: ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  };
  
  const runCompleteTest = async () => {
    try {
      // Clear previous logs
      setLogs([]);
      setToken(null);
      
      log('🔄 Starting complete direct FCM test...');
      
      // Step 1: Initialize Firebase
      const firebaseInitialized = await initFirebaseDirect();
      if (!firebaseInitialized) {
        log('❌ Firebase initialization failed - stopping test');
        return;
      }
      
      // Step 2: Get permissions
      const hasPermissions = await getPermissions();
      if (!hasPermissions) {
        log('❌ Permission request failed - continuing anyway');
      }
      
      // Step 3: Get FCM token
      const fcmToken = await getFCMTokenDirect();
      if (!fcmToken) {
        log('❌ Failed to get FCM token');
      }
      
      log('🏁 Test complete');
    } catch (error) {
      log(`❌ Test error: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ title: 'Direct FCM Token Test' }} />
      
      <View style={styles.buttonContainer}>
        <Button 
          title="🔥 Initialize Firebase" 
          onPress={initFirebaseDirect} 
        />
        <Button 
          title="📱 Get Permissions" 
          onPress={getPermissions} 
        />
        <Button 
          title="🔑 Get FCM Token" 
          onPress={getFCMTokenDirect} 
        />
      </View>
      
      <Button 
        title="🧪 Run Complete Test" 
        onPress={runCompleteTest} 
        color="#4630EB"
      />
      
      {token ? (
        <View style={styles.tokenContainer}>
          <Text style={styles.tokenTitle}>FCM Token:</Text>
          <ScrollView style={styles.tokenScroll}>
            <Text selectable={true} style={styles.tokenText}>{token}</Text>
          </ScrollView>
        </View>
      ) : (
        <Text style={styles.noToken}>No FCM token available</Text>
      )}
      
      <Text style={styles.logsTitle}>Logs:</Text>
      <ScrollView style={styles.logs}>
        {logs.map((log, index) => (
          <Text key={index} style={styles.logText}>{log}</Text>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  tokenContainer: {
    backgroundColor: '#e6f7ff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
  },
  tokenTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  tokenScroll: {
    maxHeight: 100,
    backgroundColor: '#fff',
    padding: 8,
    borderRadius: 4,
  },
  tokenText: {
    fontFamily: 'monospace',
  },
  noToken: {
    textAlign: 'center',
    marginVertical: 20,
    color: '#999',
  },
  logsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  logs: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 8,
  },
  logText: {
    fontFamily: 'monospace',
    marginBottom: 4,
  },
}); 