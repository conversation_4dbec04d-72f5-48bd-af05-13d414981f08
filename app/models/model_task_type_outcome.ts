export interface TaskOutcome {
  id: number;
  salesactivities_id: number;
  outcome: string;
  created_at: string | null;
  updated_at: string | null;
}

export interface TaskType {
  id: number;
  task_type: string;
  active: number;
  created_at: string | null;
  updated_at: string | null;
}

export interface Owner {
  id: number;
  name: string;
  email: string;
  mobile: string;
}

export interface Contact {
  id: number;
  name: string;
  email: string | null;
  mobile: string | null;
}

export interface Account {
  id: number;
  name: string;
  mobile: string | null;
}

export interface Deal {
  id: number;
  name: string;
}

export interface RelatedTo {
  contacts: Contact[];
  accounts: Account[];
  deals: Deal[];
}

export interface TaskFieldsResponse {
  status: number;
  message: string;
  tasks: {
    outcomes: TaskOutcome[];
    task_type: TaskType[];
    owners: Owner[];
    related_to: RelatedTo;
  };
}

// Helper function to get active task types
export const getActiveTaskTypes = (taskTypes: TaskType[]): TaskType[] => {
  return taskTypes.filter(type => type.active === 1);
};

// Helper function to get task type by ID
export const getTaskTypeById = (taskTypes: TaskType[], id: number): TaskType | undefined => {
  return taskTypes.find(type => type.id === id);
};

// Helper function to get task type name by ID
export const getTaskTypeName = (taskTypes: TaskType[], id: number): string => {
  const taskType = getTaskTypeById(taskTypes, id);
  return taskType ? taskType.task_type : 'Unknown Type';
};

// Helper function to get outcome by ID
export const getOutcomeById = (outcomes: TaskOutcome[], id: number): TaskOutcome | undefined => {
  return outcomes.find(outcome => outcome.id === id);
};

// Helper function to get outcome name by ID
export const getOutcomeName = (outcomes: TaskOutcome[], id: number): string => {
  const outcome = getOutcomeById(outcomes, id);
  return outcome ? outcome.outcome : 'Unknown Outcome';
};

// Helper function to get outcomes by salesactivities_id
export const getOutcomesBySalesActivityId = (outcomes: TaskOutcome[], salesactivities_id: number): TaskOutcome[] => {
  return outcomes.filter(outcome => outcome.salesactivities_id === salesactivities_id);
};

// Helper function to format the response data
export const formatTaskFieldsResponse = (response: TaskFieldsResponse) => {
  return {
    taskTypes: getActiveTaskTypes(response.tasks.task_type),
    outcomes: response.tasks.outcomes,
    owners: response.tasks.owners,
    relatedTo: response.tasks.related_to
  };
};

// Helper function to get unique salesactivities_ids from outcomes
export const getUniqueSalesActivitiesIds = (outcomes: TaskOutcome[]): number[] => {
  return [...new Set(outcomes.map(outcome => outcome.salesactivities_id))];
};

// Helper function to group outcomes by salesactivities_id
export const groupOutcomesBySalesActivity = (outcomes: TaskOutcome[]): { [key: number]: TaskOutcome[] } => {
  return outcomes.reduce((acc, outcome) => {
    const { salesactivities_id } = outcome;
    if (!acc[salesactivities_id]) {
      acc[salesactivities_id] = [];
    }
    acc[salesactivities_id].push(outcome);
    return acc;
  }, {} as { [key: number]: TaskOutcome[] });
};

// Helper function to get all related items in a flat array
export const getAllRelatedItems = (relatedTo: RelatedTo): { id: number; name: string; type: string }[] => {
  return [
    ...relatedTo.contacts.map(contact => ({ ...contact, type: 'Contact' })),
    ...relatedTo.accounts.map(account => ({ ...account, type: 'Account' })),
    ...relatedTo.deals.map(deal => ({ ...deal, type: 'Deal' }))
  ];
}; 