export interface Meeting {
  id: number;
  activity_title: string;
  description: string;
  location: string;
  start_date: string;
  start_time: string;
  end_date: string;
  end_time: string;
  all_day: string;
  time_zone: string;
  notes: string;
  outcome: string;
  task_type: string;
  targetable_id: string;
  targetable_type: string;
  attendance: string;
  attendance_type: string;
}

export interface MeetingsResponse {
  success: boolean;
  message?: string;
  meetings: Meeting[];
}

// Helper function to check if a meeting is upcoming
export const isMeetingUpcoming = (meeting: Meeting): boolean => {
  const now = new Date();
  const [day, month, year] = meeting.start_date.split('-').map(Number);
  const [hours, minutes] = meeting.start_time.toLowerCase().replace(/\s/g, '')
    .replace('am', '').replace('pm', '').split(':').map(Number);
  const isPM = meeting.start_time.toLowerCase().includes('pm');
  
  const meetingDate = new Date(year, month - 1, day,
    isPM && hours !== 12 ? hours + 12 : hours,
    minutes
  );
  
  return meetingDate > now;
};

// Helper function to check if a meeting is overdue
export const isMeetingOverdue = (meeting: Meeting): boolean => {
  const now = new Date();
  const [day, month, year] = meeting.start_date.split('-').map(Number);
  const [hours, minutes] = meeting.start_time.toLowerCase().replace(/\s/g, '')
    .replace('am', '').replace('pm', '').split(':').map(Number);
  const isPM = meeting.start_time.toLowerCase().includes('pm');
  
  const meetingDate = new Date(year, month - 1, day,
    isPM && hours !== 12 ? hours + 12 : hours,
    minutes
  );
  
  return meetingDate < now;
};

// Helper function to format meeting date and time for display
export const formatMeetingDateTime = (date: string, time: string): string => {
  const [day, month, year] = date.split('-').map(Number);
  return `${new Date(year, month - 1, day).toLocaleDateString('en-US', {
    weekday: 'short',
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })}, ${time}`;
}; 