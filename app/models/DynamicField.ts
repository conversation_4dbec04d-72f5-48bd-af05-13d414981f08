export interface DynamicField {
  id: string;
  label: string;
  field_type: 'text' | 'email' | 'phone' | 'textarea' | 'select' | 'date';
  placeholder?: string;
  is_required: boolean;
  section: string;
  options?: string[];
  validation_rules?: {
    pattern?: string;
    min_length?: number;
    max_length?: number;
  };
}

export interface DynamicFieldsResponse {
  status: string;
  message: string;
  fields: DynamicField[];
} 