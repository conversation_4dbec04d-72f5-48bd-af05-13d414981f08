export interface Account {
  id: number;
  name: string;
  website: string | null;
  phone: string | null;
  owner_id: string | null;
  custom_field: string | null;
  creater_id: number | null;
  deal_value: string | null;
  active: number;
  create_at: string | null;
  number_of_employees: string | null;
  price: string | null;
  annual_revenue: string | null;
  dealvalue: string | null;
  dealcount: string | null;
  open_dealvalue: number | null;
  open_dealcount: number | null;
  created_at: string;
  updated_at: string;
}

export interface AccountsResponse {
  status: number;
  message: string;
  sales_accounts: Account[];
}

export interface DisplayAccount extends Account {
  initial: string;
} 