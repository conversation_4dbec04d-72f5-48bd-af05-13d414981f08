export interface AccountsListResponse {
    status: number;
    message: string;
    contacts: Contact[];
    filters: Record<string, any>;
}

export interface Contact {
    id: number;
    first_name: string | null;
    last_name: string | null;
    sales_accounts: string | null;
    emails: string | null;
    telephone_numbers: string | null;
    mobile_number: string | null;
    work_number: string | null;
    owner_id: string | null;
    subscription_status: string | null;
    subscription_types: string | null;
    unsubscription_reason: string | null;
    other_unsubscription_reason: string | null;
    lifecycle_stage_id: string | null;
    contact_status_id: string | null;
    lost_reason_id: string | null;
    first_campaign: string | null;
    first_medium: string | null;
    first_source: string | null;
    last_campaign: string | null;
    last_medium: string | null;
    last_source: string | null;
    latest_campaign: string | null;
    latest_medium: string | null;
    latest_source: string | null;
    campaign_id: string | null;
    medium: string | null;
    keyword: string | null;
    country: string | null;
    lead_source_id: string | null;
    last_contacted: string | null;
    tags: string | null;
    customer_fit: string | null;
    web_form_ids: string | null;
    recent_note: string | null;
    external_id: string | null;
    job_title: string | null;
    sms_subscription_status: string | null;
    whatsapp_subscription_status: string | null;
    work_email: string | null;
    phone_numbers: string | null;
    address: string | null;
    city: string | null;
    state: string | null;
    zipcode: string | null;
    facebook: string | null;
    twitter: string | null;
    linkedin: string | null;
    territory_id: string | null;
    lists: string | null;
    last_seen_chat: string | null;
    total_sessions: string | null;
    locale: string | null;
    first_seen_chat: string | null;
    last_contacted_mode: string | null;
    last_contacted_sales_activity_mode: string | null;
    last_contacted_via_sales_activity: string | null;
    active_sales_sequences: string | null;
    completed_sales_sequences: string | null;
    last_seen: string | null;
    lead_score: string | null;
    creater_id: number | null;
    create_at: string | null;
    updater_id: string | null;
    update_at: string | null;
    last_assigned_at: string | null;
    time_zone: string | null;
    active: string | null;
    custom_field: string | null;
    deal_value: string | null;
    dealcount: string | null;
    open_dealvalue: number | null;
    open_dealcount: number | null;
    gst: string | null;
    created_at: string | null;
    updated_at: string | null;
}

export interface SalesAccountsResponse {
    status: number;
    message: string;
    sales_accounts: SalesAccount[];
}

export interface SalesAccount {
    id: number;
    name: string;
    website: string | null;
    phone: string | null;
    owner_id: string;
    custom_field: string; // This is a JSON string that will be parsed
    creater_id: number;
    deal_value: string;
    active: number;
    create_at: string;
    number_of_employees: string | null;
    price: string | null;
    annual_revenue: string | null;
    dealvalue: string | null;
    dealcount: string | null;
    open_dealvalue: number | null;
    open_dealcount: number | null;
    created_at: string;
    updated_at: string;
}

// Custom field interfaces for better type safety when parsing custom_field JSON
export interface CustomField {
    accountsgroups_id: string;
    active: string;
    choices: CustomFieldChoice[];
    contactgroups_id: string | null;
    created_at: string;
    custom_field: string;
    customchoices: any[];
    field_label: string;
    field_name: string;
    field_type: string;
    field_value: string | null;
    has_dependent: string | null;
    id: string;
    lookup_column: string | null;
    lookup_type: string | null;
    order: string | null;
    payload: string | null;
    placeholder: string;
    quick_add: string | null;
    read_only: string | null;
    required: string | null;
    tool_tip: string;
    updated_at: string;
}

export interface CustomFieldChoice {
    contactfield_id: string | null;
    created_at: string;
    custom_option: string;
    id: string;
    quotationfield_id: string | null;
    salesactivitycustomfield_id: string | null;
    updated_at: string;
} 