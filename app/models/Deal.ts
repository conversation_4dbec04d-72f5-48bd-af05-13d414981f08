export interface Deal {
    id: number;
    name: string;
    amount: string | null;
    contacts: string | null;
    deal_pipeline_id: string | null;
    deal_stage_id: string | null;
    deal_stage: string | null;
    closed_date: string | null;
    owner_id: string | null;
    currency_id: string | null;
    quotation_id: number | null;
    active: number;
    created_at: string;
    updated_at: string;
}

export interface DealsResponse {
    success: boolean;
    data: Deal[];
    message: string;
} 