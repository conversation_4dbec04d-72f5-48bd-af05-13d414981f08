export interface QuoteFieldChoice {
  id: number;
  quotationfield_id: number;
  custom_option?: string;
  value?: string;
  created_at?: string;
  updated_at?: string;
}

export interface QuoteField {
  id: number;
  custom_field: number;
  field_label: string;
  field_name: string;
  field_type: string;
  required: string | null;
  quick_add: string | null;
  tool_tip: string | null;
  placeholder: string | null;
  choices: QuoteFieldChoice[];
  read_only: string | null;
  has_dependent: string | null;
  quotationgroups_id: number;
  active: number;
  order: number | null;
  lookup_type: string | null;
  lookup_column: string | null;
  created_at: string;
  updated_at: string;
  field_value: string | null;
}

export interface QuoteFieldsResponse {
  status: boolean;
  message: string;
  response: QuoteField[];
}

export interface QuoteFormData {
  [key: string]: string | number | null;
} 