export interface MeetingListResponse {
  status: number;
  message: string;
  response: MeetingListItem[];
}

export interface MeetingListItem {
  id: number;
  salesactivities_id: number;
  activity_type: string;
  activity_date: string;
  activity_title: string;
  description: string | null;
  notes: string | null;
  all_day: string | null;
  start_date: string;
  end_date: string;
  start_time: string | null;
  end_time: string | null;
  owner_id: number;
  collaboratoes: any | null;
  sales_activity_outcome_id: number | null;
  creater_id: string;
  updater_id: string | null;
  attendees: any | null;
  attendance_type: string | null;
  conversation_time: string | null;
  targetable_id: string | null;
  targetable_type: string | null;
  custom_field: any | null;
  allow_complete: string | null;
  completed_date: string | null;
  completed_time: string | null;
  remarks: string | null;
  attendance: string | null;
  task_type: string | null;
  location: string | null;
  timezone: string | null;
  checkin_location: string | null;
  checkin_time: string | null;
  checkin_latitude: string | null;
  checkin_longitude: string | null;
  checkout_location: string | null;
  checkout_time: string | null;
  checkout_latitude: string | null;
  checkout_longitude: string | null;
  timespent: string | null;
  timespent_display: string | null;
  status: string;
  outcome: string | null;
  lattitude: string | null;
  longitude: string | null;
  distance: number | null;
  created_at: string | null;
  updated_at: string;
}

// Helper functions
export const formatMeetingDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    weekday: 'short',
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });
};

export const formatMeetingTime = (timeString: string | null): string => {
  if (!timeString) return '';
  return timeString;
};

export const getMeetingStatus = (meeting: MeetingListItem): 'Pending' | 'Checkin' | 'Completed' => {
  return meeting.status as 'Pending' | 'Checkin' | 'Completed';
};

export const isMeetingOverdue = (meeting: MeetingListItem): boolean => {
  const now = new Date();
  const meetingDate = new Date(meeting.start_date);
  const meetingTime = meeting.start_time;
  
  if (meetingTime) {
    try {
      // First, check if the time format contains a colon
      if (meetingTime.includes(':')) {
        const timeParts = meetingTime.split(':');
        if (timeParts.length >= 2) {
          let hours = parseInt(timeParts[0]);
          let minutesPart = timeParts[1];
          let minutes = 0;
          
          // Check if minutes part has AM/PM indicator
          if (minutesPart.includes(' ')) {
            const [mins, period] = minutesPart.split(' ');
            minutes = parseInt(mins);
            // Adjust hours for PM
            if (period && period.toLowerCase() === 'pm' && hours !== 12) {
              hours += 12;
            }
          } else {
            // Just extract minutes without AM/PM
            minutes = parseInt(minutesPart);
          }
          
          // Set the time components if valid
          if (!isNaN(hours) && !isNaN(minutes)) {
            meetingDate.setHours(hours, minutes);
          }
        }
      }
    } catch (error) {
      console.warn('Error parsing meeting time:', error);
      // If time parsing fails, just use the date part
    }
  }
  
  return meetingDate < now && meeting.status === 'Pending';
}; 