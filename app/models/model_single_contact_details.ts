export interface SingleContactResponse {
  status: number;
  message: string;
  contact: ContactDetails;
}

export interface ContactDetails {
  contacts: ContactField[];
  tasks: Task[];
  sales_account: any | null;
  meetings: any[];
  notes_count: number;
  count: {
    tasks: number;
    meetings: number;
    notes: number;
    sales_accounts: number;
    files: number;
  };
}

export interface ContactField {
  id: number;
  custom_field: number;
  field_label: string;
  field_name: string;
  field_type: string;
  required: string | null;
  quick_add: string;
  tool_tip: string;
  placeholder: string;
  choices: Choice[];
  read_only: string | null;
  has_dependent: string | null;
  contactgroups_id: number;
  active: number;
  order: number | null;
  lookup_type: string | null;
  lookup_column: string | null;
  created_at: string;
  updated_at: string;
  field_value: string | null;
}

export interface Choice {
  id: number;
  contactfield_id: number;
  custom_option: string;
  created_at: string;
  updated_at: string;
}

export interface Task {
  id: number;
  title: string;
  description: string | null;
}

// Helper function to get field value by field name
export const getFieldValue = (contacts: ContactField[], fieldName: string): string | null => {
  const field = contacts.find(contact => contact.field_name === fieldName);
  return field?.field_value || null;
};

// Helper function to get field choices by field name
export const getFieldChoices = (contacts: ContactField[], fieldName: string): Choice[] => {
  const field = contacts.find(contact => contact.field_name === fieldName);
  return field?.choices || [];
};

// Helper function to check if a field is required
export const isFieldRequired = (contacts: ContactField[], fieldName: string): boolean => {
  const field = contacts.find(contact => contact.field_name === fieldName);
  return field?.required === 'on';
};

// Helper function to get field by field name
export const getField = (contacts: ContactField[], fieldName: string): ContactField | undefined => {
  return contacts.find(contact => contact.field_name === fieldName);
}; 