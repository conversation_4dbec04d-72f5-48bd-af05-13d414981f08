export interface NotesData {
  id: number;
  belongsto_id: number;
  belongsto: string;
  notes: string;
  voicenote: string;
  users_id: number;
  note_time: string;
  active: string;
  is_deleted: number;
  created_at: string;
  updated_at: string;
}

export interface NotesResponse {
  status: number;
  success: boolean;
  message: string;
  data: NotesData[];
}

export class Note {
  public id: number;
  public belongsto_id: number;
  public belongsto: string;
  public notes: string;
  public voicenote: string;
  public users_id: number;
  public note_time: Date;
  public active: boolean;
  public is_deleted: boolean;
  public created_at: Date;
  public updated_at: Date;

  constructor(data: NotesData) {
    this.id = data.id;
    this.belongsto_id = data.belongsto_id;
    this.belongsto = data.belongsto;
    this.notes = data.notes;
    this.voicenote = data.voicenote;
    this.users_id = data.users_id;
    this.note_time = new Date(data.note_time);
    this.active = data.active === '1';
    this.is_deleted = data.is_deleted === 1;
    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
  }

  /**
   * Get the voice note URL
   */
  public getVoiceNoteUrl(): string | null {
    return this.voicenote ;//? `${process.env.API_BASE_URL}/uploads/notes/${this.voicenote}` : null;
  }

  /**
   * Check if the note has a voice recording
   */
  public hasVoiceNote(): boolean {
    return !!this.voicenote;
  }

  /**
   * Check if the note is text-only (no voice note)
   */
  public isTextOnly(): boolean {
    return !!this.notes && !this.voicenote;
  }

  /**
   * Check if the note is active
   */
  public isActive(): boolean {
    return this.active && !this.is_deleted;
  }

  /**
   * Format the note time for display
   */
  public formatNoteTime(format: 'short' | 'long' = 'short'): string {
    if (format === 'short') {
      const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
      const day = days[this.note_time.getDay()];
      const date = this.note_time.getDate().toString().padStart(2, '0');
      const month = (this.note_time.getMonth() + 1).toString().padStart(2, '0');
      return `${day} ${date}/${month}`;
    }
    return this.note_time.toLocaleString();
  }

  /**
   * Create a plain object representation of the note (for storage or API)
   */
  public toJSON(): NotesData {
    return {
      id: this.id,
      belongsto_id: this.belongsto_id,
      belongsto: this.belongsto,
      notes: this.notes,
      voicenote: this.voicenote,
      users_id: this.users_id,
      note_time: this.note_time.toISOString(),
      active: this.active ? '1' : '0',
      is_deleted: this.is_deleted ? 1 : 0,
      created_at: this.created_at.toISOString(),
      updated_at: this.updated_at.toISOString()
    };
  }

  /**
   * Create a Note instance from API response data
   */
  public static fromJSON(json: NotesData): Note {
    return new Note(json);
  }

  /**
   * Create multiple Note instances from API response data
   */
  public static fromJSONArray(jsonArray: NotesData[]): Note[] {
    return jsonArray.map(data => Note.fromJSON(data));
  }

  /**
   * Create Note instances from API response
   */
  public static fromResponse(response: NotesResponse): Note[] {
    if (!response.success || !Array.isArray(response.data)) {
      throw new Error(response.message || 'Invalid response format');
    }
    return Note.fromJSONArray(response.data);
  }
}