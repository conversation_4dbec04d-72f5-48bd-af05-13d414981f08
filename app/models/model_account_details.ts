// Account Details Model
// Represents the structure of account details from the API

import { apiService } from '../../services/ApiService';
import { API_ENDPOINTS } from '../../config/api';

export interface CustomFieldChoice {
  id: number;
  accountfield_id: number;
  custom_option: string;
  created_at: string;
  updated_at: string;
}

export interface AccountField {
  id: number;
  custom_field: number;
  field_label: string;
  field_name: string;
  field_type: string;
  required: string | null;
  quick_add: string | null;
  tool_tip: string;
  placeholder: string;
  choices: CustomFieldChoice[];
  read_only: string | null;
  has_dependent: string | null;
  active: number;
  order: number | null;
  lookup_type: string | null;
  lookup_column: string | null;
  created_at: string | null;
  updated_at: string | null;
  field_value: string | null;
  isLink?: boolean;
  isEmpty?: boolean;
  isCustom?: boolean;
}

export interface Account {
  id: string | number;
  name?: string;
  website?: string | null;
  phone?: string | null;
  owner_id?: string | null;
  custom_field?: string | null;
  creater_id?: number;
  deal_value?: string | null;
  active?: number;
  create_at?: string | null;
  number_of_employees?: string | null;
  price?: string | null;
  annual_revenue?: string | null;
  dealvalue?: string | null;
  dealcount?: string | null;
  open_dealvalue?: string | number | null;
  open_dealcount?: string | number | null;
  is_deleted?: number;
  created_at?: string;
  updated_at?: string;
  accounts?: AccountField[];
  tasks?: any[];
  meetings?: any[];
  sales_account?: any;
  notes_count?: number;
  contacts?: any[];
  deals_summary?: {
    count: number;
    total_value: number;
    open_count: number;
    open_value: number;
  };
}

export interface CustomActivity {
  id: number;
  activity_name: string;
  activity_icon: string;
  count: number;
}

export interface ActivityItem {
  message: string;
  date: string;
  type: string;
  name: string;
  activity_type: string;
  activity_id: string;
  icon: string;
  outcome_id: string;
  outcome: string;
  actchoice: {
    type: string;
    date: string;
    activity_id: number;
    title: string;
    outcome_id: string | null;
  };
}

export interface Contact {
  id: number;
  first_name: string;
  last_name: string | null;
  sales_accounts: string;
  emails: string | null;
  telephone_numbers: string | null;
  mobile_number: string;
  work_number: string | null;
  owner_id: string | null;
  subscription_status: string;
  custom_field: string | null;
  active: string | null;
  created_at: string | null;
  updated_at: string;
  // Add other contact fields as needed
}

export interface NewSalesAccountResponse {
  status: number;
  message: string;
  sales_account: {
    status: number;
    message: string;
    account: Account;
  };
}

export interface AccountResponse {
  status: boolean;
  message: string;
  response: {
    account: Account;
    accountcustom: any | null;
    accountfields: AccountFieldGroup[];
    notes: number;
    tasks: number;
    meetings: number;
    calllogs: number;
    customactivities: CustomActivity[];
    contacts: Contact[];
    dealscount: number;
    dealsvalue: number;
    open_dealcount: string;
    open_dealvalue: number;
    share_url: string;
    activity: ActivityItem[];
  };
}

export interface SalesAccountResponse {
  status: number;
  message: string;
  sales_account: AccountResponse;
}

export interface AccountFieldGroup {
  fields: AccountField[];
}

/**
 * Fetches account details from the API
 * @param accountId The ID of the account to fetch
 * @returns The account details or null if the fetch fails
 */
export const fetchAccountDetails = async (accountId: string): Promise<{
  account: Account;
  accountfields: AccountField[];
  notes: number;
  tasks: number;
  meetings: number;
  customactivities?: CustomActivity[];
  contacts: any[];
} | null> => {
  try {
    // Construct the API endpoint
    const endpoint = `${API_ENDPOINTS.SALES_ACCOUNTS}/${accountId}`;
    
    // Make the API call
    const response = await apiService.get<NewSalesAccountResponse>(endpoint);
    
    // Check if the response is successful
    if (response.status === 200 && response.data.sales_account.status === 200) {
      const accountData = response.data.sales_account.account;
      
      // Extract account fields from the accounts array
      const accountFields = accountData.accounts || [];
      
      // Find the name field in the account fields
      const nameField = accountFields.find(field => field.field_name === 'name');
      
      return {
        account: {
          id: accountData.id,
          name: nameField?.field_value || 'Unknown Account',
          // Include other important fields for task creation
          deals_summary: accountData.deals_summary,
          contacts: accountData.contacts || []
        },
        accountfields: accountFields,
        notes: accountData.notes_count || 0,
        tasks: accountData.tasks?.length || 0,
        meetings: accountData.meetings?.length || 0,
        contacts: accountData.contacts || []
      };
    }
    
    console.error('Failed to fetch account details:', response);
    return null;
  } catch (error) {
    console.error('Error fetching account details:', error);
    return null;
  }
};

/**
 * Helper function to get a field value by name
 * @param accountFields The account fields
 * @param fieldName The name of the field to get
 * @returns The field value or null if not found
 */
export const getFieldValue = (accountFields: AccountField[], fieldName: string): string | null => {
  for (const field of accountFields) {
    if (field.field_name === fieldName) {
      return field.field_value;
    }
  }
  return null;
};

/**
 * Helper function to format account data for display
 * @param account The account data
 * @param accountFields The account fields
 * @returns Formatted account data
 */
export const formatAccountData = (account: Account, accountFields: AccountField[]): Record<string, any> => {
  const formattedData: Record<string, any> = { ...account };
  
  // Add field values from accountfields
  for (const field of accountFields) {
    formattedData[field.field_name] = field.field_value;
  }
  
  return formattedData;
};

/**
 * Helper function to extract account information for task creation
 * @param accountDetails The account details object
 * @returns An object with account ID and name for task creation
 */
export const extractAccountInfoForTask = (accountDetails: any): { id: string, name: string } | null => {
  if (!accountDetails) return null;
  
  try {
    console.log('Extracting account info from:', accountDetails);
    
    // If accountDetails has the expected structure with accountfields
    if (accountDetails.account && accountDetails.accountfields) {
      const nameField = accountDetails.accountfields.find((field: AccountField) => 
        field.field_name === 'name'
      );
      
      return {
        id: accountDetails.account.id.toString(),
        name: nameField?.field_value || accountDetails.account.name || 'Unknown Account'
      };
    }
    
    // If accountDetails has the expected structure with accounts array
    if (accountDetails.account && accountDetails.account.accounts) {
      const nameField = accountDetails.account.accounts.find((field: AccountField) => 
        field.field_name === 'name'
      );
      
      return {
        id: accountDetails.account.id.toString(),
        name: nameField?.field_value || accountDetails.account.name || 'Unknown Account'
      };
    }
    
    // If accountDetails is just the account object
    if (accountDetails.id) {
      // Check if it has an accounts array
      if (accountDetails.accounts) {
        const nameField = accountDetails.accounts.find((field: AccountField) => 
          field.field_name === 'name'
        );
        
        return {
          id: accountDetails.id.toString(),
          name: nameField?.field_value || accountDetails.name || 'Unknown Account'
        };
      }
      
      return {
        id: accountDetails.id.toString(),
        name: accountDetails.name || 'Unknown Account'
      };
    }
    
    console.warn('Could not extract account info from provided data');
    return null;
  } catch (error) {
    console.error('Error extracting account info for task:', error);
    return null;
  }
}; 