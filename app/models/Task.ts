export interface Task {
  id: number;
  task_type: string | null;
  title: string;
  description: string;
  due_date: string;
  due_time: string;
  outcome: string | null;
  mark_as_complete: string;
  completed_date: string | null;
  completed_time: string | null;
  user_id: number;
  related_type: string;
  related_to: number;
  collaboratoes: string;
  snooze_date: string | null;
  snooze_time: string | null;
  create_at: string;
  created_at: string;
  updated_at: string;
}

export interface TasksResponse {
  status: number;
  message: string;
  tasks: Task[];
}

// Helper function to determine if a task is overdue
export const isTaskOverdue = (task: Task): boolean => {
  if (!task || !task.due_date || !task.due_time || task.mark_as_complete === "true") return false;
  
  try {
    const dueDate = new Date(`${task.due_date.split('T')[0]}T${convertTimeFormat(task.due_time)}`);
    return !isNaN(dueDate.getTime()) && dueDate < new Date();
  } catch (e) {
    console.error('Error checking if task is overdue:', e);
    return false;
  }
};

// Helper function to determine if a task is upcoming
export const isTaskUpcoming = (task: Task): boolean => {
  if (!task || !task.due_date || !task.due_time || task.mark_as_complete === "true") return false;
  
  try {
    const dueDate = new Date(`${task.due_date.split('T')[0]}T${convertTimeFormat(task.due_time)}`);
    const now = new Date();
    
    return !isNaN(dueDate.getTime()) && dueDate >= now;
  } catch (e) {
    console.error('Error checking if task is upcoming:', e);
    return false;
  }
};

// Helper function to determine if a task is completed
export const isTaskCompleted = (task: Task): boolean => {
  if (!task) return false;
  return task.mark_as_complete === "true";
};

// Helper function to convert time from "03:30 PM" format to "15:30:00"
export const convertTimeFormat = (timeStr: string): string => {
  if (!timeStr) return "00:00:00";
  
  try {
    const [time, period] = timeStr.split(' ');
    if (!time || !period) return "00:00:00";
    
    const [hourStr, minuteStr] = time.split(':');
    if (!hourStr || !minuteStr) return "00:00:00";
    
    const hour = parseInt(hourStr, 10);
    const minute = parseInt(minuteStr, 10);
    
    if (isNaN(hour) || isNaN(minute)) return "00:00:00";
    
    let hour24 = hour;
    if (period === 'PM' && hour !== 12) {
      hour24 = hour + 12;
    } else if (period === 'AM' && hour === 12) {
      hour24 = 0;
    }
    
    return `${hour24.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:00`;
  } catch (e) {
    console.error('Error converting time format:', e);
    return "00:00:00";
  }
};

// Helper function to format date for display
export const formatTaskDate = (dateStr: string, timeStr: string): string => {
  if (!dateStr) return "No date";
  
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return "Invalid date";
    
    // Format time in AM/PM format if it's in 24-hour format (HH:MM:SS)
    let formattedTime = "No time";
    if (timeStr) {
      // Check if the time is already in AM/PM format
      if (timeStr.includes('AM') || timeStr.includes('PM')) {
        formattedTime = timeStr;
      } else {
        // Parse the time string (expected format: HH:MM:SS or HH:MM)
        const timeParts = timeStr.split(':');
        if (timeParts.length >= 2) {
          const hour = parseInt(timeParts[0], 10);
          const minute = parseInt(timeParts[1], 10);
          
          if (!isNaN(hour) && !isNaN(minute)) {
            const isPM = hour >= 12;
            const hour12 = hour % 12 || 12; // Convert 0 to 12 for 12 AM
            formattedTime = `${hour12}:${minute.toString().padStart(2, '0')} ${isPM ? 'PM' : 'AM'}`;
          } else {
            formattedTime = timeStr; // Fallback to original if parsing fails
          }
        } else {
          formattedTime = timeStr; // Fallback to original if format is unexpected
        }
      }
    }
    
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    const taskDate = new Date(date);
    taskDate.setHours(0, 0, 0, 0);
    
    if (taskDate.getTime() === today.getTime()) {
      return `Today, ${formattedTime}`;
    } else if (taskDate.getTime() === tomorrow.getTime()) {
      return `Tomorrow, ${formattedTime}`;
    } else if (taskDate.getTime() === yesterday.getTime()) {
      return `Yesterday, ${formattedTime}`;
    } else {
      return `${date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}, ${formattedTime}`;
    }
  } catch (e) {
    console.error('Error formatting task date:', e);
    return "Date error";
  }
}; 