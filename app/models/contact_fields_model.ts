export interface Choice {
  id: number;
  contactfield_id: number;
  custom_option: string;
  created_at: string;
  updated_at: string;
}


export interface ContactField {
  id: number;
  custom_field: number;
  field_label: string;
  field_name: string;
  field_type: string;
  required: string | null;
  quick_add: string;
  tool_tip: string;
  placeholder: string;
  choices: Choice[];
  read_only: string | null;
  has_dependent: string | null;
  contactgroups_id: number;
  active: number;
  order: number | null;
  lookup_type: string | null;
  lookup_column: string | null;
  created_at: string;
  updated_at
  : string;
  field_value: string | null;
}

export interface ContactFieldsResponse {
  status: number;
  message: string;
  contactFields: ContactField[];
}

// Field type constants for easy reference
export const FIELD_TYPES = {
  TEXT: 'Text field',
  NUMBER: 'Number',
  DROPDOWN: 'Dropdown',
  LOOKUP: 'Lookup',
  DATE: 'Date',
  DATE_PICKER: 'Datepicker',
  TIME: 'Time',
  DATETIME: 'Date and Time',
  CHECKBOX: 'Checkbox',
  RADIO: 'Radio',
  MULTISELECT: 'Multiselect',
  TEXTAREA: 'Textarea',
  CURRENCY: 'Currency',
  PERCENTAGE: 'Percentage',
  URL: 'URL',
  EMAIL: 'Email',
  PHONE: 'Phone',
  IMAGE: 'Image',
  FILE: 'File',
  LINK: 'Link',
  COLOR: 'Color',
  RATING: 'Rating',
  BOOLEAN: 'Boolean',
  PASSWORD: 'Password',
  HIDDEN: 'Hidden',
  AUTO_NUMBER: 'Auto Number',
  FORMULA: 'Formula'

} as const;

// Helper function to check if a field is required
export const isFieldRequired = (field: ContactField): boolean => {
  return field.required === 'on';
};

// Helper function to check if a field is read only
export const isFieldReadOnly = (field: ContactField): boolean => {
  return field.read_only === 'on';
};

// Helper function to get field by name
export const getFieldByName = (fields: ContactField[], fieldName: string): ContactField | undefined => {
  return fields.find(field => field.field_name === fieldName);
};

// Helper function to get dropdown choices
export const getFieldChoices = (field: ContactField): Choice[] => {
  return field.field_type === FIELD_TYPES.DROPDOWN ? field.choices : [];
}; 