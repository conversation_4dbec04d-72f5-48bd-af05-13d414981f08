export interface ClaimResponse {
  status: number;
  success: boolean;
  message: string;
  date: {
    startDate: string;
    endDate: string;
  };
  response: ClaimItem[];
}

export interface ClaimItem {
  id: number;
  salesactivities_id: number;
  activity_type: string;
  activity_date: string | null;
  activity_title: string;
  description: string;
  notes: string;
  all_day: string;
  start_date: string;
  end_date: string;
  start_time: string;
  end_time: string;
  owner_id: number;
  collaboratoes: string | null;
  sales_activity_outcome_id: number | null;
  creater_id: string | null;
  updater_id: string;
  attendees: string | null;
  attendance_type: string | null;
  conversation_time: string | null;
  targetable_id: string;
  targetable_type: string;
  custom_field: string | null;
  allow_complete: string | null;
  completed_date: string | null;
  completed_time: string | null;
  remarks: string | null;
  attendance: string | null;
  task_type: string | null;
  location: string | null;
  timezone: string;
  checkin_location: string;
  checkin_time: string;
  checkin_latitude: string;
  checkin_longitude: string;
  checkout_location: string;
  checkout_time: string;
  checkout_latitude: string;
  checkout_longitude: string;
  timespent: string;
  timespent_display: string;
  status: string;
  outcome: string | null;
  lattitude: string | null;
  longitude: string | null;
  distance: number | null;
  transport_mode: string | null;
  check_in_attachment: string | null;
  check_out_attachment: string | null;
  claim_amount: string | null;
  conveyance_charge: string | null;
  claim_status: string | null;
  claim_applied_on: string | null;
  is_deleted: number;
  created_at: string | null;
  updated_at: string;
}

// Helper type for filtering claims
export type ClaimFilter = {
  startDate: string;
  endDate: string;
  status?: string;
};

// Enum for claim status
export enum ClaimStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  REJECTED = 'rejected',
  APPLIED = 'Applied'
}

// Enum for transport modes
export enum TransportMode {
  BIKE = 'Bike',
  CAR = 'Car',
  PUBLIC = 'Public',
  OTHER = 'Other'
}

// Helper function to format date for display
export const formatClaimDate = (dateString: string): string => {
  const date = new Date(dateString);
  // Format as dd/mm day (e.g., "15/04 Mon")
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const dayOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][date.getDay()];
  return `${day}/${month} ${dayOfWeek}`;
};

// Helper function to calculate duration between check-in and check-out
export const calculateDuration = (checkIn: string, checkOut: string): string => {
  const start = new Date(checkIn).getTime();
  const end = new Date(checkOut).getTime();
  const diff = end - start;

  const hours = Math.floor(diff / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((diff % (1000 * 60)) / 1000);

  return `${hours} hrs ${minutes} mins ${seconds} secs`;
};