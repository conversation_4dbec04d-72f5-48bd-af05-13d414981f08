import { View, Text, TextInput, StyleSheet } from 'react-native';
import React from 'react';
import { ContactField, FIELD_TYPES } from './contact_fields_model';

export interface Choice {
  id: number;
  productfield_id: number;
  custom_option: string;
  created_at: string;
  updated_at: string;
}

export interface ProductField {
  id: number;
  custom_field: boolean;
  field_label: string;
  field_name: string;
  field_type: string;
  required: boolean;
  quick_add: string;
  tool_tip: string | null;
  placeholder: string | null;
  choices: Choice[];
  read_only: boolean;
  has_dependent: boolean;
  productgroups_id: number | null;
  active: boolean;
  order: number;
  lookup_type: string | null;
  lookup_column: string | null;
  created_at: string;
  updated_at: string;
  field_value?: any;
}

export interface Product {
  id: number;
  products: ProductField[];
  owner_name: string;
}

export interface ProductFieldsResponse {
  status: number;
  message: string;
  productFields: ContactField[];
}

export class DynamicFormField extends React.Component<{
  field: ProductField;
  value: any;
  onValueChange: (value: any) => void;
  error?: string;
}> {
  renderInput() {
    const { field, value, onValueChange, error } = this.props;
    const styles = StyleSheet.create({
      container: {
        marginBottom: 20,
      },
      label: {
        fontSize: 16,
        color: '#333',
        marginBottom: 8,
        flexDirection: 'row',
      },
      required: {
        color: 'red',
        marginLeft: 4,
      },
      input: {
        height: 48,
        borderWidth: 1,
        borderColor: error ? 'red' : '#E0E0E0',
        borderRadius: 8,
        paddingHorizontal: 16,
        fontSize: 16,
        color: '#333',
        backgroundColor: '#fff',
      },
      errorText: {
        color: 'red',
        fontSize: 12,
        marginTop: 4,
      },
      tooltip: {
        fontSize: 12,
        color: '#666',
        marginTop: 4,
      },
    });

    return (
      <View style={styles.container}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text style={styles.label}>{field.field_label}</Text>
          {field.required && <Text style={styles.required}>*</Text>}
        </View>
        
        <TextInput
          style={styles.input}
          value={value?.toString() || ''}
          onChangeText={onValueChange}
          placeholder={field.placeholder || `Enter ${field.field_label.toLowerCase()}`}
          placeholderTextColor="#999"
          editable={!field.read_only}
        />
        
        {field.tool_tip && (
          <Text style={styles.tooltip}>{field.tool_tip}</Text>
        )}
        
        {error && (
          <Text style={styles.errorText}>{error}</Text>
        )}
      </View>
    );
  }

  render() {
    return this.renderInput();
  }
}

// Helper functions
export const getActiveFields = (fields: ContactField[]): ContactField[] => {
  return fields.filter(field => field.active);
};

export const getFieldValue = (fields: ProductField[], fieldName: string): any => {
  const field = fields.find(f => f.field_name === fieldName);
  return field?.field_value;
};

export const getFieldChoices = (fields: ProductField[], fieldName: string): Choice[] => {
  const field = fields.find(f => f.field_name === fieldName);
  return field?.choices || [];
};

export const isFieldRequired = (fields: ProductField[], fieldName: string): boolean => {
  const field = fields.find(f => f.field_name === fieldName);
  return field?.required || false;
};

export const getRequiredFields = (fields: ProductField[]): ProductField[] => {
  return fields.filter(field => field.required);
};

export const validateFieldValue = (field: ContactField, value: any): boolean => {
  if (field.required && !value) return false;
  
  switch (field.field_type) {
    case 'number':
      return !isNaN(Number(value));
    case 'email':
      return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
    case 'phone':
      return /^\+?[\d\s-]{8,}$/.test(value);
    default:
      return true;
  }
};

// Helper function to get field by name
export const getField = (fields: ProductField[], fieldName: string): ProductField | undefined => {
  return fields.find(f => f.field_name === fieldName);
};

// Helper function to get custom fields
export const getCustomFields = (fields: ProductField[]): ProductField[] => {
  return fields.filter(field => field.custom_field === true);
};

// Helper function to get fields by type
export const getFieldsByType = (fields: ProductField[], fieldType: string): ProductField[] => {
  return fields.filter(field => field.field_type === fieldType);
};

// Helper function to get quick add fields
export const getQuickAddFields = (fields: ProductField[]): ProductField[] => {
  return  fields.filter(field => field.quick_add === 'on');
}; 