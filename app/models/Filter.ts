export interface FilterField {
  id: number;
  name: string;
  field_id: string;
  contains: string;
  filter: string;
}

export interface SavedFilter {
  id: number;
  name: string;
  share_with: string;
  accessibility: string | null;
  users: string[];
  created_at: string | null;
  updated_at: string | null;
  filters: FilterField[];
  filter: FilterField[];
}

export interface FiltersResponse {
  status: number;
  message: string;
  filters: SavedFilter[];
}

// Example usage:
/*
const response: FiltersResponse = {
  status: 200,
  message: "Filters retrieved successfully",
  filters: [
    {
      id: 4,
      name: "All Deals",
      share_with: "Just Me",
      accessibility: null,
      users: [],
      created_at: "2024-06-07T18:22:20.000Z",
      updated_at: "2024-06-07T18:22:20.000Z",
      filters: []
    },
    // ... more filters
  ]
};
*/