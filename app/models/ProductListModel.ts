export interface Choice {
  id: number;
  productfield_id: number;
  custom_option: string;
  created_at: string;
  updated_at: string;
}

export interface ProductField {
  id: number;
  custom_field: number;
  field_label: string;
  field_name: string;
  field_type: string;
  required: string | null;
  quick_add: string | null;
  tool_tip: string;
  placeholder: string | null;
  choices: Choice[];
  read_only: string | null;
  has_dependent: string | null;
  productgroups_id: number;
  active: number;
  order: number | null;
  lookup_type: string | null;
  lookup_column: string | null;
  created_at: string;
  updated_at: string;
  field_value: string | null;
}

export interface Product {
  id: string;
  products: ProductField[];
  owner_name: string;
}

export interface ProductListModels {
  status: boolean;
  message: string;
  products: Product[];
}

// Field type constants
export const FIELD_TYPES = {
  TEXT: 'Text field',
  NUMBER: 'Number',
  DROPDOWN: 'Dropdown',
  DATEPICKER: 'Datepicker',
  LOOKUP: 'lookup'
} as const;

// Helper functions
export const getFieldValue = (fields: ProductField[], fieldName: string): string | null => {
  const field = fields.find(f => f.field_name === fieldName);
  return field?.field_value || null;
};

export const getFieldByName = (fields: ProductField[], fieldName: string): ProductField | undefined => {
  return fields.find(f => f.field_name === fieldName);
};

export const getFieldChoices = (fields: ProductField[], fieldName: string): Choice[] => {
  const field = fields.find(f => f.field_name === fieldName);
  return field?.choices || [];
};

export const isFieldRequired = (fields: ProductField[], fieldName: string): boolean => {
  const field = fields.find(f => f.field_name === fieldName);
  return field?.required === 'on';
};

export const getActiveFields = (fields: ProductField[]): ProductField[] => {
  return fields.filter(field => field.active === 1);
};

export const getRequiredFields = (fields: ProductField[]): ProductField[] => {
  return fields.filter(field => field.required === 'on');
};

export const getQuickAddFields = (fields: ProductField[]): ProductField[] => {
  return fields.filter(field => field.quick_add === 'on');
};

export const getCustomFields = (fields: ProductField[]): ProductField[] => {
  return fields.filter(field => field.custom_field === 1);
};

export const validateFieldValue = (field: ProductField, value: any): boolean => {
  if (field.required === 'on' && !value) {
    return false;
  }

  switch (field.field_type) {
    case FIELD_TYPES.NUMBER:
      return !isNaN(Number(value));
    case FIELD_TYPES.DROPDOWN:
      return field.choices.some(choice => choice.custom_option === value);
    case FIELD_TYPES.DATEPICKER:
      return !isNaN(Date.parse(value));
    default:
      return true;
  }
};

// Function to prepare field data for API submission
export const prepareFieldsForSubmission = (fields: ProductField[]): any => {
  return fields.map(field => {
    let value = field.field_value;
    
    // Handle dropdown fields
    if (field.field_type === FIELD_TYPES.DROPDOWN && value) {
      const selectedChoice = field.choices?.find(c => c.id.toString() === value);
      value = selectedChoice ? selectedChoice.custom_option : value;
    }
    
    return {
      ...field,
      field_value: value,
      value: value // Keep original value for reference
    };
  });
}; 