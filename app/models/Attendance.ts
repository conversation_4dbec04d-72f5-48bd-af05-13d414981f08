/**
 * Attendance Model
 * Represents an attendance record in the system
 */

export interface Attendance {
  id: number;
  date: string;  // ISO date string
  check_in_time: string | null;
  check_in_location: string | null;
  check_out_time: string | null;
  check_out_location: string | null;
  status: 'Pending' | 'Checkin' | 'Inprogress' | 'Completed' | string;
  time_spent: string | null;
  owner_name: string;
  updater_name: string | null;
  distance: number | null;
  claim_amount: number | null;
  transport_mode: string | null;
}

export interface AttendanceResponseData {
  filter_type: string;
  period: string;
  attendance_data: Attendance[];
}

export interface AttendanceResponse {
  status: boolean;
  message: string;
  data: AttendanceResponseData;
}

/**
 * Format date for display in attendance list
 * Converts ISO date string to dd/MM EEE format (e.g., "14/03 Fri")
 * Also handles dd/mm/yyyy format
 */
export const formatAttendanceDate = (dateString: string): string => {
  try {
    let date: Date;
    
    // Check if the date is in dd/mm/yyyy format
    if (dateString.includes('/')) {
      const [day, month, year] = dateString.split('/').map(part => parseInt(part, 10));
      date = new Date(year, month - 1, day);
    } else {
      // Treat as ISO date string
      date = new Date(dateString);
    }
    
    // Check if the date is valid
    if (isNaN(date.getTime())) {
      throw new Error('Invalid date');
    }
    
    // Get day, month
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    
    // Get day of week abbreviation
    const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const dayOfWeek = dayNames[date.getDay()];
    
    return `${day}/${month} ${dayOfWeek}`;
  } catch (error) {
    //console.error('Error formatting date:', error);
    return dateString;
  }
};

/**
 * Format time for display in attendance list
 * Converts time string to hh:mm AM/PM format
 * Handles both ISO date string and time string formats
 */
export const formatAttendanceTime = (timeString: string | null): string => {
  if (!timeString) return '-';
  
  try {
    let date: Date;
    
    // Check if we have a time-only string (e.g., "14:30:00")
    if (timeString.includes(':') && !timeString.includes('T') && !timeString.includes('-')) {
      const [hours, minutes] = timeString.split(':').map(part => parseInt(part, 10));
      date = new Date();
      date.setHours(hours, minutes, 0);
    } else {
      // Treat as full date-time string
      date = new Date(timeString);
    }
    
    // Check if the date is valid
    if (isNaN(date.getTime())) {
      throw new Error('Invalid date/time');
    }
    
    // Get hours and minutes
    let hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, '0');
    
    // Convert to 12-hour format
    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12;
    hours = hours ? hours : 12; // 0 should be displayed as 12
    
    return `${hours}:${minutes} ${ampm}`;
  } catch (error) {
    console.error('Error formatting time:', error);
    return '-';
  }
}; 