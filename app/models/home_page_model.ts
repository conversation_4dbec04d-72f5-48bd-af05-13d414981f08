export interface Activity {
  id: number;
  menu_title: string;
  activity_id: string | null;
  activity_name: string;
  activity_title: string;
  activity_icon: string | null;
  active: number;
  custom_activity: number;
  created_on: string | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface CustomActivity extends Activity {
  allow_checkin_checkout: string;
  show_calendar: string;
  mark_completed: string | null;
  allow_edit: string | null;
  lattitude: string | null;
  longitude: string | null;
}

export interface Link {
  id: number;
  name: string;
  link: string;
  active: number;
  user_id: number;
  create_at: string;
  created_at: string;
  updated_at: string;
}

export interface OverDueTask {
  id: number;
  mark_as_complete: string;
  description: string;
  title: string;
  due_date: string;
  due_time: string;
  related_to: string;
  task_type: string;
  related_type: string;
  related_id: string;
  collaboratoes: string;
  completed_date: string;


}

export interface Task {
  id: number;
  task_type: string;
  title: string;
  description: string;
  due_date: string;
  due_time: string;
  outcome: string;
  mark_as_complete: string;
  completed_date: string | null;
  completed_time: string | null;
  user_id: number;
  related_type: string;
  related_to: number;
  collaboratoes: string;
  snooze_date: string | null;
  snooze_time: string | null;
  active: number;
  is_deleted: number;
  reminded: string | null;
  create_at: string | null;
  created_at: string;
  updated_at: string;
}

export interface Currency {
  id: number;
  currency: string;
  currency_code: string;
  decimal: string;
  is_default: number;
  active: number;
  created_at: string | null;
  updated_at: string | null;
}

export interface Attendance {
  id: number;
  salesactivities_id: number;
  activity_type: string;
  activity_date: string;
  activity_title: string;
  description: string | null;
  notes: string | null;
  all_day: string | null;
  start_date: string;
  end_date: string;
  start_time: string;
  end_time: string;
  owner_id: number;
  collaboratoes: string | null;
  sales_activity_outcome_id: string | null;
  creater_id: string;
  updater_id: string | null;
  attendees: string | null;
  attendance_type: string | null;
  conversation_time: string | null;
  targetable_id: string | null;
  targetable_type: string | null;
  custom_field: string | null;
  allow_complete: string | null;
  completed_date: string | null;
  completed_time: string | null;
  remarks: string | null;
  attendance: string | null;
  task_type: string | null;
  location: string | null;
  timezone: string | null;
  checkin_location: string | null;
  checkin_time: string | null;
  checkin_latitude: string | null;
  checkin_longitude: string | null;
  checkout_location: string | null;
  checkout_time: string | null;
  checkout_latitude: string | null;
  checkout_longitude: string | null;
  timespent: string | null;
  timespent_display: string | null;
  status: string;
  outcome: string | null;
  lattitude: string | null;
  longitude: string | null;
  distance: string | null;
  created_at: string;
  updated_at: string;
}

export interface ActivitiesResponse {
  status: boolean;
  message: string;
  response: {
    records: Activity[];
    salesactivities: Activity[];
    customactivities: CustomActivity[];
    overduetasks: OverDueTask[];
    tasks: Task[];
    contacts: any[];
    recentcontacts: any[];
    links: Link[];
    activities: any[];
    currency: Currency;
    attendance: Attendance;
    locationtracking: any[];
    Locationtrackingstatus: number;
  };
}