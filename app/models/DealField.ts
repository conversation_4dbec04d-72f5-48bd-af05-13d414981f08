export interface DealFieldChoice {
  id: number;
  dealfield_id: number;
  custom_option: string;
  created_at?: string | null;
  updated_at?: string | null;
}

export interface DealField {
  id: number;
  custom_field: number;
  field_label: string;
  field_name: string;
  field_type: string;
  required: string | null;
  quick_add: string | null;
  tool_tip: string | null;
  placeholder: string;
  read_only: string | null;
  has_dependent: string | null;
  dependent_field: string | null;
  dealgroups_id: number;
  lookup_type: string | null;
  lookup_column: string | null;
  active: number;
  order: number;
  created_at: string;
  updated_at: string;
  choices: DealFieldChoice[];
  field_value: string | null;
}

export interface DealFieldsResponse {
  status: number;
  message: string;
  dealFields: DealField[];
}

export interface DealFormData {
  [key: string]: string | null;
} 