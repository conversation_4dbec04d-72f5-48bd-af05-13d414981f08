// Contains list item interface
interface ContainsListItem {
  name: string;
}

// Value interface for lookup fields
interface LookupValue {
  id: number | null;
  value: string;
}

// Product field interface
export interface ProductFilterField {
  id: number;
  custom_field: number;
  field_label: string;
  field_name: string;
  field_type: string;
  values: LookupValue[];
  contains_list: ContainsListItem[];
}

// Response interface
export interface ProductFilterFieldsResponse {
  status: boolean;
  message: string;
  response: ProductFilterField[];
}

// Field type constants
export const FILTER_FIELD_TYPES = {
  TEXT: 'Text field',
  NUMBER: 'Number',
  DROPDOWN: 'Dropdown',
  DATEPICKER: 'Datepicker',
  LOOKUP: 'lookup',
  SELECT: 'select'
} as const;

// Helper functions
export const getFieldType = (field: ProductFilterField): string => {
  return field.field_type;
};

export const isLookupField = (field: ProductFilterField): boolean => {
  return field.field_type === FILTER_FIELD_TYPES.LOOKUP;
};

export const isDropdownField = (field: ProductFilterField): boolean => {
  return field.field_type === FILTER_FIELD_TYPES.LOOKUP||field.field_type === FILTER_FIELD_TYPES.DROPDOWN;
  //return field.field_type === FILTER_FIELD_TYPES.LOOKUP||field.field_type === FILTER_FIELD_TYPES.DROPDOWN||field.field_type === FILTER_FIELD_TYPES.DATEPICKER;
};

export const isDateField = (field: ProductFilterField): boolean => {
  return field.field_type === FILTER_FIELD_TYPES.DATEPICKER;
};

export const isNumberField = (field: ProductFilterField): boolean => {
  return field.field_type === FILTER_FIELD_TYPES.NUMBER;
};

export const isTextField = (field: ProductFilterField): boolean => {
  return field.field_type === FILTER_FIELD_TYPES.TEXT;
};

export const isSelectField = (field: ProductFilterField): boolean => {
  return field.field_type === FILTER_FIELD_TYPES.SELECT;
};

export const getFieldByName = (fields: ProductFilterField[], fieldName: string): ProductFilterField | undefined => {
  return fields.find(field => field.field_name === fieldName);
};

export const getCustomFields = (fields: ProductFilterField[]): ProductFilterField[] => {
  return fields.filter(field => field.custom_field === 1);
};

export const getSystemFields = (fields: ProductFilterField[]): ProductFilterField[] => {
  return fields.filter(field => field.custom_field === 0);
};

export const getLookupValues = (field: ProductFilterField): LookupValue[] => {
  return field.values || [];
};

export const getContainsList = (field: ProductFilterField): ContainsListItem[] => {
  return field.contains_list || [];
};

// Function to get the display value for a lookup field
export const getLookupDisplayValue = (field: ProductFilterField, id: number | null): string => {
  if (!field.values || !id) return 'unassigned';
  const value = field.values.find(v => v.id === id);
  return value ? value.value : 'unassigned';
};

// Function to prepare filter data
export const prepareFilterData = (field: ProductFilterField, value: any, contains: string) => {
  return {
    field_name: field.field_name,
    field_label: field.field_label,
    value_id: value,
    contains: contains
  };
}; 