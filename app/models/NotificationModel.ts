export interface NotificationModel {
    id: string;
    title: string;
    message: string;
    type?: string;
    createdAt: string;
    isRead: boolean;
    data?: any;
}

export interface Notification {
    id: number;
    message: string;
    trigger_date: string | null;
    trigger_time: string | null;
    read_status: number;
    targetable_type: string | null;
    targetable_id: number | null;
    created_at: string | null;
    updated_at: string | null;
}

export interface NotificationData {
    total: number;
    notifications: Notification[];
}

export interface NotificationResponse {
    status: number;
    success: boolean;
    message: string;
    data: NotificationData;
}

/**
 * Check if a notification has been read
 * @param readStatus The read status value from the notification
 * @returns boolean indicating if the notification has been read
 */
export const isNotificationRead = (readStatus: number): boolean => {
    return readStatus === 1;
};

/**
 * Format the notification time to a readable string
 * @param timestamp The timestamp to format
 * @returns formatted time string
 */
export const formatNotificationTime = (timestamp: string | null): string => {
    if (!timestamp) return 'Unknown time';

    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 24) {
        if (diffInHours < 1) {
            const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
            return diffInMinutes <= 1 ? 'Just now' : `${diffInMinutes}m ago`;
        }
        return `${diffInHours}h ago`;
    } else if (diffInHours < 48) {
        return 'Yesterday';
    } else {
        return date.toLocaleDateString();
    }
};

/**
 * Get the icon name for a notification type
 * @param targetableType The notification targetable_type
 * @returns Ionicons icon name
 */
export const getNotificationTypeIcon = (targetableType: string | null): string => {
    if (!targetableType) return 'notifications-outline';

    switch (targetableType.toLowerCase()) {
        case 'tasks':
            return 'checkbox-outline';
        case 'meetings':
            return 'calendar-outline';
        case 'deals':
            return 'trending-up-outline';
        case 'contacts':
            return 'people-outline';
        case 'accounts':
        case 'sales_accounts':
            return 'business-outline';
        case 'support':
            return 'help-circle-outline';
        case 'system':
            return 'settings-outline';
        default:
            return 'notifications-outline';
    }
};

export default NotificationModel;