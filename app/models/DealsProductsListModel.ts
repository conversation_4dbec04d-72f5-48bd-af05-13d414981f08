// Model for deal products list response
export interface DealProduct {
  id: number;
  deal_id: number;
  product_id: number;
  product_name: string;
  unitprice: string;
  quantity: string;
  discount_type: string | null;
  discount: string | null;
  totalprice: string;
  setupfree: string | null;
  billingcycle: string | null;
}

export interface DealValue {
  subtotal: string;
  salestax: string;
  salestaxamount: string;
  dealvalue: string;
  autosync: boolean;
  syncquoteid: string;
  quotations: Quotation[];
}

export interface Quotation {
  id: number;
  quotation_name: string;
}

export interface DealsProductsListResponse {
  dealproducts: DealProduct[];
  dealvalue: DealValue;
}

export interface DealsProductsListModel {
  status: boolean;
  message: string;
  response: DealsProductsListResponse;
} 