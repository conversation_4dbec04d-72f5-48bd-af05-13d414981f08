interface User {
  id: number;
  name: string;
}

interface Territory {
  id: number;
  name: string;
}

interface ShareWithUsersResponse {
  users: User[];
  territories: Territory[];
}

export interface ModelShareWithUsersResponse {
  status: boolean;
  message: string;
  response: ShareWithUsersResponse;
}

export class ModelShareWithUsers {
  users: User[];
  territories: Territory[];

  constructor(data: ShareWithUsersResponse) {
    this.users = data.users || [];
    this.territories = data.territories || [];
  }

  static fromResponse(response: ModelShareWithUsersResponse): ModelShareWithUsers | null {
    if (!response.status || !response.response) {
      return null;
    }
    return new ModelShareWithUsers(response.response);
  }

  getAllOptions(): { id: number; name: string; type: 'user' | 'territory' }[] {
    return [
      ...this.users.map(user => ({ ...user, type: 'user' as const })),
      ...this.territories.map(territory => ({ ...territory, type: 'territory' as const }))
    ];
  }
} 