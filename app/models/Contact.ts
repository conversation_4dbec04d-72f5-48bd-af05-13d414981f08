export interface Contact {
    id: number;
    first_name: string;
    last_name: string | null;
    sales_accounts: string | null;
    emails: string | null;
    telephone_numbers: string | null;
    mobile_number: string;
    work_number: string | null;
    owner_id: string | null;
    subscription_status: string | null;
    subscription_types: string | null;
    unsubscription_reason: string | null;
    other_unsubscription_reason: string | null;
    lifecycle_stage_id: string | null;
    contact_status_id: string | null;
    lost_reason_id: string | null;
    first_campaign: string | null;
    first_medium: string | null;
    first_source: string | null;
    last_campaign: string | null;
    last_medium: string | null;
    last_source: string | null;
    latest_campaign: string | null;
    latest_medium: string | null;
    latest_source: string | null;
    campaign_id: string | null;
    medium: string | null;
    keyword: string | null;
    country: string | null;
    lead_source_id: string | null;
    last_contacted: string | null;
    tags: string | null;
    customer_fit: string | null;
    web_form_ids: string | null;
    recent_note: string | null;
    external_id: string | null;
    job_title: string | null;
    sms_subscription_status: string | null;
    whatsapp_subscription_status: string | null;
    work_email: string | null;
    phone_numbers: string | null;
    address: string | null;
    city: string | null;
    state: string | null;
    zipcode: string | null;
    facebook: string | null;
    twitter: string | null;
    linkedin: string | null;
    territory_id: string | null;
    lists: string | null;
    last_seen_chat: string | null;
    total_sessions: string | null;
    locale: string | null;
    first_seen_chat: string | null;
    last_contacted_mode: string | null;
    last_contacted_sales_activity_mode: string | null;
    last_contacted_via_sales_activity: string | null;
    active_sales_sequences: string | null;
    completed_sales_sequences: string | null;
    last_seen: string | null;
    lead_score: string | null;
    creater_id: number;
    create_at: string;
    updater_id: number | null;
    update_at: string | null;
    last_assigned_at: string | null;
    time_zone: string | null;
    active: string;
    custom_field: string | null;
    deal_value: string | null;
    dealcount: string | null;
    open_dealvalue: number | null;
    open_dealcount: number | null;
    gst: string | null;
    created_at: string;
    updated_at: string;
}

export interface DisplayContact extends Contact {
    initial: string;
    name: string;
    contact: string;
}

export interface ContactsResponse {
    status: number;
    message: string;
    contacts: Contact[];
} 