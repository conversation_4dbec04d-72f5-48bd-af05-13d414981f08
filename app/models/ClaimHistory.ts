export interface ClaimHistoryResponse {
  status: number;
  success: boolean;
  message: string;
  date: {
    startDate: string;
    endDate: string;
  };
  response: {
    pending: ClaimHistoryItem[];
    completed: ClaimHistoryItem[];
    rejected: ClaimHistoryItem[];
    summary: ClaimHistorySummary;
  };
}

export interface ClaimHistoryItem {
  id: number;
  activity_name: string;
  start_date: string;
  start_time: string;
  end_date: string;
  end_time: string;
  transport_mode: string | null;
  distance: number;
  claim_amount: number;
  status: string;
  target_name: string | null;
  target_type: string;
  employee_name: string;
  remarks: string | null;
  claim_image: string | null;
  claim_status?: string; // for status logic
  claim_approved_remarks?: string; // for rejected claims
}

export interface ClaimHistorySummary {
  total_claims: number;
  total_pending: number;
  total_rejected: number;
  total_completed: number;
  total_amount: {
    pending: number;
    rejected: number;
    completed: number;
  };
}

// Helper type for filtering claim history
export type ClaimHistoryFilter = {
  startDate: string;
  endDate: string;
  status?: string;
};

// Enum for claim status
export enum ClaimStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  REJECTED = 'rejected',
  APPLIED = 'Applied'
}

// Enum for transport modes
export enum TransportMode {
  BIKE = 'Bike',
  CAR = 'Car',
  PUBLIC = 'Public',
  OTHER = 'Other'
}

// Helper function to format date for display
export const formatClaimDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
};

// Helper function to calculate duration between check-in and check-out
export const calculateDuration = (checkIn: string, checkOut: string): string => {
  const start = new Date(checkIn).getTime();
  const end = new Date(checkOut).getTime();
  const diff = end - start;

  const hours = Math.floor(diff / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((diff % (1000 * 60)) / 1000);

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};