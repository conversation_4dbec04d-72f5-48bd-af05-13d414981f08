export interface AccountFieldChoice {
  id: number;
  accountsfield_id: number;
  custom_option: string;
  created_at: string;
  updated_at: string;
}

export interface AccountField {
  id: number;
  custom_field: number;
  field_label: string;
  field_name: string;
  field_type: string;
  required: string | null;
  quick_add: string | null;
  tool_tip: string;
  placeholder: string;
  choices: AccountFieldChoice[];
  read_only: string | null;
  has_dependent: string | null;
  accountsgroups_id: number;
  active: number;
  order: number | null;
  lookup_type: string | null;
  lookup_column: string | null;
  created_at: string | null;
  updated_at: string | null;
  field_value: string | null;
}

export interface AccountFieldsResponse {
  status: number;
  message: string;
  accountFields: AccountField[];
}

// Interface for submitting account data
export interface AccountFormData {
  name: string;
  owner_id?: string;
  website?: string;
  phone?: string;
  number_of_employees?: string;
  price?: string;
  annual_revenue?: string;
  mobile?: string;
  [key: string]: string | undefined;
} 