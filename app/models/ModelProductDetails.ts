export interface ProductChoice {
  id: number;
  productfield_id: number;
  custom_option: string;
  created_at: string;
  updated_at: string;
}

export interface ProductField {
  id: number;
  custom_field: number;
  field_label: string;
  field_name: string;
  field_type: string;
  required: string | null;
  quick_add: string | null;
  tool_tip: string;
  placeholder: string | null;
  choices: ProductChoice[];
  read_only: string | null;
  has_dependent: string | null;
  productgroups_id: number | null;
  active: number;
  order: number | null;
  lookup_type: string | null;
  lookup_column: string | null;
  created_at: string;
  updated_at: string;
  field_value: string | null;
}

export interface Currency {
  id: number;
  currency: string;
  currency_code: string;
  decimal: string;
  is_default: number;
  active: number;
  created_at: string | null;
  updated_at: string | null;
}

export interface PricingType {
  pricing_type: string;
}

export interface File {
  id: number;
  file_name: string;
  file_path: string;
  file_size: string;
  created_at: string;
}

export interface ProductCurrency {
  base_currency_amount: string;
  currency: string;
}

export interface ProductData {
  id: number;
  category: string | null;
  subcategory: string | null;
  product_code: string | null;
  product_name: string;
  display_name: string | null;
  product_image: string | null;
  item_description: string | null;
  sku_number: string | null;
  valid_till: string | null;
  parent_product: string | null;
  gross_amount: string | null;
  net_amount: string | null;
  create_at: string | null;
  update_at: string | null;
  base_currency_amount: string | null;
  currency: string | null;
  pricing_type: string | null;
  setup_fee: string | null;
  subscription_cycle: string | null;
  no_of_billing_cycle: string | null;
  available: string | null;
  stockquantity: string | null;
  tax: string | null;
  active: number;
  custom_field: string;
  created_at: string | null;
  updated_at: string | null;
  currencies: ProductCurrency[];
  files: File[];
}

export interface ProductDetails {
  id: string;
  product: ProductData;
  products: ProductField[];
  currencies: Currency[];
  pricingtypes: PricingType[];
  product_image: string;
  files: File[];
}

export interface ProductDetailsResponse {
  status: boolean;
  message: string;
  product: ProductDetails;
}

export interface ProductDetail {
  label: string;
  value: string;
  isReadOnly?: boolean;
}

// Helper function to get field value based on field type
export const getFieldValue = (products: ProductField[], fieldName: string): string => {
  const field = products.find(p => p.field_name === fieldName);
  if (!field) return '';

  switch (field.field_type.toLowerCase()) {
    case 'dropdown':
    case 'select':
      if (field.choices && field.choices.length > 0) {
        const choice = field.choices.find(c => c.id.toString() === field.field_value);
        return choice ? choice.custom_option : field.field_value || '';
      }
      return field.field_value || '';
      
    case 'lookup':
      return field.field_value || '';
      
    case 'datepicker':
      return field.field_value ? new Date(field.field_value).toLocaleDateString() : '';
      
    default:
      return field.field_value || '';
  }
};

// Helper function to format product fields for display
export const formatProductFields = (product: ProductDetails): { basic: ProductDetail[]; system: ProductDetail[] } => {
  const { products } = product;

  const basic = [
    { label: 'Name', value: getFieldValue(products, 'product_name') },
    { label: 'Product Code', value: getFieldValue(products, 'product_code') },
    { label: 'Category', value: getFieldValue(products, 'category') },
    { label: 'Sub Category', value: getFieldValue(products, 'subcategory') },
    { label: 'Available Count', value: getFieldValue(products, 'available') },
    { label: 'Product Cost', value: getFieldValue(products, 'base_currency_amount') },
    { label: 'Stock Quantity', value: getFieldValue(products, 'stockquantity') },
    { label: 'Tax', value: getFieldValue(products, 'tax') },
    { label: 'Owner', value: getFieldValue(products, 'owner') },
    { label: 'Active', value: product.product.active ? 'Yes' : 'No' },
  ];

  const system = [
    { label: 'Created by', value: getFieldValue(products, 'created_by'), isReadOnly: true },
    { label: 'Updated by', value: getFieldValue(products, 'updated_by'), isReadOnly: true },
    { label: 'Created at', value: getFieldValue(products, 'create_at'), isReadOnly: true },
    { label: 'Updated at', value: getFieldValue(products, 'update_at'), isReadOnly: true },
  ];

  return { basic, system };
};

// Helper function to check if a field is of a specific type
export const isFieldType = (field: ProductField, type: string): boolean => {
  return field.field_type.toLowerCase() === type.toLowerCase();
};

// Helper functions for specific field types
export const isDropdownField = (field: ProductField): boolean => isFieldType(field, 'dropdown');
export const isSelectField = (field: ProductField): boolean => isFieldType(field, 'select');
export const isLookupField = (field: ProductField): boolean => isFieldType(field, 'lookup');
export const isDateField = (field: ProductField): boolean => isFieldType(field, 'datepicker');
export const isNumberField = (field: ProductField): boolean => isFieldType(field, 'number');
export const isTextField = (field: ProductField): boolean => isFieldType(field, 'text field');

// Helper function to get field by name
export const getFieldByName = (products: ProductField[], fieldName: string): ProductField | undefined => {
  return products.find(p => p.field_name === fieldName);
};

// Helper function to get custom fields
export const getCustomFields = (products: ProductField[]): ProductField[] => {
  return products.filter(p => p.custom_field === 1);
};

// Helper function to get system fields
export const getSystemFields = (products: ProductField[]): ProductField[] => {
  return products.filter(p => p.custom_field === 0);
}; 