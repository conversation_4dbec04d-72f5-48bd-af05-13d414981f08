/**
 * Conveyance Model
 * Represents a conveyance record in the system with related activities
 */

// Interface for a sales activity within a conveyance record
export interface ConveyanceActivity {
  id: number;
  salesactivities_id: number;
  activity_type: string;
  activity_date: string | null;
  activity_title: string;
  description: string;
  notes: string;
  all_day: string;
  start_date: string | null;
  end_date: string | null;
  start_time: string | null;
  end_time: string | null;
  owner_id: number;
  collaboratoes: string | null;
  sales_activity_outcome_id: string | null;
  creater_id: string | null;
  updater_id: string | null;
  attendees: string | null;
  attendance_type: string | null;
  conversation_time: string | null;
  targetable_id: string | null;
  targetable_type: string | null;
  custom_field: string | null;
  allow_complete: string | null;
  completed_date: string | null;
  completed_time: string | null;
  remarks: string | null;
  attendance: string | null;
  task_type: string | null;
  location: string | null;
  timezone: string | null;
  checkin_location: string | null;
  checkin_time: string | null;
  checkin_latitude: string | null;
  checkin_longitude: string | null;
  checkout_location: string | null;
  checkout_time: string | null;
  checkout_latitude: string | null;
  checkout_longitude: string | null;
  timespent: string | null;
  timespent_display: string | null;
  status: string | null;
  outcome: string | null;
  lattitude: string | null;
  longitude: string | null;
  distance: number | null;
  transport_mode: string | null;
  claim_amount: string | null;
  conveyance_charge: string | null;
  claim_status: string | null;
  claim_applied_on: string | null;
  created_at: string | null;
  updated_at: string | null;
}

// Interface for a daily conveyance record
export interface ConveyanceRecord {
  conveyance_date: string;
  distance: string;
  charge: number;
  activity: ConveyanceActivity[];
}

export interface TransportMode {
  mode: string;
  rate: number;
}

export interface ConveyanceSummary {
  total_distance: string;
  total_amount: string;
}

export interface ConveyanceResponse {
  date: string;
  activities: any[]; // You can define a more specific type for activities if needed
  transport_modes: TransportMode[];
  summary: ConveyanceSummary;
}

// Interface for the API request parameters
export interface ConveyanceRequest {
  date?: string;
  filter?: string;
  month?: string;
  year?: string;
}

/**
 * Format distance for display
 * Converts distance string to formatted string with km
 */
export const formatDistance = (distance: string): string => {
  if (!distance) return '0.00 km';

  try {
    const distanceNum = parseFloat(distance);
    return `${distanceNum.toFixed(2)} km`;
  } catch (error) {
    console.error('Error formatting distance:', error);
    return '0.00 km';
  }
};

/**
 * Format charge for display
 * Converts charge number to formatted currency string
 */
export const formatCharge = (charge: number): string => {
  if (!charge) return '₹ 0.00';

  try {
    return `₹ ${charge.toLocaleString('en-IN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })}`;
  } catch (error) {
    console.error('Error formatting charge:', error);
    return '₹ 0.00';
  }
};

/**
 * Calculate total time spent for a conveyance date
 * Returns formatted time string showing only non-zero components
 * (e.g. "5 hrs 30 min" or "45 sec" or "2 hrs 15 sec")
 */
export const calculateTotalTimeSpent = (activities: ConveyanceActivity[]): string => {
  if (!activities || activities.length === 0) return '-';

  let totalSeconds = 0;

  activities.forEach(activity => {
    if (activity.timespent) {
      totalSeconds += parseInt(activity.timespent, 10);
    }
  });

  if (totalSeconds === 0) return '-';

  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  // Format the time string based on which components are non-zero
  const formattedParts = [];

  if (hours > 0) {
    formattedParts.push(`${hours} hrs`);
  }

  if (minutes > 0) {
    formattedParts.push(`${minutes} min`);
  }

  if (seconds > 0 || formattedParts.length === 0) {
    formattedParts.push(`${seconds} sec`);
  }

  return formattedParts.join(' ');
};

export interface EnhancedConveyanceResponse {
  status: number;
  success: boolean;
  message: string;
  response?: ConveyanceResponse;
  data?: {
    conveyance_data: ConveyanceRecord[];
    period: string;
  };
}