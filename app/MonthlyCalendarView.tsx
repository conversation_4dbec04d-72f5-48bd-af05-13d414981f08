import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, ScrollView, ActivityIndicator, Alert, Modal, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useColors } from '@/hooks/useThemeColor';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { convertDate, DATE_FORMAT } from './ui/utils';
import * as Location from 'expo-location';
import moment from 'moment';

// Constants for calendar
const WEEKDAYS = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
const MONTHS = [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
];

interface DayData {
  date: number;
  month: number;
  year: number;
  isCurrentMonth: boolean;
  isToday: boolean;
  hasActivity: boolean;
}

export default function MonthlyCalendarView() {
  const colors = useColors();
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [calendarDays, setCalendarDays] = useState<DayData[]>([]);
  const [loading, setLoading] = useState(false);
  const [activities, setActivities] = useState<any[]>([]);
  const [tasks, setTasks] = useState<any[]>([]);
  const [isCheckingInOut, setIsCheckingInOut] = useState(false);
  const [showAddMenu, setShowAddMenu] = useState(false);

  // Generate calendar days for the selected month
  const generateCalendarDays = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const days: DayData[] = [];

    // Add days from previous month
    const prevMonthDays = firstDay.getDay();
    const prevMonth = new Date(year, month - 1);
    const prevMonthLastDay = new Date(year, month, 0).getDate();
    for (let i = prevMonthDays - 1; i >= 0; i--) {
      days.push({
        date: prevMonthLastDay - i,
        month: prevMonth.getMonth(),
        year: prevMonth.getFullYear(),
        isCurrentMonth: false,
        isToday: false,
        hasActivity: false
      });
    }

    // Add days from current month
    const today = new Date();
    for (let i = 1; i <= lastDay.getDate(); i++) {
      days.push({
        date: i,
        month: month,
        year: year,
        isCurrentMonth: true,
        isToday: today.getDate() === i &&
                 today.getMonth() === month &&
                 today.getFullYear() === year,
        hasActivity: false
      });
    }

    // Add days from next month
    const remainingDays = 42 - days.length; // 6 rows * 7 days
    const nextMonth = new Date(year, month + 1);
    for (let i = 1; i <= remainingDays; i++) {
      days.push({
        date: i,
        month: nextMonth.getMonth(),
        year: nextMonth.getFullYear(),
        isCurrentMonth: false,
        isToday: false,
        hasActivity: false
      });
    }

    setCalendarDays(days);
  };

  // Fetch activities for the selected date
  const fetchActivities = async (date: Date) => {
    try {
      setLoading(true);
      const formattedDate = convertDate(date.toISOString(), DATE_FORMAT.SYSTEM_FORMAT, DATE_FORMAT.DATE_FORMAT);
      const response = await apiService.post(API_ENDPOINTS.HOME_PAGE, { date: formattedDate });

      if (response.data?.response) {
        setActivities(response.data.response.activities || []);
        setTasks(response.data.response.tasks || []);
      }
    } catch (error) {
      console.error('Error fetching activities:', error);
      Alert.alert('Error', 'Failed to load activities');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    generateCalendarDays(selectedDate);
    fetchActivities(selectedDate);
  }, [selectedDate]);

  const handlePrevMonth = () => {
    setSelectedDate(new Date(selectedDate.getFullYear(), selectedDate.getMonth() - 1));
  };

  const handleNextMonth = () => {
    setSelectedDate(new Date(selectedDate.getFullYear(), selectedDate.getMonth() + 1));
  };

  const handleDayPress = (day: DayData) => {
    const newDate = new Date(day.year, day.month, day.date);
    setSelectedDate(newDate);
  };

  const formatTime = (timeString: string) => {
    if (!timeString) return '';
    try {
      const [hours, minutes] = timeString.split(':');
      const date = new Date();
      date.setHours(parseInt(hours), parseInt(minutes));
      return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
    } catch (error) {
      return timeString;
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    try {
      return moment(dateString).format('DD/MM/YYYY');
    } catch (error) {
      return dateString;
    }
  };

  const handleCheckIn = async (activity: any) => {
    try {
      Alert.alert(
        'Check In Confirmation',
        'Are you sure you want to check in to this meeting?',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Confirm',
            onPress: async () => {
              try {
                setIsCheckingInOut(true);

                const { status } = await Location.requestForegroundPermissionsAsync();
                if (status !== 'granted') {
                  alert('Permission to access location was denied');
                  return;
                }

                const location = await Location.getCurrentPositionAsync({});
                const [address] = await Location.reverseGeocodeAsync({
                  latitude: location.coords.latitude,
                  longitude: location.coords.longitude
                });

                const locationString = address
                  ? `${address.street || ''}, ${address.city || ''}, ${address.region || ''}, ${address.country || ''}`
                  : '';

                Alert.alert(
                  'Confirm Location',
                  `Location:\n${locationString}`,
                  [
                    {
                      text: 'Cancel',
                      style: 'cancel',
                    },
                    {
                      text: 'Confirm',
                      onPress: async () => {
                        try {
                          const formData = new FormData();
                          formData.append('meeting_id', activity.id.toString());
                          formData.append('latitude', location.coords.latitude.toString());
                          formData.append('longitude', location.coords.longitude.toString());
                          formData.append('location', locationString);
                          formData.append('check_in_time', new Date().toISOString());

                          const response = await apiService.putFormData(
                            `${API_ENDPOINTS.MEETING_ADD}/${activity.id}/status`,
                            formData
                          );

                          if (!response.status) {
                            throw new Error('Failed to check in');
                          }

                          fetchActivities(selectedDate);
                          Alert.alert('Success', 'Successfully checked in!');
                        } catch (error) {
                          console.error('Check-in error:', error);
                          Alert.alert('Error', 'Failed to check in. Please try again.');
                        }
                      }
                    }
                  ]
                );
              } catch (error) {
                console.error('Location error:', error);
                Alert.alert('Error', 'Failed to get location. Please try again.');
              } finally {
                setIsCheckingInOut(false);
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error('Check-in error:', error);
      Alert.alert('Error', 'Failed to check in. Please try again.');
      setIsCheckingInOut(false);
    }
  };

  const handleCheckOut = async (activity: any) => {
    try {
      Alert.alert(
        'Check Out Confirmation',
        'Are you sure you want to check out from this meeting?',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Confirm',
            onPress: async () => {
              try {
                setIsCheckingInOut(true);

                const { status } = await Location.requestForegroundPermissionsAsync();
                if (status !== 'granted') {
                  alert('Permission to access location was denied');
                  return;
                }

                const location = await Location.getCurrentPositionAsync({});
                const [address] = await Location.reverseGeocodeAsync({
                  latitude: location.coords.latitude,
                  longitude: location.coords.longitude
                });

                const locationString = address
                  ? `${address.street || ''}, ${address.city || ''}, ${address.region || ''}, ${address.country || ''}`
                  : '';

                Alert.alert(
                  'Confirm Location',
                  `Location:\n${locationString}`,
                  [
                    {
                      text: 'Cancel',
                      style: 'cancel',
                    },
                    {
                      text: 'Confirm',
                      onPress: async () => {
                        try {
                          const formData = new FormData();
                          formData.append('meeting_id', activity.id.toString());
                          formData.append('latitude', location.coords.latitude.toString());
                          formData.append('longitude', location.coords.longitude.toString());
                          formData.append('location', locationString);
                          formData.append('check_out_time', new Date().toISOString());

                          const response = await apiService.putFormData(
                            `${API_ENDPOINTS.MEETING_ADD}/${activity.id}/status`,
                            formData
                          );

                          if (!response.status) {
                            throw new Error('Failed to check out');
                          }

                          fetchActivities(selectedDate);
                          Alert.alert('Success', 'Successfully checked out!');
                        } catch (error) {
                          console.error('Check-out error:', error);
                          Alert.alert('Error', 'Failed to check out. Please try again.');
                        }
                      }
                    }
                  ]
                );
              } catch (error) {
                console.error('Location error:', error);
                Alert.alert('Error', 'Failed to get location. Please try again.');
              } finally {
                setIsCheckingInOut(false);
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error('Check-out error:', error);
      Alert.alert('Error', 'Failed to check out. Please try again.');
      setIsCheckingInOut(false);
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.primary,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      backgroundColor: '#0F96BB',
    },
    headerTitle: {
      flex: 1,
      color: 'white',
      fontSize: 20,
      fontWeight: 'bold',
      marginLeft: 16,
    },
    calendarContainer: {
      backgroundColor: colors.background.primary,
    },
    calendarHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 12,
      paddingBottom: 6,
    },
    monthYearText: {
      fontSize: 18,
      fontWeight: 'bold',
      color: colors.text.primary,
    },
    weekDaysContainer: {
      flexDirection: 'row',
      borderBottomWidth: 1,
      borderBottomColor: colors.background.tertiary,
      paddingVertical: 4,
    },
    weekDay: {
      flex: 1,
      alignItems: 'center',
    },
    weekDayText: {
      color: colors.text.secondary,
      fontSize: 12,
      fontWeight: '500',
    },
    calendarGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      paddingHorizontal: 8,
      marginBottom: 4,
    },
    dayCell: {
      width: '14.28%',
      aspectRatio: 0.8,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 0,
    },
    dayContainer: {
      width: 20,
      height: 20,
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: 10,
    },
    dayText: {
      fontSize: 16,
      color: colors.text.primary,
    },
    inactiveDay: {
      color: colors.text.tertiary,
    },
    selectedDay: {
      backgroundColor: colors.primary,
    },
    selectedDayText: {
      color: 'white',
    },
    todayText: {
      color: colors.primary,
      fontWeight: 'bold',
    },
    activitiesContainer: {
      padding: 16,
      paddingTop: 0,
    },
    sectionTitleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 10,
      marginTop: 8,
      paddingHorizontal: 4,
    },
    sectionTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.text.primary,
    },
    sectionIcon: {
      marginRight: 8,
      opacity: 0.9,
    },
    activityItem: {
      backgroundColor: colors.background.secondary,
      borderRadius: 8,
      padding: 12,
      marginBottom: 12,
    },
    activityContent: {
      flex: 1,
    },
    activityRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    activityTitle: {
      fontSize: 16,
      fontWeight: '500',
      color: colors.text.primary,
    },
    activityDescription: {
      fontSize: 14,
      color: colors.text.secondary,
      marginTop: 4,
      lineHeight: 18,
    },
    activityTime: {
      fontSize: 14,
      color: colors.text.secondary,
      marginTop: 4,
    },
    activityLocation: {
      fontSize: 14,
      color: colors.text.secondary,
      marginTop: 4,
    },
    checkStatus: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 8,
    },
    checkStatusContainer: {
      marginTop: 8,
    },
    checkStatusText: {
      fontSize: 14,
      color: colors.text.secondary,
      marginLeft: 4,
    },
    locationText: {
      fontSize: 13,
      color: colors.text.tertiary,
      marginLeft: 18,
      marginTop: 2,
    },
    pendingText: {
      fontSize: 14,
      color: colors.text.tertiary,
      marginLeft: 18,
    },
    arrowIcon: {
      marginLeft: 8,
    },
    loadingContainer: {
      padding: 20,
      alignItems: 'center',
    },
    checkButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.background.secondary,
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 8,
      marginTop: 8,
    },
    checkOutButton: {
      marginTop: 12,
    },
    checkButtonText: {
      color: colors.primary,
      fontSize: 14,
      fontWeight: '600',
      marginLeft: 8,
    },
    taskItem: {
      backgroundColor: colors.background.secondary,
      borderRadius: 8,
      marginBottom: 8,
      overflow: 'hidden',
      borderWidth: 0.5,
      borderColor: colors.background.tertiary,
    },
    taskContent: {
      padding: 12,
    },
    taskHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 6,
    },
    taskIconContainer: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: `${colors.primary}10`,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 10,
    },
    taskTitleContainer: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    taskTitle: {
      fontSize: 15,
      fontWeight: '500',
      color: colors.text.primary,
      flex: 1,
      marginRight: 8,
    },
    taskDescription: {
      fontSize: 13,
      color: colors.text.secondary,
      marginBottom: 8,
      marginLeft: 42,
      lineHeight: 18,
    },
    taskMetaContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginLeft: 42,
      gap: 8,
    },
    taskMetaItem: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: `${colors.primary}08`,
      paddingHorizontal: 6,
      paddingVertical: 2,
      borderRadius: 4,
    },
    taskMetaText: {
      fontSize: 12,
      color: colors.text.secondary,
      marginLeft: 4,
    },
    taskStatusContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 12,
      paddingVertical: 6,
      backgroundColor: colors.background.primary,
    },
    taskStatus: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    statusText: {
      fontSize: 12,
      fontWeight: '500',
      marginLeft: 4,
    },
    fab: {
      position: 'absolute',
      bottom: 20,
      right: 20,
      width: 56,
      height: 56,
      borderRadius: 28,
      backgroundColor: colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      elevation: 4,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
    },
    fabIcon: {
      color: 'white',
    },
    addMenuContainer: {
      position: 'absolute',
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'white',
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      padding: 20,
      elevation: 5,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: -2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
    },
    addMenuTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text.primary,
      textAlign: 'center',
      marginBottom: 20,
    },
    addMenuOptions: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingHorizontal: 20,
      marginBottom: 20,
    },
    optionButton: {
      alignItems: 'center',
      padding: 15,
    },
    optionIcon: {
      width: 50,
      height: 50,
      borderRadius: 25,
      backgroundColor: `${colors.primary}15`,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 8,
    },
    optionText: {
      fontSize: 14,
      color: colors.text.primary,
      marginTop: 4,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'flex-end',
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Calendar</Text>
      </View>

      <View style={styles.calendarContainer}>
        <View style={styles.calendarHeader}>
          <TouchableOpacity onPress={handlePrevMonth}>
            <Ionicons name={Platform.OS === 'ios' ? 'chevron-back' : 'arrow-back'}  size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.monthYearText}>
            {MONTHS[selectedDate.getMonth()]} {selectedDate.getFullYear()}
          </Text>
          <TouchableOpacity onPress={handleNextMonth}>
            <Ionicons name="chevron-forward" size={24} color={colors.text.primary} />
          </TouchableOpacity>
        </View>

        <View style={styles.weekDaysContainer}>
          {WEEKDAYS.map((day) => (
            <View key={day} style={styles.weekDay}>
              <Text style={styles.weekDayText}>{day}</Text>
            </View>
          ))}
        </View>

        <View style={styles.calendarGrid}>
          {calendarDays.map((day, index) => (
            <TouchableOpacity
              key={index}
              style={styles.dayCell}
              onPress={() => handleDayPress(day)}
            >
              <View style={[
                styles.dayContainer,
                day.date === selectedDate.getDate() &&
                day.month === selectedDate.getMonth() &&
                day.year === selectedDate.getFullYear() &&
                styles.selectedDay
              ]}>
                <Text style={[
                  styles.dayText,
                  !day.isCurrentMonth && styles.inactiveDay,
                  day.isToday && styles.todayText,
                  day.date === selectedDate.getDate() &&
                  day.month === selectedDate.getMonth() &&
                  day.year === selectedDate.getFullYear() &&
                  styles.selectedDayText
                ]}>
                  {day.date}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <ScrollView>
        <View style={styles.activitiesContainer}>
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
            </View>
          ) : (
            <>
              {activities.length > 0 && (
                <>
                  <View style={styles.sectionTitleContainer}>
                    <Ionicons
                      name="calendar-outline"
                      size={24}
                      color={colors.text.primary}
                      style={styles.sectionIcon}
                    />
                    <Text style={styles.sectionTitle}>Meetings</Text>
                  </View>
                  {activities.map((activity) => (
                    <View key={activity.id} style={styles.activityItem}>
                      <TouchableOpacity
                        style={styles.activityRow}
                        onPress={() => {
                          router.push({
                            pathname: '/meetings/detailedMeeting' as any,
                            params: { data: JSON.stringify(activity.id) }
                          });
                        }}
                      >
                        <View style={styles.activityContent}>
                          <Text style={styles.activityTitle}>{activity.activity_title}</Text>
                          {activity.description && (
                            <Text style={styles.activityDescription} numberOfLines={2} ellipsizeMode="tail">
                              {activity.description}
                            </Text>
                          )}
                          <Text style={styles.activityTime}>
                            {formatTime(activity.start_time)} - {formatTime(activity.end_time)}
                          </Text>
                          {activity.location && (
                            <Text style={styles.activityLocation}>
                              <Ionicons name="location-outline" size={14} color={colors.text.secondary} />
                              {' '}{activity.location}
                            </Text>
                          )}
                        </View>
                        <Ionicons
                          name="chevron-forward"
                          size={20}
                          color={colors.text.secondary}
                          style={styles.arrowIcon}
                        />
                      </TouchableOpacity>

                      <View style={styles.checkStatusContainer}>
                        {!activity.checkin_time ? (
                          <TouchableOpacity
                            style={styles.checkButton}
                            onPress={() => handleCheckIn(activity)}
                            disabled={isCheckingInOut}
                          >
                            {isCheckingInOut ? (
                              <ActivityIndicator color={colors.primary} size="small" />
                            ) : (
                              <>
                                <MaterialIcons name="login" size={20} color={colors.primary} />
                                <Text style={styles.checkButtonText}>Check In</Text>
                              </>
                            )}
                          </TouchableOpacity>
                        ) : (
                          <>
                            <View style={styles.checkStatus}>
                              <Ionicons name="checkmark-circle" size={14} color={colors.primary} />
                              <Text style={[styles.checkStatusText, { color: colors.primary }]}>
                                Checked in
                              </Text>
                            </View>
                            {activity.checkin_location && (
                              <Text style={styles.locationText}>
                                at {activity.checkin_location}
                              </Text>
                            )}

                            {!activity.checkout_time && (
                              <TouchableOpacity
                                style={[styles.checkButton, styles.checkOutButton]}
                                onPress={() => handleCheckOut(activity)}
                                disabled={isCheckingInOut}
                              >
                                {isCheckingInOut ? (
                                  <ActivityIndicator color={colors.primary} size="small" />
                                ) : (
                                  <>
                                    <MaterialIcons name="logout" size={20} color={colors.primary} />
                                    <Text style={styles.checkButtonText}>Check Out</Text>
                                  </>
                                )}
                              </TouchableOpacity>
                            )}
                          </>
                        )}

                        {activity.checkout_time && (
                          <>
                            <View style={styles.checkStatus}>
                              <Ionicons name="checkmark-done-circle" size={14} color={colors.primary} />
                              <Text style={[styles.checkStatusText, { color: colors.primary }]}>
                                Checked out
                              </Text>
                            </View>
                            {activity.checkout_location && (
                              <Text style={styles.locationText}>
                                at {activity.checkout_location}
                              </Text>
                            )}
                          </>
                        )}
                      </View>
                    </View>
                  ))}
                </>
              )}

              {tasks.length > 0 && (
                <>
                  <View style={styles.sectionTitleContainer}>
                    <Ionicons
                      name="checkbox-outline"
                      size={20}
                      color={colors.text.primary}
                      style={styles.sectionIcon}
                    />
                    <Text style={styles.sectionTitle}>Tasks</Text>
                  </View>
                  {tasks.map((task) => (
                    <TouchableOpacity
                      key={task.id}
                      style={styles.taskItem}
                      onPress={() => {
                        router.push({
                          pathname: '/TaskDetailScreen' as any,
                          params: { taskId: task.id }
                        });
                      }}
                    >
                      <View style={styles.taskContent}>
                        <View style={styles.taskHeader}>
                          <View style={styles.taskIconContainer}>
                            <Ionicons
                              name={Boolean(task.mark_as_complete === "true" || task.mark_as_complete === true) ? "checkmark-circle" : "time-outline"}
                              size={18}
                              color={Boolean(task.mark_as_complete === "true" || task.mark_as_complete === true) ? "#4CAF50" : colors.accent}
                            />
                          </View>
                          <View style={styles.taskTitleContainer}>
                            <Text style={styles.taskTitle} numberOfLines={1}>
                              {task.title}
                            </Text>
                            <Ionicons
                              name="chevron-forward"
                              size={16}
                              color={colors.text.tertiary}
                            />
                          </View>
                        </View>

                        {task.description && (
                          <Text style={styles.taskDescription} numberOfLines={2} ellipsizeMode="tail">
                            {task.description}
                          </Text>
                        )}

                        <View style={styles.taskMetaContainer}>
                          {task.due_date && (
                            <View style={styles.taskMetaItem}>
                              <Ionicons name="calendar-outline" size={12} color={colors.text.secondary} />
                              <Text style={styles.taskMetaText}>{formatDate(task.due_date)}</Text>
                            </View>
                          )}
                          {task.due_time && (
                            <View style={styles.taskMetaItem}>
                              <Ionicons name="time-outline" size={12} color={colors.text.secondary} />
                              <Text style={styles.taskMetaText}>{formatTime(task.due_time)}</Text>
                            </View>
                          )}
                        </View>
                      </View>

                      <View style={styles.taskStatusContainer}>
                        {/* <View style={styles.taskStatus}>
                          <Ionicons
                            name={Boolean(task.mark_as_complete === "true" || task.mark_as_complete === true) ? "checkmark-circle" : "ellipse-outline"}
                            size={14}
                            color={Boolean(task.mark_as_complete === "true" || task.mark_as_complete === true) ? colors.primary : colors.accent}
                          />
                          <Text style={[
                            styles.statusText,
                            { color: Boolean(task.mark_as_complete === "true" || task.mark_as_complete === true) ? colors.primary : colors.accent }
                          ]}>
                            {Boolean(task.mark_as_complete === "true" || task.mark_as_complete === true) ? 'Completed' : 'Pending'}
                          </Text>
                        </View> */}
                      </View>
                    </TouchableOpacity>
                  ))}
                </>
              )}

              {activities.length === 0 && tasks.length === 0 && (
                <Text style={[styles.activityTitle, { textAlign: 'center' }]}>
                  No activities for this day
                </Text>
              )}
            </>
          )}
        </View>
      </ScrollView>

      <TouchableOpacity
        style={styles.fab}
        onPress={() => setShowAddMenu(true)}
      >
        <Ionicons name="add" size={24} style={styles.fabIcon} />
      </TouchableOpacity>

      <Modal
        visible={showAddMenu}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowAddMenu(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowAddMenu(false)}
        >
          <View style={styles.addMenuContainer}>
            <Text style={styles.addMenuTitle}>ADD NEW</Text>
            <View style={styles.addMenuOptions}>
              <TouchableOpacity
                style={styles.optionButton}
                onPress={() => {
                  setShowAddMenu(false);
                  router.push({
                    pathname: './tasks/create' as any
                  });
                }}
              >
                <View style={styles.optionIcon}>
                  <Ionicons name="checkbox-outline" size={24} color={colors.primary} />
                </View>
                <Text style={styles.optionText}>Task</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.optionButton}
                onPress={() => {
                  setShowAddMenu(false);
                  router.push({
                    pathname: '/meetings/createMeeting' as any
                  });
                }}
              >
                <View style={styles.optionIcon}>
                  <Ionicons name="calendar-outline" size={24} color={colors.primary} />
                </View>
                <Text style={styles.optionText}>Meeting</Text>
              </TouchableOpacity>
            </View>
          </View>
        </TouchableOpacity>
      </Modal>
    </SafeAreaView>
  );
}