import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Switch,
  Image,
  Modal,
  Alert,
  StatusBar,
  Linking,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView, Edge } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { useColors } from '@/hooks/useThemeColor';
import { router } from 'expo-router';
import { Contact } from './models/Contact';
import { showSnackbar, getAvatarColor, getContrastingTextColor, useAppData, readUserScope,  validateForEditAndDeleteActs, capitalizeWords } from './ui/utils';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { SingleContactResponse, ContactField, ContactDetails, getFieldValue } from './models/model_single_contact_details';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import { Appbar } from 'react-native-paper';
import { RECORDS_STORAGE_KEY, USER_STORAGE_ALWAYS } from '@/services/AuthService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SALES_ACTIVITIES_STORAGE_KEY } from '@/services/AuthService';
import { Utils } from '@react-native-firebase/app';

// Move createStyles outside the component
const createStyles = (colors: ReturnType<typeof useColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.secondary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: '#0F96BB',
    zIndex: 10,
    elevation: 4,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text.inverse,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    padding: 4,
  },
  optionsButton: {
    padding: 4,
  },
  segmentContainer: {
    flexDirection: 'row',
    backgroundColor: colors.background.primary,
    height: Platform.OS === 'ios' ? 50 : 56,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
    zIndex: 10,
    elevation: 4,
  },
  segmentButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    position: 'relative',
    ...Platform.select({
      android: {
        elevation: 0,
        paddingVertical: 2,
      }
    }),
  },
  segmentText: {
    fontSize: 14,
    color: colors.text.secondary,
  },
  activeSegment: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
  },
  activeSegmentText: {
    color: colors.primary,
    fontWeight: '500',
  },
  contentContainer: {
    flex: 1,
    backgroundColor: colors.background.secondary,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.primary,
    borderRadius: 8,
    margin: 15,
    paddingHorizontal: 10,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  searchIcon: {
    marginRight: 8,
    color: colors.text.secondary,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 10,
    fontSize: 15,
    color: colors.text.primary,
  },
  clearSearchButton: {
    padding: 4,
  },
  toggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.background.primary,
    padding: 15,
    marginHorizontal: 15,
    marginBottom: 15,
    borderRadius: 8,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  toggleLabel: {
    fontSize: 15,
    color: colors.text.primary,
  },
  sectionContainer: {
    backgroundColor: colors.background.primary,
    marginHorizontal: 15,
    marginBottom: 15,
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  sectionHeader: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.primary,
    backgroundColor: colors.background.secondary,
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
  },
  fieldRow: {
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
  },
  fieldLabel: {
    fontSize: 13,
    color: colors.text.secondary,
    marginBottom: 4,
  },
  fieldValue: {
    fontSize: 15,
    color: colors.text.primary,
  },
  fieldValueLink: {
    fontSize: 15,
    color: colors.primary,
  },
  fieldValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  requiredAsterisk: {
    color: '#FF3B30',
    fontWeight: 'bold',
  },
  emptyValue: {
    color: colors.text.tertiary,
  },
  activitiesContainer: {
    flex: 1,
    backgroundColor: colors.background.secondary,
  },
  quickActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 16,
    backgroundColor: colors.background.primary,
    marginBottom: 8,
  },
  quickAction: {
    alignItems: 'center',
    width: 100,
  },
  actionIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.background.tertiary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  actionText: {
    fontSize: 14,
    color: colors.text.primary,
  },
  activitiesListContainer: {
    backgroundColor: colors.background.primary,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
  },
  activityText: {
    fontSize: 16,
    color: colors.text.primary,
    flex: 1,
    marginLeft: 16,
  },
  chevron: {
    marginLeft: 8,
  },
  moreContainer: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  moreItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
  },
  moreItemText: {
    fontSize: 16,
    color: colors.text.primary,
    flex: 1,
    marginLeft: 16,
  },
  fab: {
    position: 'absolute',
    right: 16,
    bottom: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 999,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      },
      android: {
        elevation: 8,
        overflow: 'hidden',
      }
    }),
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  optionsContainer: {
    backgroundColor: colors.background.primary,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingVertical: 8,
    ...Platform.select({
      android: {
        elevation: 24,
        paddingBottom: 16,
      }
    }),
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  optionText: {
    fontSize: 16,
    color: colors.text.primary,
    marginLeft: 16,
  },
  optionIcon: {
    marginRight: 16,
    width: 24,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
    color: colors.text.secondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: colors.text.secondary,
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: colors.text.inverse,
    fontSize: 16,
    fontWeight: 'bold',
  },
  disabledItem: {
    opacity: 0.7,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    backgroundColor: colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
  },
  avatarContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.background.tertiary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  avatarText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text.primary,
  },
  contactName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text.primary,
  },
  contactEmail: {
    fontSize: 14,
    color: colors.text.secondary,
  },
});

export default function ContactDetailScreen() {
  const [activeSegment, setActiveSegment] = useState('Contact details');
  const [showEmptyFields, setShowEmptyFields] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [optionsVisible, setOptionsVisible] = useState(false);
  const [contactDetails, setContactDetails] = useState<ContactDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [initialContact, setInitialContact] = useState<Contact | null>(null);
  const { recordTypes, salesActivities } = useAppData();
  const router = useRouter();
  const params = useLocalSearchParams();
  const colors = useColors();
  const styles = useMemo(() => createStyles(colors), [colors]);
  const [accessScope, setAccessScope] = useState<any>(null);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);

  useEffect(() => {
    const fetchAccessScope = async () => {
      const scope = await readUserScope();
      console.log("scopesdsds: " + JSON.stringify(scope));
      setAccessScope(scope);
    };
    fetchAccessScope();
  }, []);

  useEffect(() => {
    if (accessScope && contactDetails?.contacts) {
      refreshAccessScope('delete');
      refreshAccessScope('edit');
    }
  }, [accessScope, contactDetails?.contacts]);

  useEffect(() => {
    try {
      if (typeof params.contact === 'string') {
        const parsedContact = JSON.parse(params.contact);
        if (typeof parsedContact === 'object' && parsedContact !== null && 'id' in parsedContact) {
          setInitialContact(parsedContact as Contact);
        } else {
          throw new Error('Invalid contact data structure');
        }
      } else if (params.contact && typeof params.contact === 'object' && 'id' in params.contact) {
        setInitialContact((params.contact as unknown) as Contact);
      } else if (params.id) {
        // If we only have an ID, fetch the contact details
        const fetchContactById = async () => {
          try {
            setIsLoading(true);
            const response = await apiService.get<SingleContactResponse>(`${API_ENDPOINTS.CONTACTS}/${params.id}`);
            if (response.data?.contact) {
              // Create a minimal Contact object with required fields
              const contact: Contact = {
                id: Number(params.id),
                first_name: getFieldValue(response.data.contact.contacts, 'first_name') || '',
                last_name: getFieldValue(response.data.contact.contacts, 'last_name') || '',
                emails: getFieldValue(response.data.contact.contacts, 'emails') || '',
                mobile_number: getFieldValue(response.data.contact.contacts, 'mobile_number') || '',
                work_number: getFieldValue(response.data.contact.contacts, 'work_number') || '',
                job_title: getFieldValue(response.data.contact.contacts, 'job_title') || '',
                subscription_status: getFieldValue(response.data.contact.contacts, 'subscription_status') || '',
                address: getFieldValue(response.data.contact.contacts, 'address') || '',
                city: getFieldValue(response.data.contact.contacts, 'city') || '',
                state: getFieldValue(response.data.contact.contacts, 'state') || '',
                zipcode: getFieldValue(response.data.contact.contacts, 'zipcode') || '',
                country: getFieldValue(response.data.contact.contacts, 'country') || '',
                gst: getFieldValue(response.data.contact.contacts, 'gst') || '',
                // Add required fields with default values
                sales_accounts: null,
                telephone_numbers: null,
                owner_id: null,
                subscription_types: null,
                unsubscription_reason: null,
                other_unsubscription_reason: null,
                lifecycle_stage_id: null,
                contact_status_id: null,
                lost_reason_id: null,
                first_campaign: null,
                first_medium: null,
                first_source: null,
                last_campaign: null,
                last_medium: null,
                last_source: null,
                latest_campaign: null,
                latest_medium: null,
                latest_source: null,
                campaign_id: null,
                medium: null,
                keyword: null,
                lead_source_id: null,
                last_contacted: null,
                tags: null,
                customer_fit: null,
                web_form_ids: null,
                recent_note: null,
                external_id: null,
                sms_subscription_status: null,
                whatsapp_subscription_status: null,
                work_email: null,
                phone_numbers: null,
                facebook: null,
                twitter: null,
                linkedin: null,
                territory_id: null,
                lists: null,
                last_seen_chat: null,
                total_sessions: null,
                locale: null,
                first_seen_chat: null,
                last_contacted_mode: null,
                last_contacted_sales_activity_mode: null,
                last_contacted_via_sales_activity: null,
                active_sales_sequences: null,
                completed_sales_sequences: null,
                last_seen: null,
                lead_score: null,
                creater_id: 0,
                create_at: new Date().toISOString(),
                updater_id: null,
                update_at: null,
                last_assigned_at: null,
                time_zone: null,
                active: '1',
                custom_field: null,
                deal_value: null,
                dealcount: null,
                open_dealvalue: null,
                open_dealcount: null,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              };
              setInitialContact(contact);
            } else {
              throw new Error('No contact data received');
            }
          } catch (error) {
            console.error('Error fetching contact by ID:', error);
            setError('Failed to load contact details');
          } finally {
            setIsLoading(false);
          }
        };
        fetchContactById();
      } else {
        throw new Error('Invalid contact data format');
      }
    } catch (error) {
      console.error('Error parsing contact data:', error);
      setError('Invalid contact data received');
    }
  }, [params.contact, params.id]);

  useEffect(() => {
    if (initialContact?.id) {
      fetchContactDetails();
    }
  }, [initialContact]);

  useFocusEffect(
    useCallback(() => {
      if (initialContact?.id) {
        console.log('Contact detail screen focused, refreshing contact details...');
        fetchContactDetails();
      }
    }, [initialContact?.id])
  );

  const [canDelete, setCanDelete] = useState(false);
  const [canEdit, setCanEdit] = useState(false);

  const refreshAccessScope = async (type: string) => {
    console.log("refreshAccessScope: " + JSON.stringify(accessScope));
    const ownerId = getFieldValue(contactDetails?.contacts!, 'owner_id');
    const territoryId = initialContact?.territory_id;
    
    // Get the permission value based on type
    let permission;
    if (type === 'delete') {
      permission = accessScope?.contact?.delete;
    } else if (type === 'edit') {
      permission = accessScope?.contact?.edit;
    } else if (type === 'view') {
      permission = accessScope?.contact?.view;
    }

    console.log(`${type} permission:`, permission);
    console.log("ownerId:", ownerId, "territoryId:", territoryId);

    // Handle different permission types
    let canAccess = false;
    if (permission === true || permission === 'global') {
      canAccess = true;
    } else if (permission === 'owned') {
      // For owned permission, check if the current user is the owner
      canAccess = ownerId === currentUserId; // You'll need to add currentUserId to your component
    } else if (permission === 'restricted') {
      // For restricted permission, check territory and owner
      canAccess = await validateForEditAndDeleteActs(territoryId!, permission, ownerId!);
    }

    console.log(`can${type}:`, canAccess);

    if (type === 'delete') {
      setCanDelete(canAccess);
    } else if (type === 'edit') {
      setCanEdit(canAccess);
    }
  };

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const userData = await apiService.get(API_ENDPOINTS.USER_PROFILE);
        if (userData?.data?.id) {
          setCurrentUserId(userData.data.id.toString());
        }
      } catch (error) {
        console.error('Error fetching current user:', error);
      }
    };

    fetchUserData();
  }, []);

  useEffect(() => {
    console.log("Refreshing access scope with data:", {
      accessScope: JSON.stringify(accessScope),
      hasContacts: !!contactDetails?.contacts,
      currentUserId
    });
    if (accessScope && contactDetails?.contacts && currentUserId) {
      refreshAccessScope('delete');
      refreshAccessScope('edit');
    }
  }, [accessScope, contactDetails?.contacts, currentUserId]);

  const fetchContactDetails = useCallback(async () => {
    if (!initialContact?.id) {
      setError('No contact ID available');
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const [scope, response] = await Promise.all([
        readUserScope(),
        apiService.get<SingleContactResponse>(`${API_ENDPOINTS.CONTACTS}/${initialContact.id}`)
      ]);

      // Batch state updates
      setAccessScope(scope);
      if (response.data?.contact) {
        setContactDetails(response.data.contact);
      } else {
        throw new Error('No contact data received');
      }
    } catch (err) {
      console.error('Error fetching contact details:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch contact details');
    } finally {
      setIsLoading(false);
    }
  }, [initialContact?.id]);

  const handleRefresh = async () => {
    if (initialContact?.id) {
      await fetchContactDetails();
    }
  };

  const handleTaskListNavigation = () => {
    if (!initialContact?.id) {
      showSnackbar('Cannot access tasks: Contact information is not available');
      return;
    }

    router.push({
      pathname: '/TaskListScreen' as any,
      params: { contact_id: initialContact.id }
    });
  };

  const handleMeetingListNavigation = () => {
    if (!initialContact?.id) {
      showSnackbar('Cannot access meetings: Contact information is not available');
      return;
    }

    router.push({
      pathname: '/meetings/meetingList' as any,
      params: { contact_id: initialContact.id }
    });
  };

  const handleBack = () => {
    console.log('Navigating back from contact detail');
    router.back();
  };

  const handleSegmentPress = (segment: string) => {
    setActiveSegment(segment);
  };

  const handleShowOptions = () => {
    setOptionsVisible(true);
  };

  const handleOptionPress = (option: string) => {
    setOptionsVisible(false);

    switch (option) {
      case 'Call':
        // Handle call
        handleCall();
        //Alert.alert('Call', 'Calling contact...');
        break;
      case 'Send email':
        // Handle email
        Alert.alert('Email', 'Opening email composer...');
        break;
      case 'Share via WhatsApp':
        // Handle WhatsApp share
        if (!contactDetails) {
          showSnackbar('Contact details not available');
          return;
        }

        const firstName = getFieldValue(contactDetails.contacts, 'first_name') || '';
        const lastName = getFieldValue(contactDetails.contacts, 'last_name') || '';
        const fullName = `${firstName} ${lastName}`.trim();
        const phone = getFieldValue(contactDetails.contacts, 'mobile_number') ||
          getFieldValue(contactDetails.contacts, 'work_number') || '';
        const email = getFieldValue(contactDetails.contacts, 'emails') || '';

        // Format the phone number for WhatsApp
        let formattedPhone = phone.replace(/[^\d+]/g, '');
        if (!formattedPhone.startsWith('+')) {
          formattedPhone = formattedPhone.replace(/^0+/, '');
        }

        // Create the message content
        const message = `Contact Details:\n\nName: ${fullName}\nPhone: ${phone}\nEmail: ${email}`;
        const encodedMessage = encodeURIComponent(message);

        // Try different WhatsApp URL schemes
        const whatsappSchemes = [
          'whatsapp://send',
          'whatsapp://app',
          'whatsappbusiness://send',
          'whatsappbusiness://app'
        ];

        const tryOpenWhatsApp = async () => {
          for (const scheme of whatsappSchemes) {
            const url = `${scheme}?text=${encodedMessage}`;
            try {
              const canOpen = await Linking.canOpenURL(url);
              if (canOpen) {
                await Linking.openURL(url);
                return true;
              }
            } catch (err) {
              console.log(`Failed to open ${scheme}:`, err);
            }
          }
          return false;
        };

        tryOpenWhatsApp().then(success => {
          if (!success) {
            // If no WhatsApp app is available, try opening in browser
            const webUrl = `https://api.whatsapp.com/send?text=${encodedMessage}`;
            Linking.openURL(webUrl).catch(err => {
              console.error('Error opening WhatsApp web:', err);
              showSnackbar('Could not open WhatsApp. Please make sure WhatsApp or WhatsApp Business is installed.');
            });
          }
        }).catch(err => {
          console.error('Error checking WhatsApp availability:', err);
          showSnackbar('Could not open WhatsApp. Please make sure WhatsApp or WhatsApp Business is installed.');
        });
        break;
      case 'Download as vCard':
        // Handle vCard download
        Alert.alert('Download', 'vCard download functionality will be available soon.');
        break;
      case 'Edit':
        // Check edit access
        editContact();
        break;
      case 'Delete':
        // Check delete access
        //if (accessScope?.contact?.delete === 'global' || accessScope?.contact?.delete === true) {
          Alert.alert(
            'Delete Contact',
            'Are you sure you want to delete this contact?',
            [
              { text: 'Cancel', style: 'cancel' },
              {
                text: 'Delete',
                style: 'destructive',
                onPress: () => deleteTask()
              },
            ]
          );
        //} else {
        //  showSnackbar('You do not have permission to delete contacts');
        //}
        break;
      default:
        break;
    }
  };

  const editContact = async () => {

        if (canEdit) {
          if (initialContact) {
            router.push({
              pathname: '/contact' as any,
              params: { contact: JSON.stringify(contactDetails?.contacts), id: initialContact.id }
            });
          } else {
            showSnackbar('Cannot edit contact: Contact information is not available');
          }
        } else {
          showSnackbar('You do not have permission to edit contacts');
        }
  }
  // Add function to delete account
  const deleteTask = async () => {
    if (!initialContact?.id) {
      showSnackbar('Cantact ID is missing');
      return;
    }

    try {
      // Show loading indicator or disable UI interactions here if needed
      const endpoint = API_ENDPOINTS.CONTACT_DETAILS.replace(':id', initialContact.id.toString());
      console.log('endpointsdsd', endpoint);
      const response = await apiService.delete(endpoint);
      console.log('responsesdsd', JSON.stringify(response));
      if (response.status === 200 || response.status === 204) {
        //showSnackbar('Cantact deleted successfully');
        // Navigate back to the Task list
        // router.replace('/(tabs)/contacts');
        router.back();
      } else {
        throw new Error(response.data?.message || 'Failed to delete Cantact');
      }
    } catch (error) {
      console.error('Error deleting account:', error);
      showSnackbar('Failed to delete Cantact. Please try again.');
    }
  };
  const navigateToSection = (section: string) => {
    if (!contactDetails || !initialContact?.id) return;

    switch (section) {
      case 'accounts':
        router.push({
          pathname: '/RelatedAccountsList' as any,
          params: { contact_id: initialContact.id }
        });
        break;
      case 'deals':
        router.push({
          pathname: '/RelatedDealsList' as any,
          params: { contactId: initialContact.id }
        });
        break;
      case 'files':
        router.push({
          pathname: 'files' as any,
          params: { contactId: initialContact.id }
        });
        break;
      default:
        break;
    }
  };

  const handleAddContact = () => {
    router.push('/contact' as any);
  };

  const handleCall = async () => {
    if (!contactDetails) return;

    const phoneNumber = getFieldValue(contactDetails.contacts, 'mobile_number') ||
      getFieldValue(contactDetails.contacts, 'work_number');

    if (!phoneNumber) {
      showSnackbar('No phone number available for this contact.');
      return;
    }

    // Format the number - keep only digits and plus sign at the start
    let formattedNumber = phoneNumber.replace(/[^\d+]/g, '');
    if (!formattedNumber.startsWith('+')) {
      formattedNumber = formattedNumber.replace(/^0+/, ''); // Remove leading zeros
    }

    const phoneUrl = Platform.select({
      ios: `telprompt:${formattedNumber}`,
      android: `tel:${formattedNumber}`,
      default: `tel:${formattedNumber}`
    });

    try {
      const supported = await Linking.canOpenURL(phoneUrl);
      console.log('Phone URL:', phoneUrl, 'Supported:', supported);
      await Linking.openURL(phoneUrl);
    } catch (error) {
      console.error('Phone call error:', error);
      Alert.alert('Error', 'Failed to make phone call');
    }
  };

  const shouldShowField = useCallback((label: string, value: string | null, field: ContactField) => {
    // Filter by search query if provided
    const matchesSearch = !searchQuery ||
      label.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (value && value.toString().toLowerCase().includes(searchQuery.toLowerCase()));

    // Filter empty fields based on toggle
    const showBasedOnEmpty = showEmptyFields || (value !== null && value !== '');

    return matchesSearch && showBasedOnEmpty;
  }, [searchQuery, showEmptyFields]);

  const filterFields = (fields: ContactField[]): ContactField[] => {
    if (!fields) return [];
    return fields.filter(field => {
      // Filter by search query if provided
      let matchesSearch = true;
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const labelMatch = field.field_label.toLowerCase().includes(query);

        // For dropdown/lookup fields, also search in the display name
        let valueMatch = false;
        if (field.field_value) {
          const fieldType = field.field_type.toLowerCase();
          if ((fieldType === 'dropdown' || fieldType === 'lookup') && field.choices && field.choices.length > 0) {
            const selectedChoice = field.choices.find(choice =>
              choice.id.toString() === field.field_value?.toString() ||
              choice.custom_option === field.field_value
            );
            const displayValue = selectedChoice ? selectedChoice.custom_option : field.field_value;
            valueMatch = displayValue.toString().toLowerCase().includes(query);
          } else {
            valueMatch = field.field_value.toString().toLowerCase().includes(query);
          }
        }

        matchesSearch = labelMatch || valueMatch;
      }

      // Filter empty fields based on toggle
      const hasValue = field.field_value !== null &&
                      field.field_value !== '' &&
                      field.field_value !== undefined &&
                      field.field_value.toString().trim() !== '';
      const showField = showEmptyFields || hasValue;

      return matchesSearch && showField;
    });
  };

  const renderFieldValue = useCallback((field: ContactField) => {
    // Skip rendering if field is empty and showEmptyFields is false
    if (!showEmptyFields && !field.field_value) return null;

    // Format value based on field type
    let displayValue = field.field_value || (showEmptyFields ? 'Not provided' : '');

    // Handle dropdown and lookup fields - convert ID to display name
    const fieldType = field.field_type.toLowerCase();
    if ((fieldType === 'dropdown' || fieldType === 'lookup') && field.choices && field.choices.length > 0 && field.field_value) {
      const selectedChoice = field.choices.find(choice => {
        // Try to match by ID first (most common case)
        const idMatch = choice.id.toString() === field.field_value?.toString();
        // Then try to match by custom_option (fallback)
        const optionMatch = choice.custom_option === field.field_value;

        return idMatch || optionMatch;
      });

      if (selectedChoice) {
        displayValue = selectedChoice.custom_option;
      }
      // If no choice found, keep the original value (might be a valid text value)
    }

    // Handle special field types
    switch (field.field_name) {
      case 'website':
        return field.field_value ? (
          <TouchableOpacity onPress={() => {
            const url = field.field_value!.startsWith('http') ? field.field_value! : `https://${field.field_value!}`;
            Linking.openURL(url).catch(err => {
              console.error('Error opening URL:', err);
              showSnackbar('Could not open website');
            });
          }}>
            <Text style={[styles.fieldValue, styles.fieldValueLink]}>
              {displayValue}
            </Text>
          </TouchableOpacity>
        ) : (showEmptyFields ? 'Not provided' : '');
      case 'mobile_number':
      case 'work_number':
        return field.field_value ? (
          <TouchableOpacity onPress={() => {
            const phoneUrl = Platform.select({
              ios: `telprompt:${field.field_value}`,
              android: `tel:${field.field_value}`,
              default: `tel:${field.field_value}`
            });
            Linking.openURL(phoneUrl).catch(err => {
              console.error('Error making phone call:', err);
              showSnackbar('Could not make phone call');
            });
          }}>
            <Text style={[styles.fieldValue, styles.fieldValueLink]}>
              {displayValue}
            </Text>
          </TouchableOpacity>
        ) : (showEmptyFields ? 'Not provided' : '');
      case 'emails':
        return field.field_value ? (
          <TouchableOpacity onPress={() => {
            const emailUrl = `mailto:${field.field_value}`;
            Linking.openURL(emailUrl).catch(err => {
              console.error('Error opening email:', err);
              showSnackbar('Could not open email client');
            });
          }}>
            <Text style={[styles.fieldValue, styles.fieldValueLink]}>
              {displayValue}
            </Text>
          </TouchableOpacity>
        ) : (showEmptyFields ? 'Not provided' : '');
      default:
        return (
          <Text style={[styles.fieldValue, !field.field_value && styles.emptyValue]}>
            {displayValue}
          </Text>
        );
    }
  }, [showEmptyFields, showSnackbar]);

  const renderField = useCallback((field: ContactField) => {
    if (!shouldShowField(field.field_label, field.field_value, field)) return null;

    return (
      <View style={styles.fieldRow}>
        <Text style={styles.fieldLabel}>
          {field.field_label}
          {field.required === 'on' && <Text style={styles.requiredAsterisk}> *</Text>}
        </Text>
        <View style={styles.fieldValueContainer}>
          {renderFieldValue(field)}
        </View>
      </View>
    );
  }, [shouldShowField, renderFieldValue]);

  const renderContactDetails = () => {
    if (!contactDetails?.contacts) return null;

    const fields = filterFields(contactDetails.contacts);
    
    // Group fields by their type/category
    const groupedFields = fields.reduce((acc: Record<string, ContactField[]>, field: ContactField) => {
      let section = 'BASIC INFORMATION';
      
      // Determine section based on field type or name
      if (['mobile_number', 'work_number', 'emails', 'website'].includes(field.field_name)) {
        section = 'CONTACT INFORMATION';
      } else if (['address', 'city', 'state', 'zipcode', 'country'].includes(field.field_name)) {
        section = 'ADDRESS';
      } else if (['subscription_status', 'gst'].includes(field.field_name)) {
        section = 'ADDITIONAL INFORMATION';
      }

      if (!acc[section]) {
        acc[section] = [];
      }
      acc[section].push(field);
      return acc;
    }, {});

    return (
      <ScrollView style={styles.contentContainer}>
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color={colors.text.secondary} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search fields..."
            placeholderTextColor={colors.text.secondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery ? (
            <TouchableOpacity onPress={() => setSearchQuery('')} style={styles.clearSearchButton}>
              <Ionicons name="close-circle" size={20} color={colors.text.secondary} />
            </TouchableOpacity>
          ) : null}
        </View>

        <View style={styles.toggleContainer}>
          <Text style={styles.toggleLabel}>Show empty fields</Text>
          <Switch
            value={showEmptyFields}
            onValueChange={setShowEmptyFields}
            trackColor={{ false: '#f0f0f0', true: '#d1e0ff' }}
            thumbColor={showEmptyFields ? '#0F6EBB' : '#fff'}
          />
        </View>

        {Object.entries(groupedFields).map(([section, sectionFields]) => (
          <View key={section} style={styles.sectionContainer}>
            <Text style={styles.sectionHeader}>{section}</Text>
            {sectionFields.map((field, index) => (
              <View key={field.id || index} style={styles.fieldRow}>
                <Text style={styles.fieldLabel}>
                  {field.field_label}
                  {field.required === 'on' && <Text style={styles.requiredAsterisk}> *</Text>}
                </Text>
                <View style={styles.fieldValueContainer}>
                  {renderFieldValue(field)}
                </View>
              </View>
            ))}
          </View>
        ))}
      </ScrollView>
    );
  };

  const renderActivities = () => (
    <View style={styles.activitiesContainer}>
      <View style={styles.quickActionsContainer}>
        {accessScope?.calllog.create === true && (
          <TouchableOpacity style={styles.quickAction} onPress={handleCall}>
            <View style={styles.actionIconContainer}>
              <Ionicons name="call-outline" size={24} color={colors.text.primary} />
            </View>
            <Text style={styles.actionText}>Call phone</Text>
          </TouchableOpacity>
        )}
        {accessScope?.notes.create === true && (
          <TouchableOpacity style={styles.quickAction} onPress={() =>
            router.push({
              pathname: '/ui/helpers/AddNotesPage' as any,
              params: { id: initialContact!.id, name: "contacts" }
            })}
          >
            <View style={styles.actionIconContainer}>
              <Ionicons name="mic-outline" size={24} color={colors.text.primary} />
            </View>
            <Text style={styles.actionText}>Add note</Text>
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.activitiesListContainer}>
        {accessScope?.notes.view === true && (
          <TouchableOpacity style={[styles.activityItem, (contactDetails?.count.notes === 0) && styles.disabledItem]} onPress={() =>
            // {if(contactDetails?.notes_count && contactDetails.notes_count > 0){
            router.push({
              pathname: '/ui/screens/NotesListScreen' as any,
              params: { id: initialContact!.id, type: "contacts" }
            })
            //} else {
            //showSnackbar('No notes available for this contact');
            //}
            //}
          }
          >
            <Ionicons name="document-text-outline" size={22} color={colors.text.primary} />
            <Text style={styles.activityText}>Notes ({contactDetails?.count.notes || 0})</Text>
            <Ionicons name="chevron-forward" size={22} color={colors.text.secondary} style={styles.chevron} />
          </TouchableOpacity>
        )}
        {accessScope?.task.view === true && (
          <TouchableOpacity
            style={[styles.activityItem, (contactDetails?.count.tasks === 0) && styles.disabledItem]}
            onPress={() => {
              //if (contactDetails?.tasks.length && contactDetails.tasks.length > 0) {
              handleTaskListNavigation();
              //} else {
              //showSnackbar('No tasks available for this contact');
              //}
            }}
          >
            <Ionicons name="calendar-outline" size={22} color={colors.text.primary} />
            <Text style={styles.activityText}>Tasks ({contactDetails?.count.tasks || 0})</Text>
            <Ionicons name="chevron-forward" size={22} color={colors.text.secondary} style={styles.chevron} />
          </TouchableOpacity>
        )}
        {accessScope?.appointment.view === true && (
          <TouchableOpacity
            style={[styles.activityItem, (contactDetails?.count.meetings === 0) && styles.disabledItem]}
            onPress={() => {
              //if (contactDetails?.meetings.length && contactDetails.meetings.length > 0) {
              handleMeetingListNavigation();
              //} else {
              //showSnackbar('No meetings available for this contact');
              //}
            }}

          >
            <Ionicons name="people-outline" size={22} color={colors.text.primary} />
            <Text style={styles.activityText}>Meetings ({contactDetails?.count.meetings || 0})</Text>
            <Ionicons name="chevron-forward" size={22} color={colors.text.secondary} style={styles.chevron} />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  const renderMore = () => (
    <View style={styles.moreContainer}>
      <TouchableOpacity
        style={styles.moreItem}
        onPress={() => navigateToSection('accounts')}
      >
        <Ionicons name="business-outline" size={22} color={colors.text.primary} />
        { /* ({contactDetails?.count.sales_accounts || 0})*/}
        <Text style={styles.moreItemText}>Accounts</Text> 
        <Ionicons name="chevron-forward" size={22} color={colors.text.secondary} style={styles.chevron} />
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.moreItem}
        onPress={() => navigateToSection('deals')}
      >
        <Ionicons name="cash-outline" size={22} color={colors.text.primary} />
        <Text style={styles.moreItemText}>Opportunities </Text>
        <Ionicons name="chevron-forward" size={22} color={colors.text.secondary} style={styles.chevron} />
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.moreItem}
        onPress={() => navigateToSection('files')}
      >
        <Ionicons name="document-attach-outline" size={22} color={colors.text.primary} />
        {/* <Text style={styles.moreItemText}>Files ({contactDetails?.count.files || 0})</Text> */}
        <Text style={styles.moreItemText}>Files</Text>
        <Ionicons name="chevron-forward" size={22} color={colors.text.secondary} style={styles.chevron} />
      </TouchableOpacity>
    </View>
  );

  const renderContent = useCallback(() => {
    switch (activeSegment) {
      case 'Contact details':
        return renderContactDetails();
      case 'Activities':
        return renderActivities();
      case 'More':
        return renderMore();
      default:
        return renderContactDetails();
    }
  }, [activeSegment, renderContactDetails, renderActivities, renderMore]);

  const contactName = useMemo(() => {
    if (!contactDetails?.contacts) return 'Contact Details';
    const firstName = contactDetails.contacts.find(field => field.field_name === 'first_name')?.field_value;
    const lastName = contactDetails.contacts.find(field => field.field_name === 'last_name')?.field_value;
    return capitalizeWords(`${firstName || ''} ${lastName || ''}`.trim() || 'Contact Details');
  }, [contactDetails?.contacts]);

  const renderContactHeader = () => {
    if (!contactDetails) return null;
    const firstName = getFieldValue(contactDetails.contacts, 'first_name') || '';
    const lastName = getFieldValue(contactDetails.contacts, 'last_name') || '';
    const fullName = `${firstName} ${lastName}`.trim();
    const email = getFieldValue(contactDetails.contacts, 'emails');
    const avatarBgColor = getAvatarColor(fullName);
    const textColor = getContrastingTextColor(avatarBgColor);
    return (
      <View style={styles.headerContainer}>
        <View style={[styles.avatarContainer, { backgroundColor: avatarBgColor }]}>
          <Text style={[styles.avatarText, { color: textColor }]}>
            {fullName ? fullName.substring(0, 2).toUpperCase() : 'NA'}
          </Text>
        </View>
        <View>
          <Text style={styles.contactName}>{fullName || 'No Name'}</Text>
          {email && (
            <Text style={styles.contactEmail}>{email}</Text>
          )}
        </View>
      </View>
    );
  };

  const renderHeaderActions = () => {
    return (
      <View style={styles.headerActions}>
        {canEdit && (
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => editContact()}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons name="pencil" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        )}

        {canDelete && (
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => handleOptionPress('Delete')}
          >
            <Ionicons name="trash-outline" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        )}
        {/*
        <TouchableOpacity style={styles.optionsButton} onPress={handleShowOptions}>
          <Ionicons name="ellipsis-vertical" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        */}
      </View>
    );
  };

  const renderOptionsModal = () => {
    return (
      <Modal
        visible={optionsVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setOptionsVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setOptionsVisible(false)}
        >

          <View style={styles.optionsContainer}>
            <TouchableOpacity
              style={styles.optionItem}
              onPress={() => handleOptionPress('Share via WhatsApp')}
            >
              <Ionicons name="logo-whatsapp" size={24} color="#25D366" style={styles.optionIcon} />
              <Text style={styles.optionText}>Share via WhatsApp</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.optionItem}
              onPress={() => handleOptionPress('Download as vCard')}
            >
              <Ionicons name="download-outline" size={24} color={colors.text.primary} style={styles.optionIcon} />
              <Text style={styles.optionText}>Download as vCard</Text>
            </TouchableOpacity>

            {canDelete && (
              <TouchableOpacity
                style={styles.optionItem}
                onPress={() => handleOptionPress('Delete')}
              >
                <Ionicons name="trash-outline" size={24} color="#FF4444" style={styles.optionIcon} />
                <Text style={[styles.optionText, { color: '#FF4444' }]}>Delete</Text>
              </TouchableOpacity>
            )}
          </View>
        </TouchableOpacity>
      </Modal>
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container} edges={['top'] as Edge[]}>
        <StatusBar barStyle="dark-content" backgroundColor={Colors.white} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Loading contact details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container} edges={['top'] as Edge[]}>
        <StatusBar barStyle="dark-content" backgroundColor={Colors.white} />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={fetchContactDetails}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  if (!contactDetails) {
    return null;
  }

  return (
    <SafeAreaView style={styles.container} edges={['top'] as Edge[]}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.white} />
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      {/* Custom Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{contactName}</Text>
        {renderHeaderActions()}
      </View>

      {/* Segments */}
      <View style={styles.segmentContainer}>
        {['Contact details', 'Activities', 'More'].map((segment) => (
          <TouchableOpacity
            key={segment}
            style={[
              styles.segmentButton,
              activeSegment === segment && styles.activeSegment,
            ]}
            onPress={() => handleSegmentPress(segment)}
          >
            <Text
              style={[
                styles.segmentText,
                activeSegment === segment && styles.activeSegmentText,
              ]}
            >
              {segment}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Content */}
      {renderContent()}

      {/* Floating Action Button */}
      {accessScope?.contact?.create === true && (
        <TouchableOpacity style={styles.fab} onPress={handleAddContact}>
          <Ionicons name="add" size={25} color="white" />
        </TouchableOpacity>
      )}

      {renderOptionsModal()}
    </SafeAreaView>
  );
}