import React, { useState, useMemo, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  Modal,
  SafeAreaView,
  ActivityIndicator,
  ViewStyle,
  TextStyle,
  StatusBar,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter } from 'expo-router';
import { useColors } from '@/hooks/useThemeColor';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { ClaimResponse, ClaimItem, formatClaimDate } from '@/app/models/Claim';
import { showSnackbar } from './ui/utils';
import MarqueeView from 'react-native-marquee-view';

// Generate claim cycles for the last 4 completed periods
const generateClaimCycles = () => {
  const cycles = [];
  let cycleId = 1;

  // Get current date
  const today = new Date();

  // Start from current month
  let currentDate = new Date(today);
  currentDate.setDate(1); // Set to first of month to avoid date issues

  // Add periods based on current date
  if (today.getDate() > 15) {
    // Current month's 1-15 period is completed
    cycles.push({
      id: cycleId.toString(),
      period: `01 ${new Intl.DateTimeFormat('en-US', { month: 'short' }).format(currentDate)} ${currentDate.getFullYear()} to 15 ${new Intl.DateTimeFormat('en-US', { month: 'short' }).format(currentDate)} ${currentDate.getFullYear()}`,
      startDate: `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-01`,
      endDate: `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-15`
    });
    cycleId++;

    // Add previous month's periods
    currentDate.setMonth(currentDate.getMonth() - 1);
  }

  // Add remaining periods
  let periodsNeeded = cycles.length === 0 ? 4 : 3;

  while (periodsNeeded > 0) {
    // Add 16-end period first
    const lastDay = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate();
    cycles.push({
      id: cycleId.toString(),
      period: `16 ${new Intl.DateTimeFormat('en-US', { month: 'short' }).format(currentDate)} ${currentDate.getFullYear()} to ${lastDay} ${new Intl.DateTimeFormat('en-US', { month: 'short' }).format(currentDate)} ${currentDate.getFullYear()}`,
      startDate: `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-16`,
      endDate: `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${lastDay}`
    });
    cycleId++;
    periodsNeeded--;

    // Add 1-15 period if needed
    if (periodsNeeded > 0) {
      cycles.push({
        id: cycleId.toString(),
        period: `01 ${new Intl.DateTimeFormat('en-US', { month: 'short' }).format(currentDate)} ${currentDate.getFullYear()} to 15 ${new Intl.DateTimeFormat('en-US', { month: 'short' }).format(currentDate)} ${currentDate.getFullYear()}`,
        startDate: `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-01`,
        endDate: `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-15`
      });
      cycleId++;
      periodsNeeded--;
    }

    // Move to previous month
    currentDate.setMonth(currentDate.getMonth() - 1);
  }

  return cycles;
};

// Generate claim cycles
const CLAIM_CYCLES = generateClaimCycles();

interface Styles {
  safeArea: ViewStyle;
  container: ViewStyle;
  header: ViewStyle;
  backButton: ViewStyle;
  headerActions: ViewStyle;
  iconButton: ViewStyle;
  datePeriodCard: ViewStyle; // Added for date period card
  dateRangeContainer: ViewStyle;
  datePeriodTitle: TextStyle;
  dateRangeSelectorContent: ViewStyle;
  buttonContainer: ViewStyle;
  applyButton: ViewStyle;
  applyButtonDisabled: ViewStyle;
  applyButtonContent: ViewStyle;
  tableHeader: ViewStyle;
  tableRow: ViewStyle;
  loadingContainer: ViewStyle;
  emptyContainer: ViewStyle;
  summaryContainer: ViewStyle;
  summaryItem: ViewStyle;
  summaryTextContainer: ViewStyle; // Added for summary text container
  modalOverlay: ViewStyle;
  modalContent: ViewStyle;
  modalHeader: ViewStyle;
  modalDivider: ViewStyle;
  cycleItem: ViewStyle;
  selectedCycleItem: ViewStyle;
  cycleItemContent: ViewStyle;
  closeButton: ViewStyle;
  headerTitle: TextStyle;
  dateRangeText: TextStyle;
  applyButtonText: TextStyle;
  headerCell: TextStyle;
  cell: TextStyle;
  emptyText: TextStyle;
  summaryLabel: TextStyle;
  summaryValue: TextStyle;
  modalTitle: TextStyle;
  cycleText: TextStyle;
  selectedCycleText: TextStyle;
  customDateContainer: ViewStyle;
  datePickerButton: ViewStyle;
  dateText: TextStyle;
  dateRangeSeparator: TextStyle;
  checkmarkContainer: ViewStyle;
  marqueeContainer: ViewStyle;
}

const createStyles = (colors: ReturnType<typeof useColors>): Styles => StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#FFFFFF', // Same as header background
  },
  container: {
    flex: 1,
    backgroundColor: colors.background.secondary,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12, // Reduced from 15 to 12
    paddingHorizontal: 15,
    backgroundColor: '#0F96BB',
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
    marginBottom: 0, // Ensure no margin at bottom
  },
  backButton: {
    padding: 5,
    width: 70, // Fixed width to match headerActions
  },
  headerActions: {
    flexDirection: 'row',
    width: 70, // Fixed width to match backButton
    justifyContent: 'flex-end',
  },
  iconButton: {
    padding: 5,
    marginLeft: 10,
  },
  datePeriodCard: {
    backgroundColor: colors.background.primary,
    marginTop: 5,
    marginBottom: 5,
    marginHorizontal: 10,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    paddingVertical: 5,
  },
  dateRangeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 5,
    backgroundColor: colors.background.primary,
  },
  datePeriodTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 2,
    paddingHorizontal: 15,
    paddingTop: 3,
    paddingBottom: 0,
  },
  dateRangeSelectorContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginRight: 10, // Reduced from 15 to 10 to give more space to date display
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 8,
    backgroundColor: colors.background.tertiary,
  },
  buttonContainer: {
    padding: 16,
    backgroundColor: colors.background.primary,
  },
  applyButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 8,
    width: '100%', // Changed from minWidth to full width
    justifyContent: 'center',
    alignItems: 'center',
  },
  applyButtonDisabled: {
    opacity: 0.7,
  },
  applyButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: colors.background.primary,
    paddingVertical: 12,
    paddingHorizontal: 10,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
  },
  tableRow: {
    flexDirection: 'row',
    backgroundColor: colors.background.primary,
    paddingVertical: 12,
    paddingHorizontal: 10,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
    alignItems: 'center', // Ensure vertical alignment
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.background.primary,
  },
  summaryContainer: {
    flexDirection: 'row',
    backgroundColor: colors.background.primary,
    padding: 5,
    marginTop: 8,
    borderRadius: 10,
    marginHorizontal: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginBottom: 10,
  },
  summaryItem: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 10,
    backgroundColor: 'rgba(15, 150, 187, 0.08)',
    borderRadius: 8,
    margin: 4,
    borderWidth: 1,
    borderColor: 'rgba(15, 150, 187, 0.1)',
  },
  summaryTextContainer: {
    flex: 1,
    marginLeft: 10,
    alignItems: 'flex-start',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.background.primary,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
  },
  closeButton: {
    padding: 5,
  },
  modalDivider: {
    height: 1,
    backgroundColor: colors.background.tertiary,
  },
  cycleItem: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  selectedCycleItem: {
    backgroundColor: colors.background.secondary,
  },
  cycleItemContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },

  // Text styles
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    flex: 1,
  },
  dateRangeText: {
    fontSize: 16,
    color: colors.text.primary,
    letterSpacing: 0.2, // Added letter spacing for better readability
  },
  applyButtonText: {
    color: colors.text.inverse,
    fontSize: 16,
    fontWeight: 'bold',
  },
  headerCell: {
    fontSize: 15,
    fontWeight: 'bold',
    color: colors.text.primary,
    textAlign: 'center',
  },
  cell: {
    fontSize: 14,
    color: colors.text.primary,
    textAlign: 'center',
    paddingHorizontal: 2, // Add padding for better text display
  },
  emptyText: {
    fontSize: 16,
    color: colors.text.secondary,
    textAlign: 'center',
  },
  summaryLabel: {
    fontSize: 14,
    color: colors.text.secondary,
    marginBottom: 4,
    fontWeight: '500',
  },
  summaryValue: {
    fontSize: 18,
    color: '#0F96BB',
    fontWeight: 'bold',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text.primary,
  },
  cycleText: {
    fontSize: 16,
    color: colors.text.primary,
    flex: 1,
  },
  selectedCycleText: {
    color: colors.text.primary,
    fontWeight: 'bold',
  },
  checkmarkContainer: {
    width: 24,
    alignItems: 'center',
    marginLeft: 15,
  },
  customDateContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 15,
  },
  datePickerButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.background.secondary,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  dateText: {
    fontSize: 14,
    color: colors.text.primary,
  },
  dateRangeSeparator: {
    marginHorizontal: 8,
    color: colors.text.primary,
  },
  marqueeContainer: {
    flex: 1,
    overflow: 'hidden',
  },
});

// Custom ConditionalMarquee component that only scrolls text longer than 12 characters
const ConditionalMarquee = ({
  text,
  style,
  minLength = 12, // Only apply marquee if text length exceeds this value
  speed = 0.1
}: {
  text: string,
  style?: any,
  minLength?: number,
  speed?: number
}) => {
  // Check if text length exceeds the minimum length for scrolling
  const shouldScroll = text.length > minLength;

  if (!shouldScroll) {
    // If text is short enough, just display it normally
    return <Text style={style} numberOfLines={1} ellipsizeMode="tail">{text}</Text>;
  }

  // If text is long, use MarqueeView for scrolling
  return (
    <MarqueeView
      style={{ flex: 1, overflow: 'hidden' }}
      speed={speed}
    >
      <Text style={style}>{text}</Text>
    </MarqueeView>
  );
};



export default function ClaimScreen() {
  const [claimCycleModalVisible, setClaimCycleModalVisible] = useState(false);
  const [selectedClaimCycle, setSelectedClaimCycle] = useState(CLAIM_CYCLES[0]);
  const [loading, setLoading] = useState(false);
  const [claimHistory, setClaimHistory] = useState<ClaimItem[]>([]);
  const [applyingClaim, setApplyingClaim] = useState(false);
  // Removed unused date-related state variables

  const router = useRouter();
  const colors = useColors();
  const styles = useMemo(() => createStyles(colors), [colors]);

  const fetchClaimHistory = async () => {
    try {
      setLoading(true);
      const response = await apiService.get<ClaimResponse>(
        `${API_ENDPOINTS.CLAIM_HISTORYLIST}?startDate=${selectedClaimCycle.startDate}&endDate=${selectedClaimCycle.endDate}`
      );
      console.log("Claim history response:", response.data);

      if (response.data?.success) {
        setClaimHistory(response.data.response);
      } else {
        showSnackbar('Failed to fetch claims');
      }
    } catch (error) {
      console.error('Error fetching claims:', error);
      showSnackbar('Error loading claims');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchClaimHistory();
  }, [selectedClaimCycle]);

  const handleBack = () => {
    router.back();
  };

  const handleFilterPress = () => {
    setClaimCycleModalVisible(true);
  };

  const handleClaimCycleSelect = (cycle: typeof CLAIM_CYCLES[0]) => {
    setSelectedClaimCycle(cycle);
    setClaimCycleModalVisible(false);
  };

  const handleViewClaimHistory = () => {
    console.log("startDatesdsdfrom",selectedClaimCycle.startDate+" endDate "+selectedClaimCycle.endDate);
    router.push({
      pathname: '/claimHistory',
      params: {
        startDate: selectedClaimCycle.startDate,
        endDate: selectedClaimCycle.endDate
      }
    } as any);
  };

  const handleApplyClaim = async () => {
    try {
      setApplyingClaim(true);
      const response = await apiService.post(API_ENDPOINTS.CONVEYANCE_CLAIM_APPLY, {
        startDate: selectedClaimCycle.startDate,
        endDate: selectedClaimCycle.endDate
      });

      console.log('Apply claim response:', response.data);

      if (response.data?.success) {
        showSnackbar('Claim applied successfully');
        // Refresh the claim history after successful application
        await fetchClaimHistory();
      } else {
        showSnackbar(response.data?.message || 'Failed to apply claim');
      }
    } catch (error) {
      console.error('Error applying claim:', error);
      showSnackbar('Error applying claim');
    } finally {
      setApplyingClaim(false);
    }
  };

  // Calculate total distance and charge
  const totals = useMemo(() => {
    return claimHistory.reduce((acc, item) => {
      return {
        distance: acc.distance + (item.distance || 0),
        charge: acc.charge + (parseFloat(item.claim_amount || '0') || 0)
      };
    }, { distance: 0, charge: 0 });
  }, [claimHistory]);

  // Table Header Component
  const TableHeader = () => (
    <View style={styles.tableHeader}>
      <View style={{ flex: 0.3, alignItems: 'center' }}>
        <Text style={styles.headerCell}>#</Text>
      </View>
      <View style={{ flex: 1.2, alignItems: 'center' }}>
        <Text style={styles.headerCell}>Date</Text>
      </View>
      <View style={{ flex: 1.3, alignItems: 'center' }}>
        <Text style={styles.headerCell}>Distance</Text>
      </View>
      <View style={{ flex: 1, alignItems: 'center' }}>
        <Text style={styles.headerCell}>Time spent</Text>
      </View>
      <View style={{ flex: 1, alignItems: 'center' }}>
        <Text style={styles.headerCell}>Charge</Text>
      </View>
    </View>
  );

  // Format time spent to show only non-zero time components
  const formatTimeSpent = (timeString: string | null): string => {
    if (!timeString) return '-';

    // Parse the time string to extract hours, minutes, and seconds
    let hours = 0, minutes = 0, seconds = 0;

    // If already in a format with time units
    if (timeString.includes('hrs') || timeString.includes('min') || timeString.includes('sec')) {
      const parts = timeString.split(' ');

      for (let i = 0; i < parts.length; i += 2) {
        const value = parseInt(parts[i]);
        if (isNaN(value)) continue;

        const unit = parts[i+1]?.toLowerCase();
        if (!unit) continue;

        if (unit.includes('hr')) hours = value;
        else if (unit.includes('min')) minutes = value;
        else if (unit.includes('sec')) seconds = value;
      }
    }
    // If it's just a number of seconds
    else {
      const totalSeconds = parseInt(timeString);
      if (!isNaN(totalSeconds)) {
        hours = Math.floor(totalSeconds / 3600);
        minutes = Math.floor((totalSeconds % 3600) / 60);
        seconds = totalSeconds % 60;
      } else {
        return timeString; // Return original if we can't parse it
      }
    }

    // Format the time string based on which components are non-zero
    const formattedParts = [];

    if (hours > 0) {
      formattedParts.push(`${hours} hrs`);
    }

    if (minutes > 0) {
      formattedParts.push(`${minutes} min`);
    }

    if (seconds > 0 || formattedParts.length === 0) {
      formattedParts.push(`${seconds} sec`);
    }

    return formattedParts.join(' ');
  };

  // Table Row Component
  const renderItem = ({ item, index }: { item: ClaimItem; index: number }) => {
    // Format values for display
    const dateText = formatClaimDate(item.start_date);
    const distanceText = item.distance ? `${Number(item.distance).toFixed(2)} km` : '-';
    const timeSpentText = formatTimeSpent(item.timespent_display);
    const chargeText = `₹ ${item.claim_amount || '0'}`;

    return (
      <View style={styles.tableRow}>
        {/* Serial number - no marquee needed */}
        <Text style={[styles.cell, { flex: 0.3 }]}>{index + 1}</Text>

        {/* Date with conditional marquee */}
        <View style={[{ flex: 1.2, alignItems: 'center', justifyContent: 'center' }]}>
          <ConditionalMarquee
            text={dateText}
            style={styles.cell}
            minLength={12}
            speed={0.1}
          />
        </View>

        {/* Distance with conditional marquee */}
        <View style={[{ flex: 1.3, alignItems: 'center', justifyContent: 'center' }]}>
          <ConditionalMarquee
            text={distanceText}
            style={styles.cell}
            minLength={12}
            speed={0.1}
          />
        </View>

        {/* Time spent with conditional marquee */}
        <View style={[{ flex: 1, alignItems: 'center', justifyContent: 'center' }]}>
          <ConditionalMarquee
            text={timeSpentText}
            style={styles.cell}
            minLength={12}
            speed={0.1}
          />
        </View>

        {/* Charge with conditional marquee */}
        <View style={[{ flex: 1, alignItems: 'center', justifyContent: 'center' }]}>
          <ConditionalMarquee
            text={chargeText}
            style={styles.cell}
            minLength={12}
            speed={0.1}
          />
        </View>
      </View>
    );
  };

  // Update the date range container to be clickable
  const DateRangeSelector = () => (
    <View style={styles.dateRangeContainer}>
      <TouchableOpacity
        style={[
          styles.dateRangeSelectorContent,
          {
            height: 45,
            flex: 1, // Takes full width
          }
        ]}
        onPress={handleFilterPress}
      >
        <Text
          style={[
            styles.dateRangeText,
            {
              fontWeight: '500',
              flex: 1,
              marginRight: 5
            }
          ]}
          numberOfLines={1}
          ellipsizeMode="tail"
        >
          {selectedClaimCycle.period}
        </Text>
        <Ionicons
          name={claimCycleModalVisible ? "chevron-up" : "chevron-down"}
          size={24}
          color={colors.text.primary}
        />
      </TouchableOpacity>
    </View>
  );

  // Removed unused BottomApplyButton component

  // Update Modal to include custom date option
  const renderModalContent = () => (
    <View style={styles.modalContent}>
      <View style={styles.modalHeader}>
        <Text style={styles.modalTitle}>Select Claim Cycle</Text>
        <TouchableOpacity
          onPress={() => setClaimCycleModalVisible(false)}
          style={styles.closeButton}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons name="close" size={24} color={colors.text.primary} />
        </TouchableOpacity>
      </View>

      <View style={styles.modalDivider} />

      {CLAIM_CYCLES.map((cycle) => (
        <TouchableOpacity
          key={cycle.id}
          style={[
            styles.cycleItem,
            selectedClaimCycle.id === cycle.id && styles.selectedCycleItem
          ]}
          onPress={() => handleClaimCycleSelect(cycle)}
        >
          <View style={styles.cycleItemContent}>
            <Text style={[
              styles.cycleText,
              selectedClaimCycle.id === cycle.id && styles.selectedCycleText
            ]}>
              {cycle.period}
            </Text>
            <View style={styles.checkmarkContainer}>
              {selectedClaimCycle.id === cycle.id && (
                <Ionicons
                  name="checkmark"
                  size={24}
                  color={colors.primary}
                />
              )}
            </View>
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar
        barStyle="light-content"
        backgroundColor="#0F96BB"
        translucent={true}
      />
      <View style={styles.container}>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      {/* Custom Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Claims</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.iconButton} onPress={handleViewClaimHistory}>
            <Ionicons name="document-text-outline" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.iconButton} onPress={handleFilterPress}>
            <Ionicons name="funnel-outline" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </View>

        {/* Date Period Card */}
        <View style={styles.datePeriodCard}>
          <Text style={styles.datePeriodTitle}>Claim Period</Text>
          <DateRangeSelector />
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        ) : (
          <>
            {claimHistory.length > 0 ? (
              <>
                {/* Summary Section */}
                <View style={styles.summaryContainer}>
                  <View style={styles.summaryItem}>
                    <Ionicons
                      name="speedometer-outline"
                      size={22}
                      color="#0F96BB"
                      style={{ alignSelf: 'flex-start', marginTop: 10 }}
                    />
                    <View style={styles.summaryTextContainer}>
                      <Text style={styles.summaryLabel}>Total Distance</Text>
                      <Text style={styles.summaryValue}>{totals.distance.toFixed(2)} km</Text>
                    </View>
                  </View>
                  <View style={{width: 1, backgroundColor: colors.background.tertiary, marginHorizontal: 8}} />
                  <View style={styles.summaryItem}>
                    <Ionicons
                      name="cash-outline"
                      size={22}
                      color="#0F96BB"
                      style={{ alignSelf: 'flex-start', marginTop: 10 }}
                    />
                    <View style={styles.summaryTextContainer}>
                      <Text style={styles.summaryLabel}>Total Claim</Text>
                      <Text style={styles.summaryValue}>₹ {totals.charge.toFixed(2)}</Text>
                    </View>
                  </View>
                </View>

                {/* Claims List with padding bottom for the fixed button */}
                <FlatList
                  data={claimHistory}
                  renderItem={renderItem}
                  keyExtractor={item => item.id.toString()}
                  ListHeaderComponent={TableHeader}
                  stickyHeaderIndices={[0]}
                  contentContainerStyle={{ paddingBottom: 70 }} // Add padding to account for the fixed button
                />
              </>
            ) : (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>No claims found for selected period</Text>
              </View>
            )}
          </>
        )}

        {/* Fixed Apply Button at the bottom */}
        <View style={{
          position: 'absolute',
          bottom: 5, // Adjusted from 0 to 5 to move slightly up
          left: 0,
          right: 0,
          backgroundColor: colors.background.primary,
          paddingVertical: 10, // Reduced from 12 to 10
          paddingHorizontal: 20,
          borderTopWidth: 1,
          borderTopColor: colors.background.tertiary,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.1,
          shadowRadius: 3,
          elevation: 5,
        }}>
          <TouchableOpacity
            style={[
              styles.applyButton,
              { height: 45 },
              applyingClaim && styles.applyButtonDisabled
            ]}
            onPress={handleApplyClaim}
            disabled={applyingClaim}
          >
            {applyingClaim ? (
              <View style={styles.applyButtonContent}>
                <ActivityIndicator size="small" color={colors.text.inverse} />
                <Text style={[styles.applyButtonText, { marginLeft: 8 }]}>
                  Applying...
                </Text>
              </View>
            ) : (
              <Text style={styles.applyButtonText}>Apply Claim</Text>
            )}
          </TouchableOpacity>
        </View>

        {/* Updated Claim Cycle Modal */}
        <Modal
          visible={claimCycleModalVisible}
          transparent
          animationType="slide"
          onRequestClose={() => setClaimCycleModalVisible(false)}
        >
          <TouchableOpacity
            style={styles.modalOverlay}
            activeOpacity={1}
            onPress={() => setClaimCycleModalVisible(false)}
          >
            {renderModalContent()}
          </TouchableOpacity>
        </Modal>
      </View>
    </SafeAreaView>
  );
}