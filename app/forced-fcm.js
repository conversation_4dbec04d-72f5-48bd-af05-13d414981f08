import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Button, ScrollView, SafeAreaView, ActivityIndicator, TouchableOpacity } from 'react-native';
import { setupForcedFCM, showTestNotification } from '../utils/forcedFCM';

export default function ForcedFCMScreen() {
  const [token, setToken] = useState(null);
  const [loading, setLoading] = useState(false);
  const [logs, setLogs] = useState([]);
  const [usingFallback, setUsingFallback] = useState(false);

  const log = (message) => {
    console.log(message);
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const startForcedFCM = async () => {
    try {
      setLoading(true);
      setUsingFallback(false);
      log('Starting forced FCM token retrieval...');
      setLogs([]);
      
      // Use the forced FCM approach
      const result = await setupForcedFCM();
      
      if (result) {
        // Check if it's a fallback ID (they start with "fallback-")
        if (result.startsWith('fallback-')) {
          log('⚠️ Using fallback notification ID - FCM unavailable');
          setUsingFallback(true);
        } else {
          log('✅ Successfully obtained FCM token!');
          setUsingFallback(false);
        }
        
        setToken(result);
      } else {
        log('❌ Failed to get any notification identifier');
        setToken(null);
      }
      
      setLoading(false);
    } catch (error) {
      log(`❌ Error: ${error.message}`);
      setLoading(false);
    }
  };

  const sendTestNotification = async () => {
    try {
      log('Sending test notification...');
      const success = await showTestNotification();
      
      if (success) {
        log('✅ Test notification sent successfully. Check your notification tray.');
      } else {
        log('❌ Failed to send test notification');
      }
    } catch (error) {
      log(`❌ Test notification error: ${error.message}`);
    }
  };

  useEffect(() => {
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;
    
    // Override console methods to capture logs
    console.log = (...args) => {
      originalConsoleLog(...args);
      const message = args.map(arg => 
        typeof arg === 'object' ? JSON.stringify(arg) : arg
      ).join(' ');
      
      // Only add ForcedFCM logs to our UI
      if (message.includes('[ForcedFCM]')) {
        setLogs(prev => [...prev, message]);
      }
    };
    
    console.error = (...args) => {
      originalConsoleError(...args);
      const message = args.map(arg => 
        typeof arg === 'object' ? JSON.stringify(arg) : arg
      ).join(' ');
      
      // Only add ForcedFCM logs to our UI
      if (message.includes('[ForcedFCM]')) {
        setLogs(prev => [...prev, `ERROR: ${message}`]);
      }
    };
    
    // Restore original console methods on cleanup
    return () => {
      console.log = originalConsoleLog;
      console.error = originalConsoleError;
    };
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <Text style={styles.title}>Forced FCM Token Test</Text>
      
      <View style={styles.buttonRow}>
        <TouchableOpacity 
          style={[styles.button, styles.primaryButton]} 
          onPress={startForcedFCM}
          disabled={loading}
        >
          <Text style={styles.buttonText}>🚀 Get Notification Token</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, styles.secondaryButton]}
          onPress={sendTestNotification}
          disabled={loading}
        >
          <Text style={styles.buttonText}>📱 Test Notification</Text>
        </TouchableOpacity>
      </View>
      
      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0000ff" />
          <Text style={styles.loadingText}>
            Retrieving token...{'\n'}
            This may take up to 60 seconds
          </Text>
        </View>
      )}
      
      {token ? (
        <View style={[styles.tokenContainer, usingFallback && styles.fallbackContainer]}>
          <Text style={styles.tokenLabel}>
            {usingFallback ? '⚠️ Fallback ID (FCM unavailable):' : '✅ FCM Token:'}
          </Text>
          <ScrollView style={styles.tokenScrollView}>
            <Text style={styles.tokenText} selectable={true}>
              {token}
            </Text>
          </ScrollView>
          
          {usingFallback && (
            <Text style={styles.fallbackNote}>
              Using fallback mechanism. Your device cannot connect to FCM services.
              Local notifications will still work.
            </Text>
          )}
        </View>
      ) : (
        <Text style={styles.noToken}>No notification token yet</Text>
      )}
      
      <Text style={styles.logsTitle}>Logs:</Text>
      <ScrollView style={styles.logsContainer}>
        {logs.map((log, index) => (
          <Text key={index} style={styles.logText}>{log}</Text>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    marginVertical: 16,
    textAlign: 'center',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  button: {
    flex: 1,
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  primaryButton: {
    backgroundColor: '#4630EB',
    marginRight: 8,
  },
  secondaryButton: {
    backgroundColor: '#0E7AFE',
    marginLeft: 8,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    textAlign: 'center',
    color: '#555',
  },
  tokenContainer: {
    marginTop: 20,
    backgroundColor: '#e6f7ff',
    padding: 16,
    borderRadius: 8,
  },
  fallbackContainer: {
    backgroundColor: '#fff3cd',
  },
  tokenLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  tokenScrollView: {
    maxHeight: 80,
    backgroundColor: '#fff',
    padding: 8,
    borderRadius: 4,
  },
  tokenText: {
    fontFamily: 'monospace',
    fontSize: 12,
  },
  fallbackNote: {
    marginTop: 8,
    color: '#856404',
    fontSize: 12,
  },
  noToken: {
    marginTop: 20,
    textAlign: 'center',
    color: '#999',
    fontStyle: 'italic',
  },
  logsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 8,
  },
  logsContainer: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 8,
    borderRadius: 8,
  },
  logText: {
    fontFamily: 'monospace',
    fontSize: 11,
    marginBottom: 2,
  },
}); 