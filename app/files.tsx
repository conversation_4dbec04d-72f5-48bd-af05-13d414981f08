import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  SafeAreaView,
  Image,
  Modal,
  Alert,
  Platform,
  ActivityIndicator,
  StatusBar,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import * as ImagePicker from 'expo-image-picker';
import * as MediaLibrary from 'expo-media-library';
import * as FileSystem from 'expo-file-system';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { apiService } from '../services/ApiService';
import { API_BASE_URL, API_ENDPOINTS, UPLOADS_BASE_URL } from '../config/api';
import { showSnackbar } from './ui/utils';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Types and Interfaces
type RelatedType = 'contacts' | 'accounts' | 'deals';

interface FileDetails {
  id: number;
  file_name: string;
  display_name: string;
  file_size: string;
  share_with_team: string;
  owner: number;
  type: RelatedType;
  type_id: number;
  upload_at: string;
  created_at: string;
  updated_at: string;
}

interface FileResponse {
  id: number;
  file_size: string;
  file_url: string;
  uploaded_by: string;
  created_at: string;
  file_name: string;
}

interface FilesListResponse {
  status: boolean;
  message: string;
  response: FileResponse[];
}

interface FileUploadResponse {
  status: number;
  success: boolean;
  message: string;
  data: FileDetails;
}

interface FileItem {
  id: string;
  file_name: string;
  author: string;
  date: string;
  size: string;
  uri: string;
}

// Add loading state interface
interface LoadingState {
  isLoading: boolean;
  message: string;
}

const STATUS_BAR_CONFIG = {
  barStyle: "light-content" as const,
  backgroundColor: "#0F96BB",
  translucent: Platform.OS === 'android',
};

export default function FilesScreen() {
  const insets = useSafeAreaInsets();
  const [files, setFiles] = useState<FileItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploadLoading, setUploadLoading] = useState<LoadingState>({
    isLoading: false,
    message: ''
  });
  const [error, setError] = useState<string | null>(null);
  const [optionsVisible, setOptionsVisible] = useState(false);
  const [addOptionsVisible, setAddOptionsVisible] = useState(false);
  const [selectedFile, setSelectedFile] = useState<FileItem | null>(null);
  const [cameraPermission, setCameraPermission] = useState<boolean | null>(null);
  const [galleryPermission, setGalleryPermission] = useState<boolean | null>(null);

  const router = useRouter();
  const params = useLocalSearchParams();

  // Determine the related type and ID based on params
  const getRelatedInfo = (): { type: RelatedType; id: string } => {
    if (params.contactId) {
      return { type: 'contacts', id: params.contactId as string };
    } else if (params.accountId) {
      return { type: 'accounts', id: params.accountId as string };
    } else if (params.dealId) {
      return { type: 'deals', id: params.dealId as string };
    }
    throw new Error('No valid ID found in parameters');
  };

  const { type: relatedType, id: relatedId } = getRelatedInfo();

  // Fetch files from API
  const fetchFiles = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.get<FilesListResponse>(
        `${API_ENDPOINTS.GET_FILES}${relatedType}/${relatedId}`
      );

      if (response.data?.status) {
        const transformedFiles = response.data.response.map(file => ({
          id: file.id.toString(),
          file_name: file.file_name,
          author: file.uploaded_by,
          date: new Date(file.created_at).toLocaleString(),
          size: `${(parseInt(file.file_size) / (1024 * 1024)).toFixed(2)} MB`,
          uri: file.file_url || 'https://via.placeholder.com/100',
        }));
        setFiles(transformedFiles);
      } else {
        throw new Error(response.data?.message || 'Failed to fetch files');
      }
    } catch (error) {
      console.error('Error fetching files:', error);
      setError('Failed to fetch files. Please try again.');
      showSnackbar('Failed to fetch files');
    } finally {
      setLoading(false);
    }
  };


  const handleFileUpload = async (fileUri: string, fileName: string) => {
    try {
      setUploadLoading({
        isLoading: true,
        message: 'Preparing to upload...'
      });
      
      // Validate file URI
      if (!fileUri) {
        throw new Error('Invalid file URI');
      }

      setUploadLoading({
        isLoading: true,
        message: 'Processing file...'
      });

      console.log('Starting file upload process:', {
        fileUri,
        fileName,
        platform: Platform.OS
      });

      // Get file info with error handling
      let fileInfo;
      try {
        fileInfo = await FileSystem.getInfoAsync(fileUri);
        console.log('File info:', fileInfo);
      } catch (error) {
        console.error('Error getting file info:', error);
        throw new Error('Could not access file information');
      }

      if (!fileInfo.exists) {
        throw new Error(`File not found at path: ${fileUri}`);
      }

      setUploadLoading({
        isLoading: true,
        message: 'Uploading file...'
      });

      // Create form data with detailed error handling
      const formData = new FormData();
      
      try {
        // Prepare file object based on platform
        const fileObject = {
          uri: Platform.OS === 'android' ? fileUri : fileUri.replace('file://', ''),
          name: fileName || 'file.jpg',
          type: 'image/jpeg',
          size: fileInfo.size
        } as any;

        console.log('Preparing file object:', {
          uri: fileObject.uri,
          name: fileObject.name,
          type: fileObject.type,
          size: fileObject.size
        });

        // Append file to form data
        formData.append('file', fileObject);
        formData.append('related_to', relatedType);
        formData.append('related_id', relatedId);
        formData.append('share_with_team', 'true');

      } catch (formError) {
        console.error('Error creating form data:', formError);
        throw new Error('Failed to prepare file for upload');
      }

      // Get auth token
      const token = await AsyncStorage.getItem('auth_token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      // Make API request with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      try {
        console.log('Making API request to:', `${API_BASE_URL}${API_ENDPOINTS.UPLOAD_FILE}`);
        
        const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.UPLOAD_FILE}`, {
          method: 'POST',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'multipart/form-data',
            'Authorization': `Bearer ${token}`
          },
          body: formData,
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        const responseData = await response.json();
        console.log('Upload response:', {
          status: response.status,
          ok: response.ok,
          data: responseData
        });

        if (!response.ok) {
          throw new Error(responseData.message || `Upload failed with status ${response.status}`);
        }

        if (responseData.success) {
          setUploadLoading({
            isLoading: true,
            message: 'Upload successful! Refreshing...'
          });
          showSnackbar('File uploaded successfully');
          await fetchFiles(); // Refresh the list
        } else {
          throw new Error(responseData.message || 'Upload failed');
        }
      } catch (apiError: any) {
        console.error('API Error:', apiError);
        if (apiError.name === 'AbortError') {
          throw new Error('Upload timed out. Please try again.');
        }
        throw new Error(apiError.message || 'Failed to upload file');
      }
    } catch (error: any) {
      console.error('Error in handleFileUpload:', error);
      Alert.alert(
        'Upload Error',
        error.message || 'Failed to upload file. Please try again.'
      );
    } finally {
      setUploadLoading({
        isLoading: false,
        message: ''
      });
    }
  };

  const takePicture = async () => {
    setAddOptionsVisible(false);
    
    if (!cameraPermission) {
      Alert.alert(
        'Permission Required',
        'Camera permission is required to take pictures.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Settings', onPress: () => {
            ImagePicker.requestCameraPermissionsAsync();
          }}
        ]
      );
      return;
    }

    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        await handleFileUpload(asset.uri, asset.uri.split('/').pop() || 'camera_image.jpg');
      }
    } catch (error) {
      console.error('Error taking picture:', error);
      Alert.alert('Error', 'Failed to take picture. Please try again.');
    }
  };

  const pickAndUploadFile = async () => {
    setAddOptionsVisible(false);
    try {
      // Check and request permissions with better error handling
      if (Platform.OS === 'ios') {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert(
            'Permission Required',
            'Please allow access to your photo library to upload files.',
            [
              { text: 'Cancel', style: 'cancel' },
              { text: 'Settings', onPress: () => Linking.openSettings() }
            ]
          );
          return;
        }
      } else {
        const { status } = await MediaLibrary.requestPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert(
            'Permission Required',
            'Please allow access to your files to upload.',
            [
              { text: 'Cancel', style: 'cancel' },
              { text: 'Settings', onPress: () => Linking.openSettings() }
            ]
          );
          return;
        }
      }

      console.log('Launching image picker...');
      
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        quality: 1,
        allowsMultipleSelection: false
      });

      console.log('Image picker result:', result);

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        
        // Validate selected file
        if (!asset.uri) {
          throw new Error('No file selected');
        }

        // Get file name from URI
        const fileName = asset.uri.split('/').pop() || 'image.jpg';
        
        console.log('Selected file:', {
          uri: asset.uri,
          fileName,
          fileSize: asset.fileSize,
          type: asset.type
        });

        await handleFileUpload(asset.uri, fileName);
      }
    } catch (error: any) {
      console.error('Error in pickAndUploadFile:', error);
      Alert.alert(
        'File Selection Error',
        error.message || 'Failed to select or process file. Please try again.'
      );
    }
  };

  // Delete file
  const deleteFile = async (fileId: string) => {
    try {
      const response = await apiService.delete(`${API_ENDPOINTS.DELETE_FILE}/${fileId}`);
      
      if (response.status === 200 || response.status === 204) {
        showSnackbar('File deleted successfully');
        fetchFiles(); // Refresh the list
      } else {
        throw new Error(response.data?.message || 'Failed to delete file');
      }
    } catch (error) {
      console.error('Error deleting file:', error);
      showSnackbar('Failed to delete file');
    }
  };

  useEffect(() => {
    const setupPermissions = async () => {
      try {
        // Request camera permissions
        const cameraStatus = await ImagePicker.requestCameraPermissionsAsync();
        setCameraPermission(cameraStatus.status === 'granted');

        // Request media library permissions
        if (Platform.OS === 'ios') {
          const galleryStatus = await ImagePicker.requestMediaLibraryPermissionsAsync();
          setGalleryPermission(galleryStatus.status === 'granted');
        } else {
          const galleryStatus = await MediaLibrary.requestPermissionsAsync();
          setGalleryPermission(galleryStatus.status === 'granted');
        }
      } catch (error) {
        console.error('Error setting up permissions:', error);
      }
    };

    setupPermissions();
    fetchFiles();
  }, [relatedType, relatedId]);

  const handleBack = () => {
    router.back();
  };

  const handleAddNew = () => {
    setAddOptionsVisible(true);
  };

  const handleFileOptions = (file: FileItem) => {
    setSelectedFile(file);
    setOptionsVisible(true);
  };

  const handleDeleteFile = () => {
    if (selectedFile) {
      Alert.alert(
        'Delete File',
        'Are you sure you want to delete this file?',
        [
          { text: 'Cancel', style: 'cancel', onPress: () => setOptionsVisible(false) },
          { 
            text: 'Delete', 
            style: 'destructive', 
            onPress: async () => {
              setOptionsVisible(false);
              await deleteFile(selectedFile.id);
              setSelectedFile(null);
            } 
          },
        ]
      );
    }
  };

  const pickImage = async () => {
    try {
      setUploadLoading({
        isLoading: true,
        message: 'Opening gallery...'
      });
      
      setAddOptionsVisible(false);
      
      let hasPermission = false;
      
      if (Platform.OS === 'ios') {
        const galleryStatus = await ImagePicker.getMediaLibraryPermissionsAsync();
        hasPermission = galleryStatus.status === 'granted';
        
        if (!hasPermission) {
          const newPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();
          hasPermission = newPermission.status === 'granted';
        }
      } else {
        const galleryStatus = await MediaLibrary.getPermissionsAsync();
        hasPermission = galleryStatus.status === 'granted';
        
        if (!hasPermission) {
          const newPermission = await MediaLibrary.requestPermissionsAsync();
          hasPermission = newPermission.status === 'granted';
        }
      }
      
      if (!hasPermission) {
        setUploadLoading({
          isLoading: false,
          message: ''
        });
        Alert.alert(
          'Permission Required',
          'Gallery permission is required to select images.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Try Again', onPress: () => pickImage() }
          ]
        );
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        quality: 0.8,
        allowsMultipleSelection: false,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        await handleFileUpload(asset.uri, asset.uri.split('/').pop() || 'gallery_image.jpg');
      } else {
        setUploadLoading({
          isLoading: false,
          message: ''
        });
      }
    } catch (error) {
      console.error('Error picking image:', error);
      setUploadLoading({
        isLoading: false,
        message: ''
      });
      Alert.alert('Error', 'Failed to select image. Please try again.');
    }
  };

  const renderItem = ({ item, index }: { item: FileItem; index: number }) => (
    <TouchableOpacity 
      style={styles.fileItemContainer}
      onPress={() => {
        router.push({
          pathname: '/imageViewer' as any, 
          params: { fileName: item.file_name }
        });
      }}
    >
      <View style={styles.fileItemContent}>
        <Image 
          source={{ uri: UPLOADS_BASE_URL + item.file_name }} 
          style={styles.fileThumb} 
          resizeMode="cover"
        />
        <View style={styles.fileDetails}>
          <Text style={styles.fileName}>{item.file_name}</Text>
          <Text style={styles.fileAuthor}>By {item.author}</Text>
          <Text style={styles.fileDate}>{item.date}</Text>
          <Text style={styles.fileSize}>{item.size}</Text>
        </View>
        <TouchableOpacity 
          style={styles.deleteButton}
          onPress={(e) => {
            e.stopPropagation();
            Alert.alert(
              'Delete File',
              'Are you sure you want to delete this file?',
              [
                { text: 'Cancel', style: 'cancel' },
                { 
                  text: 'Delete', 
                  style: 'destructive', 
                  onPress: async () => {
                    await deleteFile(item.id);
                  } 
                },
              ]
            );
          }}
        >
          <Ionicons name="trash-outline" size={20} color="#FF3B30" />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  // Update the header title based on the related type
  const getHeaderTitle = () => {
    const count = files.length;
    switch (relatedType) {
      case 'contacts':
        return `Contact Files (${count})`;
      case 'accounts':
        return `Account Files (${count})`;
      case 'deals':
        return `Deal Files (${count})`;
      default:
        return `Files (${count})`;
    }
  };

  const renderAddOptions = () => (
    <View style={styles.addOptionsContainer}>
      <TouchableOpacity 
        style={styles.addOption}
        onPress={takePicture}
      >
        <Ionicons name="camera-outline" size={24} color="#333" />
        <Text style={styles.addOptionText}>Take Photo</Text>
      </TouchableOpacity>
      
      <View style={styles.optionDivider} />
      
      {/* <TouchableOpacity 
        style={styles.addOption}
        onPress={pickAndUploadFile}
      >
        <Ionicons name="document-outline" size={24} color="#333" />
        <Text style={styles.addOptionText}>Upload File</Text>
      </TouchableOpacity> */}
      
      <View style={styles.optionDivider} />
      
      <TouchableOpacity 
        style={styles.addOption}
        onPress={pickAndUploadFile}
      >
        <Ionicons name="image-outline" size={24} color="#333" />
        <Text style={styles.addOptionText}>Choose from Gallery</Text>
      </TouchableOpacity>
    </View>
  );

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={fetchFiles}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { paddingTop: insets.top }]}>
      <StatusBar {...STATUS_BAR_CONFIG} />
      <Stack.Screen options={{ headerShown: false }} />

      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Ionicons name={Platform.OS === 'ios' ? 'chevron-back' : 'arrow-back'}  size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitleText}>{getHeaderTitle()}</Text>
        </View>
        <View style={styles.headerRight} />
      </View>

      <View style={styles.mainContainer}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#0F96BB" />
            <Text style={styles.loadingText}>Loading files...</Text>
          </View>
        ) : (
          <>
            <FlatList
              data={files}
              keyExtractor={item => item.id}
              renderItem={renderItem}
              contentContainerStyle={[
                styles.listContainer,
                files.length === 0 && styles.emptyListContainer
              ]}
              ItemSeparatorComponent={() => <View style={styles.separator} />}
              ListEmptyComponent={() => (
                <View style={styles.emptyContainer}>
                  <View style={styles.emptyIconContainer}>
                    <Ionicons name="document-text-outline" size={80} color="#CCCCCC" />
                  </View>
                  <Text style={styles.emptyTitle}>No files found</Text>
                  <Text style={styles.emptySubtitle}>Add your first file by tapping the button below</Text>
                  <TouchableOpacity style={styles.addButton} onPress={handleAddNew}>
                    <Text style={styles.addButtonText}>Add new</Text>
                  </TouchableOpacity>
                </View>
              )}
            />

            {files.length > 0 && (
              <TouchableOpacity style={styles.fab} onPress={handleAddNew}>
                <Ionicons name="add" size={28} color="#FFFFFF" />
              </TouchableOpacity>
            )}
          </>
        )}
      </View>

      {/* Loading Overlay */}
      {uploadLoading.isLoading && (
        <View style={styles.loadingOverlay}>
          <View style={styles.uploadLoadingContainer}>
            <ActivityIndicator size="large" color="#FFFFFF" />
            <Text style={styles.uploadLoadingText}>{uploadLoading.message}</Text>
          </View>
        </View>
      )}

      {/* File Options Modal */}
      <Modal
        visible={optionsVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setOptionsVisible(false)}
      >
        <TouchableOpacity 
          style={styles.modalOverlay} 
          activeOpacity={1} 
          onPress={() => setOptionsVisible(false)}
        >
          <View style={styles.modalContainer}>
            <TouchableOpacity 
              style={styles.modalOption}
              onPress={handleDeleteFile}
            >
              <Text style={styles.deleteText}>Delete</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>

      {/* Add Options Modal */}
      <Modal
        visible={addOptionsVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setAddOptionsVisible(false)}
      >
        <TouchableOpacity 
          style={styles.modalOverlay} 
          activeOpacity={1} 
          onPress={() => setAddOptionsVisible(false)}
        >
          {renderAddOptions()}
        </TouchableOpacity>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0F96BB',
  },
  mainContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#0F96BB',
    height: Platform.OS === 'ios' ? 44 : 56,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
    zIndex: 1000,
    paddingHorizontal: Platform.OS === 'ios' ? 8 : 0,
  },
  backButton: {
    width: Platform.OS === 'ios' ? 44 : 56,
    height: Platform.OS === 'ios' ? 44 : 56,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: Platform.OS === 'ios' ? -8 : 0,
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: Platform.OS === 'ios' ? 4 : 16,
  },
  headerRight: {
    width: Platform.OS === 'ios' ? 44 : 56,
    height: Platform.OS === 'ios' ? 44 : 56,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Platform.OS === 'ios' ? -8 : 0,
  },
  headerTitleText: {
    color: '#FFFFFF',
    fontSize: Platform.OS === 'ios' ? 17 : 20,
    fontWeight: Platform.OS === 'ios' ? '600' : '500',
    textAlign: 'center',
  },
  listContainer: {
    padding: 16,
  },
  emptyListContainer: {
    flexGrow: 1,
  },
  fileItemContainer: {
    backgroundColor: 'white',
    borderRadius: 8,
    marginBottom: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  fileItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  fileThumb: {
    width: 60,
    height: 60,
    borderRadius: 5,
    backgroundColor: '#e1e1e1',
  },
  fileDetails: {
    flex: 1,
    marginLeft: 15,
  },
  fileName: {
    fontSize: 16,
    color: '#3068E1',
    fontWeight: '500',
    marginBottom: 3,
  },
  fileAuthor: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  fileDate: {
    fontSize: 14,
    color: '#999',
    marginBottom: 2,
  },
  fileSize: {
    fontSize: 14,
    color: '#999',
  },
  deleteButton: {
    padding: 10,
  },
  separator: {
    height: 1,
    backgroundColor: '#e1e1e1',
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  emptyIconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#F5F5F5',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
  },
  addButton: {
    backgroundColor: '#0F96BB',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#0F96BB',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    paddingVertical: 20,
  },
  modalOption: {
    paddingVertical: 15,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  deleteText: {
    fontSize: 18,
    color: 'red',
  },
  addOptionsContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    paddingVertical: 20,
  },
  addOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 20,
  },
  addOptionText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 15,
  },
  optionDivider: {
    height: 1,
    backgroundColor: '#e1e1e1',
    marginHorizontal: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    color: '#0F2F52',
    marginTop: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: 'red',
    marginBottom: 20,
  },
  retryButton: {
    padding: 10,
    backgroundColor: '#0F2F52',
    borderRadius: 5,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
  },
  uploadLoadingContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderRadius: 10,
    padding: 20,
    alignItems: 'center',
    maxWidth: '80%',
  },
  uploadLoadingText: {
    color: '#FFFFFF',
    marginTop: 10,
    fontSize: 16,
    textAlign: 'center',
  },
}); 