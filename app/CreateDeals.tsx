import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  Platform,
  KeyboardAvoidingView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { DealField, DealFieldsResponse } from './models/DealField';
import { ContactField } from './models/contact_fields_model';
import { useColors } from '@/hooks/useThemeColor';
import { useAuth } from '../context/AuthContext';
import { DynamicFormField } from './dynamic_fields/DynamicFormField';

// Update the DealFormData type to support both string and number values
type DealFormData = {
  [key: string]: string | number | null;
};

const CreateDeals = () => {
  const [fields, setFields] = useState<DealField[]>([]);
  const [formData, setFormData] = useState<DealFormData>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);

  const [showAllFields, setShowAllFields] = useState(false); // Toggle for showing quick_add "off" fields
  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false);

  const router = useRouter();
  const colors = useColors();
  const { user } = useAuth(); // Get the current logged-in user

  // Type adapter function to convert DealField to ContactField
  const adaptDealFieldToContactField = (dealField: DealField): ContactField => {
    return {
      id: dealField.id,
      custom_field: dealField.custom_field,
      field_label: dealField.field_label,
      field_name: dealField.field_name,
      field_type: dealField.field_type,
      required: dealField.required,
      quick_add: dealField.quick_add || '',
      tool_tip: dealField.tool_tip || '',
      placeholder: dealField.placeholder,
      read_only: dealField.read_only,
      has_dependent: dealField.has_dependent,
      contactgroups_id: dealField.dealgroups_id,
      active: dealField.active,
      order: dealField.order,
      lookup_type: dealField.lookup_type,
      lookup_column: dealField.lookup_column,
      created_at: dealField.created_at,
      updated_at: dealField.updated_at,
      field_value: dealField.field_value,
      choices: dealField.choices.map(choice => ({
        id: choice.id,
        contactfield_id: choice.dealfield_id,
        custom_option: choice.custom_option,
        created_at: choice.created_at || '',
        updated_at: choice.updated_at || '',
      })),
    };
  };



  // Fetch deal fields on component mount
  useEffect(() => {
    fetchDealFields();
  }, []);

  // Set default owner when form fields are loaded
  useEffect(() => {
    if (fields.length > 0 && user) {
      // Find the owner field
      const ownerField = fields.find(field =>
        field.field_name.toLowerCase().includes('owner') ||
        field.field_label.toLowerCase().includes('owner') ||
        field.lookup_type === 'users'
      );

      if (ownerField && ownerField.choices) {
        // Find the choice that matches the current user's ID
        const userChoice = ownerField.choices.find(choice => choice.id === user.id);

        if (userChoice) {
          // Set the owner to the current user
          setFormData(prevData => ({
            ...prevData,
            [`${ownerField.field_name}_id`]: user.id, // Store ID for API
            [ownerField.field_name]: userChoice.custom_option, // Store display text for UI
            [`${ownerField.field_name}_display`]: userChoice.custom_option // Additional display reference
          }));
          console.log('Set default opportunity owner to current user:', user.id, userChoice.custom_option);
        }
      }
    }
  }, [fields, user]);

  // Fetch deal fields from API
  const fetchDealFields = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('Fetching opportunity fields...');
      const response = await apiService.get<DealFieldsResponse>(API_ENDPOINTS.DEAL_FIELDS);
      
      if (!response.data || !response.data.dealFields) {
        throw new Error('Failed to fetch opportunity fields');
      }

      // Sort fields by order property
      const sortedFields = [...response.data.dealFields].sort((a, b) => {
        if (a.order === null) return 1;
        if (b.order === null) return -1;
        return a.order - b.order;
      });

      console.log(`Fetched ${sortedFields.length} opportunity fields`);
      setFields(sortedFields);
      
      // Initialize form data
      const initialFormData: DealFormData = {};
      sortedFields.forEach(field => {
        initialFormData[field.field_name] = field.field_value;
      });
      
      setFormData(initialFormData);
    } catch (err) {
      console.error('Error fetching opportunity fields:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Handle text input changes
  const handleInputChange = (fieldName: string, value: string) => {
    setFormData(prevData => ({
      ...prevData,
      [fieldName]: value
    }));
  };



  // Function to get quick_add "on" fields
  const getQuickAddFields = () => {
    return fields
      .filter(field => field.active === 1 && field.quick_add === "on")
      .sort((a, b) => (a.order || 0) - (b.order || 0));
  };

  // Function to get quick_add "off" fields
  const getQuickAddOffFields = () => {
    return fields
      .filter(field => field.active === 1 && field.quick_add === "off")
      .sort((a, b) => (a.order || 0) - (b.order || 0));
  };

  // Function to render the toggle for showing all fields
  const renderFieldToggle = () => {
    const quickAddOffFields = getQuickAddOffFields();

    if (quickAddOffFields.length === 0) {
      return null; // Don't show toggle if there are no quick_add "off" fields
    }

    return (
      <View style={styles.toggleContainer}>
        <Text style={styles.toggleLabel}>Show all fields</Text>
        <TouchableOpacity
          style={[styles.toggleButton, showAllFields && styles.toggleButtonActive]}
          onPress={() => setShowAllFields(!showAllFields)}
        >
          <View style={[styles.toggleSlider, showAllFields && styles.toggleSliderActive]} />
        </TouchableOpacity>
      </View>
    );
  };

  const validateForm = () => {
    const errors: string[] = [];

    // Get fields to validate based on current visibility
    const fieldsToValidate = showAllFields
      ? fields.filter(field => field.active === 1)
      : fields.filter(field => field.active === 1 && field.quick_add === "on");

    fieldsToValidate.forEach(field => {
      const value = formData[field.field_name];
      if (field.required === 'on' && (!value || (typeof value === 'string' && value.trim() === ''))) {
        errors.push(`${field.field_label} is required`);
      }
    });
    return errors;
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      // Set hasAttemptedSubmit to true to show validation errors
      setHasAttemptedSubmit(true);

      const errors = validateForm();
      if (errors.length > 0) {
        Alert.alert('Validation Error', errors[0]);
        return;
      }

      setSubmitting(true);

      // Prepare data for submission
      const deal = fields
        .filter(field => field.active === 1)
        .map(field => {
          let value = formData[field.field_name] || '';

          // For dropdown and lookup fields, we need to handle the value differently
          if ((field.field_type === 'Dropdown' || field.field_type === 'Lookup') && value) {
            const selectedChoice = field.choices?.find(c => c.id.toString() === value);
            return {
              ...field,
              field_value: selectedChoice ? selectedChoice.id.toString() : value, // Pass the ID for API
            };
          }

          return {
            ...field,
            field_value: value,
          };
        });

      const requestData = {
        deal: deal
      };

      console.log('Submitting opportunity data:', requestData);

      const response = await apiService.post(API_ENDPOINTS.CREATE_DEAL, requestData);

      // Log the entire response for debugging
      console.log('API Response:', JSON.stringify(response.data, null, 2));

      // Show response message from API
      const responseMessage = response.data?.message;

      if (response.data?.success === true && response.data?.data?.id) {
        // Show success message from API response
        Alert.alert(
          'Success',
          responseMessage,
          [{
            text: 'OK',
            onPress: () => {
              router.replace({
                pathname: '/dealDetails',
                params: { dealId: response.data.data.id.toString() }
              });
            }
          }]
        );
        return;
      }

      // If we get here, it means there was an error - show the API error message
      throw new Error(responseMessage);
      
    } catch (err) {
      console.error('Error creating opportunity:', err);

      // Extract error message from the nested structure
      let errorMessage = 'An error occurred';

      if (err && typeof err === 'object') {
        // Check for nested error structure: err.data.message
        if ((err as any).data?.message) {
          errorMessage = (err as any).data.message;
        }
        // Check for direct message property
        else if ((err as any).message) {
          errorMessage = (err as any).message;
        }
      }

      Alert.alert(
        'Error',
        errorMessage,
        [{ text: 'OK' }]
      );
    } finally {
      setSubmitting(false);
    }
  };





  if (loading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0F96BB" />
        <Text style={styles.loadingText}>Loading form fields...</Text>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchDealFields}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color={colors.text.inverse} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Create Opportunity</Text>
          <View style={{ width: 40 }} />
        </View>
        
        {/* Form Content */}
        <View style={styles.mainContainer}>
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.contentContainer}
            keyboardShouldPersistTaps="handled"
          >
            {Array.isArray(fields) && fields.length > 0 ? (
              fields.some(item => item.active === 1) ? (
                <>
                  {/* Render toggle for showing all fields */}
                  {renderFieldToggle()}

                  {/* Render quick_add "on" fields first */}
                  {getQuickAddFields().map((item) => (
                    <View key={item.id}>
                      <DynamicFormField
                        field={adaptDealFieldToContactField(item)}
                        value={String(formData[item.field_name] || '')}
                        onChangeText={(value) => handleInputChange(item.field_name, value)}
                        showRequired={hasAttemptedSubmit}
                        showAllFields={showAllFields}
                      />
                    </View>
                  ))}

                  {/* Render quick_add "off" fields if toggle is enabled */}
                  {showAllFields && getQuickAddOffFields().map((item) => (
                    <View key={item.id}>
                      <DynamicFormField
                        field={adaptDealFieldToContactField(item)}
                        value={String(formData[item.field_name] || '')}
                        onChangeText={(value) => handleInputChange(item.field_name, value)}
                        showRequired={hasAttemptedSubmit}
                        showAllFields={showAllFields}
                      />
                    </View>
                  ))}
                </>
              ) : (
                <View style={styles.centerContent}>
                  <Text style={styles.noFieldsText}>No active fields available</Text>
                  <Text style={styles.noFieldsSubText}>All fields are currently inactive</Text>
                </View>
              )
            ) : (
              <View style={styles.centerContent}>
                <Text style={styles.noFieldsText}>No form fields available</Text>
              </View>
            )}
          </ScrollView>
          
          {/* Fixed Create Opportunity Button */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.submitButton, submitting && styles.submittingButton]}
              onPress={handleSubmit}
              disabled={submitting}
            >
              {submitting ? (
                <ActivityIndicator color="#fff" size="small" />
              ) : (
                <Text style={styles.submitButtonText}>Create Opportunity</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
        

      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  mainContainer: {
    flex: 1,
    position: 'relative',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#555',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#dc3545',
    marginBottom: 20,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#0F96BB',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#0F96BB',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 80,
  },
  fieldContainer: {
    marginBottom: 20,
  },
  fieldLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    color: '#333',
  },
  requiredStar: {
    color: 'red',
  },
  textInput: {
    backgroundColor: '#fff',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#ddd',
    fontSize: 16,
  },
  dropdownField: {
    backgroundColor: '#fff',
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#ddd',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dropdownText: {
    fontSize: 16,
    color: '#333',
  },
  placeholderText: {
    color: '#999',
  },
  submitButton: {
    backgroundColor: '#0F96BB',
    paddingVertical: 15,
    borderRadius: 5,
    alignItems: 'center',
    width: '100%',
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#f5f5f5',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderTopWidth: 1,
    borderTopColor: '#ddd',
  },
  submittingButton: {
    backgroundColor: '#0F96BB',
    opacity: 0.7,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  dropdownContainer: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    maxHeight: '80%',
  },
  dropdownHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  dropdownTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  dropdownItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  dropdownOptionText: {
    fontSize: 16,
    color: '#333',
  },
  selectedOptionText: {
    color: '#0F96BB',
    fontWeight: '500',
  },
  separator: {
    height: 1,
    backgroundColor: '#eee',
  },
  pickerModalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: 'auto',
  },
  pickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#f8f9fa',
  },
  pickerHeaderButton: {
    padding: 8,
  },
  pickerHeaderButtonText: {
    fontSize: 16,
    color: '#0077B6',
  },
  doneButton: {
    fontWeight: '600',
  },
  pickerHeaderTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  picker: {
    height: 200,
    backgroundColor: '#fff',
  },
  errorBorder: {
    borderWidth: 1,
    borderColor: '#dc3545',
  },
  errorMessage: {
    color: '#dc3545',
    fontSize: 12,
    marginTop: 4,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#fff',
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
    color: '#333',
  },
  emptyContainer: {
    padding: 16,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  noFieldsText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 8,
  },
  noFieldsSubText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    opacity: 0.8,
  },
  toggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 16,
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  toggleLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  toggleButton: {
    width: 50,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#ddd',
    justifyContent: 'center',
    paddingHorizontal: 2,
  },
  toggleButtonActive: {
    backgroundColor: '#0F96BB',
  },
  toggleSlider: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  toggleSliderActive: {
    transform: [{ translateX: 22 }],
  },
});

export default CreateDeals; 