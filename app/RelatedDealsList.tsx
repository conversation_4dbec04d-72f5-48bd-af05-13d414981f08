import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  RefreshControl,
  StatusBar,
  SafeAreaView,
  ScrollView,
  Platform,
} from 'react-native';
import { Stack, useRouter, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useColors } from '@/hooks/useThemeColor';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { readUserScope, showSnackbar } from './ui/utils';

interface Deal {
  id: number;
  name: string;
  amount: string;
  probability: string | null;
  close_date?: string | null;
  stage: {
    id: string | null;
    name: string | null;
  };
  contact_name?: string | null;
  account_name?: string | null;
  created_at: string | null;
}

interface DealsSummary {
  total_deals: number;
  total_value: number;
  open_deals: string;
  open_value: number;
  won_deals: string;
  won_value: number;
}

const STATUS_BAR_CONFIG = {
  barStyle: "light-content" as const,
  backgroundColor: "#0F96BB",
  translucent: Platform.OS === 'android',
};

export default function RelatedDealsList() {
  const [deals, setDeals] = useState<Deal[]>([]);
  const [summary, setSummary] = useState<DealsSummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const router = useRouter();
  const params = useLocalSearchParams();
  const contactId = params.contactId as string | undefined;
  const accountId = params.accountId as string | undefined;
  const colors = useColors();
  const insets = useSafeAreaInsets();
  const [accessScope, setAccessScope] = useState<any>(null);

  useEffect(() => {
    const fetchAccessScope = async () => {
      const scope = await readUserScope();
      console.log("Access_Scope___meeting: " + JSON.stringify(scope));
      setAccessScope(scope);
    };
    fetchAccessScope();
  }, []);

  const fetchDeals = async (showLoader = true) => {
    if (!contactId && !accountId) {
      setError('Required Contact or Account ID is missing');
      setIsLoading(false);
      return;
    }
    if (showLoader) setIsLoading(true);
    setError(null);

    try {
      let endpoint = '';
      if (contactId) {
        endpoint = `${API_ENDPOINTS.CONTACTS}/${contactId}/relations`;
      } else if (accountId) {
        endpoint = `${API_ENDPOINTS.SALES_ACCOUNTS}/${accountId}/relations`;
      }

      const response = await apiService.get(endpoint);

      if (response?.data?.data?.deals?.deals) {
        setDeals(response.data.data.deals.deals);
        setSummary(response.data.data.deals.summary);
      } else {
        if (response?.data?.success) {
          setDeals([]);
          setSummary(response.data.data?.deals?.summary || null);
        } else {
          throw new Error(response?.data?.message || 'Invalid response format');
        }
      }
    } catch (err) {
      console.error('Error fetching deals:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to load deals';
      setError(errorMessage);
      showSnackbar(errorMessage);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    fetchDeals();
  }, [contactId, accountId]);

  // Refresh data when screen comes into focus (after returning from SelectDealForContact)
  useFocusEffect(
    useCallback(() => {
      console.log('RelatedDealsList screen focused, refreshing data...');
      fetchDeals(false); // Don't show loader when refreshing on focus
    }, [contactId, accountId])
  );

  const handleRefresh = () => {
    setIsRefreshing(true);
    fetchDeals(false);
  };

  const handleBack = () => {
    router.back();
  };

  const handleAddNew = () => {
    // Navigate to create deal page with the correct context parameter
    console.log('Navigate to create deal');
    console.log('Contact ID:', contactId);
    console.log('Account ID:', accountId);

    const navigationParams: any = {};

    // Pass the appropriate context based on what we have
    if (contactId) {
      navigationParams.contactId = contactId;
      navigationParams.source = 'contact';
      console.log('Navigating from contact context');
    } else if (accountId) {
      navigationParams.accountId = accountId;
      navigationParams.source = 'account';
      console.log('Navigating from account context');
    }

    console.log('Final navigation params:', navigationParams);

    router.push({
      pathname: '/CreateDeals' as any,
      params: navigationParams
    });
  };

  const handleAddExistingDeal = () => {
    // Navigate to select existing deal page
    const navigationParams: any = {
      callFrom: 'RelatedDealsList'
    };

    if (contactId) {
      navigationParams.contact_id = contactId;
    } else if (accountId) {
      navigationParams.account_id = accountId;
    }

    router.push({
      pathname: '/SelectDealForContact' as any,
      params: navigationParams
    });
  };

  const renderDealItem = ({ item }: { item: Deal }) => {
    const subtitle = item.contact_name ? item.contact_name : item.account_name;
    const subtitleText = subtitle ? subtitle : (contactId ? 'No account' : 'No contact');

    return (
      <TouchableOpacity
        style={styles.dealItem}
        onPress={() => {
          const href = {
            pathname: '/dealDetails',
            params: {
              dealId: item.id.toString()
            }
          };
          router.push(href as any);
        }}
      >
        <View style={styles.dealContent}>
          <Text style={styles.dealName}>{item.name}</Text>
          <Text style={styles.dealSubtitle}>{subtitleText}</Text>
          <View style={styles.dealInfo}>
            <Text style={styles.stageValue}>{item.stage.name || 'Not set'}</Text>
            <Text style={styles.dealAmount}>
              ₹{Number(item.amount).toLocaleString()}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const totalDealsCount = summary?.total_deals ?? deals.length;

  if (isLoading) {
    return (
      <SafeAreaView style={[styles.safeAreaContainer, { paddingTop: insets.top }]}>
        <StatusBar {...STATUS_BAR_CONFIG} />
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <View style={styles.headerContent}>
            <Text style={styles.headerTitleText}>Related Deals</Text>
          </View>
          <View style={styles.headerRight} />
        </View>
        <View style={styles.mainContainer}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#0F96BB" />
            <Text style={styles.loadingText}>Loading Opportunities...</Text>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.safeAreaContainer, { paddingTop: insets.top }]}>
      <StatusBar {...STATUS_BAR_CONFIG} />
      <Stack.Screen options={{ headerShown: false }} />

      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitleText}>Related Deals</Text>
        </View>
        {accessScope?.deal?.create === true && (
        <TouchableOpacity onPress={handleAddExistingDeal} style={styles.plusButton}>
          <Ionicons name="add" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        )}
      </View>

      <View style={styles.mainContainer}>
        {error ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
          </View>
        ) : deals.length === 0 ? (
          <View style={styles.emptyContainer}>
            <View style={styles.emptyIconContainer}>
              <Ionicons name="briefcase-outline" size={80} color="#C7C7CC" />
            </View>
            <Text style={styles.emptyTitle}>No deals found</Text>
            <Text style={styles.emptySubtitle}>
              {contactId
                ? 'Use the + button above to add deals to this contact'
                : 'Use the + button above to add deals to this account'
              }
            </Text>
          </View>
        ) : (
          <FlatList
            data={deals}
            renderItem={renderDealItem}
            keyExtractor={(item) => item.id.toString()}
            refreshControl={
              <RefreshControl
                refreshing={isRefreshing}
                onRefresh={handleRefresh}
                colors={[colors.primary]}
                tintColor={colors.primary}
              />
            }
            contentContainerStyle={styles.listContent}
            style={styles.container}
          />
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeAreaContainer: {
    flex: 1,
    backgroundColor: '#0F96BB',
  },
  mainContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#0F96BB',
    height: Platform.OS === 'ios' ? 44 : 56,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
    zIndex: 1000,
    paddingHorizontal: Platform.OS === 'ios' ? 8 : 0,
  },
  backButton: {
    padding: 8,
    marginLeft: 8,
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: Platform.OS === 'ios' ? 4 : 16,
  },
  headerRight: {
    width: Platform.OS === 'ios' ? 44 : 56,
    height: Platform.OS === 'ios' ? 44 : 56,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Platform.OS === 'ios' ? -8 : 0,
  },
  plusButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  headerTitleText: {
    color: '#FFFFFF',
    fontSize: Platform.OS === 'ios' ? 17 : 20,
    fontWeight: Platform.OS === 'ios' ? '600' : '500',
    textAlign: 'center',
  },
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  listContent: {
    flexGrow: 1,
    padding: 16,
  },
  summaryContainer: {
    backgroundColor: '#fff',
    padding: 16,
    margin: 16,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  summaryItem: {
    flex: 1,
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  dealItem: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  dealContent: {
    padding: 16,
  },
  dealName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#0F96BB',
    marginBottom: 4,
  },
  dealSubtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  dealInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 4,
    alignItems: 'center',
  },
  stageValue: {
    fontSize: 15,
    color: '#333',
    fontWeight: '500',
  },
  dealAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0F96BB',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    backgroundColor: 'white',
  },
  emptyIconContainer: {
    marginBottom: 24,
    opacity: 0.6,
  },
  emptyTitle: {
    fontSize: 22,
    fontWeight: '600',
    color: '#1C1C1E',
    textAlign: 'center',
    marginBottom: 12,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
    lineHeight: 22,
    maxWidth: 280,
    marginBottom: 32,
  },
  addExistingDealButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4CAF50',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 25,
    marginTop: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#4CAF50',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  addExistingDealText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
    marginLeft: 8,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  sectionContainer: {
    padding: 16,
  },
});