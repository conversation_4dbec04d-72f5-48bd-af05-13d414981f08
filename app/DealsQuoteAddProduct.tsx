import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Platform,
  TextInput,
  ScrollView,
  KeyboardAvoidingView,
  ViewStyle,
  TextStyle,
  Alert,
  Modal,
  Pressable,
  Animated,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { Appbar } from 'react-native-paper';

interface Product {
  id: string;  // This will be the catalog product ID
  quoteProductId?: string;  // This will store the quote product ID when editing
  name: string;
  price: string;
  quantity: string;
  currency?: string;
  pricing_type?: string;
  setup_fee?: string;
}

interface ProductCurrency {
  id: number;
  currency_name: string;
  base_currency_amount: string;
  setup_fee: string | null;
}

interface CategoryProduct {
  id: string;
  product_name: string;
  owner_name: string | null;
  base_currency_amount: string | null;
  currency: string | null;
  pricing_type: string | null;
  created_at: string | null;
  product_image: string;
  productcurrencies: ProductCurrency[];
}

interface Category {
  id: number;
  category: string;
  category_name: string;
  product_count: number;
  products: CategoryProduct[];
}

interface ApiResponse {
  status: boolean;
  message: string;
  response: Category[];
}

interface ProductWithCurrencies extends Product {
  availableCurrencies?: ProductCurrency[];
}

// Add new interface for API request
interface ProductRequest {
  id: number | null;
  product_id: string;
  unitprice: number;
  quantity: number;
  discount_type: 'percentage' | 'flat';
  discount: number;
  setup_fee?: number;
  billing_cycle?: string;
  no_of_billing_cycle?: number;
}

// Interface for items expected in the quotationproducts array from the API
interface QuoteProductDetail { 
    id: number;
    product_id: number;
    product_name: string;
    product_code?: string;
    unitprice: string;
    quantity: string | number;
    discount?: string | number;
    discount_type?: 'percentage' | 'flat';
    totalprice?: number;
    setupfee?: string;
    billingcycle?: string;
    no_of_billing_cycle?: string | number;
    pricing_type?: string;
}

export default function DealsQuoteAddProduct() {
  const [products, setProducts] = useState<Product[]>([]);
  const [showProductSearch, setShowProductSearch] = useState(false);
  const [showCategoryPicker, setShowCategoryPicker] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('All categories');
  const [searchQuery, setSearchQuery] = useState('');
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCurrencies, setSelectedCurrencies] = useState<{[key: string]: ProductCurrency}>({});
  const [productDiscounts, setProductDiscounts] = useState<{[key: string]: string}>({});
  const [expandedProducts, setExpandedProducts] = useState<{[key: string]: boolean}>({});
  const [discountTypes, setDiscountTypes] = useState<{[key: string]: 'percentage' | 'flat'}>({});
  const [showDiscountTypePicker, setShowDiscountTypePicker] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [showSummary, setShowSummary] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editProductId, setEditProductId] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [discountSelectorPosition, setDiscountSelectorPosition] = useState({ x: 0, y: 0 });
  const [summary, setSummary] = useState<{ subtotal: string; total: string } | null>(null);
  
  const router = useRouter();
  const params = useLocalSearchParams();
  const quoteId = params.quoteId as string;
  const editMode = params.editMode === 'true';
  const productId = params.productId as string;
  
  // Load categories first
  useEffect(() => {
    const loadInitialData = async () => {
      await fetchCategories();
      setIsInitialized(true);
    };
    
    loadInitialData();
  }, []);
  
  // Handle quote products fetching after categories are loaded
  useEffect(() => {
    if (!isInitialized) return;
    
    if (quoteId) {
      fetchQuoteProducts();
    }
  }, [isInitialized, quoteId]);
  
  // Handle edit mode after quote products are loaded
  useEffect(() => {
    if (!isInitialized) return;
    
    if (editMode && productId) {
      setIsEditMode(true);
      setEditProductId(productId);
      setShowProductSearch(false);
    }
  }, [isInitialized, editMode, productId]);
  
  const fetchQuoteProducts = async () => {
    if (!quoteId) return;
    
    try {
      setIsLoading(true);
      
      const response = await apiService.get(`${API_ENDPOINTS.QUOTATIONS}/${quoteId}/products`);
      
      if (response.data.status) {
        const quoteProducts = response.data.response.products || [];
        const quoteSummary = response.data.response.summary || null;
        
        if (quoteSummary) {
          setSummary(quoteSummary);
        }
        
        if (quoteProducts.length > 0) {
          // Convert API products to our component's product format
          const formattedProducts = quoteProducts.map((product: QuoteProductDetail) => {
            const formattedProduct: Product = {
              id: product.product_id.toString(),
              quoteProductId: product.id.toString(),
              name: product.product_name,
              price: product.unitprice,
              quantity: product.quantity.toString(),
              currency: '₹', // Using Rupee symbol
              pricing_type: product.pricing_type || (product.billingcycle ? 'Subscription Pricing' : 'One-time pricing'),
              setup_fee: product.setupfee,
            };
            
            // Set discount information
            setProductDiscounts(prev => ({
              ...prev,
              [formattedProduct.id]: product.discount?.toString() || '0'
            }));
            
            // Set discount type
            setDiscountTypes(prev => ({
              ...prev,
              [formattedProduct.id]: product.discount_type || 'percentage'
            }));
            
            // Expand the product card if in edit mode and this is the product being edited
            if (editMode && product.id.toString() === productId) {
              setExpandedProducts(prev => ({
                ...prev,
                [formattedProduct.id]: true
              }));
            }
            
            return formattedProduct;
          });
          
          setProducts(formattedProducts);
        }
      } else {
        setError(response.data.message || 'Failed to load quote products');
        console.error('API error:', response.data.message);
      }
    } catch (error) {
      console.error('Error fetching quote products:', error);
      setError('Failed to load quote products');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await apiService.get<ApiResponse>(API_ENDPOINTS.PRODUCT_CATEGORIES);
      if (response.data.status) {
        setCategories(response.data.response);
        if (response.data.response.length > 0) {
          setSelectedCategory('All categories');
        }
      } else {
        setError(response.data.message);
      }
    } catch (err) {
      setError('Failed to fetch categories. Please try again.');
      console.error('Error fetching categories:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    if (showProductSearch) {
      setShowProductSearch(false);
      router.back();
    } else {
      router.back();
    }
  };

  const toggleSummary = () => {
    setShowSummary(!showSummary);
  };

  const calculateQuoteValue = (products: Product[], discounts: {[key: string]: string}, discountTypes: {[key: string]: 'percentage' | 'flat'}) => { // Renamed function
    const total = products.reduce((total, product) => {
      const price = parseFloat(product.price) || 0;
      const quantity = parseInt(product.quantity) || 0;
      const discount = parseFloat(discounts[product.id]) || 0;
      const discountType = discountTypes[product.id] || 'percentage';

      let itemTotal = price * quantity;

      if (discountType === 'percentage') {
        itemTotal = itemTotal * (1 - discount / 100);
      } else {
        itemTotal = itemTotal - discount;
      }

      if (product.setup_fee) {
        itemTotal += parseFloat(product.setup_fee);
      }

      console.log(`Product ${product.name}: price=${price}, qty=${quantity}, discount=${discount}(${discountType}), setup_fee=${product.setup_fee}, itemTotal=${itemTotal}`);

      return total + itemTotal;
    }, 0);

    console.log('Total quote value calculated:', total);
    return total;
  };
  
  const handleSave = async () => {
    // Add check for empty products
    if (products.length === 0) {
      Alert.alert('Error', 'Please add at least one product');
      return;
    }

    setIsSaving(true);
    setError(null);
  
    // Map products to the required API format
    const productsToSave = products.map(product => {
      const discount = parseFloat(productDiscounts[product.id] || '0') || 0;
      const discountType = discountTypes[product.id] || 'percentage';
      const setupFee = product.setup_fee ? parseFloat(product.setup_fee) : undefined;
      
      // Determine billing_cycle and no_of_billing_cycle based on pricing_type
      let billingcycle: string | undefined;
      let no_of_billing_cycle: number | undefined;
      
      if (product.pricing_type === 'Subscription Pricing') {
        billingcycle = 'monthly'; // Default to monthly for subscription
        no_of_billing_cycle = 12; // Default to 12 months
      }
      
      return {
        id: product.quoteProductId ? product.quoteProductId : null,
        product_id: product.id,
        unitprice: parseFloat(product.price) || 0,
        quantity: parseInt(product.quantity) || 1,
        discount_type: discountType,
        discount: discount,
        ...(setupFee !== undefined && { setupfee: setupFee }),
        ...(billingcycle && { billingcycle }),
        ...(no_of_billing_cycle && { no_of_billing_cycle })
      };
    });

    try {
      const response = await apiService.post(API_ENDPOINTS.QUOTATION_PRODUCTS_MANAGE, {
        quoteid: quoteId,
        products: productsToSave
      });

      if (response.data.status) {
        const successMessage = isEditMode ? 'Product updated successfully' : 'Products saved successfully';
        Alert.alert('Success', successMessage, [
          { text: 'OK', onPress: () => router.back() }
        ]);
      } else {
        const errorMessage = response.data.message || `Failed to ${isEditMode ? 'update' : 'save'} products.`;
        setError(errorMessage);
        Alert.alert('Error', errorMessage);
      }
    } catch (error) {
      console.error(`Error ${isEditMode ? 'updating' : 'saving'} products:`, error);
      const failureMessage = `An error occurred while ${isEditMode ? 'updating' : 'saving'} products.`;
      setError(failureMessage);
      Alert.alert('Error', failureMessage);
    } finally {
      setIsSaving(false);
    }
  };
  
  const handleAddProduct = () => {
    // Add check for edit mode
    if (!isEditMode) {
      // Clear search query and reset category when opening the modal
      setSearchQuery('');
      setSelectedCategory('All categories');
      setShowCategoryPicker(false);
      setShowProductSearch(true);
    }
  };
  
  const handleUpdateProduct = (id: string, field: keyof Product, value: string) => {
    setProducts(prevProducts =>
      prevProducts.map((p: ProductWithCurrencies) => { 
        if (p.id === id) {
          const updatedProduct = { ...p, [field]: value };

          // If updating price, reset the currency and setup fee if needed based on availableCurrencies
          if (field === 'price' && updatedProduct.availableCurrencies) {
            const selectedCurrency = updatedProduct.availableCurrencies.find((c: ProductCurrency) => c.base_currency_amount === value);
            updatedProduct.currency = selectedCurrency ? selectedCurrency.currency_name : p.currency;
            updatedProduct.setup_fee = selectedCurrency ? selectedCurrency.setup_fee || undefined : p.setup_fee;
          }
          // If updating setup_fee explicitly, just update it
          // (The above block handles price changes affecting setup_fee via currency selection)
          
          return updatedProduct;
        }
        return p;
      })
    );

    // Replicate selectedCurrencies update logic from DealsAddProduct
    const product: ProductWithCurrencies | undefined = products.find(p => p.id === id);
    if (product && product.availableCurrencies) {
      if (field === 'price') {
        const matchingCurrency = product.availableCurrencies.find((c: ProductCurrency) => c.base_currency_amount === value);
        if (matchingCurrency) {
            setSelectedCurrencies(prev => ({ ...prev, [id]: matchingCurrency }));
        } else {
            // Handle cases where the entered price doesn't match an available currency option?
            // Maybe keep the existing currency or clear it?
            // For now, keep existing if no match
        }
      } else if (field === 'setup_fee' && selectedCurrencies[id]) {
         // If setup_fee is edited directly, update it in selectedCurrencies state too
         setSelectedCurrencies(prev => ({
           ...prev,
           [id]: {
             ...prev[id],
             setup_fee: value
           }
         }));
      }
    }

    // Ensure the updated product is expanded
    setExpandedProducts(prevExpanded => ({ ...prevExpanded, [id]: true }));
  };
  
  const handleRemoveProduct = (id: string) => {
    // Add confirmation alert
    Alert.alert(
      'Remove Product',
      'Are you sure you want to remove this product?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Remove', 
          style: 'destructive',
          onPress: async () => {
            const product = products.find(p => p.id === id);
            
            // If product has a quoteProductId, it exists in the database and needs to be deleted via API
            if (product?.quoteProductId && quoteId) {
              try {
                setIsLoading(true);
                const response = await apiService.delete(
                  API_ENDPOINTS.QUOTATION_PRODUCT_DELETE,
                  undefined,  // No params needed for path/query
                  { 
                    data: {  // Correctly passing data in the config object
                      quoteproductid: parseInt(product.quoteProductId),
                      quoteid: parseInt(quoteId)
                    } 
                  }
                );
                
                if (response.data.status) {
                  // Remove product from state after successful API call
                  setProducts(prevProducts => prevProducts.filter(p => p.id !== id));
                  
                  // Remove associated state for the removed product
                  setSelectedCurrencies(prev => {
                    const newSelected = { ...prev };
                    delete newSelected[id];
                    return newSelected;
                  });
                  setProductDiscounts(prev => {
                    const newDiscounts = { ...prev };
                    delete newDiscounts[id];
                    return newDiscounts;
                  });
                  setDiscountTypes(prev => {
                    const newTypes = { ...prev };
                    delete newTypes[id];
                    return newTypes;
                  });
                  setExpandedProducts(prev => {
                    const newExpanded = { ...prev };
                    delete newExpanded[id];
                    return newExpanded;
                  });
                } else {
                  Alert.alert('Error', response.data.message || 'Failed to remove product');
                }
              } catch (error) {
                console.error('Error removing product:', error);
                Alert.alert('Error', 'Failed to remove product. Please try again.');
              } finally {
                setIsLoading(false);
              }
            } else {
              // For new products not yet saved to the database, just remove from state
              setProducts(prevProducts => prevProducts.filter(p => p.id !== id));
              
              // Remove associated state for the removed product
              setSelectedCurrencies(prev => {
                const newSelected = { ...prev };
                delete newSelected[id];
                return newSelected;
              });
              setProductDiscounts(prev => {
                const newDiscounts = { ...prev };
                delete newDiscounts[id];
                return newDiscounts;
              });
              setDiscountTypes(prev => {
                const newTypes = { ...prev };
                delete newTypes[id];
                return newTypes;
              });
              setExpandedProducts(prev => {
                const newExpanded = { ...prev };
                delete newExpanded[id];
                return newExpanded;
              });
            }
          }
        }
      ]
    );
  };

  const handleSelectCategory = (category: string) => {
    setSelectedCategory(category);
    setShowCategoryPicker(false);
    // Clear search query when changing categories
    setSearchQuery('');
  };

  const getAllProducts = () => {
    return categories.flatMap(category => category.products);
  };

  const getFilteredProducts = () => {
    let filtered = getAllProducts();
    if (selectedCategory !== 'All categories') {
      const category = categories.find(c => c.category_name === selectedCategory);
      filtered = category ? category.products : [];
    }
    if (searchQuery) {
      filtered = filtered.filter(product =>
        product.product_name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    return filtered;
  };

  const getProductPrice = (product: CategoryProduct): string => {
    // Match DealsAddProduct logic
    if (product.productcurrencies && product.productcurrencies.length > 0) {
      return product.productcurrencies[0].base_currency_amount;
    }
    return product.base_currency_amount || '0';
  };

  const getProductCurrency = (product: CategoryProduct): string => {
    // Match DealsAddProduct logic
    if (product.productcurrencies && product.productcurrencies.length > 0) {
      return product.productcurrencies[0].currency_name;
    }
    return product.currency || 'INR'; // Keep default INR if nothing else found
  };

  const getProductSetupFee = (product: CategoryProduct): string | undefined => {
    // Match DealsAddProduct logic
    if (product.productcurrencies && product.productcurrencies.length > 0) {
      return product.productcurrencies[0].setup_fee || undefined;
    }
    return undefined; // Return undefined if not in currencies
  };

  const toggleProductExpansion = (productId: string) => {
    setExpandedProducts(prevExpanded => ({
      ...prevExpanded,
      [productId]: !prevExpanded[productId]
    }));
  };

  const handleCloseProductSearch = () => {
    // Clear search state when closing the modal
    setSearchQuery('');
    setSelectedCategory('All categories');
    setShowCategoryPicker(false);
    setShowProductSearch(false);
  };

  const handleProductSelect = (product: CategoryProduct) => {
    const existingProduct = products.find(p => p.id === product.id);
    if (existingProduct) {
      handleUpdateProduct(product.id, 'quantity', (parseInt(existingProduct.quantity) + 1).toString());
    } else {
      const newProduct: ProductWithCurrencies = {
        id: product.id,
        name: product.product_name,
        price: getProductPrice(product),
        quantity: '1',
        currency: getProductCurrency(product),
        pricing_type: product.pricing_type || 'One-time pricing', // Set pricing type from catalog
        setup_fee: getProductSetupFee(product),
        availableCurrencies: product.productcurrencies // Store available currencies
      };
      setProducts(prevProducts => [...prevProducts, newProduct]);
      // Initialize default values for new product state
      setProductDiscounts(prev => ({ ...prev, [product.id]: '0' }));
      setDiscountTypes(prev => ({ ...prev, [product.id]: 'percentage' }));
      setExpandedProducts(prev => ({ ...prev, [product.id]: true })); // Expand new product by default
      // Ensure selectedCurrencies is updated - Match DealsAddProduct
      if (product.productcurrencies && product.productcurrencies.length > 0) {
        setSelectedCurrencies(prev => ({ ...prev, [product.id]: product.productcurrencies[0] }));
      }
    }
    handleCloseProductSearch(); // Use the new close function that clears search state
  };
  
  const handleDiscountTypeSelect = (productId: string, type: 'percentage' | 'flat') => {
    setDiscountTypes(prevTypes => ({ ...prevTypes, [productId]: type }));
    setShowDiscountTypePicker(null);
  };
  
  // Helper function to render product card
  const renderProduct = (product: ProductWithCurrencies) => {
    const isExpanded = !!expandedProducts[product.id];
    const discountType = discountTypes[product.id] || 'percentage';
    const discountValue = productDiscounts[product.id] || '0';
    // Use selectedCurrencies state like in DealsAddProduct
    const selectedCurrencyInfo = selectedCurrencies[product.id];
    const currencySymbol = '₹'; // Changed from using currency_name to fixed ₹ symbol
    const isSubscription = product.pricing_type === 'Subscription Pricing';
    
    // Calculate display price based on quantity
    const displayPrice = (parseFloat(product.price || '0') * parseInt(product.quantity || '1')).toFixed(2);

    return (
      <View key={product.id} style={styles.productCard}>
        <TouchableOpacity onPress={() => toggleProductExpansion(product.id)} style={styles.productHeader}>
            <Text style={styles.productName}>{product.name}</Text>
            <View style={styles.productHeaderRight}>
              {/* <Text style={styles.productPrice}> {`${currencySymbol}${displayPrice}`} </Text> Removed price from header for now */}
              <TouchableOpacity onPress={() => handleRemoveProduct(product.id)} style={styles.removeButton}>
                <Ionicons name="trash-outline" size={20} color="#dc3545" />
              </TouchableOpacity>
              <Ionicons name={isExpanded ? "chevron-up" : "chevron-down"} size={20} color="#666" />
            </View>
        </TouchableOpacity>
        {isExpanded ? (
          <View style={styles.productExpandedContent}>
             <View style={styles.inputGroup}>
              {/* Use currencySymbol from state */}
              <Text style={styles.label}>Unit Price ({currencySymbol})</Text>
              <TextInput
                style={styles.input}
                value={product.price}
                onChangeText={(value) => handleUpdateProduct(product.id, 'price', value)}
                keyboardType="numeric"
                placeholder="0.00"
              />
            </View>
            {/* Use isSubscription flag */}
            {isSubscription && (
              <View style={styles.inputGroup}>
                <Text style={styles.label}>Setup Fee ({currencySymbol})</Text>
                <TextInput
                  style={styles.input}
                  value={product.setup_fee || '0'} // Default to '0' if undefined
                  onChangeText={(value) => handleUpdateProduct(product.id, 'setup_fee', value)}
                  keyboardType="numeric"
                  placeholder="0.00"
                />
              </View>
            )}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Quantity</Text>
              <TextInput
                style={styles.input}
                value={product.quantity}
                onChangeText={(value) => handleUpdateProduct(product.id, 'quantity', value)}
                keyboardType="numeric"
                placeholder="1"
              />
            </View>
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Discount</Text>
              <View style={styles.discountContainer}>
                <View style={styles.discountTypeSelectorContainer}>
                  <TouchableOpacity
                    style={styles.discountTypeSelector}
                    onPress={() => setShowDiscountTypePicker(product.id)}
                  >
                    <Text style={styles.discountTypeText}>
                      {discountType === 'percentage' ? 'Percentage (%)' : `Flat value (${currencySymbol})`}
                    </Text>
                    <Ionicons name="chevron-down" size={20} color="#666" />
                  </TouchableOpacity>
                  {showDiscountTypePicker === product.id && (
                    <View style={styles.discountTypePopup}>
                      <TouchableOpacity
                        style={[
                          styles.discountTypeOption,
                          discountType === 'flat' && styles.selectedDiscountType
                        ]}
                        onPress={() => handleDiscountTypeSelect(product.id, 'flat')}
                      >
                        <Text style={[
                          styles.discountTypeOptionText,
                          discountType === 'flat' && styles.selectedDiscountTypeText
                        ]}>
                          Flat value ({currencySymbol})
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={[
                          styles.discountTypeOption,
                          discountType === 'percentage' && styles.selectedDiscountType
                        ]}
                        onPress={() => handleDiscountTypeSelect(product.id, 'percentage')}
                      >
                        <Text style={[
                          styles.discountTypeOptionText,
                          discountType === 'percentage' && styles.selectedDiscountTypeText
                        ]}>
                          Percentage (%)
                        </Text>
                      </TouchableOpacity>
                    </View>
                  )}
                </View>
                <TextInput
                  style={[styles.input, styles.discountInput]}
                  value={discountValue}
                  onChangeText={(value) => {
                    setProductDiscounts(prev => ({ ...prev, [product.id]: value }));
                    console.log('Discount updated for product', product.id, ':', value);
                  }}
                  keyboardType="numeric"
                  placeholder="0"
                />
              </View>
            </View>
          </View>
        ) : (
          // Add non-expanded summary view - Match DealsAddProduct
          <View style={styles.productSummary}>
            <View style={styles.summaryRow}>
              <View style={styles.summaryItem}>
                <Text style={styles.summaryLabel}>Unit price :</Text>
                <Text style={styles.summaryValue}> {currencySymbol} {product.price}</Text>
              </View>
            </View>
            {isSubscription && (
              <View style={styles.summaryRow}>
                <View style={styles.summaryItem}>
                  <Text style={styles.summaryLabel}>Setup fee :</Text>
                  <Text style={styles.summaryValue}> {currencySymbol} {product.setup_fee || '0'}</Text>
                </View>
              </View>
            )}
            <View style={styles.summaryRow}>
              <View style={styles.summaryItem}>
                <Text style={styles.summaryLabel}>Quantity :</Text>
                <Text style={styles.summaryValue}> {product.quantity}</Text>
              </View>
              <View style={[styles.summaryItem, { marginLeft: 16 }]}>
                <Text style={styles.summaryLabel}>Discount :</Text>
                <Text style={styles.summaryValue}> {discountValue} {discountType === 'percentage' ? '%' : currencySymbol}</Text>
              </View>
            </View>
          </View>
        )}
      </View>
    );
  };

  // Calculate total quote value - always use calculated value for real-time updates
  const totalQuoteValue = calculateQuoteValue(products, productDiscounts, discountTypes);

  if (isLoading && !isInitialized) {
    return (
      <SafeAreaView style={styles.safeAreaContainer}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0F96BB" />
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.safeAreaContainer}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={isEditMode ? fetchQuoteProducts : fetchCategories}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar 
        barStyle="light-content" 
        backgroundColor="#0F96BB"
      />
      <Stack.Screen options={{ headerShown: false }} />
      
      <Appbar.Header style={styles.header}>
        <Appbar.BackAction onPress={() => router.back()} color="#FFFFFF" />
        <Appbar.Content
          title={isEditMode ? 'EDIT PRODUCT' : `PRODUCTS (${products.length})`}
          titleStyle={styles.headerTitleText}
        />
        <Appbar.Action
          icon="check"
          color="#FFFFFF"
          onPress={handleSave}
          disabled={isSaving}
        />
      </Appbar.Header>

      <View style={styles.content}>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#0F96BB" />
            <Text style={styles.loadingText}>Loading product data...</Text>
          </View>
        ) : (
          <KeyboardAvoidingView
            style={{ flex: 1 }}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
          >
            <ScrollView
              style={styles.scrollView}
              contentContainerStyle={styles.scrollContentContainer}
              keyboardShouldPersistTaps="handled"
            >
              <View style={styles.scrollInnerContent}>
                {products.map(renderProduct)}

                {!isEditMode && (
                  <TouchableOpacity
                    style={styles.addProductButton}
                    onPress={handleAddProduct}
                  >
                    <Text style={styles.addProductLinkText}>Add product</Text>
                  </TouchableOpacity>
                )}
              </View>
            </ScrollView>

            <View style={styles.totalValueContainer}>
              {showSummary && (
                <View style={styles.summaryContent}>
                  <View style={styles.summaryRow}>
                    <Text style={styles.summaryLabel}>Sub total</Text>
                    <Text style={styles.summaryValue}>₹ {totalQuoteValue.toFixed(2)}</Text>
                  </View>
                </View>
              )}
              
              <TouchableOpacity
                style={styles.totalValueContent}
                onPress={toggleSummary}
                activeOpacity={0.7}
              >
                <Text style={styles.totalValueLabel}>Total Quote value</Text>
                <View style={styles.totalValueAmountContainer}>
                  <Text style={styles.totalValueAmount}>
                    ₹ {totalQuoteValue.toFixed(2)}
                  </Text>
                  <Ionicons 
                    name={showSummary ? "chevron-down" : "chevron-up"} 
                    size={24} 
                    color="#333" 
                  />
                </View>
              </TouchableOpacity>
            </View>
          </KeyboardAvoidingView>
        )}

        <Modal
          visible={showProductSearch && !isEditMode}
          animationType="slide"
          transparent={true}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalView}>
              <SafeAreaView style={styles.modalContainer}>
                <View style={styles.modalHeader}>
                  <TouchableOpacity
                    onPress={handleCloseProductSearch}
                    hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
                  >
                    <Text style={styles.modalCancelButton}>Cancel</Text>
                  </TouchableOpacity>
                  <Text style={styles.modalTitle}>Add Product</Text>
                  <View style={styles.modalHeaderRight} />
                </View>

                <View style={styles.categoryWrapper}>
                  <TouchableOpacity 
                    style={styles.categorySelector}
                    onPress={() => setShowCategoryPicker(!showCategoryPicker)}
                  >
                    <Text style={styles.categoryText}>{selectedCategory}</Text>
                    <Ionicons 
                      name={showCategoryPicker ? "chevron-up" : "chevron-down"} 
                      size={24} 
                      color="#333" 
                    />
                  </TouchableOpacity>

                  {showCategoryPicker && (
                    <View style={styles.categoryPickerContainer}>
                      <TouchableOpacity
                        style={[
                          styles.categoryItem,
                          selectedCategory === 'All categories' && styles.selectedCategoryItem
                        ]}
                        onPress={() => handleSelectCategory('All categories')}
                      >
                        <Text style={[
                          styles.categoryItemText,
                          selectedCategory === 'All categories' && styles.selectedCategoryItemText
                        ]}>
                          All categories
                        </Text>
                      </TouchableOpacity>
                      {categories.map((category) => (
                        <TouchableOpacity
                          key={category.id}
                          style={[
                            styles.categoryItem,
                            selectedCategory === category.category_name && styles.selectedCategoryItem
                          ]}
                          onPress={() => handleSelectCategory(category.category_name)}
                        >
                          <Text style={[
                            styles.categoryItemText,
                            selectedCategory === category.category_name && styles.selectedCategoryItemText
                          ]}>
                            {category.category_name}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  )}
                </View>

                <View style={styles.searchContainer}>
                  <Ionicons name="search" size={20} color="#999" style={styles.searchIcon} />
                  <TextInput
                    style={styles.searchInput}
                    placeholder="Search for product to add"
                    placeholderTextColor="#999"
                    value={searchQuery}
                    onChangeText={setSearchQuery}
                  />
                </View>

                <ScrollView 
                  style={styles.productList}
                  keyboardShouldPersistTaps="handled"
                >
                  {isLoading ? (
                    <View style={styles.loadingContainer}>
                      <ActivityIndicator size="large" color="#0F96BB" />
                    </View>
                  ) : error ? (
                    <View style={styles.errorContainer}>
                      <Text style={styles.errorText}>{error}</Text>
                      <TouchableOpacity 
                        style={styles.retryButton}
                        onPress={fetchCategories}
                      >
                        <Text style={styles.retryButtonText}>Retry</Text>
                      </TouchableOpacity>
                    </View>
                  ) : (
                    getFilteredProducts().map((product) => (
                      <TouchableOpacity 
                        key={product.id}
                        style={styles.productListItem}
                        onPress={() => {
                          handleProductSelect(product);
                        }}
                      >
                        <Text style={styles.productListItemText}>{product.product_name}</Text>
                      </TouchableOpacity>
                    ))
                  )}
                </ScrollView>
              </SafeAreaView>
            </View>
          </View>
        </Modal>
      </View>
    </View>
  );
}

// Define styles type
type Styles = {
  safeAreaContainer: ViewStyle;
  appBar: ViewStyle;
  appBarLeft: ViewStyle;
  appBarCenter: ViewStyle;
  appBarRight: ViewStyle;
  appBarButton: ViewStyle;
  appBarTitle: TextStyle;
  cancelButton: TextStyle;
  saveButton: TextStyle;
  container: ViewStyle;
  scrollView: ViewStyle;
  content: ViewStyle;
  productCard: ViewStyle;
  productHeader: ViewStyle;
  productHeaderRight: ViewStyle;
  productName: TextStyle;
  productPrice: TextStyle;
  removeButton: ViewStyle;
  productSummary: ViewStyle;
  summaryRow: ViewStyle;
  summaryItem: ViewStyle;
  summaryLabel: TextStyle;
  summaryValue: TextStyle;
  productExpandedContent: ViewStyle;
  inputGroup: ViewStyle;
  label: TextStyle;
  input: TextStyle;
  discountContainer: ViewStyle;
  discountTypeSelectorContainer: ViewStyle;
  discountTypeSelector: ViewStyle;
  discountTypeText: TextStyle;
  discountInput: TextStyle;
  discountTypePopup: ViewStyle;
  discountTypeOption: ViewStyle;
  selectedDiscountType: ViewStyle;
  discountTypeOptionText: TextStyle;
  selectedDiscountTypeText: TextStyle;
  addProductLink: ViewStyle;
  addProductLinkText: TextStyle;
  addProductButton: ViewStyle;
  headerTitleText: TextStyle;
  totalValueContainer: ViewStyle;
  totalValueContent: ViewStyle;
  totalValueLabel: TextStyle;
  totalValueAmountContainer: ViewStyle;
  totalValueAmount: TextStyle;
  modalOverlay: ViewStyle;
  modalView: ViewStyle;
  modalContainer: ViewStyle;
  modalHeader: ViewStyle;
  modalCancelButton: TextStyle;
  modalTitle: TextStyle;
  modalHeaderRight: ViewStyle;
  categoryWrapper: ViewStyle;
  categorySelector: ViewStyle;
  categoryText: TextStyle;
  searchContainer: ViewStyle;
  searchIcon: TextStyle;
  searchInput: TextStyle;
  productList: ViewStyle;
  productListItem: ViewStyle;
  productListItemText: TextStyle;
  categoryPickerContainer: ViewStyle;
  categoryItem: ViewStyle;
  selectedCategoryItem: ViewStyle;
  categoryItemText: TextStyle;
  selectedCategoryItemText: TextStyle;
  loadingContainer: ViewStyle;
  loadingText: TextStyle;
  errorContainer: ViewStyle;
  errorText: TextStyle;
  retryButton: ViewStyle;
  retryButtonText: TextStyle;
  summaryContent: ViewStyle;
  header: ViewStyle;
  scrollContentContainer: ViewStyle;
  scrollInnerContent: ViewStyle;
};

// Updated styles to match DealsAddProduct
const styles = StyleSheet.create<Styles>({
  safeAreaContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  container: {
    flex: 1,
    backgroundColor: '#0F96BB',
  },
  content: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    backgroundColor: '#0F96BB',
    elevation: Platform.OS === 'android' ? 4 : 0,
    shadowColor: Platform.OS === 'ios' ? '#000000' : 'transparent',
    shadowOffset: { 
      width: 0, 
      height: Platform.OS === 'ios' ? 2 : 0 
    },
    shadowOpacity: Platform.OS === 'ios' ? 0.25 : 0,
    shadowRadius: Platform.OS === 'ios' ? 3.84 : 0,
    height: Platform.OS === 'android' ? 56 : 44,
    zIndex: 1000,
  },
  headerTitleText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  appBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#0F96BB',
    height: Platform.OS === 'ios' ? 44 : 56,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#0F96BB',
  },
  appBarLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  appBarCenter: {
    flex: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  appBarRight: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  appBarButton: {
    padding: 8,
  },
  cancelButton: {
    fontSize: 17,
    color: '#FFFFFF',
    fontWeight: '400',
  },
  saveButton: {
    fontSize: 17,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContentContainer: {
    flexGrow: 1,
    paddingBottom: 16,
  },
  scrollInnerContent: {
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  productCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    marginBottom: 16,
    overflow: 'hidden',
    padding: 16,
  },
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  productHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#222222',
    flex: 1,
  },
  productPrice: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginRight: 12,
  },
  removeButton: {
    padding: 4,
  },
  productSummary: {
    marginTop: 4,
  },
  summaryRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  summaryItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 16,
    color: '#666666',
    fontWeight: '400',
  },
  summaryValue: {
    fontSize: 16,
    color: '#222222',
    fontWeight: '500',
  },
  productExpandedContent: {
    marginTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    padding: 12,
    fontSize: 16,
    color: '#333',
    backgroundColor: '#fff',
  },
  discountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  discountTypeSelectorContainer: {
    position: 'relative',
    zIndex: 1,
    flex: 3,
  },
  discountTypeSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 12,
    backgroundColor: '#fff',
    width: '100%',
  },
  discountTypeText: {
    fontSize: 14,
    color: '#333',
    flex: 1,
    marginRight: 8,
    flexShrink: 1,
    flexWrap: 'nowrap',
  },
  discountInput: {
    flex: 1,
  },
  discountTypePopup: {
    position: 'absolute',
    bottom: '100%',
    marginBottom: 4,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderRadius: 6,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    zIndex: 1000,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  discountTypeOption: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  selectedDiscountType: {
    backgroundColor: '#f0f0f0',
  },
  discountTypeOptionText: {
    fontSize: 16,
    color: '#333',
  },
  selectedDiscountTypeText: {
    color: '#007AFF',
    fontWeight: '500',
  },
  addProductLink: {
    paddingVertical: 12,
  },
  addProductLinkText: {
    fontSize: 17,
    color: '#007AFF',
    fontWeight: '400',
  },
  addProductButton: {
    paddingVertical: 12,
    marginTop: 10,
    marginBottom: 80,
  },
  totalValueContainer: {
    borderTopWidth: 1,
    borderTopColor: '#ddd',
    backgroundColor: 'white',
  },
  totalValueContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  totalValueLabel: {
    fontSize: 18,
    color: '#666666',
    fontWeight: '400',
  },
  totalValueAmountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  totalValueAmount: {
    fontSize: 18,
    fontWeight: '600',
    color: '#222222',
    marginRight: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalView: {
    height: '60%',
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalCancelButton: {
    fontSize: 17,
    color: '#007AFF',
    fontWeight: '400',
  },
  modalTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: '#000',
  },
  modalHeaderRight: {
    width: 70,
  },
  categoryWrapper: {
    backgroundColor: 'white',
    zIndex: 1,
  },
  categorySelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  categoryText: {
    fontSize: 17,
    color: '#333',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#eee',
    margin: 16,
    paddingHorizontal: 12,
    borderRadius: 10,
  },
  searchIcon: {
    marginRight: 8,
  } as TextStyle,
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 17,
    color: '#333',
  },
  productList: {
    flex: 1,
    backgroundColor: 'white',
  },
  productListItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  productListItemText: {
    fontSize: 17,
    color: '#333',
  },
  categoryPickerContainer: {
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#eee',
    maxHeight: 300,
  },
  categoryItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  selectedCategoryItem: {
    backgroundColor: '#f0f0f0',
  },
  categoryItemText: {
    fontSize: 17,
    color: '#333',
  },
  selectedCategoryItemText: {
    color: '#007AFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#777',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  errorText: {
    color: '#ff4444',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 12,
  },
  retryButton: {
    backgroundColor: '#0F96BB',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 6,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  summaryContent: {
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  appBarTitle: {
    fontSize: 18,
    color: '#FFFFFF',
    fontWeight: '600',
  },
}); 