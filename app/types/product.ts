export interface Currency {
  id: number;
  currency: string;
  currency_code: string;
  decimal: string;
  is_default: number;
  active: number;
}

export interface PricingType {
  pricing_type: string;
}

export interface ProductPrice {
  id: number;
  currency_code: string;
  amount: string;
  pricing_type: string;
}

export interface ProductDetailsResponse {
  status: number;
  message: string;
  product: {
    id: number;
    currencies: Currency[];
    pricing_types: PricingType[];
    prices: ProductPrice[];
    // ... other product fields
  };
} 