import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Stack, useRouter } from 'expo-router';

export default function TestNavigationScreen() {
  const router = useRouter();

  const navigateToDealDetails = () => {
    console.log('Attempting to navigate to dealDetails');
    try {
      // Try different navigation methods
      router.push('/dealDetails?dealId=60');
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };

  return (
    <View style={styles.container}>
      <Stack.Screen options={{ title: 'Test Navigation' }} />
      
      <Text style={styles.title}>Navigation Test</Text>
      <Text style={styles.description}>
        This screen helps test navigation to the dealDetails page.
      </Text>
      
      <TouchableOpacity 
        style={styles.button}
        onPress={navigateToDealDetails}
      >
        <Text style={styles.buttonText}>Go to Deal Details</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
  },
  button: {
    backgroundColor: '#0F6EBB',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  }
}); 