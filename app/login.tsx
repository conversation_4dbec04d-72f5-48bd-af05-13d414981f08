import React, { useState, useEffect } from 'react';
import {
  StyleSheet, View, Image, TouchableOpacity,
  KeyboardAvoidingView, Platform, Alert, ActivityIndicator,
  LogBox
} from 'react-native';
import { useAuth } from '../context/AuthContext';
import Toast from 'react-native-toast-message';
import { TextInput, Button, Text } from 'react-native-paper';
import { showSnackbar, showToast } from '../app/ui/utils';
import { useColors } from '@/hooks/useThemeColor';

export default function LoginScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const { signIn, isLoading } = useAuth();
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);
  const colors = useColors();

  useEffect(() => {
    LogBox.ignoreAllLogs();
    if (!isLoading) {
      setTimeout(() => {
        setIsCheckingAuth(false);
      }, 300);
    }
  }, [isLoading]);

  const handleLogin = async () => {
    if (!email || !password) {
      showSnackbar('Please enter both email and password');
      return;
    }

    // Simple email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      showSnackbar('Please enter a valid email address');
      return;
    }

    try {
      console.log('Login attempt with email:', email, 'and password:', password);
      await signIn(email, password);
      showToast('Login successful');
    } catch (error) {
      let errorMessage = 'Invalid credentials. Try again!';
      if (error && typeof error === 'object' && 'message' in error) {
        errorMessage = ""+error.message;
      }

      // Use Alert instead of Snackbar for login errors to ensure they're visible
      Alert.alert(
        'Login Failed',
        errorMessage,
        [{ text: 'OK' }],
        { cancelable: false }
      );
    }
  };

  if (isCheckingAuth) {
    return (
      <View style={styles.loadingContainer}>
        <Image source={require('../assets/images/logo.png')} style={styles.logo} />
        <ActivityIndicator size="large" color="#6461E1" style={styles.loadingIndicator} />
        <Text style={styles.loadingText}>Checking login status...</Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* Logo Section */}
      <View style={styles.logoContainer}>
        <Image source={require('../assets/images/logo.png')} style={styles.logo} />
      </View>

      {/* Login Title */}
      <Text style={[styles.loginTitle, { color: colors.primary }]}>Login</Text>

      {/* Input Fields */}
      <View style={styles.form}>
        {/* Email Input */}
        <TextInput
          label="Email"
          mode="outlined"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
          style={styles.input}
          disabled={isLoading}
          theme={{ colors: { primary: colors.primary } }}
        />

        {/* Password Input with Visibility Toggle */}
        <TextInput
          label="Password"
          mode="outlined"
          value={password}
          onChangeText={setPassword}
          secureTextEntry={!showPassword}
          autoCapitalize="none"
          style={styles.input}
          disabled={isLoading}
          theme={{ colors: { primary: colors.primary } }}
          right={
            <TextInput.Icon
              icon={showPassword ? 'eye-off' : 'eye'}
              onPress={() => setShowPassword(!showPassword)}
            />
          }
        />

        {/* Login Button */}
        <TouchableOpacity
  activeOpacity={0.7}
  onPress={handleLogin}
  disabled={isLoading}
  style={styles.loginButtonWrapper}
>
  <Button
    mode="contained"
    loading={isLoading}
    style={[styles.loginButton, { backgroundColor: colors.primary }]}
    labelStyle={styles.loginButtonText}
    contentStyle={styles.loginButtonContent}
    disabled={isLoading}
    pointerEvents="none"
  >
    Login
  </Button>
</TouchableOpacity>

        {/* Forgot Password */}
        {/*<TouchableOpacity onPress={() => Alert.alert('Forgot Password', 'Reset your password')}>
          <Text style={styles.forgotPassword}>Forgot Password?</Text>
        </TouchableOpacity>*/}
      </View>

      {/* Toast Notification */}
      <Toast />
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    justifyContent: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingIndicator: {
    marginTop: 30,
  },
  loadingText: {
    marginTop: 15,
    fontSize: 16,
    color: '#333',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  logo: {
    width: 120,
    height: 120,
  },
  loginTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  form: {
    width: '100%',
  },
  input: {
    marginBottom: 15,
  },
  loginButtonWrapper: {
    width: '100%',
    borderRadius: 8,
    overflow: 'hidden', // Ensures rounded corners work on Android
    marginTop: 10,
    height: 70,
  },
  loginButton: {
    borderRadius: 8,
    paddingVertical: 0,
    width: '100%',
  },
  loginButtonText: {
    fontWeight: 'bold',
    fontSize: 18,
    textAlign: 'center',
    width: '100%',
  },
  loginButtonContent: {
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    height: 44,
  },
  forgotPassword: {
    marginTop: 10,
    textAlign: 'center',
    fontSize: 14,
  },
});


