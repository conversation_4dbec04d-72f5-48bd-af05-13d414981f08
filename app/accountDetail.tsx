import React, { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Switch,
  Image,
  Modal,
  Alert,
  StatusBar,
  Platform,
  Linking,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { SafeAreaView, Edge } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { useColors } from '@/hooks/useThemeColor';
import { router } from 'expo-router';
import { handleAddNote, showSnackbar, readUserScope, handleSendEmail, validateForEditAndDeleteActs, capitalizeWords } from './ui/utils';
import { getFieldValue } from './models/model_account_details';
import {
  AccountField,
  Account
} from './models/model_account_details';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';

// Define interfaces for the component
interface AccountDetails {
  account: {
    id: string | number;
    accounts: AccountField[];
    tasks: any[];
    meetings: any[];
    sales_account: any | null;
    notes_count: number;
    contacts: any[];
    deals_summary: {
      count: number;
      total_value: number;
      open_count: number;
      open_value: number;
    };
  };
}

// Update the AccessScope interface
interface AccessScope {
  contact?: {
    create?: boolean | 'global' | 'restricted';
    view?: boolean | 'global' | 'restricted';
    edit?: boolean | 'global' | 'restricted';
    delete?: boolean | 'global' | 'restricted';
  };
  task?: {
    create?: boolean | 'global' | 'restricted';
    view?: boolean | 'global' | 'restricted';
    edit?: boolean | 'global' | 'restricted';
    delete?: boolean | 'global' | 'restricted';
  };
  appointment?: {
    create?: boolean | 'global' | 'restricted';
    view?: boolean | 'global' | 'restricted';
    edit?: boolean | 'global' | 'restricted';
    delete?: boolean | 'global' | 'restricted';
  };
  notes?: {
    create?: boolean | 'global' | 'restricted';
    view?: boolean | 'global' | 'restricted';
    edit?: boolean | 'global' | 'restricted';
    delete?: boolean | 'global' | 'restricted';
  };
  sales_account?: {
    create?: boolean | 'global' | 'restricted';
    view?: boolean | 'global' | 'restricted';
    edit?: boolean | 'global' | 'restricted';
    delete?: boolean | 'global' | 'restricted';
  };
  calllog?: {
    create?: boolean | 'global' | 'restricted';
    view?: boolean | 'global' | 'restricted';
    edit?: boolean | 'global' | 'restricted';
    delete?: boolean | 'global' | 'restricted';
  };
}

// Use ReturnType from useColors
type ThemeColors = ReturnType<typeof useColors>;

export default function AccountDetailScreen() {
  const [activeSegment, setActiveSegment] = useState('Account details');
  const [showEmptyFields, setShowEmptyFields] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [optionsVisible, setOptionsVisible] = useState(false);
  const [addOptionsVisible, setAddOptionsVisible] = useState(false);
  const [accountDetails, setAccountDetails] = useState<AccountDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [customFields, setCustomFields] = useState<AccountField[]>([]);
  const [accessScope, setAccessScope] = useState<AccessScope | null>(null);
  const [canEdit, setCanEdit] = useState(false);
  const [canDelete, setCanDelete] = useState(false);

  // Add a ref to track if we've already processed the custom fields
  const customFieldsProcessed = useRef(false);

  const router = useRouter();
  const params = useLocalSearchParams();
  const accountId = params.id as string;
  const accountName = params.name as string;
  const accountPhone = params.phone as string;
  const accountWebsite = params.website as string;
  const accountCreatedAt = params.created_at as string;

  // Safely parse the item parameter
  const distributorItem = useMemo(() => {
    if (!params.item) return null;

    try {
      return JSON.parse(params.item as string);
    } catch (error) {
      console.error('Error parsing distributor item:', error);
      return null;
    }
  }, [params.item]);

  const colors = useColors();
  const styles = useMemo(() => createStyles(colors), [colors]);

  // Define a header background color that will be used for both the status bar and header
  const headerBackgroundColor = '#0F96BB';

  {/*
  console.log('Account Detail Page - Received ID:', accountId);
  console.log('Account Detail Page - Received Name:', accountName);
  console.log('Account Detail Page - Received Phone:', accountPhone);
  console.log('Account Detail Page - Received Website:', accountWebsite);
  console.log('Account Detail Page - Received Created At:', accountCreatedAt);
  console.log('Account Detail Page - Received Distributor Item:', distributorItem);*/}

  const loadAccountDetails = async () => {
    if (!accountId) return;

    setIsLoading(true);
    setError(null);

    try {
      const endpoint = `${API_ENDPOINTS.SALES_ACCOUNTS}/${accountId}`;
      console.log(`Fetching account details from: ${endpoint}`);

      const response = await apiService.get(endpoint);
      console.log('Account API response:', JSON.stringify(response));

      if (response && response.data) {
        const accountData = response.data.account;

        if (accountData) {
          const formattedResponse = {
            account: {
              id: accountData.id,
              accounts: accountData.accounts || [],
              tasks: accountData.tasks || [],
              meetings: accountData.meetings || [],
              sales_account: accountData.sales_account || null,
              notes_count: accountData.notes_count || 0,
              contacts: accountData.contacts || [],
              deals_summary: accountData.deals_summary || {
                count: 0,
                total_value: 0,
                open_count: 0,
                open_value: 0
              }
            }
          };

          setAccountDetails(formattedResponse);
          console.log('Account details loaded successfully:', JSON.stringify(formattedResponse));
        } else {
          setError('Invalid account data returned from the server');
          showSnackbar('Invalid account data returned from the server');
        }
      } else {
        setError('Failed to load account details');
        showSnackbar('Failed to load account details');
      }
    } catch (err) {
      console.error('Error loading account details:', err);
      setError('An error occurred while loading account details');
      showSnackbar('An error occurred while loading account details');
    } finally {
      setIsLoading(false);
    }
  };

  // Replace the useEffect with useFocusEffect for data refresh
  useFocusEffect(
    useCallback(() => {
      console.log('Account detail screen focused, refreshing data...');
      loadAccountDetails();
    }, [accountId])
  );

  // Keep the initial useEffect for first load and ID validation
  useEffect(() => {
    // Extract account ID ensuring it works on both platforms
    const id = params.id ? params.id.toString() : null;
    console.log('Account Detail - Checking ID:', id);

    if (!id) {
      console.log('No account ID provided, navigating back to accounts list');
      router.replace('./distributors' as any);
    }
  }, [params.id, router]);

  // Update the initial useEffect for access scope
  useEffect(() => {
    const fetchAccessScope = async () => {
      try {
        const scope = await readUserScope();
        console.log("Account Detail - Access Scope:", JSON.stringify(scope));
        console.log("Call permission:", scope?.calllog?.create);
        console.log("Notes permission:", scope?.notes?.create);
        setAccessScope(scope);
        refreshAccessScope('delete');
        refreshAccessScope('edit');
      } catch (error) {
        console.error("Error fetching access scope:", error);
        setAccessScope(null);
      }
    };
    fetchAccessScope();
  }, []); // Keep empty dependency array for initial load only

  // Replace the validateAccess function with refreshAccessScope
  const refreshAccessScope = async (type: string) => {
    console.log("refreshAccessScopesdsd: " + JSON.stringify(accessScope));
    const ownerId = getFieldValue(accountDetails?.account?.accounts!, 'owner_id');
    const territoryId = getFieldValue(accountDetails?.account?.accounts!, 'territory_id');
    console.log("ownerId: " + ownerId + " territoryId: " + territoryId+" permission: "+accessScope?.sales_account?.delete?.toString() + " edit "+accessScope?.account?.edit?.toString());

    const permission = type === 'delete' ? accessScope?.sales_account?.delete?.toString() : accessScope?.sales_account?.edit?.toString();
    console.log(`${type} permission:`, permission);

    const canAccess = await validateForEditAndDeleteActs(territoryId!, permission!, ownerId!);
    console.log(`can${type}:`, canAccess);

    if (type === 'delete') {
      setCanDelete(canAccess);
    } else {
      setCanEdit(canAccess);
    }
  };

  // Update the useEffect for access scope validation
  useEffect(() => {
    console.log("Refreshing access scope with data:", {
      accessScope: JSON.stringify(accessScope),
      hasAccounts: !!accountDetails?.account?.accounts
    });
    if (accessScope && accountDetails?.account?.accounts) {
      console.log("Refreshing access scope with data:", {
        accessScope: JSON.stringify(accessScope),
        hasAccounts: !!accountDetails.account.accounts
      });
      refreshAccessScope('delete');
      refreshAccessScope('edit');
    }
  }, [accessScope, accountDetails?.account?.accounts]);

  const handleBack = () => {
    console.log('Navigating back from account detail');

    // Use platform-agnostic approach with expo-router
    try {
      router.back();
    } catch (error) {
      // Fallback if back() fails
      console.error('Error navigating back:', error);
      router.replace('./distributors');
    }
  };

  const handleSegmentPress = (segment: string) => {
    setActiveSegment(segment);
  };

  const handleShowOptions = () => {
    setOptionsVisible(true);
  };

  const handleOptionPress = (option: string) => {
    setOptionsVisible(false);

    switch (option) {
      case 'Call':
        // Handle call
        handleCall();
        Alert.alert('Call', 'Calling account...');
        break;
      case 'Send email':
        // Get email from account fields
        const recipientEmail = accountDetails?.account?.accounts?.find(field => field.field_name === 'email')?.field_value;
        const recipientName = accountDetails?.account?.accounts?.find(field => field.field_name === 'name')?.field_value || 'Unknown Account';

        if (!recipientEmail) {
          showSnackbar('No email address available for this account');
          return;
        }

        // Format email subject and body
        const emailSubject = `Regarding ${recipientName}`;
        const emailBody = `Hello,\n\nI hope this email finds you well. I am reaching out regarding ${recipientName}.\n\nBest regards,`;

        // Send email using the utility function
        handleSendEmail(recipientEmail, emailSubject, emailBody);
        break;
      case 'Share via WhatsApp':
        // Format account details for sharing
        const accountName = accountDetails?.account?.accounts?.find(field => field.field_name === 'name')?.field_value || 'Unknown Account';
        const accountPhone = accountDetails?.account?.accounts?.find(field => field.field_name === 'phone')?.field_value || '';
        const accountEmail = accountDetails?.account?.accounts?.find(field => field.field_name === 'email')?.field_value || '';
        const accountWebsite = accountDetails?.account?.accounts?.find(field => field.field_name === 'website')?.field_value || '';

        const shareMessage = `Account Details:\n\nName: ${accountName}\nPhone: ${accountPhone}\nEmail: ${accountEmail}\nWebsite: ${accountWebsite}`;

        // Try to open WhatsApp
        const whatsappUrl = `whatsapp://send?text=${encodeURIComponent(shareMessage)}`;
        Linking.canOpenURL(whatsappUrl)
          .then(supported => {
            if (supported) {
              return Linking.openURL(whatsappUrl);
            } else {
              // Fallback to WhatsApp Web
              const webUrl = `https://api.whatsapp.com/send?text=${encodeURIComponent(shareMessage)}`;
              return Linking.openURL(webUrl);
            }
          })
          .catch(err => {
            console.error('Error sharing via WhatsApp:', err);
            showSnackbar('Could not open WhatsApp. Please try again.');
          });
        break;
      case 'Clone':
        // Handle clone
        Alert.alert('Clone', 'Cloning account...');
        break;
      case 'Edit':
        if (canEdit) {
          router.push({
            pathname: './UpdateAccounts' as const,
            params: { id: accountId }
          });
        } else {
          showSnackbar('You do not have permission to edit accounts');
        }
        break;
      case 'Delete':
        if (canDelete) {
          Alert.alert(
            'Delete Account',
            'Are you sure you want to delete this account? This action cannot be undone.',
            [
              { text: 'Cancel', style: 'cancel' },
              {
                text: 'Delete',
                style: 'destructive',
                onPress: () => deleteAccount()
              },
            ]
          );
        } else {
          showSnackbar('You do not have permission to delete accounts');
        }
        break;
      default:
        break;
    }
  };

  // Add function to delete account
  const deleteAccount = async () => {
    if (!accountId) {
      showSnackbar('Account ID is missing');
      return;
    }

    try {
      // Show loading indicator or disable UI interactions here if needed
      const endpoint = API_ENDPOINTS.ACCOUNT_DETAILS.replace(':id', accountId);
      console.log('endpointsdsd', endpoint);
      const response = await apiService.delete(endpoint);

      if (response.status === 200 || response.status === 204) {
        showSnackbar('Account deleted successfully');
        // Navigate back to the accounts list
        //router.replace('/distributors');
        router.back();
      } else {
        throw new Error(response.data?.message || 'Failed to delete account');
      }
    } catch (error) {
      console.error('Error deleting account:', error);
      showSnackbar('Failed to delete account. Please try again.');
    }
  };

  const navigateToSection = (section: string) => {
    switch (section) {
      case 'contacts':
        router.push({
          pathname: './RelatedContactsList' as const,
          params: { accountId }
        });
        break;
      case 'deals':
        router.push({
          pathname: './RelatedDealsList' as const,
          params: { accountId }
        });
        break;
      case 'files':
        router.push({
          pathname: './files' as const,
          params: { accountId }
        });
        break;
      default:
        break;
    }
  };

  const handleAddAccount = () => {
    // Show the bottom sheet with add options
    setAddOptionsVisible(true);
  };

  const handleAddOptionPress = (option: string) => {
    setAddOptionsVisible(false);

    switch (option) {
      case 'Contact':
    router.push({
      pathname: './contact' as any,
          params: { id:  accountId,callFrom: 'accountDetail'  ,accountDetails: JSON.stringify(accountDetails) }
        });
        break;
      case 'Task':
        console.log('Navigating to task creation with account ID:', accountId);
        router.push({
          pathname: './tasks/create' as any,
          params: {
            sales_account_id: accountId,
            callFrom: 'accountDetail',
            accountDetails: JSON.stringify(accountDetails)
          }
        });
        break;
      case 'Meeting':
        router.push({
          pathname: './meetings/createMeeting' as any,
          params: { sales_account_id: accountId,callFrom: 'accountDetail' ,accountDetails: JSON.stringify(accountDetails) }
        });
        break;
      case 'Note':
        // Navigate to add note screen
        //Alert.alert('Add Note', 'This would navigate to add a note');
        handleAddNote(accountId, "accounts");
        break;
      default:
        break;
    }
  };

  const handleCall = async () => {
    console.log('handleCall', accountDetails);
    if (!accountDetails?.account) {
      console.log('No account details available');
      return;
    }

    // Find phone number from account fields
    console.log('Accounts fields:', accountDetails.account.accounts);
    const phoneField = accountDetails.account.accounts?.find(field =>
      field.field_name === 'phone' || field.field_name === 'mobile'
    );

    console.log('Found phone field:', phoneField);
    const phoneNumber = phoneField?.field_value;

    if (!phoneNumber) {
      console.log('No phone number found in account');
      showSnackbar('No phone number available for this account.');
      return;
    }

    console.log('Found phone number:', phoneNumber);

    // Format the number - keep only digits and plus sign at the start
    let formattedNumber = phoneNumber.replace(/[^\d+]/g, '');
    if (!formattedNumber.startsWith('+')) {
      formattedNumber = formattedNumber.replace(/^0+/, ''); // Remove leading zeros
    }

    console.log('Formatted phone number:', formattedNumber);

    const phoneUrl = Platform.select({
      ios: `telprompt:${formattedNumber}`,
      android: `tel:${formattedNumber}`,
      default: `tel:${formattedNumber}`
    });

    try {
      const supported = await Linking.canOpenURL(phoneUrl);
      console.log('Phone URL:', phoneUrl, 'Supported:', supported);
      //if (supported) {
        await Linking.openURL(phoneUrl);
      //} else {
        //showSnackbar('Phone calls are not supported on this device');
      //}
    } catch (error) {
      console.error('Phone call error:', error);
      Alert.alert('Error', 'Failed to make phone call');
    }
  };

  const filterFields = (fields: AccountField[]): AccountField[] => {
    if (!fields) return [];
    return fields.filter(field => {
      // Skip fields that should be hidden
      if (field.field_name === 'sales_account_id' && field.active === 0) return false;

      // Filter by search query if provided
      const matchesSearch = searchQuery
        ? field.field_label.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (field.field_value && field.field_value?.toString().toLowerCase().includes(searchQuery?.toLowerCase()))
        : true;

      // Filter empty fields based on toggle
      const hasValue = field.field_value !== null &&
                      field.field_value !== '' &&
                      field.field_value !== undefined &&
                      field.field_value.toString().trim() !== '';
      const showField = showEmptyFields || hasValue;

      return matchesSearch && showField;
    });
  };

  const renderFieldValue = (field: AccountField) => {
    // Skip rendering if field is empty and showEmptyFields is false
    if (!showEmptyFields && !field.field_value) return null;

    // Format value based on field type
    let displayValue = field.field_value || (showEmptyFields ? 'Not provided' : '');

    // Handle dropdown and lookup fields
    if ((field.field_type.toLowerCase() === 'dropdown' || field.field_type.toLowerCase() === 'lookup') && field.choices) {
      const selectedChoice = field.choices.find(choice => 
        choice.id.toString() === field.field_value?.toString() || 
        choice.custom_option === field.field_value
      );
      if (selectedChoice) {
        displayValue = selectedChoice.custom_option;
      }
    }

    // Handle special field types
    switch (field.field_name) {
      case 'website':
        return field.field_value ? (
          <TouchableOpacity onPress={() => Linking.openURL(field.field_value!)}>
            <Text style={[styles.fieldValue, styles.linkText]}>
              {displayValue}
            </Text>
          </TouchableOpacity>
        ) : (showEmptyFields ? 'Not provided' : '');
      case 'phone':
      case 'mobile':
        return field.field_value ? (
          <TouchableOpacity onPress={() => handleCall()}>
            <Text style={[styles.fieldValue, styles.linkText]}>
              {displayValue}
            </Text>
          </TouchableOpacity>
        ) : (showEmptyFields ? 'Not provided' : '');
      case 'email':
        return field.field_value ? (
          <TouchableOpacity onPress={() => handleSendEmail(field.field_value!, '', '')}>
            <Text style={[styles.fieldValue, styles.linkText]}>
              {displayValue}
            </Text>
          </TouchableOpacity>
        ) : (showEmptyFields ? 'Not provided' : '');
      case 'annual_revenue':
      case 'price':
      case 'deal_value':
        // Format currency values
        if (field.field_value) {
          const currencyField = accountDetails?.account?.accounts?.find(f => f.field_name === 'currency_id');
          const currencyValue = currencyField?.field_value;
          const currencySymbol = currencyValue === 'USD' ? '$' : '₹';
          displayValue = `${currencySymbol} ${field.field_value}`;
        }
        return (
          <Text style={[styles.fieldValue, !field.field_value && styles.emptyValue]}>
            {displayValue}
          </Text>
        );
      default:
        return (
          <Text style={[styles.fieldValue, !field.field_value && styles.emptyValue]}>
            {displayValue}
          </Text>
        );
    }
  };

  const renderAccountDetails = () => {
    if (!accountDetails?.account?.accounts) return null;

    const fields = filterFields(accountDetails.account.accounts);
    
    // Group fields by their type/category
    const groupedFields = fields.reduce((acc, field) => {
      let section = 'BASIC INFORMATION';
      
      // Determine section based on field type or name
      if (['phone', 'mobile', 'email', 'website'].includes(field.field_name)) {
        section = 'CONTACT INFORMATION';
      } else if (['annual_revenue', 'price', 'deal_value'].includes(field.field_name)) {
        section = 'FINANCIAL INFORMATION';
      } else if (['number_of_employees', 'industry'].includes(field.field_name)) {
        section = 'COMPANY INFORMATION';
      } else if (['created_at', 'updated_at', 'owner_id'].includes(field.field_name)) {
        section = 'SYSTEM INFORMATION';
      }

      if (!acc[section]) {
        acc[section] = [];
      }
      acc[section].push(field);
      return acc;
    }, {} as Record<string, AccountField[]>);

    return (
      <ScrollView style={styles.contentContainer}>
        {/* Search and Filter Section */}
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color={colors.text.secondary} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search fields..."
            placeholderTextColor={colors.text.secondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery ? (
            <TouchableOpacity onPress={() => setSearchQuery('')} style={styles.clearSearchButton}>
              <Ionicons name="close-circle" size={20} color={colors.text.secondary} />
            </TouchableOpacity>
          ) : null}
        </View>

        <View style={styles.toggleContainer}>
          <Text style={styles.toggleLabel}>Show empty fields</Text>
          <Switch
            value={showEmptyFields}
            onValueChange={setShowEmptyFields}
            trackColor={{ false: '#f0f0f0', true: '#d1e0ff' }}
            thumbColor={showEmptyFields ? '#0F6EBB' : '#fff'}
          />
        </View>

        {/* Fields Sections */}
        {Object.entries(groupedFields).map(([section, sectionFields]) => (
          <View key={section} style={styles.sectionContainer}>
            <Text style={styles.sectionHeader}>{section}</Text>
            {sectionFields.map((field, index) => (
              <View key={field.id || index} style={styles.fieldRow}>
                <Text style={styles.fieldLabel}>
                  {field.field_label}
                  {field.required === 'on' && <Text style={styles.requiredAsterisk}> *</Text>}
                </Text>
                <View style={styles.fieldValueContainer}>
                  {renderFieldValue(field)}
                </View>
              </View>
            ))}
          </View>
        ))}
      </ScrollView>
    );
  };

  const renderActivities = () => (
    <ScrollView style={styles.activitiesContainer} nestedScrollEnabled={true}>
      <View style={styles.quickActionsContainer}>
        {accessScope?.calllog?.create === true && (
          <TouchableOpacity style={styles.quickAction} onPress={handleCall}>
            <View style={styles.quickActionIconContainer}>
              <Ionicons name="call-outline" size={24} color={colors.text.primary} />
            </View>
            <Text style={styles.actionText}>Call phone</Text>
          </TouchableOpacity>
        )}
        {accessScope?.notes?.create === true && (
          <TouchableOpacity style={styles.quickAction} onPress={() => handleAddNote(accountId, "accounts")}>
            <View style={styles.quickActionIconContainer}>
              <Ionicons name="mic-outline" size={24} color={colors.text.primary} />
            </View>
            <Text style={styles.actionText}>Add note</Text>
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.activitiesListContainer}>
        {accessScope?.notes?.view === true && (
          <TouchableOpacity
            style={[styles.activityItem, (accountDetails?.account?.notes_count === 0) && styles.disabledItem]}
            onPress={() => {
              router.push({
                pathname: './ui/screens/NotesListScreen' as const,
                params: { id: accountId, type: "accounts" }
              });
            }}
          >
            <Ionicons
              name="document-text-outline"
              size={22}
              color={(accountDetails?.account?.notes_count === 0) ? colors.text.tertiary : colors.text.primary}
            />
            <Text style={[
              styles.activityText,
              (accountDetails?.account?.notes_count === 0) && styles.disabledText
            ]}>Notes ({accountDetails?.account?.notes_count || 0})</Text>
            <Ionicons
              name="chevron-forward"
              size={22}
              color={(accountDetails?.account?.notes_count === 0) ? colors.text.tertiary : colors.text.secondary}
              style={styles.chevron}
            />
          </TouchableOpacity>
        )}

        {accessScope?.task?.view === true && (
          <TouchableOpacity
            style={[styles.activityItem, (accountDetails?.account?.tasks?.length === 0) && styles.disabledItem]}
            onPress={() => {
              router.push({
                pathname: './TaskListScreen' as const,
                params: { sales_account_id: accountId }
              });
            }}
          >
            <Ionicons
              name="calendar-outline"
              size={22}
              color={colors.text.primary}
            />
            <Text style={styles.activityText}>Tasks ({accountDetails?.account?.tasks?.length || 0})</Text>
            <Ionicons name="chevron-forward" size={22} color={colors.text.secondary} style={styles.chevron} />
          </TouchableOpacity>
        )}

        {accessScope?.appointment?.view === true && (
          <TouchableOpacity
            style={[styles.activityItem, (accountDetails?.account?.meetings?.length === 0) && styles.disabledItem]}
            onPress={() => {
              router.push({
                pathname: './meetings/meetingList' as const,
                params: { sales_account_id: accountId }
              });
            }}
          >
            <Ionicons
              name="people-outline"
              size={22}
              color={colors.text.primary}
            />
            <Text style={styles.activityText}>Meetings ({accountDetails?.account?.meetings?.length || 0})</Text>
            <Ionicons name="chevron-forward" size={22} color={colors.text.secondary} style={styles.chevron} />
          </TouchableOpacity>
        )}
      </View>
    </ScrollView>
  );

  const renderMore = () => {
    return (
      <ScrollView style={styles.moreContainer} nestedScrollEnabled={true}>
        <View style={styles.quickActionsContainer}>
          {accessScope?.calllog?.create === true && (
            <TouchableOpacity style={styles.quickAction} onPress={handleCall}>
              <View style={styles.quickActionIconContainer}>
                <Ionicons name="call-outline" size={24} color={colors.text.primary} />
              </View>
              <Text style={styles.actionText}>Call phone</Text>
            </TouchableOpacity>
          )}
          {accessScope?.notes?.create === true && (
            <TouchableOpacity style={styles.quickAction} onPress={() => handleAddNote(accountId, "accounts")}>
              <View style={styles.quickActionIconContainer}>
                <Ionicons name="mic-outline" size={24} color={colors.text.primary} />
              </View>
              <Text style={styles.actionText}>Add note</Text>
            </TouchableOpacity>
          )}
        </View>

        <TouchableOpacity
          style={styles.moreItem}
          onPress={() => navigateToSection('contacts')}
        >
          <Ionicons name="person-outline" size={22} color={colors.text.primary} />
          { /* ({accountDetails?.account?.contacts?.length || 0})*/}
          <Text style={styles.moreItemText}>Contacts</Text> 
          <Ionicons name="chevron-forward" size={22} color={colors.text.secondary} style={styles.chevron} />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.moreItem}
          onPress={() => navigateToSection('deals')}
        >
          <Ionicons name="cash-outline" size={22} color={colors.text.primary} />
          <Text style={styles.moreItemText}>Deals </Text>
          <View style={styles.dealsInfoContainer}>
            {/* <Text style={styles.dealsInfoText}>
              {openDealsCount} open • ${openDealsValue.toLocaleString()}
            </Text> */}
            <Ionicons name="chevron-forward" size={22} color={colors.text.secondary} style={styles.chevron} />
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.moreItem}
          onPress={() => navigateToSection('files')}
        >
          <Ionicons name="document-attach-outline" size={22} color={colors.text.primary} />
          <Text style={styles.moreItemText}>Files</Text>
          <Ionicons name="chevron-forward" size={22} color={colors.text.secondary} style={styles.chevron} />
        </TouchableOpacity>
      </ScrollView>
    );
  };

  const renderHeaderActions = () => {
    return (
      <View style={styles.headerActions}>
        {canEdit && (
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => handleOptionPress('Edit')}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons name="pencil" size={24} color={colors.text.inverse} />
          </TouchableOpacity>
        )}

         {canDelete && (
              <TouchableOpacity
                style={styles.headerButton}
                onPress={() => handleOptionPress('Delete')}

              >
                <Ionicons name="trash-outline" size={24} color="#FFFFFF" />

              </TouchableOpacity>
        )}
        { /*
          <TouchableOpacity style={styles.optionsButton} onPress={handleShowOptions}>
            <Ionicons name="ellipsis-vertical" size={24} color={colors.text.inverse} />
          </TouchableOpacity>
        */}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={['top'] as Edge[]}>
      <StatusBar barStyle="light-content" backgroundColor={headerBackgroundColor} />
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      {/* Custom Header */}
      <View style={[styles.header, { backgroundColor: headerBackgroundColor }]}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Ionicons name="arrow-back" size={24} color={colors.text.inverse} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
         {capitalizeWords(accountDetails?.account?.accounts?.find(field => field.field_name === 'name')?.field_value ||
           (accountDetails?.account?.sales_account && accountDetails?.account?.sales_account.name) ||
           accountName ||
           'Account Details')}
        </Text>
        {renderHeaderActions()}
      </View>

      {/* Segments */}
      <View style={styles.segmentContainer}>
        {['Account details', 'Activities', 'More'].map((segment) => (
          <TouchableOpacity
            key={segment}
            style={[
              styles.segmentButton,
              activeSegment === segment && styles.activeSegment,
            ]}
            onPress={() => handleSegmentPress(segment)}
          >
            <Text
              style={[
                styles.segmentText,
                activeSegment === segment && styles.activeSegmentText,
              ]}
            >
              {segment}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Quick Actions Bar - Always visible below tabs 
      <View style={styles.quickActionsBar}>
        <TouchableOpacity 
          style={styles.quickActionButton} 
          onPress={handleCall}
        >
          <View style={styles.quickActionIconWrapper}>
            <Ionicons name="call-outline" size={24} color={colors.text.primary} />
          </View>
          <Text style={styles.quickActionText}>Call Phone</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.quickActionButton} 
          onPress={() => handleAddNote(accountId, "accounts")}
        >
          <View style={styles.quickActionIconWrapper}>
            <Ionicons name="mic-outline" size={24} color={colors.text.primary} />
          </View>
          <Text style={styles.quickActionText}>Add Note</Text>
        </TouchableOpacity>
        
      </View>
      */}

      {/* Main Content Area */}
      <View style={{ flex: 1, position: 'relative' }}>
        {activeSegment === 'Account details' && renderAccountDetails()}
        {activeSegment === 'Activities' && renderActivities()}
        {activeSegment === 'More' && renderMore()}

        {/* Floating Action Button */}
        {accessScope?.contact?.create === true && accessScope?.task?.create === true &&
          accessScope?.appointment?.create === true && accessScope?.notes?.create === true && (
          <TouchableOpacity style={styles.fab} onPress={handleAddAccount}>
            <Ionicons name="add" size={25} color="white" />
          </TouchableOpacity>
        )}
      </View>

      {/* Options Modal */}
      <Modal
        visible={optionsVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setOptionsVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setOptionsVisible(false)}
        >
          <View style={styles.optionsContainer}>
            {[
              { title: 'Send email', icon: 'mail-outline' },
              { title: 'Share via WhatsApp', icon: 'logo-whatsapp' },
              { title: 'Clone', icon: 'copy-outline' },
              ...(canDelete ?
                [{ title: 'Delete', icon: 'trash-outline', destructive: true }] :
                []
              ),
            ].map((option, index) => (
              <TouchableOpacity
                key={index}
                style={styles.optionItem}
                onPress={() => handleOptionPress(option.title)}
              >
                <Ionicons
                  name={option.icon as any}
                  size={24}
                  color={option.destructive ? '#FF3B30' : colors.text.primary}
                />
                <Text
                  style={[
                    styles.optionText,
                    option.destructive && styles.destructiveText
                  ]}
                >
                  {option.title}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>

      {/* Add Options Modal */}
      <Modal
        visible={addOptionsVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setAddOptionsVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setAddOptionsVisible(false)}
        >
          <View style={styles.optionsContainer}>
            <View style={styles.dragIndicator} />
            <Text style={styles.bottomSheetTitle}>Add New</Text>

            {[
              {
                title: 'Add Contact',
                icon: 'person-add-outline',
                action: 'Contact',
                permission: accessScope?.contact?.create === true
              },
              {
                title: 'Add Task',
                icon: 'calendar-outline',
                action: 'Task',
                permission: accessScope?.task?.create === true
              },
              {
                title: 'Add Meeting',
                icon: 'people-outline',
                action: 'Meeting',
                permission: accessScope?.appointment?.create === true
              },
              {
                title: 'Add Note',
                icon: 'document-text-outline',
                action: 'Note',
                permission:  accessScope?.notes?.create === true
              },
            ].filter(option => option.permission).map((option, index) => (
              <TouchableOpacity
                key={index}
                style={styles.optionItem}
                onPress={() => handleAddOptionPress(option.action)}
              >
                <View style={styles.addOptionIconContainer}>
                  <Ionicons
                    name={option.icon as any}
                    size={24}
                    color={colors.primary}
                  />
                </View>
                <Text style={styles.optionText}>
                  {option.title}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>
    </SafeAreaView>
  );
}

// Use ThemeColors type for createStyles
const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.secondary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: '#2E74BB',
    zIndex: 10,
    elevation: 4,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text.inverse,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    padding: 4,
  },
  optionsButton: {
    padding: 4,
  },
  segmentContainer: {
    flexDirection: 'row',
    backgroundColor: colors.background.primary,
    height: Platform.OS === 'ios' ? 50 : 56,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
    zIndex: 10,
    elevation: 4,
  },
  segmentButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    position: 'relative',
    ...Platform.select({
      android: {
        elevation: 0,
        paddingVertical: 2,
      }
    }),
  },
  segmentText: {
    fontSize: 14,
    color: colors.text.secondary,
  },
  activeSegment: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
  },
  activeSegmentText: {
    color: colors.primary,
    fontWeight: '500',
  },
  contentContainer: {
    flex: 1,
    backgroundColor: colors.background.secondary,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.primary,
    borderRadius: 8,
    margin: 15,
    paddingHorizontal: 10,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  searchIcon: {
    marginRight: 8,
    color: colors.text.secondary,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 10,
    fontSize: 15,
    color: colors.text.primary,
  },
  clearSearchButton: {
    padding: 4,
  },
  toggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.background.primary,
    padding: 15,
    marginHorizontal: 15,
    marginBottom: 15,
    borderRadius: 8,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  toggleLabel: {
    fontSize: 15,
    color: colors.text.primary,
  },
  sectionContainer: {
    backgroundColor: colors.background.primary,
    marginHorizontal: 15,
    marginBottom: 15,
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  sectionHeader: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.primary,
    backgroundColor: colors.background.secondary,
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
  },
  fieldRow: {
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
  },
  fieldLabel: {
    fontSize: 13,
    color: colors.text.secondary,
    marginBottom: 4,
  },
  fieldValue: {
    fontSize: 15,
    color: colors.text.primary,
  },
  emptyValue: {
    color: colors.text.tertiary,
  },
  fieldValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  requiredAsterisk: {
    color: '#FF3B30',
    fontWeight: 'bold',
  },
  activitiesContainer: {
    flex: 1,
    backgroundColor: colors.background.secondary,
  },
  quickActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 16,
    backgroundColor: colors.background.primary,
    marginBottom: 8,
  },
  quickAction: {
    alignItems: 'center',
    width: 100,
  },
  quickActionIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.background.tertiary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  actionText: {
    fontSize: 14,
    color: colors.text.primary,
  },
  activitiesListContainer: {
    backgroundColor: colors.background.primary,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
  },
  activityText: {
    fontSize: 16,
    color: colors.text.primary,
    flex: 1,
    marginLeft: 16,
  },
  chevron: {
    marginLeft: 8,
  },
  moreContainer: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  moreItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
  },
  moreItemText: {
    fontSize: 16,
    color: colors.text.primary,
    flex: 1,
    marginLeft: 16,
  },
  fab: {
    position: 'absolute',
    right: 16,
    bottom: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 999,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      },
      android: {
        elevation: 8,
        overflow: 'hidden', // Helps with Android ripple effect
      }
    }),
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  optionsContainer: {
    backgroundColor: colors.background.primary,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingVertical: 8,
    ...Platform.select({
      android: {
        elevation: 24,
        paddingBottom: 16,
      }
    }),
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  optionText: {
    fontSize: 16,
    color: colors.text.primary,
    marginLeft: 16,
  },
  destructiveText: {
    color: '#FF3B30',
  },
  dragIndicator: {
    height: 4,
    backgroundColor: colors.background.tertiary,
    borderRadius: 2,
    alignSelf: 'center',
    marginBottom: 8,
  },
  bottomSheetTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text.primary,
    marginBottom: 16,
    alignSelf: 'center',
  },
  addOptionIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.background.tertiary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    color: colors.text.tertiary,
    marginBottom: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
    color: colors.text.secondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: colors.text.secondary,
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: colors.text.inverse,
    fontSize: 16,
    fontWeight: 'bold',
  },
  disabledItem: {
    opacity: 0.7,
  },
  disabledText: {
    color: colors.text.tertiary,
  },
  requiredField: {
    fontWeight: '500',
  },
  requiredAsterisk: {
    color: '#FF3B30',
    fontWeight: 'bold',
  },
  dealsInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dealsInfoText: {
    fontSize: 12,
    color: colors.text.secondary,
    marginRight: 8,
  },
  detailsContainer: {
    flex: 1,
    backgroundColor: colors.background.primary,
  } as ViewStyle,
  quickActionsBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    backgroundColor: colors.background.primary,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
  } as ViewStyle,
  quickActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: colors.background.secondary,
  } as ViewStyle,
  quickActionIconWrapper: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors.background.tertiary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  } as ViewStyle,
  quickActionText: {
    fontSize: 14,
    color: colors.text.primary,
    fontWeight: '500',
  } as TextStyle,
  linkText: {
    color: colors.primary,
    textDecorationLine: 'underline',
  },
});