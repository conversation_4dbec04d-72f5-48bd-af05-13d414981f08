import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { ContactField, ContactFieldsResponse } from '../models/contact_fields_model';
import { DynamicFormField } from './DynamicFormField';

interface DynamicFormProps {
  fields: ContactField[];
  initialValues?: Record<string, string>;
  onSubmit: (values: Record<string, string>) => void;
}

export const DynamicForm: React.FC<DynamicFormProps> = ({
  fields,
  initialValues = {},
  onSubmit,
}) => {
  const [values, setValues] = useState<Record<string, string>>(initialValues);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleChange = (fieldName: string, value: string) => {
    setValues((prev) => ({
      ...prev,
      [fieldName]: value,
    }));
    // Clear error when user starts typing
    if (errors[fieldName]) {
      setErrors((prev) => ({
        ...prev,
        [fieldName]: '',
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    let isValid = true;

    fields.forEach((field) => {
      if (field.required === 'on' && !values[field.field_name]) {
        newErrors[field.field_name] = `${field.field_label} is required`;
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      onSubmit(values);
    }
  };

  return (
    <ScrollView style={styles.container}>
      {fields.map((field) => (
        <DynamicFormField
          key={field.id}
          field={field}
          value={values[field.field_name] || ''}
          onChangeText={(value) => handleChange(field.field_name, value)}
          error={errors[field.field_name]}
        />
      ))}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
}); 