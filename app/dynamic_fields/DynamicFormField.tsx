import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, StyleSheet, TouchableOpacity, Platform, Modal, FlatList, Pressable, Switch, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ContactField, FIELD_TYPES, Choice } from '../models/contact_fields_model';
import { useColors } from '@/hooks/useThemeColor';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format } from 'date-fns';
import { capitalizeWords } from '../ui/utils';
const getFieldMaxLength = (field: ContactField): number => {
  switch (field.field_type) {
    case FIELD_TYPES.NUMBER:
      if(field.field_label.includes("Telephone")||field.field_label.includes("Mobile")||field.field_label.includes("Phone")||field.field_label.includes("contact")){
        return 10;
      }else{
        return 255;
      };
    case FIELD_TYPES.TEXTAREA:
      return 1000;
    case FIELD_TYPES.TEXT:
    default:
      return 255;
  }
};
interface DynamicFormFieldProps {
  field: ContactField;
  value: string;
  onChangeText: (value: string) => void;
  error?: string;
  showRequired?: boolean;
  hasInteracted?: boolean;
  showAllFields?: boolean; // New prop to control visibility of quick_add "off" fields
}
export const DynamicFormField: React.FC<DynamicFormFieldProps> = ({
  field,
  value,
  onChangeText,
  error,
  showRequired = true,
  hasInteracted = false,
  showAllFields = false
}) => {
  // Return null if quick_add is not "on" and showAllFields is false
  if (field.quick_add !== "on" && !showAllFields) {
    return null;
  }
  const colors = useColors();
  const styles = createStyles(colors);
  const [isBottomSheetVisible, setIsBottomSheetVisible] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date>(value ? new Date(value) : new Date());
  const [selectedMultiValues, setSelectedMultiValues] = useState<string[]>(value ? value.split(',') : []);
  const [searchQuery, setSearchQuery] = useState('');
  const selectedChoice = field.choices?.find(choice => choice.id.toString() === value);
  const isRequired = field.required === 'on';
  const isEmpty = !value || value.trim() === '';
  // Only show error if the field is required, empty, and user has interacted with the form or there's an explicit error
  const showError = isRequired && isEmpty && showRequired && (hasInteracted || !!error);
  useEffect(() => {
    console.log(`Field: ${field.field_label}, Type: ${field.field_type}, Lookup Type: ${field.lookup_type}, Lookup Column: ${field.lookup_column}`);
    if (field.field_type === FIELD_TYPES.LOOKUP) {
      console.log('LOOKUP field detected:', field);
    }
  }, [field]);
  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setSelectedDate(selectedDate);
      onChangeText(format(selectedDate, 'yyyy-MM-dd'));
    }
  };
  const handleTimeChange = (event: any, selectedTime?: Date) => {
    setShowTimePicker(Platform.OS === 'ios');
    if (selectedTime) {
      setSelectedDate(selectedTime);
      onChangeText(format(selectedTime, 'HH:mm:ss'));
    }
  };
  const handleDateTimeChange = (event: any, selectedDateTime?: Date) => {
    setShowDatePicker(Platform.OS === 'ios');
    if (selectedDateTime) {
      setSelectedDate(selectedDateTime);
      onChangeText(format(selectedDateTime, 'yyyy-MM-dd HH:mm:ss'));
    }
  };
  const handleMultiSelectChange = (itemId: string) => {
    let newValues: string[];
    if (selectedMultiValues.includes(itemId)) {
      newValues = selectedMultiValues.filter(id => id !== itemId);
    } else {
      newValues = [...selectedMultiValues, itemId];
    }
    setSelectedMultiValues(newValues);
    onChangeText(newValues.join(','));
  };
  // Function to filter choices based on search query
  const getFilteredChoices = (choices: Choice[] = []) => {
    if (!searchQuery.trim()) return choices;
    
    return choices.filter(choice => 
      choice.custom_option.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };
  const renderField = () => {
    console.log("Field type:", field.field_type, "Label:", field.field_label, "Choices:", field.choices?.length || 0);

    // Normalize field type to handle case variations and different naming conventions
    const normalizedFieldType = field.field_type.toLowerCase().trim();
    console.log("Normalized field type:", normalizedFieldType);

    switch (normalizedFieldType) {
      case 'dropdown':
      case 'lookup':
        const showSearch = (field.choices?.length || 0) > 10;
        const filteredChoices = showSearch ? getFilteredChoices(field.choices) : field.choices;
        
        return (
          <View style={styles.container}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{field.field_label}</Text>
              {isRequired && <Text style={styles.required}>*</Text>}
              {field.read_only === 'on' && <Text style={styles.readOnlyLabel}> (Read Only)</Text>}
            </View>
            <TouchableOpacity 
              style={[
                styles.dropdownButton,
                field.read_only === 'on' && styles.readOnlyInput,
                showError && styles.inputError
              ]}
              onPress={() => {
                if (field.read_only !== 'on') {
                  setIsBottomSheetVisible(true);
                  setSearchQuery('');
                }
              }}
              disabled={field.read_only === 'on'}
            >
              <Text style={[
                styles.dropdownButtonText,
                !selectedChoice && styles.placeholderText
              ]}>
                {selectedChoice?.custom_option || field.placeholder || "Select option"}
              </Text>
              <Ionicons 
                name="chevron-down" 
                size={20} 
                color={colors.text.secondary} 
              />
            </TouchableOpacity>
            <Modal
              visible={isBottomSheetVisible}
              transparent={true}
              animationType="slide"
              onRequestClose={() => {
                setIsBottomSheetVisible(false);
                setSearchQuery('');
              }}
            >
              <TouchableOpacity 
                style={styles.modalOverlay}
                activeOpacity={1}
                onPress={() => {
                  setIsBottomSheetVisible(false);
                  setSearchQuery('');
                }}
              >
                <View style={styles.bottomSheet}>
                  <View style={styles.bottomSheetHeader}>
                    <Text style={styles.bottomSheetTitle}>{field.field_label}</Text>
                    <TouchableOpacity 
                      onPress={() => {
                        setIsBottomSheetVisible(false);
                        setSearchQuery('');
                      }}
                      style={styles.closeButton}
                    >
                      <Ionicons name="close" size={24} color={colors.text.primary} />
                    </TouchableOpacity>
                  </View>
                  
                  {showSearch && (
                    <View style={styles.searchContainer}>
                      <TextInput
                        style={styles.searchInput}
                        placeholder="Search..."
                        value={searchQuery}
                        onChangeText={setSearchQuery}
                        placeholderTextColor={colors.text.tertiary}
                        autoCapitalize="none"
                      />
                    </View>
                  )}
                  
                  {field.choices && field.choices.length > 0 ? (
                    <>
                      {filteredChoices && filteredChoices.length > 0 ? (
                        <FlatList
                          data={filteredChoices}
                          keyExtractor={(item) => item.id.toString()}
                          renderItem={({ item }) => (
                            <Pressable
                              style={[
                                styles.optionItem,
                                item.id.toString() === value && styles.selectedOption
                              ]}
                              onPress={() => {
                                onChangeText(item.id.toString());
                                setIsBottomSheetVisible(false);
                                setSearchQuery('');
                              }}
                            >
                              <Text style={[
                                styles.optionText,
                                item.id.toString() === value && styles.selectedOptionText
                              ]}>
                                {capitalizeWords(item.custom_option)}
                              </Text>
                              {item.id.toString() === value && (
                                <Ionicons name="checkmark" size={24} color={colors.primary} />
                              )}
                            </Pressable>
                          )}
                        />
                      ) : (
                        <View style={styles.emptyChoicesContainer}>
                          <Text style={styles.emptyChoicesText}>No matches found</Text>
                        </View>
                      )}
                    </>
                  ) : (
                    <View style={styles.emptyChoicesContainer}>
                      <Text style={styles.emptyChoicesText}>No options available</Text>
                    </View>
                  )}
                </View>
              </TouchableOpacity>
            </Modal>
          </View>
        );
     
      case 'date':
      case 'datepicker':
      case 'date picker':
          console.log("Field_type_inside_date:",field.field_type);
        return (
          <View style={styles.container}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{field.field_label}</Text>
              {isRequired && <Text style={styles.required}>*</Text>}
              {field.read_only === 'on' && <Text style={styles.readOnlyLabel}> (Read Only)</Text>}
            </View>
            <TouchableOpacity 
              style={[
                styles.datePickerButton,
                field.read_only === 'on' && styles.readOnlyInput
              ]}
              onPress={() => field.read_only !== 'on' && setShowDatePicker(true)}
              disabled={field.read_only === 'on'}
            >
              <Text style={styles.datePickerText}>
                {value ? format(new Date(value), 'MM/dd/yyyy') : field.placeholder || 'Select date'}
              </Text>
              <Ionicons name="calendar" size={20} color={colors.text.secondary} />
            </TouchableOpacity>
            
            {showDatePicker && (
              <DateTimePicker
                value={selectedDate}
                mode="date"
                display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                onChange={handleDateChange}
                minimumDate={new Date()}
              />
            )}
          </View>
        );
      case 'time':
        return (
          <View style={styles.container}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{field.field_label}</Text>
              {isRequired && <Text style={styles.required}>*</Text>}
              {field.read_only === 'on' && <Text style={styles.readOnlyLabel}> (Read Only)</Text>}
            </View>
            <TouchableOpacity 
              style={[
                styles.datePickerButton,
                field.read_only === 'on' && styles.readOnlyInput
              ]}
              onPress={() => field.read_only !== 'on' && setShowTimePicker(true)}
              disabled={field.read_only === 'on'}
            >
              <Text style={styles.datePickerText}>
                {value ? format(new Date(`2000-01-01T${value}`), 'hh:mm a') : field.placeholder || 'Select time'}
              </Text>
              <Ionicons name="time" size={20} color={colors.text.secondary} />
            </TouchableOpacity>
            
            {showTimePicker && (
              <DateTimePicker
                value={selectedDate}
                mode="time"
                display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                onChange={handleTimeChange}
              />
            )}
          </View>
        );
      case 'datetime':
      case 'date and time':
        return (
          <View style={styles.container}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{field.field_label}</Text>
              {isRequired && <Text style={styles.required}>*</Text>}
              {field.read_only === 'on' && <Text style={styles.readOnlyLabel}> (Read Only)</Text>}
            </View>
            <TouchableOpacity 
              style={[
                styles.datePickerButton,
                field.read_only === 'on' && styles.readOnlyInput
              ]}
              onPress={() => field.read_only !== 'on' && setShowDatePicker(true)}
              disabled={field.read_only === 'on'}
            >
              <Text style={styles.datePickerText}>
                {value ? format(new Date(value), 'MM/dd/yyyy hh:mm a') : field.placeholder || 'Select date & time'}
              </Text>
              <Ionicons name="calendar" size={20} color={colors.text.secondary} />
            </TouchableOpacity>
            
            {showDatePicker && (
              <DateTimePicker
                value={selectedDate}
                mode={Platform.OS === 'ios' ? 'datetime' : 'date'}
                display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                onChange={handleDateTimeChange}
                minimumDate={new Date()}
              />
            )}
          </View>
        );
      case 'checkbox':
      case 'boolean':
        return (
          <View style={styles.container}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{field.field_label}</Text>
              {isRequired && <Text style={styles.required}>*</Text>}
              {field.read_only === 'on' && <Text style={styles.readOnlyLabel}> (Read Only)</Text>}
            </View>
            <View style={styles.checkboxContainer}>
              <Switch
                value={value === 'true'}
                onValueChange={(newValue) => onChangeText(newValue ? 'true' : 'false')}
                disabled={field.read_only === 'on'}
                trackColor={{ false: colors.background.tertiary, true: colors.primary }}
                thumbColor={Platform.OS === 'ios' ? undefined : colors.background.primary}
              />
              <Text style={styles.checkboxLabel}>{field.placeholder || 'Yes/No'}</Text>
            </View>
          </View>
        );
      case 'radio':
        const showRadioSearch = (field.choices?.length || 0) > 10;
        const filteredRadioChoices = showRadioSearch ? getFilteredChoices(field.choices) : field.choices;
        
        if (showRadioSearch) {
          // For many options, show a modal with search like dropdown
          return (
            <View style={styles.container}>
              <View style={styles.labelContainer}>
                <Text style={styles.label}>{field.field_label}</Text>
                {isRequired && <Text style={styles.required}>*</Text>}
                {field.read_only === 'on' && <Text style={styles.readOnlyLabel}> (Read Only)</Text>}
              </View>
              <TouchableOpacity 
                style={[
                  styles.dropdownButton,
                  field.read_only === 'on' && styles.readOnlyInput
                ]}
                onPress={() => {
                  if (field.read_only !== 'on') {
                    setIsBottomSheetVisible(true);
                    setSearchQuery('');
                  }
                }}
                disabled={field.read_only === 'on'}
              >
                <Text style={[
                  styles.dropdownButtonText,
                  !value && styles.placeholderText
                ]}>
                  {selectedChoice?.custom_option || field.placeholder || "Select option"}
                </Text>
                <Ionicons 
                  name="chevron-down" 
                  size={20} 
                  color={colors.text.secondary} 
                />
              </TouchableOpacity>
              <Modal
                visible={isBottomSheetVisible}
                transparent={true}
                animationType="slide"
                onRequestClose={() => {
                  setIsBottomSheetVisible(false);
                  setSearchQuery('');
                }}
              >
                <TouchableOpacity 
                  style={styles.modalOverlay}
                  activeOpacity={1}
                  onPress={() => {
                    setIsBottomSheetVisible(false);
                    setSearchQuery('');
                  }}
                >
                  <View style={styles.bottomSheet}>
                    <View style={styles.bottomSheetHeader}>
                      <Text style={styles.bottomSheetTitle}>{field.field_label}</Text>
                      <TouchableOpacity 
                        onPress={() => {
                          setIsBottomSheetVisible(false);
                          setSearchQuery('');
                        }}
                        style={styles.closeButton}
                      >
                        <Ionicons name="close" size={24} color={colors.text.primary} />
                      </TouchableOpacity>
                    </View>
                    
                    <View style={styles.searchContainer}>
                      <TextInput
                        style={styles.searchInput}
                        placeholder="Search..."
                        value={searchQuery}
                        onChangeText={setSearchQuery}
                        placeholderTextColor={colors.text.tertiary}
                        autoCapitalize="none"
                      />
                    </View>
                    
                    {filteredRadioChoices && filteredRadioChoices.length > 0 ? (
                      <FlatList
                        data={filteredRadioChoices}
                        keyExtractor={(item) => item.id.toString()}
                        renderItem={({ item }) => (
                          <Pressable
                            style={[
                              styles.optionItem,
                              item.id.toString() === value && styles.selectedOption
                            ]}
                            onPress={() => {
                              onChangeText(item.id.toString());
                              setIsBottomSheetVisible(false);
                              setSearchQuery('');
                            }}
                          >
                            <View style={styles.radioItemContainer}>
                              <View style={[
                                styles.radioOuter,
                                field.read_only === 'on' && styles.readOnlyInput
                              ]}>
                                {item.id.toString() === value && (
                                  <View style={styles.radioInner} />
                                )}
                              </View>
                              <Text style={[
                                styles.radioLabel,
                                item.id.toString() === value && styles.selectedOptionText
                              ]}>
                                {capitalizeWords(item.custom_option)}
                              </Text>
                            </View>
                          </Pressable>
                        )}
                      />
                    ) : (
                      <View style={styles.emptyChoicesContainer}>
                        <Text style={styles.emptyChoicesText}>No matches found</Text>
                      </View>
                    )}
                  </View>
                </TouchableOpacity>
              </Modal>
            </View>
          );
        } else {
          // For few options, show traditional radio buttons
          return (
            <View style={styles.container}>
              <View style={styles.labelContainer}>
                <Text style={styles.label}>{field.field_label}</Text>
                {isRequired && <Text style={styles.required}>*</Text>}
                {field.read_only === 'on' && <Text style={styles.readOnlyLabel}> (Read Only)</Text>}
              </View>
              <View>
                {field.choices?.map((choice) => (
                  <TouchableOpacity 
                    key={choice.id}
                    style={styles.radioContainer}
                    onPress={() => field.read_only !== 'on' && onChangeText(choice.id.toString())}
                    disabled={field.read_only === 'on'}
                  >
                    <View style={[
                      styles.radioOuter,
                      field.read_only === 'on' && styles.readOnlyInput
                    ]}>
                      {value === choice.id.toString() && (
                        <View style={styles.radioInner} />
                      )}
                    </View>
                    <Text style={styles.radioLabel}>{capitalizeWords(choice.custom_option)}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          );
        }
      case 'multiselect':
      case 'multi select':
        const showMultiSearch = (field.choices?.length || 0) > 10;
        const filteredMultiChoices = showMultiSearch ? getFilteredChoices(field.choices) : field.choices;
        
        return (
          <View style={styles.container}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{field.field_label}</Text>
              {isRequired && <Text style={styles.required}>*</Text>}
              {field.read_only === 'on' && <Text style={styles.readOnlyLabel}> (Read Only)</Text>}
            </View>
            <TouchableOpacity 
              style={[
                styles.dropdownButton,
                field.read_only === 'on' && styles.readOnlyInput
              ]}
              onPress={() => {
                if (field.read_only !== 'on') {
                  setIsBottomSheetVisible(true);
                  setSearchQuery('');
                }
              }}
              disabled={field.read_only === 'on'}
            >
              <Text style={[
                styles.dropdownButtonText,
                !selectedMultiValues.length && styles.placeholderText
              ]}>
                {selectedMultiValues.length 
                  ? `${selectedMultiValues.length} selected` 
                  : field.placeholder || "Select options"}
              </Text>
              <Ionicons 
                name="chevron-down" 
                size={20} 
                color={colors.text.secondary} 
              />
            </TouchableOpacity>
            <Modal
              visible={isBottomSheetVisible}
              transparent={true}
              animationType="slide"
              onRequestClose={() => {
                setIsBottomSheetVisible(false);
                setSearchQuery('');
              }}
            >
              <TouchableOpacity 
                style={styles.modalOverlay}
                activeOpacity={1}
                onPress={() => {
                  setIsBottomSheetVisible(false);
                  setSearchQuery('');
                }}
              >
                <View style={styles.bottomSheet}>
                  <View style={styles.bottomSheetHeader}>
                    <Text style={styles.bottomSheetTitle}>{field.field_label}</Text>
                    <TouchableOpacity 
                      onPress={() => {
                        setIsBottomSheetVisible(false);
                        setSearchQuery('');
                      }}
                      style={styles.closeButton}
                    >
                      <Ionicons name="close" size={24} color={colors.text.primary} />
                    </TouchableOpacity>
                  </View>
                  
                  {showMultiSearch && (
                    <View style={styles.searchContainer}>
                      <TextInput
                        style={styles.searchInput}
                        placeholder="Search..."
                        value={searchQuery}
                        onChangeText={setSearchQuery}
                        placeholderTextColor={colors.text.tertiary}
                        autoCapitalize="none"
                      />
                    </View>
                  )}
                  
                  {field.choices && field.choices.length > 0 ? (
                    <>
                      {filteredMultiChoices && filteredMultiChoices.length > 0 ? (
                        <FlatList
                          data={filteredMultiChoices}
                          keyExtractor={(item) => item.id.toString()}
                          renderItem={({ item }) => (
                            <Pressable
                              style={[
                                styles.optionItem,
                                selectedMultiValues.includes(item.id.toString()) && styles.selectedOption
                              ]}
                              onPress={() => handleMultiSelectChange(item.id.toString())}
                            >
                              <Text style={[
                                styles.optionText,
                                selectedMultiValues.includes(item.id.toString()) && styles.selectedOptionText
                              ]}>
                                {capitalizeWords(item.custom_option)}
                              </Text>
                              {selectedMultiValues.includes(item.id.toString()) && (
                                <Ionicons name="checkmark" size={24} color={colors.primary} />
                              )}
                            </Pressable>
                          )}
                        />
                      ) : (
                        <View style={styles.emptyChoicesContainer}>
                          <Text style={styles.emptyChoicesText}>No matches found</Text>
                        </View>
                      )}
                    </>
                  ) : (
                    <View style={styles.emptyChoicesContainer}>
                      <Text style={styles.emptyChoicesText}>No options available</Text>
                    </View>
                  )}
                  
                  <TouchableOpacity 
                    style={styles.doneButton}
                    onPress={() => {
                      setIsBottomSheetVisible(false);
                      setSearchQuery('');
                    }}
                  >
                    <Text style={styles.doneButtonText}>Done</Text>
                  </TouchableOpacity>
                </View>
              </TouchableOpacity>
            </Modal>
          </View>
        );
      case 'textarea':
      case 'text area':
        return (
          <View style={styles.container}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{field.field_label}</Text>
              {isRequired && <Text style={styles.required}>*</Text>}
              {field.read_only === 'on' && <Text style={styles.readOnlyLabel}> (Read Only)</Text>}
            </View>
            <TextInput
              style={[
                styles.textArea,
                (error || showError) && styles.inputError,
                field.read_only === 'on' && styles.readOnlyInput
              ]}
              value={value}
              onChangeText={onChangeText}
              placeholder={field.placeholder}
              multiline={true}
              numberOfLines={4}
              maxLength={getFieldMaxLength(field)}
              placeholderTextColor={colors.text.tertiary}
              editable={field.read_only !== 'on'}
            />
          </View>
        );
      case 'currency':
        return (
          <View style={styles.container}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{field.field_label}</Text>
              {isRequired && <Text style={styles.required}>*</Text>}
              {field.read_only === 'on' && <Text style={styles.readOnlyLabel}> (Read Only)</Text>}
            </View>
            <View style={styles.currencyContainer}>
              <Text style={styles.currencySymbol}>$</Text>
              <Text style={styles.currencySymbol}>₹</Text>
              <TextInput
                style={[
                  styles.currencyInput, 
                  (error || showError) && styles.inputError,
                  field.read_only === 'on' && styles.readOnlyInput
                ]}
                value={value}
                onChangeText={onChangeText}
                placeholder={field.placeholder}
                keyboardType="numeric"
                placeholderTextColor={colors.text.tertiary}
                editable={field.read_only !== 'on'}
              />
            </View>
          </View>
        );
      case 'percentage':
        return (
          <View style={styles.container}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{field.field_label}</Text>
              {isRequired && <Text style={styles.required}>*</Text>}
              {field.read_only === 'on' && <Text style={styles.readOnlyLabel}> (Read Only)</Text>}
            </View>
            <View style={styles.percentageContainer}>
              <TextInput
                style={[
                  styles.percentageInput,
                  (error || showError) && styles.inputError,
                  field.read_only === 'on' && styles.readOnlyInput
                ]}
                value={value}
                onChangeText={onChangeText}
                placeholder={field.placeholder}
                keyboardType="numeric"
                placeholderTextColor={colors.text.tertiary}
                editable={field.read_only !== 'on'}
              />
              <Text style={styles.percentageSymbol}>%</Text>
            </View>
          </View>
        );
      case 'url':
        return (
          <View style={styles.container}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{field.field_label}</Text>
              {isRequired && <Text style={styles.required}>*</Text>}
              {field.read_only === 'on' && <Text style={styles.readOnlyLabel}> (Read Only)</Text>}
            </View>
            <TextInput
              style={[
                styles.input, 
                (error || showError) && styles.inputError,
                field.read_only === 'on' && styles.readOnlyInput
              ]}
              value={value}
              onChangeText={onChangeText}
              placeholder={field.placeholder || "https://example.com"}
              keyboardType="url"
              autoCapitalize="none"
              placeholderTextColor={colors.text.tertiary}
              editable={field.read_only !== 'on'}
            />
          </View>
        );
      case 'email':
        return (
          <View style={styles.container}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{field.field_label}</Text>
              {isRequired && <Text style={styles.required}>*</Text>}
              {field.read_only === 'on' && <Text style={styles.readOnlyLabel}> (Read Only)</Text>}
            </View>
            <TextInput
              style={[
                styles.input, 
                (error || showError) && styles.inputError,
                field.read_only === 'on' && styles.readOnlyInput
              ]}
              value={value}
              onChangeText={onChangeText}
              placeholder={field.placeholder || "<EMAIL>"}
              keyboardType="email-address"
              autoCapitalize="none"
              placeholderTextColor={colors.text.tertiary}
              editable={field.read_only !== 'on'}
            />
          </View>
        );
      case 'phone':
        return (
          <View style={styles.container}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{field.field_label}</Text>
              {isRequired && <Text style={styles.required}>*</Text>}
              {field.read_only === 'on' && <Text style={styles.readOnlyLabel}> (Read Only)</Text>}
            </View>
            <TextInput
              style={[
                styles.input, 
                (error || showError) && styles.inputError,
                field.read_only === 'on' && styles.readOnlyInput
              ]}
              value={value}
              onChangeText={onChangeText}
              placeholder={field.placeholder || "(*************"}
              keyboardType="phone-pad"
              maxLength={10}
              placeholderTextColor={colors.text.tertiary}
              editable={field.read_only !== 'on'}
            />
          </View>
        );
      case 'password':
        return (
          <View style={styles.container}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{field.field_label}</Text>
              {isRequired && <Text style={styles.required}>*</Text>}
              {field.read_only === 'on' && <Text style={styles.readOnlyLabel}> (Read Only)</Text>}
            </View>
            <TextInput
              style={[
                styles.input, 
                (error || showError) && styles.inputError,
                field.read_only === 'on' && styles.readOnlyInput
              ]}
              value={value}
              onChangeText={onChangeText}
              placeholder={field.placeholder || "Enter password"}
              secureTextEntry={true}
              placeholderTextColor={colors.text.tertiary}
              editable={field.read_only !== 'on'}
            />
          </View>
        );
      case 'color':
        return (
          <View style={styles.container}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{field.field_label}</Text>
              {isRequired && <Text style={styles.required}>*</Text>}
              {field.read_only === 'on' && <Text style={styles.readOnlyLabel}> (Read Only)</Text>}
            </View>
            <View style={styles.colorPickerContainer}>
              <TextInput
                style={[
                  styles.colorInput, 
                  (error || showError) && styles.inputError,
                  field.read_only === 'on' && styles.readOnlyInput
                ]}
                value={value}
                onChangeText={onChangeText}
                placeholder={field.placeholder || "#RRGGBB"}
                placeholderTextColor={colors.text.tertiary}
                editable={field.read_only !== 'on'}
              />
              {value && (
                <View 
                  style={[
                    styles.colorPreview, 
                    { backgroundColor: value }
                  ]} 
                />
              )}
            </View>
          </View>
        );
      case 'rating':
        return (
          <View style={styles.container}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{field.field_label}</Text>
              {isRequired && <Text style={styles.required}>*</Text>}
              {field.read_only === 'on' && <Text style={styles.readOnlyLabel}> (Read Only)</Text>}
            </View>
            <View style={styles.ratingContainer}>
              {[1, 2, 3, 4, 5].map((rating) => (
                <TouchableOpacity
                  key={rating}
                  onPress={() => field.read_only !== 'on' && onChangeText(rating.toString())}
                  disabled={field.read_only === 'on'}
                >
                  <Ionicons
                    name={parseInt(value) >= rating ? "star" : "star-outline"}
                    size={24}
                    color={parseInt(value) >= rating ? "#FFD700" : colors.text.tertiary}
                  />
                </TouchableOpacity>
              ))}
            </View>
          </View>
        );
      case 'number':
      case 'text field':
      case 'text':
      default:
        console.log("Using default text input for field type:", normalizedFieldType);
        return (
          <View style={styles.container}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{field.field_label}</Text>
              {isRequired && <Text style={styles.required}>*</Text>}
              {field.read_only === 'on' && <Text style={styles.readOnlyLabel}> (Read Only)</Text>}
            </View>
            <TextInput
              style={[
                styles.input,
                (error || showError) && styles.inputError,
                field.read_only === 'on' && styles.readOnlyInput
              ]}
              value={value}
              onChangeText={onChangeText}
              placeholder={field.placeholder}
              maxLength={getFieldMaxLength(field)}
              keyboardType={normalizedFieldType === 'number' ? "numeric" : "default"}
              placeholderTextColor={colors.text.tertiary}
              editable={field.read_only !== 'on'}
            />
          </View>
        );
    }
  };
  return (
    <View style={styles.container}>
      {renderField()}
      {(error || showError) && (
        <Text style={styles.errorText}>
          {error || `${field.field_label} is required`}
        </Text>
      )}
      {/* 
      {field.tool_tip !== '-' && (
        <Text style={styles.tooltip}>{field.tool_tip}</Text>
      )}
      */}
    </View>
  );
};
const createStyles = (colors: ReturnType<typeof useColors>) => StyleSheet.create({
  container: {
    marginBottom: 4,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  label: {
    marginTop: 4,
    fontSize: 14,
    fontWeight: '500',
    color: colors.text.primary,
  },
  required: {
    color: 'red',
    marginLeft: 4,
  },
  input: {
    height: 40,
    borderWidth: 1,
    borderColor: colors.background.tertiary,
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: colors.background.primary,
    color: colors.text.primary,
  },
  inputError: {
    borderColor: 'red',
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: 40,
    borderWidth: 1,
    borderColor: colors.background.tertiary,
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: colors.background.primary,
  },
  dropdownButtonText: {
    fontSize: 16,
    color: colors.text.primary,
    flex: 1,
    fontWeight: '400',
  },
  placeholderText: {
    color: '#666666',
    fontSize: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  bottomSheet: {
    backgroundColor: colors.background.primary,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  bottomSheetHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
    backgroundColor: colors.background.primary,
  },
  bottomSheetTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text.primary,
    letterSpacing: 0.5,
  },
  closeButton: {
    padding: 4,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
  },
  selectedOption: {
    backgroundColor: colors.background.secondary,
  },
  optionText: {
    fontSize: 16,
    color: colors.text.primary,
    fontWeight: '400',
    paddingVertical: 4,
  },
  selectedOptionText: {
    color: colors.primary,
    fontWeight: '500',
  },
  errorText: {
    color: 'red',
    fontSize: 12,
  
  },
  tooltip: {
    fontSize: 12,
    color: colors.text.tertiary,
  
  },
  readOnlyInput: {
    backgroundColor: colors.background.tertiary,
    opacity: 0.8,
  },
  readOnlyLabel: {
    fontSize: 12,
    color: colors.text.tertiary,
    marginLeft: 4,
    fontStyle: 'italic',
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: 40,
    borderWidth: 1,
    borderColor: colors.background.tertiary,
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: colors.background.primary,
  },
  datePickerText: {
    fontSize: 16,
    color: colors.text.primary,
  },
  textArea: {
    minHeight: 100,
    borderWidth: 1,
    borderColor: colors.background.tertiary,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: colors.background.primary,
    color: colors.text.primary,
    textAlignVertical: 'top',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkboxLabel: {
    marginLeft: 8,
    fontSize: 16,
    color: colors.text.primary,
  },
  radioContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  radioOuter: {
    height: 20,
    width: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioInner: {
    height: 10,
    width: 10,
    borderRadius: 5,
    backgroundColor: colors.primary,
  },
  radioLabel: {
    marginLeft: 8,
    fontSize: 16,
    color: colors.text.primary,
  },
  doneButton: {
    backgroundColor: colors.primary,
    padding: 16,
    alignItems: 'center',
    margin: 16,
    borderRadius: 8,
  },
  doneButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
  currencyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currencySymbol: {
    fontSize: 16,
    color: colors.text.primary,
    marginRight: 4,
  },
  currencyInput: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderColor: colors.background.tertiary,
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: colors.background.primary,
    color: colors.text.primary,
  },
  percentageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  percentageInput: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderColor: colors.background.tertiary,
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: colors.background.primary,
    color: colors.text.primary,
  },
  percentageSymbol: {
    fontSize: 16,
    color: colors.text.primary,
    marginLeft: 4,
  },
  colorPickerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  colorInput: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderColor: colors.background.tertiary,
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: colors.background.primary,
    color: colors.text.primary,
  },
  colorPreview: {
    width: 30,
    height: 30,
    borderRadius: 15,
    marginLeft: 8,
    borderWidth: 1,
    borderColor: colors.background.tertiary,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  emptyChoicesContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyChoicesText: {
    fontSize: 16,
    color: colors.text.tertiary,
    fontStyle: 'italic',
  },
  searchContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
    backgroundColor: colors.background.primary,
  },
  searchInput: {
    height: 40,
    backgroundColor: colors.background.secondary,
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
    color: colors.text.primary,
  },
  radioItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
}); 