import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  StatusBar,
  Platform,
  Dimensions,
  Alert,
  Animated,
  Easing
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Appbar } from 'react-native-paper';
import { apiService } from '../services/ApiService';
import Modal from 'react-native-modal';
import { Image } from 'expo-image';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

// MarqueeText component types
interface MarqueeTextProps {
  text: string;
  style: any;
  containerWidth: number;
}

// MarqueeText component for horizontally scrolling text
const MarqueeText = ({ text, style, containerWidth }: MarqueeTextProps) => {
  const scrollX = useRef(new Animated.Value(0)).current;
  const textWidth = useRef(0);
  const [shouldScroll, setShouldScroll] = useState(false);

  const startScrolling = () => {
    if (shouldScroll) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(scrollX, {
            toValue: -textWidth.current,
            duration: 8000,
            easing: Easing.linear,
            useNativeDriver: true,
          }),
          Animated.timing(scrollX, {
            toValue: containerWidth,
            duration: 0,
            useNativeDriver: true,
          }),
        ])
      ).start();
    }
  };

  return (
    <View style={[styles.marqueeContainer, { width: containerWidth }]}>
      <Animated.Text
        style={[style, { transform: [{ translateX: scrollX }] }]}
        onLayout={(event) => {
          const width = event.nativeEvent.layout.width;
          textWidth.current = width;
          setShouldScroll(width > containerWidth);
          startScrolling();
        }}
      >
        {text}
      </Animated.Text>
    </View>
  );
};

// Function to get date from record if available
const getDateFromRecord = (recordStr: string | undefined): string | null => {
  if (!recordStr) return null;
  try {
    const record = JSON.parse(recordStr);
    return record.conveyance_date || null;
  } catch (error) {
    console.error('Error parsing record:', error);
    return null;
  }
};

// Function to format date for API
const formatDateForApi = (dateStr: string, recordStr: string | undefined): string => {
  try {
    // First try to get date from record as it contains the full date with year
    const recordDate = getDateFromRecord(recordStr);
    if (recordDate) {
      // Convert from DD/MM/YYYY to YYYY-MM-DD
      const [day, month, year] = recordDate.split('/');
      return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
    }

    // Fallback to formatting from list view date
    const [datePart] = dateStr.split(' '); // Get "DD/MM" from "DD/MM Day"
    const [day, month] = datePart.split('/');

    // Get current year
    const currentYear = new Date().getFullYear();

    // Create a date object for comparison
    const dateToCheck = new Date(currentYear, parseInt(month) - 1, parseInt(day));
    const today = new Date();

    // If the date has passed for this year, use next year
    const year = dateToCheck < today ? currentYear + 1 : currentYear;

    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  } catch (error) {
    console.error('Error formatting date:', error, 'Input date:', dateStr);
    return dateStr;
  }
};

interface ConveyanceActivity {
  id: number;
  activity_name: string;
  start_time: string;
  end_time: string;
  transport_mode: string | null;
  distance: number;
  rate_per_km: number;
  claim_amount: number;
  claim_status: string;
  target_name: string;
  target_type: string;
  remarks: string | null;
  claim_image?: string;
  checkin_latitude: string;
  checkin_longitude: string;
  checkout_latitude: string;
  checkout_longitude: string;
}

interface ConveyanceResponse {
  date: string;
  activities: ConveyanceActivity[];
  transport_modes: Array<{
    mode: string;
    rate: number;
  }>;
  summary: {
    total_distance: string;
    total_amount: string;
  };
}

export default function ConveyanceDetailsScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const date = params.date as string;
  const recordStr = params.record as string | undefined;
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<ConveyanceResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [selectedActivity, setSelectedActivity] = useState<ConveyanceActivity | null>(null);
  const [isDetailsModalVisible, setIsDetailsModalVisible] = useState(false);

  const fetchConveyanceDetails = async (formattedDate: string) => {
    try {
      setLoading(true);
      setError(null);

      console.log('Fetching conveyance details for date:', formattedDate);

      const response = await apiService.post<{
        status: number;
        success: boolean;
        message: string;
        response: ConveyanceResponse;
      }>(
        'conveyance/view',
        { date: formattedDate }
      );

      console.log('API Response:', response.data);

      if (response.status === 200 && response.data.success) {
        setData(response.data.response);
      } else {
        setError(response.data.message || 'Failed to fetch conveyance details');
      }
    } catch (err: any) {
      console.error('Error fetching conveyance details:', err);
      setError(err.message || 'An error occurred while fetching data');
      Alert.alert(
        'Error',
        err.message || 'Failed to load conveyance details. Please try again later.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  // Effect to fetch data when component mounts
  useEffect(() => {
    if (date) {
      const formattedDate = formatDateForApi(date, recordStr);
      console.log('Original date:', date);
      console.log('Record string:', recordStr);
      console.log('Formatted date for API:', formattedDate);
      fetchConveyanceDetails(formattedDate);
    }
  }, [date, recordStr]);

  // Refresh data when screen comes into focus (e.g., after updating a claim)
  useFocusEffect(
    useCallback(() => {
      if (date) {
        const formattedDate = formatDateForApi(date, recordStr);
        console.log('Screen focused - refreshing conveyance details');
        fetchConveyanceDetails(formattedDate);
      }
    }, [date, recordStr])
  );

  const handleBack = () => {
    router.back();
  };

  // Define a helper function to map transport modes to icon names.
  const getIconName = (mode: string | null) => {
    if (!mode) return 'car'; // Fallback icon if mode is missing

    // Normalize the input to lowercase for a case-insensitive comparison
    const normalizedMode = mode.toLowerCase();

    switch (normalizedMode) {
      case 'bike':
        return 'bicycle';
      case 'car':
        return 'car';
      case 'public transport':
        // Depending on your preference, you could use 'bus', 'train' or another icon.
        return 'train';
      case 'auto':
        // If there's a specific icon for autos available, use it; otherwise, you can fall back to 'car'
        return 'car';
      case 'bus':
        return 'bus';
      default:
        return 'car'; // Default icon if the mode doesn't match any case
    }
  };

  const renderTableHeader = () => (
    <View style={styles.tableHeader}>
      <Text style={[styles.headerCell, { width: SCREEN_WIDTH * 0.08 }]}>#</Text>
      <Text style={[styles.headerCell, { width: SCREEN_WIDTH * 0.20 }]}>Check In</Text>
      <Text style={[styles.headerCell, { width: SCREEN_WIDTH * 0.20 }]}>Check Out</Text>
      <Text style={[styles.headerCell, { width: SCREEN_WIDTH * 0.12 }]}>Mode</Text>
      <Text style={[styles.headerCell, { width: SCREEN_WIDTH * 0.12 }]}>Kms</Text>
      <Text style={[styles.headerCell, { width: SCREEN_WIDTH * 0.22 }]}>Amount</Text>
    </View>
  );

  const renderTableRow = (item: ConveyanceActivity, index: number) => {
    const handlePress = () => {
      console.log('Row pressed:', item);
      handleActivityPress(item);
    };

    return (
      <TouchableOpacity
        style={styles.tableRow}
        key={item.id}
        onPress={handlePress}
        activeOpacity={0.7}
      >
        <View style={[styles.cell, { width: SCREEN_WIDTH * 0.08 }]}>
          <Text style={styles.cellText}>{index + 1}</Text>
        </View>
        <View style={[styles.cell, { width: SCREEN_WIDTH * 0.20 }]}>
          <MarqueeText
            text={item.start_time}
            style={styles.cellText}
            containerWidth={SCREEN_WIDTH * 0.22}
          />
        </View>
        <View style={[styles.cell, { width: SCREEN_WIDTH * 0.20 }]}>
          <MarqueeText
            text={item.end_time}
            style={styles.cellText}
            containerWidth={SCREEN_WIDTH * 0.22}
          />
        </View>
        <View style={[styles.cell, { width: SCREEN_WIDTH * 0.12, alignItems: 'center' }]}>
          <Ionicons
            name={getIconName(item.transport_mode)}
            size={20}
            color="#333"
          />
        </View>
        <View style={[styles.cell, { width: SCREEN_WIDTH * 0.12 }]}>
          <Text style={styles.cellText}>{item.distance.toFixed(2)}</Text>
        </View>
        <View style={[styles.cell, { width: SCREEN_WIDTH * 0.22 }]}>
          <Text style={styles.cellText}>₹{item.claim_amount}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  // Function to handle activity selection
  const handleActivityPress = (activity: ConveyanceActivity) => {
    setSelectedActivity(activity);
    setIsDetailsModalVisible(true);
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar backgroundColor="#0F96BB" barStyle="light-content" />
      <Stack.Screen options={{ headerShown: false }} />

      {/* App Bar */}
      <Appbar.Header style={styles.appbar} statusBarHeight={0}>
        <Appbar.BackAction onPress={handleBack} color="#FFFFFF" />
        <Appbar.Content
          title="Conveyance Details"
          titleStyle={styles.appbarTitle}
        />
      </Appbar.Header>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0F96BB" />
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => {
              const formattedDate = formatDateForApi(date, recordStr);
              fetchConveyanceDetails(formattedDate);
            }}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      ) : data ? (
        data.activities && data.activities.length > 0 ? (
          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            <View style={styles.dateContainer}>
              <View style={styles.dateRow}>
                <Text style={styles.dateLabel}>Date: </Text>
                <Text style={styles.dateText}>{date}</Text>
              </View>
            </View>

            <View style={styles.tableContainer}>
              {renderTableHeader()}
              {data.activities.map((activity, index) =>
                renderTableRow(activity, index)
              )}
            </View>

            <View style={styles.summaryContainer}>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Total Distance:</Text>
                <Text style={styles.summaryValue}>{data.summary.total_distance} km</Text>
              </View>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Total Amount:</Text>
                <Text style={styles.summaryValue}>₹{data.summary.total_amount}</Text>
              </View>
            </View>
          </ScrollView>
        ) : (
          <ScrollView
            style={styles.emptyStateWrapper}
            contentContainerStyle={styles.emptyScrollContent}
          >
            <View style={styles.emptyActivitiesContainer}>
              <Ionicons name="car-outline" size={64} color="#ccc" />
              <Text style={styles.emptyActivitiesText}>No conveyance records found</Text>
              <Text style={styles.emptyActivitiesSubtext}>
                No activities recorded for {date}
              </Text>
            </View>
          </ScrollView>
        )
      ) : (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No data available</Text>
        </View>
      )}

      {/* Details Modal */}
      <Modal
        isVisible={isDetailsModalVisible}
        onBackdropPress={() => setIsDetailsModalVisible(false)}
        style={styles.modal}
      >
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Task Details</Text>
            <TouchableOpacity onPress={() => setIsDetailsModalVisible(false)}>
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>

          {selectedActivity && (
            <>
              <View style={styles.taskDetails}>
                <Ionicons name="location" size={24} color="#0F96BB" />
                <Text style={styles.taskText}>
                  {selectedActivity.activity_name}
                </Text>
                <TouchableOpacity onPress={() => {
                  if (selectedActivity) {
                    const params = {
                      checkin_latitude: selectedActivity.checkin_latitude,
                      checkin_longitude: selectedActivity.checkin_longitude,
                      checkout_latitude: selectedActivity.checkout_latitude,
                      checkout_longitude: selectedActivity.checkout_longitude,
                      activity_name: selectedActivity.activity_name,
                      start_time: selectedActivity.start_time,
                      end_time: selectedActivity.end_time
                    };
                    setIsDetailsModalVisible(false);
                    router.push({
                      pathname: '/conveyanceMap/view' as any,
                      params
                    });
                  }
                }}>
                  <Ionicons name="map" size={24} color="#0F96BB" />
                </TouchableOpacity>
              </View>

              <View style={styles.claimDetails}>
                <View style={styles.claimRow}>
                  <Ionicons name="car" size={24} color="#666" />
                  <Text style={styles.claimLabel}>
                    {selectedActivity.transport_mode || 'No transport mode'}
                  </Text>
                  <Text style={styles.claimAmount}>
                    ₹{selectedActivity.claim_amount}
                  </Text>
                </View>
                <View style={styles.claimStatus}>
                  <Text>Status: </Text>
                  <Text style={[
                    styles.statusText,
                    { color: selectedActivity.claim_status === 'Pending' ? '#FFA500' : '#4CAF50' }
                  ]}>
                    {selectedActivity.claim_status}
                  </Text>
                </View>
                {selectedActivity.claim_image && (
                  <View style={styles.imagePreview}>
                    <Image
                      source={{ uri: selectedActivity.claim_image }}
                      style={styles.claimImage}
                      contentFit="cover"
                    />
                  </View>
                )}
              </View>

              {selectedActivity.claim_status === 'Pending' && (
                <TouchableOpacity
                  style={styles.editButton}
                  onPress={() => {
                    setIsDetailsModalVisible(false);
                    router.push({
                      pathname: 'updateClaim',
                      params: {
                        activityId: selectedActivity.id,
                        currentMode: selectedActivity.transport_mode || '',
                        currentAmount: selectedActivity.claim_amount.toString(),
                        currentRemarks: selectedActivity.remarks || '',
                        transportModes: JSON.stringify(data?.transport_modes || [])
                      }
                    } as any);
                  }}
                >
                  <Text style={styles.editButtonText}>Edit</Text>
                </TouchableOpacity>
              )}
            </>
          )}
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  appbar: {
    backgroundColor: '#0F96BB',
    elevation: Platform.OS === 'android' ? 4 : 0,
    shadowColor: Platform.OS === 'ios' ? '#000' : 'transparent',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: Platform.OS === 'ios' ? 0.1 : 0,
    shadowRadius: Platform.OS === 'ios' ? 3 : 0,
    height: Platform.OS === 'android' ? 56 : 44,
  },
  appbarTitle: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 15,
  },
  dateContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginBottom: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  dateRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  tableContainer: {
    backgroundColor: 'white',
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#f0f0f0',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
  },
  headerCell: {
    fontSize: 13,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: 'white',
  },
  cell: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 2,
  },
  cellText: {
    fontSize: 14,
    color: '#444',
    textAlign: 'center',
  },
  summaryContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginTop: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 15,
    color: '#666',
  },
  dateLabel: {
    fontSize: 18,
    color: '#000000',
    fontWeight: '500',
  },
  summaryValue: {
    fontSize: 15,
    fontWeight: 'bold',
    color: '#333',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#ff3b30',
    textAlign: 'center',
    marginBottom: 15,
  },
  retryButton: {
    backgroundColor: '#0F96BB',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  emptyStateWrapper: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  emptyScrollContent: {
    flexGrow: 1,
  },
  emptyActivitiesContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 20,
    minHeight: 400,
  },
  emptyActivitiesText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#555',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyActivitiesSubtext: {
    fontSize: 14,
    color: '#888',
    textAlign: 'center',
    lineHeight: 20,
  },
  marqueeContainer: {
    overflow: 'hidden',
  },
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  taskDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  taskText: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    marginLeft: 10,
  },
  claimDetails: {
    marginBottom: 20,
  },
  claimRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  claimLabel: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    marginLeft: 10,
  },
  claimAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  claimStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 16,
    marginLeft: 5,
  },
  imagePreview: {
    marginTop: 10,
    alignItems: 'center',
    width: 100,
    height: 100,
    borderRadius: 8,
    overflow: 'hidden',
  },
  claimImage: {
    width: '100%',
    height: '100%',
  },
  editButton: {
    backgroundColor: '#0F96BB',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
  },
  editButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});