/**
 * API Configuration
 * This file contains all API-related configuration including base URL and endpoints
 */
import Constants from "expo-constants";

// Fallback API URL in case environment variables are not set
const DEFAULT_API_URL = 'http://13.202.88.90:5000/api/';

// Safely access API_URL with fallback
const API_URL = Constants.expoConfig?.extra?.API_URL || DEFAULT_API_URL;

// Base URL for all API requests
export const API_BASE_URL = API_URL;

// Define all API endpoints here
export const API_ENDPOINTS = {
  // Auth
  LOGIN: 'auth/login',
  REGISTER: 'auth/register',
  FORGOT_PASSWORD: 'auth/forgot-password',
  RESET_PASSWORD: 'auth/reset-password',
  LOGOUT: 'auth/logout',

  // Distributors
  DISTRIBUTORS: 'distributors',

  // User Profile
  USER_PROFILE: 'users/profile',
  UPDATE_PROFILE: 'users/profile/update',

  // Accounts
  SALES_ACCOUNTS: 'sales-accounts',
  ACCOUNTS: 'accounts',

  // Contacts
  CONTACTS: 'contacts',
  CONTACT_DETAILS: 'contacts',
  CONTACTS_DYNAMIC_FIELD: 'contacts/fields',
  CONTACTS_FIELD_CHOICES: 'contacts/fields/choices',
  CONTACTS_FIELD_GROUPS: 'contacts/fields/groups',
  CONTACTS_FIELD_TYPES: 'contacts/fields/types',
  CONTACTS_FIELD_VALUES: 'contacts/fields/values',
  CONTACTS_FIELD_VALIDATION: 'contacts/fields/validation',
  CONTACTS_FIELD_VISIBILITY: 'contacts/fields/visibility',
  CONTACTS_FIELD_DEPENDENCIES: 'contacts/fields/dependencies',
  CONTACTS_FIELD_PERMISSIONS: 'contacts/fields/permissions',

  // Deals
  DEALS: 'deals',
  DEALS_DYNAMIC_FIELD: 'deals/fields',
  DEALS_FIELD_CHOICES: 'deals/fields/choices',
  DEALS_FIELD_GROUPS: 'deals/fields/groups',
  DEALS_FIELD_TYPES: 'deals/fields/types',
  DEALS_FIELD_VALUES: 'deals/fields/values',
  DEALS_FIELD_VALIDATION: 'deals/fields/validation',
  DEALS_FIELD_VISIBILITY: 'deals/fields/visibility',
  DEALS_FIELD_DEPENDENCIES: 'deals/fields/dependencies',
  DEALS_FIELD_PERMISSIONS: 'deals/fields/permissions',

  // Tasks
  TASKS: 'tasks',
  TASKS_DYNAMIC_FIELD: 'tasks/fields',
  TASKS_FIELD_CHOICES: 'tasks/fields/choices',
  TASKS_FIELD_GROUPS: 'tasks/fields/groups',
  TASKS_FIELD_TYPES: 'tasks/fields/types',
  TASKS_FIELD_VALUES: 'tasks/fields/values',
  TASKS_FIELD_VALIDATION: 'tasks/fields/validation',
  TASKS_FIELD_VISIBILITY: 'tasks/fields/visibility',
  TASKS_FIELD_DEPENDENCIES: 'tasks/fields/dependencies',
  TASKS_FIELD_PERMISSIONS: 'tasks/fields/permissions',

  // Meetings
  MEETINGS: 'meetings',
  MEETINGS_DYNAMIC_FIELD: 'meetings/fields',
  MEETINGS_FIELD_CHOICES: 'meetings/fields/choices',
  MEETINGS_FIELD_GROUPS: 'meetings/fields/groups',
  MEETINGS_FIELD_TYPES: 'meetings/fields/types',
  MEETINGS_FIELD_VALUES: 'meetings/fields/values',
  MEETINGS_FIELD_VALIDATION: 'meetings/fields/validation',
  MEETINGS_FIELD_VISIBILITY: 'meetings/fields/visibility',
  MEETINGS_FIELD_DEPENDENCIES: 'meetings/fields/dependencies',
  MEETINGS_FIELD_PERMISSIONS: 'meetings/fields/permissions',

  // Notes
  NOTES: 'notes',
  NOTES_DYNAMIC_FIELD: 'notes/fields',
  NOTES_FIELD_CHOICES: 'notes/fields/choices',
  NOTES_FIELD_GROUPS: 'notes/fields/groups',
  NOTES_FIELD_TYPES: 'notes/fields/types',
  NOTES_FIELD_VALUES: 'notes/fields/values',
  NOTES_FIELD_VALIDATION: 'notes/fields/validation',
  NOTES_FIELD_VISIBILITY: 'notes/fields/visibility',
  NOTES_FIELD_DEPENDENCIES: 'notes/fields/dependencies',
  NOTES_FIELD_PERMISSIONS: 'notes/fields/permissions',

  // Products
  PRODUCTS: 'products',
  PRODUCTS_DYNAMIC_FIELD: 'products/fields',
  PRODUCTS_FIELD_CHOICES: 'products/fields/choices',
  PRODUCTS_FIELD_GROUPS: 'products/fields/groups',
  PRODUCTS_FIELD_TYPES: 'products/fields/types',
  PRODUCTS_FIELD_VALUES: 'products/fields/values',
  PRODUCTS_FIELD_VALIDATION: 'products/fields/validation',
  PRODUCTS_FIELD_VISIBILITY: 'products/fields/visibility',
  PRODUCTS_FIELD_DEPENDENCIES: 'products/fields/dependencies',
  PRODUCTS_FIELD_PERMISSIONS: 'products/fields/permissions',

  // Quotations
  QUOTATIONS: 'quotations',
  QUOTATION_LIST: 'quotations/list',

  // Other endpoints
  FILTERS_LIST: 'products/fields',
  SAVE_VIEW: 'products/views',
  GET_FILES: 'files/list/',
  UPLOAD_FILE: 'files/upload',

  // Claim History (updated)
  CLAIM_HISTORY: 'conveyance/history/status',
} as const;