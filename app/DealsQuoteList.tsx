import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
  Modal,
  Alert,
  Image,
  Platform,
  RefreshControl,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Appbar } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { showSnackbar } from './ui/utils';

// Define interfaces for the quote data
interface Quote {
  id: number;
  quotation_name: string;
  quotation_no: string;
  total_price: string | null;
  status: string;
  formatted_create_at: string;
  account_name: string;
  quotation_stage: string | null;
  synced?: string;
}

interface QuoteListResponse {
  status: boolean;
  message: string;
  response: Quote[];
}

const STATUS_BAR_CONFIG = {
  barStyle: "light-content" as const,
  backgroundColor: "#0F96BB",
  translucent: Platform.OS === 'android',
};

export default function DealsQuoteList() {
  const insets = useSafeAreaInsets();
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedQuote, setSelectedQuote] = useState<Quote | null>(null);
  const [optionsVisible, setOptionsVisible] = useState(false);

  const router = useRouter();
  const params = useLocalSearchParams();
  const dealId = params.dealId as string;

  const fetchQuotes = async () => {
    try {
      setError(null);
      const response = await apiService.get<QuoteListResponse>(`${API_ENDPOINTS.QUOTATION_LIST}${dealId}`);
      
      if (response.data.status) {
        setQuotes(response.data.response);
      } else {
        throw new Error(response.data.message || 'Failed to fetch quotes');
      }
    } catch (err) {
      console.error('Error fetching quotes:', err);
      setError('Failed to load quotes. Please try again.');
      showSnackbar('Failed to load quotes. Pull down to refresh.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchQuotes();
    }, [dealId])
  );

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    fetchQuotes();
  }, []);

  const handleBack = () => {
    router.back();
  };

  const handleAddNew = () => {
    router.push({
      pathname: '/DealsAddQuote' as any,
      params: { dealId }
    });
  };

  const handleQuoteOptions = (quote: Quote) => {
    setSelectedQuote(quote);
    setOptionsVisible(true);
  };

  const handleOptionPress = async (option: string) => {
    setOptionsVisible(false);
    
    if (!selectedQuote) return;

    switch (option) {
      case 'Edit':
        router.push({
          pathname: '/DealsQuoteEdit' as any,
          params: { quoteId: selectedQuote.id }
        });
        break;
      case 'Delete':
        Alert.alert(
          'Delete Quote',
          'Are you sure you want to delete this quote?',
          [
            { 
              text: 'Cancel', 
              style: 'cancel'
            },
            {
              text: 'Delete',
              style: 'destructive',
              onPress: () => handleDeleteQuote(selectedQuote.id)
            }
          ],
          { cancelable: true }
        );
        break;

    }
  };

  const handleDeleteQuote = async (quoteId: number) => {
    try {
      setLoading(true);
      const response = await apiService.post(`${API_ENDPOINTS.QUOTATIONS}/${quoteId}/delete`);
      
      if (response.data.status) {
        showSnackbar(response.data.message || 'Quote deleted successfully');
        setQuotes(prevQuotes => prevQuotes.filter(quote => quote.id !== quoteId));
      } else {
        throw new Error(response.data.message || 'Failed to delete quote');
      }
    } catch (error: any) {
      console.error('Error deleting quote:', error);
      showSnackbar(error.message || 'Failed to delete quote. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderQuoteItem = ({ item }: { item: Quote }) => (
    <TouchableOpacity 
      style={styles.quoteCard}
      onPress={() => {
        router.push({
          pathname: '/DealsQuotesDetail' as any,
          params: { quoteId: item.id }
        });
      }}
    >
      <View style={styles.quoteHeader}>
        <View style={styles.quoteInfo}>
          <Text style={styles.quoteName}>{item.quotation_name}</Text>
        </View>
        <TouchableOpacity
          onPress={(e) => {
            e.stopPropagation(); // Prevent triggering the parent onPress
            handleQuoteOptions(item);
          }}
          hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
          style={styles.optionsButton}
        >
          <Ionicons name="ellipsis-vertical" size={20} color="#666" />
        </TouchableOpacity>
      </View>

      <View style={styles.quoteDetails}>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Quote number: <Text style={styles.detailValue}>{item.quotation_no}</Text></Text>
        </View>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Quote value: <Text style={styles.detailValue}>{item.total_price || '0'}</Text></Text>
        </View>
        {item.synced === "synced" && (
          <View style={styles.syncedContainer}>
            <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />
            <Text style={styles.syncedText}>Synced</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <View style={styles.emptyIconContainer}>
        <Ionicons name="document-text-outline" size={80} color="#CCCCCC" />
      </View>
      <Text style={styles.emptyTitle}>No quotes found</Text>
      <Text style={styles.emptySubtitle}>Create your first quote by tapping the button below</Text>
      <TouchableOpacity style={styles.addButton} onPress={handleAddNew}>
        <Text style={styles.addButtonText}>Add new</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { paddingTop: insets.top }]}>
        <StatusBar {...STATUS_BAR_CONFIG} />
        <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Ionicons name={Platform.OS === 'ios' ? 'chevron-back' : 'arrow-back'}  size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitleText}>Quotes</Text>
        </View>
        <View style={styles.headerRight} />
      </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0F96BB" />
          <Text style={styles.loadingText}>Loading quotes...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { paddingTop: insets.top }]}>
      <StatusBar {...STATUS_BAR_CONFIG} />
      <Stack.Screen options={{ headerShown: false }} />

      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitleText}>{`Quotes (${quotes.length})`}</Text>
        </View>
        {quotes.length > 0 && (
          <TouchableOpacity onPress={handleAddNew} style={styles.headerRight}>
            <Ionicons name="add" size={28} color="#FFFFFF" />
          </TouchableOpacity>
        )}
      </View>

      {error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={fetchQuotes}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      ) : quotes.length === 0 ? (
        renderEmptyState()
      ) : (
        <FlatList
          data={quotes}
          renderItem={renderQuoteItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={['#0F96BB']}
              tintColor="#0F96BB"
            />
          }
        />
      )}

      <Modal
        visible={optionsVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setOptionsVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setOptionsVisible(false)}
        >
          <View style={styles.optionsContainer}>
            {[
              { name: 'Edit', icon: 'pencil' },
              { name: 'Delete', icon: 'trash-outline' }
            ].map((option) => (
              <TouchableOpacity
                key={option.name}
                style={styles.optionItem}
                onPress={() => handleOptionPress(option.name)}
              >
                <Ionicons name={option.icon as any} size={22} color="#555" style={styles.optionIcon} />
                <Text style={styles.optionText}>{option.name}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#0F96BB',
    height: Platform.OS === 'ios' ? 44 : 56,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
    zIndex: 1000,
    paddingHorizontal: Platform.OS === 'ios' ? 8 : 0,
  },
  backButton: {
    width: Platform.OS === 'ios' ? 44 : 56,
    height: Platform.OS === 'ios' ? 44 : 56,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: Platform.OS === 'ios' ? -8 : 0,
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: Platform.OS === 'ios' ? 4 : 16,
  },
  headerRight: {
    width: Platform.OS === 'ios' ? 44 : 56,
    height: Platform.OS === 'ios' ? 44 : 56,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Platform.OS === 'ios' ? -8 : 0,
  },
  headerTitleText: {
    color: '#FFFFFF',
    fontSize: Platform.OS === 'ios' ? 17 : 20,
    fontWeight: Platform.OS === 'ios' ? '600' : '500',
    textAlign: 'center',
  },
  listContainer: {
    padding: 16,
  },
  quoteCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  quoteHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  quoteInfo: {
    flex: 1,
  },
  quoteName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#0066CC',
    marginBottom: 2,
  },
  optionsButton: {
    padding: 4,
  },
  quoteDetails: {
    marginBottom: 4,
  },
  detailRow: {
    marginBottom: 6,
  },
  detailLabel: {
    fontSize: 16,
    color: '#666',
    lineHeight: 22,
  },
  detailValue: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
    marginLeft: 4,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  emptyIconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#F5F5F5',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
  },
  addButton: {
    backgroundColor: '#0F96BB',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'flex-end',
  },
  optionsContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    paddingTop: 8,
    paddingBottom: Platform.OS === 'ios' ? 20 : 8,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  optionIcon: {
    marginRight: 16,
    width: 24,
    textAlign: 'center',
  },
  optionText: {
    fontSize: 16,
    color: '#333',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: '#0F96BB',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  syncedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  syncedText: {
    fontSize: 14,
    color: '#4CAF50',
    marginLeft: 4,
    fontWeight: '500',
  },
});
