import React, { useEffect, useState, useCallback } from 'react';
import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { StatusBar } from 'expo-status-bar';
import { View, Image, Platform } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Provider as PaperProvider } from 'react-native-paper';
import 'react-native-reanimated';

import { useColorScheme } from '@/hooks/useColorScheme';
import { useForceLight } from '@/hooks/useForceLight';
import { AuthProvider, useAuth } from '../context/AuthContext';
import { StatusBarConfig } from '@/components/StatusBarConfig';
import { useColors } from '@/hooks/useThemeColor';
import { initializeFirebase, isFirebaseReady } from '@/utils/firebaseConfig';
import { AppDataProvider } from './ui/utils';
import { RecordsProvider } from '../context/RecordsContext';

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

// Initialize Firebase immediately before any component rendering
// This ensures Firebase is ready before any Firebase services are used
try {
  const app = initializeFirebase();
  console.log('Firebase initialized immediately:', app.name);
} catch (error) {
  console.error('Failed to initialize Firebase immediately:', error);
}

// Main layout wrapper that includes the AuthProvider
export default function RootLayout() {
  // Force light mode throughout the app
  useForceLight();

  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  // Double-check Firebase initialization
  useEffect(() => {
    if (!isFirebaseReady()) {
      try {
        const app = initializeFirebase();
        console.log('Firebase initialized in RootLayout effect:', app.name);
      } catch (error) {
        console.error('Failed to initialize Firebase in RootLayout effect:', error);
      }
    } else {
      console.log('Firebase was already initialized');
    }
  }, []);

  const onLayoutRootView = useCallback(async () => {
    if (loaded) {
      // This tells the splash screen to hide immediately
      await SplashScreen.hideAsync();
    }
  }, [loaded]);

  useEffect(() => {
    if (loaded) {
      // Hide splash screen after a delay to ensure smooth transition
      setTimeout(async () => {
        await SplashScreen.hideAsync();
      }, 1000);
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return (
    <PaperProvider>
      <AuthProvider>
        <RecordsProvider>
          <AppDataProvider>
            <View style={{ flex: 1 }} onLayout={onLayoutRootView}>
              <RootLayoutNav />
            </View>
          </AppDataProvider>
        </RecordsProvider>
      </AuthProvider>
    </PaperProvider>
  );
}

// Navigation handling with authentication
function RootLayoutNav() {
  const { isLoading, user } = useAuth();
  const colors = useColors();

  if (isLoading) {
    return null;
  }

  return (
    <SafeAreaProvider>
      <View style={{ flex: 1, backgroundColor: '#0F96BB' }}>
        <StatusBarConfig />
        <Stack
          screenOptions={{
            headerShown: false,
            contentStyle: {
              backgroundColor: colors.background.primary,
            },
            animation: 'fade',
          }}
        >
          {!user ? (
            <Stack.Screen
              name="(auth)"
              options={{
                headerShown: false,
              }}
            />
          ) : (
            <Stack.Screen
              name="(app)"
              options={{
                headerShown: false,
              }}
            />
          )}
        </Stack>
      </View>
    </SafeAreaProvider>
  );
}
