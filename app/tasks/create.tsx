import React, { useState, useEffect } from "react";
import { View, Text, TextInput, TouchableOpacity, ScrollView, Switch, Platform, Alert, Dimensions } from "react-native";
import { Appbar, Menu, Divider, Provider as PaperProvider, Portal, Checkbox } from "react-native-paper";
import { Modal as RNModal } from "react-native";
import DateTimePicker from '@react-native-community/datetimepicker';
import LinearGradient from "react-native-linear-gradient";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";
import { StyleSheet } from "react-native";
import { router, useLocalSearchParams } from "expo-router";
import { showSnackbar } from "../ui/utils";
import { apiService } from "../../services/ApiService";
import { API_ENDPOINTS } from "../../config/api";
import { TaskType, TaskOutcome, TaskFieldsResponse, formatTaskFieldsResponse, Owner, RelatedTo } from "../models/model_task_type_outcome";
import { extractAccountInfoForTask } from "../models/model_account_details";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors } from "react-native/Libraries/NewAppScreen";

// Update type definition for related entity to match API response
interface RelatedEntity {
  id: number;
  name: string;
  type?: 'Contact' | 'Account' | 'Deal';
  email?: string | null;  // Changed from string | undefined to string | null
  mobile?: string | null;
}

const CreateTaskScreen = () => {
  const params = useLocalSearchParams();
  const isEditMode = params.mode === 'edit';
  const [taskFromParams, setTaskFromParams] = useState<any>(null);
  const callFrom = params.callFrom as string;
  const salesAccountId = params.sales_account_id as string;
  let contactId = params.id as string;
  const [accountDetailsData, setAccountDetailsData] = useState<any>(null);

  const [date, setDate] = useState(new Date());
  const [time, setTime] = useState(new Date());
  const [taskTypeVisible, setTaskTypeVisible] = useState(false);
  const [completed, setCompleted] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [tempDate, setTempDate] = useState(new Date());
  const [tempTime, setTempTime] = useState(new Date());
  const [dateError, setDateError] = useState<string>("");
  const [timeError, setTimeError] = useState<string>("");
  const [taskType, setTaskType] = useState('Select Task Type');
  const [outcome, setOutcome] = useState('Select outcome');
  const [owner, setOwner] = useState('Select owner');
  const [relatedTo, setRelatedTo] = useState('Select related to');
  const [collaborator, setCollaborator] = useState('Select collaborator');

  // Menu visibility states
  const [taskTypeMenu, setTaskTypeMenu] = useState(false);
  const [outcomeMenu, setOutcomeMenu] = useState(false);
  const [ownerMenu, setOwnerMenu] = useState(false);
  const [relatedToMenu, setRelatedToMenu] = useState(false);
  const [collaboratorMenu, setCollaboratorMenu] = useState(false);

  // Updated states with proper typing
  const [taskTypes, setTaskTypes] = useState<TaskType[]>([]);
  const [outcomes, setOutcomes] = useState<TaskOutcome[]>([]);
  const [selectedTaskType, setSelectedTaskType] = useState<TaskType | null>(null);
  const [selectedOutcome, setSelectedOutcome] = useState<TaskOutcome | null>(null);
  const [owners, setOwners] = useState<Owner[]>([]);
  const [collaborators, setCollaborators] = useState<Owner[]>([]);
  const [relatedToItems, setRelatedToItems] = useState<RelatedTo>({
    contacts: [],
    accounts: [],
    deals: []
  });
  const [selectedRelatedType, setSelectedRelatedType] = useState<'Contact' | 'Account' | 'Deal' | ''>('');
  const [selectedRelatedId, setSelectedRelatedId] = useState<number | null>(null);

  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [userId, setUserId] = useState<string>('');

  const [activeTab, setActiveTab] = useState<'contacts' | 'accounts' | 'deals'>('contacts');
  const [searchQuery, setSearchQuery] = useState('');

  // Add these state variables at the top with other states
  const [taskTypeSearchQuery, setTaskTypeSearchQuery] = useState('');
  const [outcomeSearchQuery, setOutcomeSearchQuery] = useState('');
  const [ownerSearchQuery, setOwnerSearchQuery] = useState('');
  const [collaboratorSearchQuery, setCollaboratorSearchQuery] = useState('');

  // Additional state to track current operation mode
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Parse task from params if in edit mode
  useEffect(() => {
    console.log("params_task: " + contactId+" sdfsdf "+params.task+" callFrom  "+callFrom);
    if (isEditMode && params.task) {
     
      try {
        const parsedTask = JSON.parse(params.task as string);
        console.log('Editing_task:', parsedTask);
        setTaskFromParams(parsedTask);
      
         setSelectedRelatedType(parsedTask.related_type);
        setSelectedRelatedId(parseInt(parsedTask.related_to));
        contactId = parsedTask.related_to;
        console.log("contactIdEdit: " + contactId);
        
      } catch (error) {
        console.error('Error parsing task data:', error);
        showSnackbar('Error loading task data');
      }
    }
  }, [params.task, isEditMode]);

  // Initialize form with task data if in edit mode
  useEffect(() => {
    if (taskFromParams) {
      // Set title and description
      setTitle(taskFromParams.title || '');
      setDescription(taskFromParams.description || '');

      // Set date and time
      if (taskFromParams.due_date) {
        const dueDate = new Date(taskFromParams.due_date);
        setDate(dueDate);
      }

      if (taskFromParams.due_time) {
        // Handle time format conversion
        try {
          const timeValues = taskFromParams.due_time.match(/(\d+):(\d+)\s?(AM|PM)/i);
          if (timeValues) {
            const [_, hours, minutes, period] = timeValues;
            const timeObj = new Date();
            let hour = parseInt(hours);

            // Convert to 24-hour format if PM
            if (period.toUpperCase() === 'PM' && hour < 12) {
              hour += 12;
            } else if (period.toUpperCase() === 'AM' && hour === 12) {
              hour = 0;
            }

            timeObj.setHours(hour, parseInt(minutes), 0);
            setTime(timeObj);
          }
        } catch (error) {
          console.error('Error parsing time:', error);
        }
      }

      // Set completion status
      setCompleted(taskFromParams.mark_as_complete === 'true');
    }
  }, [taskFromParams]);

  // Set related items when task data and related items are available
  useEffect(() => {
    if (taskFromParams && taskTypes?.length > 0 && taskFromParams.task_type) {
      const taskId = parseInt(taskFromParams.task_type);
      const foundTaskType = taskTypes.find(t => t.id === taskId);
      if (foundTaskType) {
        setSelectedTaskType(foundTaskType);
        setTaskType(foundTaskType.task_type);
      }
    }
  }, [taskFromParams, taskTypes]);

  // Set outcome when task data and outcomes are available
  useEffect(() => {
    if (taskFromParams && outcomes?.length > 0 && taskFromParams.outcome) {
      const outcomeId = parseInt(taskFromParams.outcome);
      const foundOutcome = outcomes.find(o => o.id === outcomeId);
      if (foundOutcome) {
        setSelectedOutcome(foundOutcome);
        setOutcome(foundOutcome.outcome);
      }
    }
  }, [taskFromParams, outcomes]);

  // Set collaborators when task data and owners are available
  useEffect(() => {
    if (taskFromParams && owners?.length > 0 && taskFromParams.collaboratoes) {
      const collaboratorId = taskFromParams.collaboratoes;
      const foundCollaborator = owners.find(o => o.id === parseInt(collaboratorId));
      if (foundCollaborator) {
        setCollaborator(foundCollaborator.name);
      }
    }
  }, [taskFromParams, owners]);

  // Set owner when in edit mode and owners are available
  useEffect(() => {
    if (isEditMode && taskFromParams && owners?.length > 0) {
      // Check if task has owner_id
      if (taskFromParams.owner_id) {
        const ownerId = parseInt(taskFromParams.owner_id);
        const foundOwner = owners.find(o => o.id === ownerId);
        if (foundOwner && foundOwner.name) {
          setOwner(foundOwner.name);
          console.log('Set owner from task data:', foundOwner.name);
        } else {
          // Owner ID exists in task but not found in owners list
          // Will use logged-in user from session
          console.log('Owner ID in task not found in owners list:', ownerId);
          setCurrentUserAsOwner(owners);
        }
      } else {
        // No owner_id in task, use logged-in user
        console.log('No owner_id in task data, using logged-in user');
        setCurrentUserAsOwner(owners);
      }
    }
  }, [isEditMode, taskFromParams, owners]);

  // Set initial date and time to current time + 1 hour
  useEffect(() => {
    const now = new Date();
    now.setHours(now.getHours() + 1);
    setTime(now);
    setDate(now);
  }, []);

  // Get user_id from AsyncStorage when component mounts
  useEffect(() => {
    const getUserId = async () => {
      try {
        const userDataString = await AsyncStorage.getItem('user_data');
        const userData = JSON.parse(userDataString || '{}');
        const storedUserId = userData.id;
        console.log("storedUserId "+storedUserId);
        if (storedUserId) {
          setUserId(storedUserId);

          // If not in edit mode and no account details, set the current user as the owner
          if (!isEditMode && !taskFromParams && !accountDetailsData && owner === 'Select owner') {
            // Find the owner with matching ID and set it as selected
            const currentUserName = userData.name || userData.username || '';
            if (currentUserName) {
              setOwner(currentUserName);
              console.log('Setting owner from user data:', currentUserName);
            }
          } else if (isEditMode && taskFromParams && owner === 'Select owner') {
            // In edit mode, if owner is not set, use the logged-in user
            const currentUserName = userData.name || userData.username || '';
            if (currentUserName) {
              setOwner(currentUserName);
              console.log('Setting owner in edit mode from user data:', currentUserName);
            }
          }
        } else {
          showSnackbar('User session not found');
      //    router.replace('/login'); // Redirect to login if no user session
        }
      } catch (error) {
        console.error('Error fetching user ID:', error);
        showSnackbar('Error fetching user session');
      }
    };

    getUserId();
  }, [isEditMode, taskFromParams, accountDetailsData, owner]);

  // Fetch task types and outcomes
  useEffect(() => {
    const fetchData = async () => {
      await fetchTaskFields();
    };

    fetchData();
  }, [callFrom, salesAccountId, isEditMode]);

  const fetchTaskFields = async () => {
    try {
      const response = await apiService.get<TaskFieldsResponse>(API_ENDPOINTS.TASK_TYPE);
      console.log("Task fields response: "+JSON.stringify(response.data));
      if (response.data) {
        const { taskTypes: activeTaskTypes, outcomes: allOutcomes, owners: allOwners, relatedTo } = formatTaskFieldsResponse(response.data);
        setTaskTypes(activeTaskTypes);
        setOutcomes(allOutcomes);
        setOwners(allOwners);
        setCollaborators(allOwners);
        setRelatedToItems(relatedTo);

        // Priority order for setting data:
        // 1. taskFromParams (edit mode)
        // 2. accountDetailsData (create from account detail)
        // 3. contactId (create from contact detail)
        // 4. logged-in user (create from elsewhere)
        console.log("taskFromParams: " + taskFromParams);
        if (taskFromParams) {
          // Edit mode - data will be set by the other useEffects
          console.log("Using task data for edit mode ss "+taskFromParams.related_type +" slkdj "+relatedTo?.contacts?.length);
          if (taskFromParams.related_type === 'contacts') {
    
            // Try to find the contact in the related items
            if (relatedTo?.contacts?.length > 0) {
              console.log("relatedTo.contacts: " + JSON.stringify(relatedTo.contacts));
              const contact = relatedTo.contacts.find(c => c.id === parseInt(taskFromParams.related_to)) as RelatedEntity;
              console.log("dsafhljdshkl: " + contact);
              if (contact && contact.name) {
                console.log("Setting relatedTo from relatedTo.contacts:", contact.name);
                setRelatedTo(contact.name);
                setSelectedRelatedType('Contact');
                setSelectedRelatedId(contact.id);
              }
            }
          }else if (taskFromParams.related_type === 'accounts') {
    
            // Try to find the contact in the related items
            if (relatedTo?.accounts?.length > 0) {
              console.log("relatedTo.accounts: " + JSON.stringify(relatedTo.accounts));
              const contact = relatedTo.accounts.find(c => c.id === parseInt(taskFromParams.related_to)) as RelatedEntity;
              console.log("dsafhljdshkl: " + contact);
              if (contact && contact.name) {
                console.log("Setting relatedTo from relatedTo.accounts:", contact.name);
                setRelatedTo(contact.name);
                setSelectedRelatedType('Account');
                setSelectedRelatedId(contact.id);
              }
            }
          } else if (taskFromParams.related_type === 'deals') {
      
            // Try to find the contact in the related items
            if (relatedTo?.deals?.length > 0) {
              console.log("relatedTo.deals: " + JSON.stringify(relatedTo.deals));
              const contact = relatedTo.deals.find(c => c.id === parseInt(taskFromParams.related_to)) as RelatedEntity;
              console.log("dsafhljdshkl: " + contact);
              if (contact && contact.name) {
                console.log("Setting relatedTo from relatedTo.deals:", contact.name);
                setRelatedTo(contact.name);
                setSelectedRelatedType('Deal');
                setSelectedRelatedId(contact.id);
              }
            }
          } 
        } else {
          // Create mode
          if (callFrom === 'accountDetail' && salesAccountId) {
            // Coming from account detail page
            console.log("Setting related account from account detail, salesAccountId:", salesAccountId);
            setSelectedRelatedType('Account');
            setSelectedRelatedId(parseInt(salesAccountId));

            // Set the related to text if account details are available
            if (accountDetailsData) {
              const accountInfo = extractAccountInfoForTask(accountDetailsData);
              console.log("Account info extracted:", accountInfo);
              if (accountInfo && accountInfo.name) {
                console.log("Setting relatedTo from account info:", accountInfo.name);
                setRelatedTo(accountInfo.name);
              }
            } else if (relatedTo?.accounts?.length > 0) {
              // Try to find the account in the related items
              const account = relatedTo.accounts.find(a => a.id === parseInt(salesAccountId));
              if (account && account.name) {
                console.log("Setting relatedTo from relatedTo.accounts:", account.name);
                setRelatedTo(account.name);
              }
            }
          } else if (callFrom === 'contactDetail'  && contactId) {
            // Coming from contact detail page
            console.log("Setting related contact from contact detail, contactId:", contactId);
            setSelectedRelatedType('Contact');
            setSelectedRelatedId(parseInt(contactId));

            // Try to find the contact in the related items
            if (relatedTo?.contacts?.length > 0) {
              const contact = relatedTo.contacts.find(c => c.id === parseInt(contactId)) as RelatedEntity;
              if (contact && contact.name) {
                console.log("Setting relatedTo from relatedTo.contacts:", contact.name);
                setRelatedTo(contact.name);
              }
            }
          }
          else if (callFrom === 'dealDetail'  && contactId) {
            // Coming from contact detail page
            console.log("Setting related contact from contact detail, contactId:", contactId);
            setSelectedRelatedType('Deal');
            setSelectedRelatedId(parseInt(contactId));

            // Try to find the contact in the related items
            if (relatedTo?.deals?.length > 0) {
              const contact = relatedTo.deals.find(c => c.id === parseInt(contactId)) as RelatedEntity;
              if (contact && contact.name) {
                console.log("Setting relatedTo from relatedTo.deals:", contact.name);
                setRelatedTo(contact.name);
              }
            }
          }
        }

        // Set current user as owner for create mode (if not already set)
        if (owner === 'Select owner') {
          await setCurrentUserAsOwner(allOwners);
        }
      }
    } catch (error) {
      console.error('Error fetching task fields:', error);
      showSnackbar('Failed to load task types and outcomes');
    }
  };

  // Helper function to set the current user as owner
  const setCurrentUserAsOwner = async (ownersList: Owner[]) => {
    try {
      const userDataString = await AsyncStorage.getItem('user_data');
      if (!userDataString) return;

      const userData = JSON.parse(userDataString);
      const currentUserId = userData.id;
      const currentUserName = userData.name || userData.username || '';

      if (ownersList?.length > 0) {
        // First try to find the owner with matching ID
        let currentOwner = ownersList.find(owner => owner.id === parseInt(currentUserId));

        // If not found by ID, try to find by name
        if (!currentOwner && currentUserName) {
          currentOwner = ownersList.find(owner =>
            owner.name && owner.name.toLowerCase() === currentUserName.toLowerCase()
          );
        }

        // If found either by ID or name, set it as the owner
        if (currentOwner && currentOwner.name) {
          setOwner(currentOwner.name);
          console.log('Set current user as owner:', currentOwner.name);
        } else if (currentUserName) {
          // If user not found in owners list but we have the name, still set it
          // This will be used when submitting the form
          setOwner(currentUserName);
          console.log('Set current user name as owner (not in list):', currentUserName);
        }
      } else if (currentUserName) {
        // If owners list is empty but we have the user name, use it
        setOwner(currentUserName);
        console.log('Set current user name as owner (empty list):', currentUserName);
      }
    } catch (error) {
      console.error('Error setting current user as owner:', error);
    }
  };

  const isDateValid = (selectedDate: Date): boolean => {
    const now = new Date();
    now.setHours(0, 0, 0, 0);
    const selected = new Date(selectedDate);
    selected.setHours(0, 0, 0, 0);
    return selected >= now;
  };

  const isTimeValid = (selectedTime: Date): boolean => {
    const now = new Date();
    const selected = new Date(selectedTime);

    // If date is today, time must be in future
    if (date.toDateString() === now.toDateString()) {
      return selected > now;
    }

    // If date is in future, any time is valid
    return true;
  };

  // Date picker handlers
  const onDateChange = (event: any, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      setShowDatePicker(false);
      if (selectedDate) {
        if (isDateValid(selectedDate)) {
          setDate(selectedDate);
          setDateError("");
        } else {
          setDateError("Please select a future date");
          Alert.alert("Invalid Date", "Please select a future date");
        }
      }
    } else {
      if (selectedDate) setTempDate(selectedDate);
    }
  };
  const handleDateConfirm = () => {
    setShowDatePicker(false);
    if (isDateValid(tempDate)) {
      setDate(tempDate);
      setDateError("");
    } else {
      setDateError("Please select a future date");
      Alert.alert("Invalid Date", "Please select a future date");
    }
  };

  // Time picker handlers
  const onTimeChange = (event: any, selectedTime?: Date) => {
    if (Platform.OS === 'android') {
      setShowTimePicker(false);
      if (selectedTime) {
        if (isTimeValid(selectedTime)) {
          setTime(selectedTime);
          setTimeError("");
        } else {
          setTimeError("Please select a future time");
          Alert.alert("Invalid Time", "Please select a future time");
        }
      }
    } else {
      if (selectedTime) setTempTime(selectedTime);
    }
  };
  const handleTimeConfirm = () => {
    setShowTimePicker(false);
    if (isTimeValid(tempTime)) {
      setTime(tempTime);
      setTimeError("");
    } else {
      setTimeError("Please select a future time");
      Alert.alert("Invalid Time", "Please select a future time");
    }
  };

  // Update task type selection
  const handleTaskTypeSelect = (taskType: TaskType) => {
    setSelectedTaskType(taskType);
    setTaskTypeMenu(false);
  };

  // Update outcome selection
  const handleOutcomeSelect = (outcome: TaskOutcome) => {
    setSelectedOutcome(outcome);
    setOutcomeMenu(false);
  };

  // Modified bottom sheet renderer for task types and outcomes
  const renderTaskTypeBottomSheet = () => {
    console.log('Task Types:', taskTypes);
    const filteredTaskTypes = (taskTypes || []).filter(type =>
      type?.task_type?.toLowerCase().includes((taskTypeSearchQuery || '').toLowerCase())
    );

    return (
      <Portal>
        <RNModal
          visible={taskTypeMenu}
          transparent
          animationType="slide"
          onRequestClose={() => {
            setTaskTypeMenu(false);
            setTaskTypeSearchQuery('');
          }}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <View style={styles.bottomSheetHeader}>
                <Text style={styles.bottomSheetTitle}>Select Task Type</Text>
                <TouchableOpacity
                  onPress={() => {
                    setTaskTypeMenu(false);
                    setTaskTypeSearchQuery('');
                  }}
                >
                  <Ionicons name="close" size={24} color="#333" />
                </TouchableOpacity>
              </View>

              <View style={styles.searchContainer}>
                <TextInput
                  style={styles.searchInput}
                  placeholder="Search task types..."
                  value={taskTypeSearchQuery}
                  onChangeText={setTaskTypeSearchQuery}
                />
              </View>

              <ScrollView style={styles.bottomSheetContent}>
                {filteredTaskTypes && filteredTaskTypes.length > 0 ? (
                  filteredTaskTypes.map((type) => (
                    <TouchableOpacity
                      key={type.id}
                      style={[
                        styles.bottomSheetItem,
                        selectedTaskType?.id === type.id && styles.selectedItem
                      ]}
                      onPress={() => {
                        handleTaskTypeSelect(type);
                        setTaskTypeSearchQuery('');
                      }}
                    >
                      <Text style={[
                        styles.bottomSheetItemText,
                        selectedTaskType?.id === type.id && styles.selectedItemText
                      ]}>
                        {type.task_type}
                      </Text>
                      {selectedTaskType?.id === type.id && (
                        <Ionicons name="checkmark" size={20} color="#2575FC" />
                      )}
                    </TouchableOpacity>
                  ))
                ) : (
                  <View style={styles.noResults}>
                    <Text style={styles.noResultsText}>No task types found</Text>
                  </View>
                )}
              </ScrollView>
            </View>
          </View>
        </RNModal>
      </Portal>
    );
  };

  const renderOutcomeBottomSheet = () => {
    console.log('Outcomes:', outcomes);
    const filteredOutcomes = (outcomes || []).filter(outcome =>
      outcome?.outcome?.toLowerCase().includes((outcomeSearchQuery || '').toLowerCase())
    );

    return (
      <Portal>
        <RNModal
          visible={outcomeMenu}
          transparent
          animationType="slide"
          onRequestClose={() => {
            setOutcomeMenu(false);
            setOutcomeSearchQuery('');
          }}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <View style={styles.bottomSheetHeader}>
                <Text style={styles.bottomSheetTitle}>Select Outcome</Text>
                <TouchableOpacity
                  onPress={() => {
                    setOutcomeMenu(false);
                    setOutcomeSearchQuery('');
                  }}
                >
                  <Ionicons name="close" size={24} color="#333" />
                </TouchableOpacity>
              </View>

              <View style={styles.searchContainer}>
                <TextInput
                  style={styles.searchInput}
                  placeholder="Search outcomes..."
                  value={outcomeSearchQuery}
                  onChangeText={setOutcomeSearchQuery}
                />
              </View>

              <ScrollView style={styles.bottomSheetContent}>
                {filteredOutcomes?.length > 0 ? (
                  filteredOutcomes.map((outcome) => (
                    <TouchableOpacity
                      key={outcome.id}
                      style={[
                        styles.bottomSheetItem,
                        selectedOutcome?.id === outcome.id && styles.selectedItem
                      ]}
                      onPress={() => {
                        handleOutcomeSelect(outcome);
                        setOutcomeSearchQuery('');
                      }}
                    >
                      <Text style={[
                        styles.bottomSheetItemText,
                        selectedOutcome?.id === outcome.id && styles.selectedItemText
                      ]}>
                        {outcome.outcome}
                      </Text>
                      {selectedOutcome?.id === outcome.id && (
                        <Ionicons name="checkmark" size={20} color="#2575FC" />
                      )}
                    </TouchableOpacity>
                  ))
                ) : (
                  <View style={styles.noResults}>
                    <Text style={styles.noResultsText}>No outcomes found</Text>
                  </View>
                )}
              </ScrollView>
            </View>
          </View>
        </RNModal>
      </Portal>
    );
  };

  const renderOwnerBottomSheet = () => {
    console.log('Owners:', owners);
    const filteredOwners = (owners || []).filter(item =>
      item?.name?.toLowerCase().includes((ownerSearchQuery || '').toLowerCase())
    );

    return (
      <Portal>
        <RNModal
          visible={ownerMenu}
          transparent
          animationType="slide"
          onRequestClose={() => {
            setOwnerMenu(false);
            setOwnerSearchQuery('');
          }}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <View style={styles.bottomSheetHeader}>
                <Text style={styles.bottomSheetTitle}>Select Owner</Text>
                <TouchableOpacity
                  onPress={() => {
                    setOwnerMenu(false);
                    setOwnerSearchQuery('');
                  }}
                >
                  <Ionicons name="close" size={24} color="#333" />
                </TouchableOpacity>
              </View>

              <View style={styles.searchContainer}>
                <TextInput
                  style={styles.searchInput}
                  placeholder="Search owners..."
                  value={ownerSearchQuery}
                  onChangeText={setOwnerSearchQuery}
                />
              </View>

              <ScrollView style={styles.bottomSheetContent}>
                {filteredOwners?.length > 0 ? (
                  filteredOwners.map((item) => (
                    <TouchableOpacity
                      key={item.id}
                      style={[
                        styles.bottomSheetItem,
                        owner === item.name && styles.selectedItem
                      ]}
                      onPress={() => {
                        setOwner(item.name);
                        setOwnerMenu(false);
                        setOwnerSearchQuery('');
                      }}
                    >
                      <Text style={[
                        styles.bottomSheetItemText,
                        owner === item.name && styles.selectedItemText
                      ]}>
                        {item.name}
                      </Text>
                      {owner === item.name && (
                        <Ionicons name="checkmark" size={20} color="#2575FC" />
                      )}
                    </TouchableOpacity>
                  ))
                ) : (
                  <View style={styles.noResults}>
                    <Text style={styles.noResultsText}>No owners found</Text>
                  </View>
                )}
              </ScrollView>
            </View>
          </View>
        </RNModal>
      </Portal>
    );
  };

  // Add this function to filter items based on search query
  const getFilteredItems = () => {
    console.log('Related To Items:', relatedToItems);
    const query = (searchQuery || '').toLowerCase();
    if (!relatedToItems) return [];

    switch (activeTab) {
      case 'contacts':
        return (relatedToItems.contacts || []).filter(item =>
          item?.name?.toLowerCase().includes(query)
        );
      case 'accounts':
        return (relatedToItems.accounts || []).filter(item =>
          item?.name?.toLowerCase().includes(query)
        );
      case 'deals':
        return (relatedToItems.deals || []).filter(item =>
          item?.name?.toLowerCase().includes(query)
        );
      default:
        return [];
    }
  };

  // Helper function to force refresh the UI
  const forceUIRefresh = () => {
    // This is a hack to force the UI to refresh
    // It works by triggering a state update that doesn't actually change anything
    setIsSubmitting(false);
    setTimeout(() => {
      console.log('Forcing UI refresh');
    }, 0);
  };

  const renderRelatedToBottomSheet = () => {
    const filteredItems = getFilteredItems();

    return (
      <Portal>
        <RNModal
          visible={relatedToMenu}
          transparent
          animationType="slide"
          onRequestClose={() => {
            setRelatedToMenu(false);
            setSearchQuery('');
          }}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <View style={styles.bottomSheetHeader}>
                <Text style={styles.bottomSheetTitle}>Select Related To</Text>
                <TouchableOpacity
                  onPress={() => {
                    setRelatedToMenu(false);
                    setSearchQuery('');
                  }}
                >
                  <Ionicons name="close" size={24} color="#333" />
                </TouchableOpacity>
              </View>

              <View style={styles.searchContainer}>
                <TextInput
                  style={styles.searchInput}
                  placeholder="Search..."
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                />
              </View>

              <View style={styles.tabContainer}>
                <TouchableOpacity
                  style={[styles.tab, activeTab === 'contacts' && styles.activeTab]}
                  onPress={() => setActiveTab('contacts')}
                >
                  <Text style={[
                    styles.tabText,
                    activeTab === 'contacts' && styles.activeTabText
                  ]}>
                    Contacts ({relatedToItems?.contacts?.length || 0})
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.tab, activeTab === 'accounts' && styles.activeTab]}
                  onPress={() => setActiveTab('accounts')}
                >
                  <Text style={[
                    styles.tabText,
                    activeTab === 'accounts' && styles.activeTabText
                  ]}>
                    Accounts ({relatedToItems?.accounts?.length || 0})
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.tab, activeTab === 'deals' && styles.activeTab]}
                  onPress={() => setActiveTab('deals')}
                >
                  <Text style={[
                    styles.tabText,
                    activeTab === 'deals' && styles.activeTabText
                  ]}>
                    Deals ({relatedToItems?.deals?.length || 0})
                  </Text>
                </TouchableOpacity>
              </View>

              <ScrollView style={styles.bottomSheetContent}>
                {filteredItems?.length > 0 ? (
                  filteredItems.map((item) => (
                    <TouchableOpacity
                      key={`${activeTab}-${item.id}`}
                      style={[
                        styles.bottomSheetItem,
                        selectedRelatedId === item.id && activeTab === selectedRelatedType.toLowerCase() && styles.selectedItem
                      ]}
                      onPress={() => {
                        // Make sure to set the relatedTo text with the item name
                        console.log('Setting related to item:', item.name);

                        // Explicitly set the relatedTo state with the item name
                        const relatedToText = item.name || 'Unknown';

                        // Directly set the relatedTo state
                        setRelatedTo(relatedToText);
                        console.log('Updated relatedTo to:', relatedToText);

                        // Set the related type based on the active tab
                        const relatedType = activeTab === 'contacts' ? 'Contact' :
                                          activeTab === 'accounts' ? 'Account' : 'Deal';
                        setSelectedRelatedType(relatedType);

                        // Set the related ID
                        setSelectedRelatedId(item.id);

                        // Close the modal and clear search
                        setRelatedToMenu(false);
                        setSearchQuery('');

                        // Force UI refresh
                        forceUIRefresh();

                        // Debug log to confirm the values were set
                        console.log(`Selected ${relatedType} with ID ${item.id} and name "${relatedToText}"`);
                      }}
                    >
                      <Text style={[
                        styles.bottomSheetItemText,
                        selectedRelatedId === item.id && activeTab === selectedRelatedType.toLowerCase() && styles.selectedItemText
                      ]}>
                        {item.name}
                      </Text>
                      {selectedRelatedId === item.id && activeTab === selectedRelatedType.toLowerCase() && (
                        <Ionicons name="checkmark" size={20} color="#2575FC" />
                      )}
                    </TouchableOpacity>
                  ))
                ) : (
                  <View style={styles.noResults}>
                    <Text style={styles.noResultsText}>
                      {searchQuery ? 'No results found' : `No ${activeTab} available`}
                    </Text>
                  </View>
                )}
              </ScrollView>
            </View>
          </View>
        </RNModal>
      </Portal>
    );
  };

  const renderCollaboratorBottomSheet = () => {
    console.log('Collaborators:', collaborators);
    const filteredCollaborators = (collaborators || []).filter(item => {
      const itemName = item?.name;
      return typeof itemName === 'string' && itemName.toLowerCase().includes((collaboratorSearchQuery || '').toLowerCase());
    });

    return (
      <Portal>
        <RNModal
          visible={collaboratorMenu}
          transparent
          animationType="slide"
          onRequestClose={() => {
            setCollaboratorMenu(false);
            setCollaboratorSearchQuery('');
          }}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <View style={styles.bottomSheetHeader}>
                <Text style={styles.bottomSheetTitle}>Select Collaborator</Text>
                <TouchableOpacity
                  onPress={() => {
                    setCollaboratorMenu(false);
                    setCollaboratorSearchQuery('');
                  }}
                >
                  <Ionicons name="close" size={24} color="#333" />
                </TouchableOpacity>
              </View>

              <View style={styles.searchContainer}>
                <TextInput
                  style={styles.searchInput}
                  placeholder="Search collaborators..."
                  value={collaboratorSearchQuery}
                  onChangeText={setCollaboratorSearchQuery}
                />
              </View>

              <ScrollView style={styles.bottomSheetContent}>
                {filteredCollaborators?.length > 0 ? (
                  filteredCollaborators.map((collaboratorItem) => (
                    <TouchableOpacity
                      key={collaboratorItem.id}
                      style={[
                        styles.bottomSheetItem,
                        collaborator === collaboratorItem.name && styles.selectedItem
                      ]}
                      onPress={() => {
                        if (collaboratorItem.name) {
                          setCollaborator(collaboratorItem.name);
                          setCollaboratorMenu(false);
                          setCollaboratorSearchQuery('');
                        }
                      }}
                    >
                      <Text style={[
                        styles.bottomSheetItemText,
                        collaborator === collaboratorItem.name && styles.selectedItemText
                      ]}>
                        {collaboratorItem.name || 'Unnamed'}
                      </Text>
                      {collaborator === collaboratorItem.name && (
                        <Ionicons name="checkmark" size={20} color="#2575FC" />
                      )}
                    </TouchableOpacity>
                  ))
                ) : (
                  <View style={styles.noResults}>
                    <Text style={styles.noResultsText}>No collaborators found</Text>
                  </View>
                )}
              </ScrollView>
            </View>
          </View>
        </RNModal>
      </Portal>
    );
  };

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    // Required field validation
    if (!title.trim()) newErrors.title = 'Title is required';
    if (!description.trim()) newErrors.description = 'Description is required';

    // Task Type validation - required in all cases
    if (!selectedTaskType || selectedTaskType.task_type === 'Select Task Type') {
      newErrors.taskType = 'Task Type is required';
    }

    // Related To validation - not required if it's from taskFromParams, account detail, or contact detail
    if (selectedRelatedType === '' && !taskFromParams && 
        !(callFrom === 'accountDetail' && salesAccountId) && 
        !(callFrom === 'contactDetail' && contactId)) {
      newErrors.relatedTo = 'Related To is required';
    }

    // Collaborator validation - required in all cases
    if (collaborator === 'Select collaborator' && !taskFromParams) {
      newErrors.collaborator = 'Collaborator is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCreateTask = async () => {
    if (!validateForm()) {
      showSnackbar('Please fill in all required fields');
      return;
    }

    if (!userId) {
      showSnackbar('User session not found. Please login again.');
      router.replace('/login');
      return;
    }

    setIsSubmitting(true);

    try {
      const formattedDate = date.toISOString().split('T')[0];

      // Define task data type to handle all possible properties
      type TaskData = {
        title: string;
        description: string;
        due_date: string;
        due_time: string;
        outcome: string | number;
        mark_as_complete: string;
        task_type?: string;
        related_to?: string;
        related_type?: string;
        collaboratoes?: string;
        user_id?: string;
      };

      // Format the payload according to the API requirements
      const taskData: TaskData = {
        title,
        description,
        due_date: formattedDate,
        due_time: `${time.getHours().toString().padStart(2, '0')}:${time.getMinutes().toString().padStart(2, '0')}:${time.getSeconds().toString().padStart(2, '0')}`,
        outcome: selectedOutcome?.id || "",
        mark_as_complete: completed.toString()
      };

      // Add task type if available
      if (selectedTaskType?.id) {
        taskData.task_type = selectedTaskType.id.toString();
      } else if (taskFromParams?.task_type) {
        taskData.task_type = taskFromParams.task_type.toString();
      }

      // Add related to if available
      if (selectedRelatedId) {
        taskData.related_to = selectedRelatedId.toString();
      } else if (taskFromParams?.related_to) {
        taskData.related_to = taskFromParams.related_to.toString();
      }

      // Add related type if available
      if (selectedRelatedType) {
        taskData.related_type = selectedRelatedType.toLowerCase()+"s";
      } else if (taskFromParams?.related_type) {
        taskData.related_type = taskFromParams.related_type.toString().toLowerCase()+"s";
      }

      // Add collaborator if available
      if (collaborator && collaborator !== 'Select collaborator') {
        // Find collaborator ID from name
        const collaboratorObj = collaborators?.find(c => c.name === collaborator);
        if (collaboratorObj) {
          taskData.collaboratoes = collaboratorObj.id.toString();
        }
      } else if (taskFromParams?.collaboratoes) {
        taskData.collaboratoes = taskFromParams.collaboratoes.toString();
      }

      // Add user ID
      if (userId) {
        taskData.user_id = userId;
      } else if (taskFromParams?.user_id) {
        taskData.user_id = taskFromParams.user_id.toString();
      }

      console.log('Submitting task data:', taskData);

      let response;

      // Get auth token from AsyncStorage
      const token = await AsyncStorage.getItem('user_token');

      // Initialize headers with Content-Type
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      };

      // Add Authorization header if token exists
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      } else {
        console.warn('No authentication token found, proceeding without auth header');
      }

      if (isEditMode && taskFromParams) {
        // Update existing task
        const taskId = taskFromParams.id.toString();
        const endpoint = `${API_ENDPOINTS.TASK_UPDATE.replace(':id', taskId)}`;

        // Use apiService directly with explicit endpoint and headers
        response = await apiService.put(endpoint, taskData, { headers });
      } else {
        // Create new task
        response = await apiService.post(API_ENDPOINTS.TASK_LIST, taskData, { headers });
      }

      if (response.status === 200 || response.status === 201) {
        // Extract the task data from the response
        const createdTask = response.data.response;

        // Show success message
        Alert.alert(
          'Success',
          `${response.data.message}`,
          [{
            text: 'OK',
            onPress: () => {
              router.back();
              // Navigate to task details page with the created/updated task data
              if (createdTask && createdTask.id) {
                // Convert task data to string for passing as parameter
                const taskDataString = JSON.stringify(createdTask);
                console.log('Navigating to task detail with data:', taskDataString);

                router.push({
                  pathname: '/TaskDetailScreen' as any,
                  params: { task: taskDataString, from: 'create' }
                });
              } else {
                // Fallback to going back if task data is not available
                console.log('Task data not available, going back');
                router.back();
              }
            }
          }]
        );
      } else {
        throw new Error(`Failed to ${isEditMode ? 'update' : 'create'} task`);
      }
    } catch (error) {
      console.error(`Error ${isEditMode ? 'updating' : 'creating'} task:`, error);
      showSnackbar(`Failed to ${isEditMode ? 'update' : 'create'} task. Please try again.`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const RequiredLabel = ({ text }: { text: string }) => (
    <Text style={styles.dropdownLabel}>
      {text} <Text style={styles.requiredStar}>*</Text>
    </Text>
  );

  const OptionalLabel = ({ text }: { text: string }) => (
    <Text style={styles.dropdownLabel}>
      {text}
    </Text>
  );

  // Parse account details if available
  useEffect(() => {
    if (params.accountDetails) {
      try {
        const parsedDetails = JSON.parse(params.accountDetails as string);
        console.log('Parsed account details:', parsedDetails);
        setAccountDetailsData(parsedDetails);
      } catch (error) {
        console.error('Error parsing account details:', error);
      }
    }
  }, [params.accountDetails]);

  // Add a new useEffect for contact detail page
  useEffect(() => {
    // Only run this if we're coming from contact detail page and not in edit mode
    if (!isEditMode && callFrom === 'contactDetail' && contactId &&
        relatedTo === 'Select related to' && relatedToItems?.contacts?.length > 0) {

      console.log('Coming from contact detail page, trying to update related to text');

      // Try to find the contact in the related items
      const contact = relatedToItems.contacts.find(c => c.id === parseInt(contactId)) as RelatedEntity;
      console.log('Found contact in relatedToItems for contact detail page:', contact);

      if (contact) {
        setRelatedTo(contact.name);
        setSelectedRelatedType('Contact');
        setSelectedRelatedId(parseInt(contactId));
        console.log('Updated related to text to contact from contact detail page:', contact.name);
      }
    }
  }, [isEditMode, callFrom, contactId, relatedToItems, relatedTo]);

  // Add a debug useEffect to track relatedTo changes
  useEffect(() => {
    console.log('=== RELATED TO STATE CHANGE ===');
    console.log('relatedTo state changed:', relatedTo);
    console.log('selectedRelatedType:', selectedRelatedType);
    console.log('selectedRelatedId:', selectedRelatedId);
    console.log('isEditMode:', isEditMode);
    console.log('taskFromParams:', taskFromParams);
    console.log('================================');
  }, [relatedTo, selectedRelatedType, selectedRelatedId]);

  // Add a useEffect to ensure relatedTo is never empty and handle fallback cases
  useEffect(() => {
    // If relatedTo is empty or still showing default text but we have selectedRelatedType and selectedRelatedId, try to set it
    if ((relatedTo === '' || relatedTo === 'Select related to') && selectedRelatedType && selectedRelatedId) {
      console.log('relatedTo is empty/default but we have selectedRelatedType and selectedRelatedId, trying to set it');

      let relatedEntity: RelatedEntity | undefined;
      if (selectedRelatedType === 'Contact' && relatedToItems?.contacts?.length > 0) {
        relatedEntity = relatedToItems.contacts.find(c => c.id === selectedRelatedId) as RelatedEntity;
        if (relatedEntity) {
          console.log('Found contact entity, setting relatedTo to:', relatedEntity.name);
          setRelatedTo(relatedEntity.name);
        }
      } else if (selectedRelatedType === 'Account' && relatedToItems?.accounts?.length > 0) {
        relatedEntity = relatedToItems.accounts.find(a => a.id === selectedRelatedId) as RelatedEntity;
        if (relatedEntity) {
          console.log('Found account entity, setting relatedTo to:', relatedEntity.name);
          setRelatedTo(relatedEntity.name);
        }
      } else if (selectedRelatedType === 'Deal' && relatedToItems?.deals?.length > 0) {
        relatedEntity = relatedToItems.deals.find(d => d.id === selectedRelatedId) as RelatedEntity;
        if (relatedEntity) {
          console.log('Found deal entity, setting relatedTo to:', relatedEntity.name);
          setRelatedTo(relatedEntity.name);
        }
      }
    }
  }, [relatedTo, selectedRelatedType, selectedRelatedId, relatedToItems]);

  // Additional useEffect specifically for edit mode to ensure contact name is set
  useEffect(() => {
    // This runs after all other useEffects and ensures the contact name is displayed
    if (isEditMode && taskFromParams && taskFromParams.related_type && taskFromParams.related_to &&
        relatedToItems && (relatedTo === 'Select related to' || relatedTo === '')) {

      console.log('=== FALLBACK MECHANISM FOR EDIT MODE ===');
      console.log('Attempting to set related to text as fallback');

      // Fix the related type mapping - API returns 'contacts' but we need 'Contact'
      const apiRelatedType = taskFromParams.related_type.toLowerCase();
      let relatedType: 'Contact' | 'Account' | 'Deal';
      if (apiRelatedType === 'contacts') {
        relatedType = 'Contact';
      } else if (apiRelatedType === 'accounts') {
        relatedType = 'Account';
      } else if (apiRelatedType === 'deals') {
        relatedType = 'Deal';
      } else {
        relatedType = taskFromParams.related_type.charAt(0).toUpperCase() + taskFromParams.related_type.slice(1) as 'Contact' | 'Account' | 'Deal';
      }

      const relatedId = parseInt(taskFromParams.related_to);

      let relatedEntity: RelatedEntity | undefined;
      const allItems: RelatedEntity[] = [
        ...(relatedToItems?.contacts || []).map(c => ({ ...c, type: 'Contact' as const })),
        ...(relatedToItems?.accounts || []).map(a => ({ ...a, type: 'Account' as const })),
        ...(relatedToItems?.deals || []).map(d => ({ ...d, type: 'Deal' as const }))
      ];

      relatedEntity = allItems.find(item => item.id === relatedId && item.type === relatedType);

      if (relatedEntity) {
        console.log('Fallback: Found related entity:', relatedEntity.name);
        setRelatedTo(relatedEntity.name);
        setSelectedRelatedType(relatedType);
        setSelectedRelatedId(relatedId);
      } else {
        console.log('Fallback: No related entity found for:', { relatedType, relatedId });
        console.log('Available items:', allItems);
      }
    }
  }, [isEditMode, taskFromParams, relatedToItems, relatedTo]);

  // Add a useEffect to check and select collaborator from taskFromParams or accountDetailsData
  useEffect(() => {
    // Only run this if collaborators are loaded and collaborator is not set yet
    if (collaborators?.length > 0 && collaborator === 'Select collaborator') {
      console.log('Checking for collaborator match in taskFromParams or accountDetailsData');

      // Check taskFromParams first
      if (taskFromParams && taskFromParams.collaboratoes) {
        const collaboratorId = parseInt(taskFromParams.collaboratoes);
        const foundCollaborator = collaborators?.find(c => c.id === collaboratorId);

        if (foundCollaborator && foundCollaborator.name) {
          console.log('Found collaborator match in taskFromParams:', foundCollaborator.name);
          setCollaborator(foundCollaborator.name);
          // Force UI refresh
          forceUIRefresh();
          console.log('Refreshing UI after setting collaborator from taskFromParams');
        }
      }
      // Then check accountDetailsData if no match found in taskFromParams
      else if (accountDetailsData) {
        // Try to find a collaborator based on accountDetailsData
        // This depends on your specific data structure, adjust as needed
        if (accountDetailsData.owner_id) {
          const ownerId = parseInt(accountDetailsData.owner_id);
          const foundCollaborator = collaborators?.find(c => c.id === ownerId);

          if (foundCollaborator && foundCollaborator.name) {
            console.log('Found collaborator match in accountDetailsData:', foundCollaborator.name);
            setCollaborator(foundCollaborator.name);
            // Force UI refresh
            forceUIRefresh();
            console.log('Refreshing UI after setting collaborator from accountDetailsData');
          }
        }
      }
    }
  }, [collaborators, collaborator, taskFromParams, accountDetailsData]);

  // Update the useEffect that handles taskFromParams
  useEffect(() => {
    if (taskFromParams && taskFromParams.related_type && taskFromParams.related_to) {
      console.log('Processing taskFromParams for related entity:', {
        related_type: taskFromParams.related_type,
        related_to: taskFromParams.related_to
      });

      // Convert API related_type to our format (e.g., 'contacts' -> 'Contact')
      const apiRelatedType = taskFromParams.related_type.toLowerCase();
      let relatedType: 'Contact' | 'Account' | 'Deal';
      
      if (apiRelatedType === 'contacts') {
        relatedType = 'Contact';
      } else if (apiRelatedType === 'accounts') {
        relatedType = 'Account';
      } else if (apiRelatedType === 'deals') {
        relatedType = 'Deal';
      } else {
        // Fallback: capitalize first letter
        relatedType = taskFromParams.related_type.charAt(0).toUpperCase() + 
                     taskFromParams.related_type.slice(1) as 'Contact' | 'Account' | 'Deal';
      }

      const relatedId = parseInt(taskFromParams.related_to);
      console.log('Setting related type and ID:', { relatedType, relatedId });

      // Set the related type and ID
      setSelectedRelatedType(relatedType);
      setSelectedRelatedId(relatedId);

      // Try to find the entity in the related items and set the name
      if (relatedToItems) {
        let relatedEntity: RelatedEntity | undefined;

        switch (relatedType) {
          case 'Contact':
            if (relatedToItems.contacts?.length > 0) {
              relatedEntity = relatedToItems.contacts.find(c => c.id === relatedId) as RelatedEntity;
              console.log('Found contact entity:', relatedEntity);
            }
            break;
          case 'Account':
            if (relatedToItems.accounts?.length > 0) {
              relatedEntity = relatedToItems.accounts.find(a => a.id === relatedId) as RelatedEntity;
              console.log('Found account entity:', relatedEntity);
            }
            break;
          case 'Deal':
            if (relatedToItems.deals?.length > 0) {
              relatedEntity = relatedToItems.deals.find(d => d.id === relatedId) as RelatedEntity;
              console.log('Found deal entity:', relatedEntity);
            }
            break;
        }

        if (relatedEntity && relatedEntity.name) {
          console.log('Setting relatedTo to:', relatedEntity.name);
          setRelatedTo(relatedEntity.name);
        } else {
          console.log('Related entity not found in items:', {
            type: relatedType,
            id: relatedId,
            availableItems: {
              contacts: relatedToItems.contacts?.map(c => ({ id: c.id, name: c.name })),
              accounts: relatedToItems.accounts?.map(a => ({ id: a.id, name: a.name })),
              deals: relatedToItems.deals?.map(d => ({ id: d.id, name: d.name }))
            }
          });
        }
      }
    }
  }, [taskFromParams, relatedToItems]);

  return (
    <PaperProvider>
      <View style={styles.container}>
        <Appbar.Header style={styles.appBar}>
          <TouchableOpacity onPress={() => router.back()} style={{ padding: 8 }}>
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Appbar.Content title={isEditMode ? "Edit Task" : "Create Task"} color="white" />
        </Appbar.Header>

        <View style={styles.mainContent}>
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.scrollViewContent}
            showsVerticalScrollIndicator={false}
          >
            {/* Title Input */}
            <RequiredLabel text="Title" />
            <TextInput
              placeholder="Enter title"
              placeholderTextColor="#777"
              style={[styles.input, errors.title && styles.inputError]}
              value={title}
              onChangeText={setTitle}
            />
            {errors.title && <Text style={styles.errorMessage}>{errors.title}</Text>}

            {/* Description Input */}
            <RequiredLabel text="Description" />
            <TextInput
              placeholder="Enter description"
              placeholderTextColor="#777"
              style={[styles.input, { height: 100, textAlignVertical: 'top' }, errors.description && styles.inputError]}
              multiline
              numberOfLines={4}
              value={description}
              onChangeText={setDescription}
            />
            {errors.description && <Text style={styles.errorMessage}>{errors.description}</Text>}

            {/* Date & Time Label */}
            <RequiredLabel text="Task Date & Time" />

            {/* Date & Time Pickers */}
            <View style={styles.dateTimeContainer}>
              <View style={styles.dateTimeBox}>
                <Text style={styles.dateTimeLabel}>Date</Text>
                <TouchableOpacity
                  style={[styles.datePickerButton, dateError ? styles.errorBorder : null]}
                  onPress={() => setShowDatePicker(true)}
                >
                  <Text style={[styles.dateText, dateError ? styles.errorText : null]}>
                    {date.toLocaleDateString()}
                  </Text>
                  <Ionicons name="calendar" size={20} color={dateError ? "#dc3545" : "#777"} />
                </TouchableOpacity>
                {dateError ? <Text style={styles.errorMessage}>{dateError}</Text> : null}
              </View>

              <View style={styles.dateTimeBox}>
                <Text style={styles.dateTimeLabel}>Time</Text>
                <TouchableOpacity
                  style={[styles.datePickerButton, timeError ? styles.errorBorder : null]}
                  onPress={() => setShowTimePicker(true)}
                >
                  <Text style={[styles.dateText, timeError ? styles.errorText : null]}>
                    {time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </Text>
                  <Ionicons name="time" size={20} color={timeError ? "#dc3545" : "#777"} />
                </TouchableOpacity>
                {timeError ? <Text style={styles.errorMessage}>{timeError}</Text> : null}
              </View>
            </View>

            {/* Date Picker Modal */}
            {Platform.OS === 'ios' ? (
              <RNModal
                visible={showDatePicker}
                transparent
                animationType="slide"
                onRequestClose={() => setShowDatePicker(false)}
              >
                <View style={styles.modalContainer}>
                  <View style={styles.pickerModalContent}>
                    <View style={styles.pickerHeader}>
                      <TouchableOpacity onPress={() => setShowDatePicker(false)} style={styles.pickerHeaderButton}>
                        <Text style={styles.pickerHeaderButtonText}>Cancel</Text>
                      </TouchableOpacity>
                      <Text style={styles.pickerHeaderTitle}>Select Date</Text>
                      <TouchableOpacity onPress={handleDateConfirm} style={styles.pickerHeaderButton}>
                        <Text style={[styles.pickerHeaderButtonText, styles.doneButton]}>Done</Text>
                      </TouchableOpacity>
                    </View>
                    <DateTimePicker
                      testID="dateTimePicker"
                      value={tempDate}
                      mode="date"
                      display="spinner"
                      onChange={onDateChange}
                      minimumDate={new Date()}
                      style={styles.picker}
                    />
                  </View>
                </View>
              </RNModal>
            ) : (
              showDatePicker && (
                <DateTimePicker
                  testID="dateTimePicker"
                  value={tempDate}
                  mode="date"
                  display="default"
                  onChange={onDateChange}
                  minimumDate={new Date()}
                />
              )
            )}

            {/* Time Picker Modal */}
            {Platform.OS === 'ios' ? (
              <RNModal
                visible={showTimePicker}
                transparent
                animationType="slide"
                onRequestClose={() => setShowTimePicker(false)}
              >
                <View style={styles.modalContainer}>
                  <View style={styles.pickerModalContent}>
                    <View style={styles.pickerHeader}>
                      <TouchableOpacity onPress={() => setShowTimePicker(false)} style={styles.pickerHeaderButton}>
                        <Text style={styles.pickerHeaderButtonText}>Cancel</Text>
                      </TouchableOpacity>
                      <Text style={styles.pickerHeaderTitle}>Select Time</Text>
                      <TouchableOpacity onPress={handleTimeConfirm} style={styles.pickerHeaderButton}>
                        <Text style={[styles.pickerHeaderButtonText, styles.doneButton]}>Done</Text>
                      </TouchableOpacity>
                    </View>
                    <DateTimePicker
                      testID="timeTimePicker"
                      value={tempTime}
                      mode="time"
                      is24Hour={false}
                      display="spinner"
                      onChange={onTimeChange}
                      style={styles.picker}
                    />
                  </View>
                </View>
              </RNModal>
            ) : (
              showTimePicker && (
                <DateTimePicker
                  testID="timeTimePicker"
                  value={tempTime}
                  mode="time"
                  is24Hour={false}
                  display="default"
                  onChange={onTimeChange}
                />
              )
            )}

            {/* Task Type Selection */}
            <View style={styles.dropdownContainer}>
              <RequiredLabel text="Task Type" />
              <TouchableOpacity
                style={[styles.selectBox, errors.taskType && styles.inputError]}
                onPress={() => setTaskTypeMenu(true)}
              >
                <Text style={[
                  styles.selectText,
                  selectedTaskType && styles.filledSelectText
                ]}>
                  {selectedTaskType ? selectedTaskType.task_type : 'Select Task Type'}
                </Text>
                <Ionicons name="chevron-down" size={20} color="#777" />
              </TouchableOpacity>
              {errors.taskType && <Text style={styles.errorMessage}>{errors.taskType}</Text>}
              {renderTaskTypeBottomSheet()}
            </View>

            {/* Outcome Selection - Hidden */}
            <View style={[styles.dropdownContainer, { display: 'none' }]}>
              <Text style={styles.dropdownLabel}>Outcome</Text>
              <TouchableOpacity
                style={styles.selectBox}
                onPress={() => setOutcomeMenu(true)}
              >
                <Text style={[
                  styles.selectText,
                  selectedOutcome && styles.filledSelectText
                ]}>
                  {selectedOutcome ? selectedOutcome.outcome : 'Select Outcome'}
                </Text>
                <MaterialIcons name="track-changes" size={20} color="#777" />
              </TouchableOpacity>
              {renderOutcomeBottomSheet()}
            </View>

            {/* Owner Selection */}
            <View style={styles.dropdownContainer}>
              <OptionalLabel text="Owner" />
              <TouchableOpacity
                style={[styles.selectBox, errors.owner && styles.inputError]}
                onPress={() => setOwnerMenu(true)}
              >
                <Text style={[
                  styles.selectText,
                  owner !== 'Select owner' && styles.filledSelectText
                ]}>{owner}</Text>
                <Ionicons name="person" size={20} color="#777" />
              </TouchableOpacity>
              {errors.owner && <Text style={styles.errorMessage}>{errors.owner}</Text>}
              {renderOwnerBottomSheet()}
            </View>

            {/* Related To Selection */}
            <View style={styles.dropdownContainer}>
              <RequiredLabel text="Related To" />
              <TouchableOpacity
                style={[styles.selectBox, errors.relatedTo && styles.inputError]}
                onPress={() => setRelatedToMenu(true)}
              >
                <Text style={[
                  styles.selectText,
                  relatedTo !== 'Select related to' && styles.filledSelectText
                ]}>
                  {relatedTo === 'Select related to' ? 'Select related to' : relatedTo}
                </Text>
                <Ionicons name="people" size={20} color="#777" />
              </TouchableOpacity>
              {errors.relatedTo && <Text style={styles.errorMessage}>{errors.relatedTo}</Text>}
              {selectedRelatedType && selectedRelatedId && (
                <Text style={styles.helperText}>
                  {`Selected ${selectedRelatedType}: ID ${selectedRelatedId}`}
                </Text>
              )}
              {renderRelatedToBottomSheet()}
            </View>

            {/* Collaborator Selection */}
            <View style={styles.dropdownContainer}>
              <RequiredLabel text="Collaborator" />
              <TouchableOpacity
                style={[styles.selectBox, errors.collaborator && styles.inputError]}
                onPress={() => setCollaboratorMenu(true)}
              >l̥s
                <Text style={[
                  styles.selectText,
                  collaborator !== 'Select collaborator' && styles.filledSelectText
                ]}>{collaborator}</Text>
                <Ionicons name="person" size={20} color="#777" />
              </TouchableOpacity>
              {errors.collaborator && <Text style={styles.errorMessage}>{errors.collaborator}</Text>}
              {renderCollaboratorBottomSheet()}
            </View>

            {/* Mark as Completed Switch 
            <View style={styles.switchContainer}>
              <Text style={styles.switchLabel}>Mark as completed</Text>
              <Switch
                value={completed}
                onValueChange={setCompleted}
                trackColor={{ false: "#e0e0e0", true: "#0077B6" }}
                thumbColor={completed ? "#fff" : "#f4f3f4"}
              />
            </View>
            */}
          </ScrollView>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              onPress={handleCreateTask}
              disabled={isSubmitting}
              style={[styles.button, isSubmitting && styles.buttonDisabled]}
            >
              <Text style={styles.buttonText}>
                {isSubmitting
                  ? 'Processing...'
                  : isEditMode
                    ? 'Update Task'
                    : 'Create Task'
                }
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </PaperProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F8F9FA",
  },
  mainContent: {
    flex: 1,
    position: 'relative',
  },
  scrollView: {
    flex: 1,
    backgroundColor: "#F8F9FA",
    padding: 20,
  },
  scrollViewContent: {
    paddingBottom: Platform.OS === 'ios' ? 120 : 100,
  },
  buttonWrapper: {
    paddingHorizontal: 20,
    width: '100%',
  },
  button: {
    backgroundColor: '#0F96BB',
    padding: Platform.OS === 'ios' ? 16 : 14,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
    width: '100%',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  buttonDisabled: {
    backgroundColor: '#B0BEC5',
    elevation: 0,
    shadowOpacity: 0,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#fff",
    textAlign: 'center',
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingTop: 15,
    paddingBottom: Platform.OS === 'ios' ? 34 : 20,
    borderTopWidth: 1,
    borderTopColor: '#eee',
    zIndex: 999,
  },
  requiredStar: {
    color: 'red',
    fontSize: 16,
  },
  input: {
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 15,
    fontSize: 16,
    marginBottom: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    color: '#000',
  },
  row: {
    flexDirection: "row" as const,
    justifyContent: "space-between" as const,
    marginBottom: 10,
  },
  datePickerButton: {
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 15,
    width: "100%",
    flexDirection: "row" as const,
    justifyContent: "space-between" as const,
    alignItems: "center" as const,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  dateText: {
    fontSize: 16,
    color: "#000",
  },
  selectBox: {
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 15,
    flexDirection: "row" as const,
    justifyContent: "space-between" as const,
    alignItems: "center" as const,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  selectText: {
    fontSize: 16,
    color: "#777",
  },
  filledSelectText: {
    color: '#000',
  },
  switchRow: {
    flexDirection: "row" as const,
    alignItems: "center" as const,
    marginVertical: 10,
  },
  errorBorder: {
    borderWidth: 1,
    borderColor: "#dc3545",
  },
  errorText: {
    color: "#dc3545",
  },
  errorMessage: {
    color: "#dc3545",
    fontSize: 12,
    marginTop: 4,
  },
  dateTimeContainer: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    marginBottom: 20,
    gap: 15,
  },
  dateTimeBox: {
    flex: 1,
  },
  dateTimeLabel: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    fontWeight: '500' as const,
  },
  sectionLabel: {
    fontSize: 16,
    color: '#333',
    fontWeight: '600' as const,
    marginBottom: 12,
    marginTop: 8,
  },
  dropdownContainer: {
    marginBottom: 15,
  },
  dropdownLabel: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    fontWeight: '500' as const,
  },
  menuContent: {
    backgroundColor: '#fff',
    marginTop: 5,
    borderRadius: 8,
    elevation: 4,
  },
  menuItemText: {
    fontSize: 16,
    color: '#333',
  },
  bottomSheet: {
    backgroundColor: 'white',
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 16,
    paddingBottom: 20,
    height: Dimensions.get('window').height / 2,
  },
  bottomSheetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  bottomSheetTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  bottomSheetContent: {
    padding: 16,
  },
  bottomSheetItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  bottomSheetItemText: {
    fontSize: 16,
    color: '#333',
  },
  switchContainer: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    marginVertical: 10,
    marginBottom: 20,
    elevation: 2,
  },
  switchLabel: {
    fontSize: 16,
    color: '#555',
  },
  inputError: {
    borderWidth: 1,
    borderColor: '#dc3545',
  },
  sectionHeader: {
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#dee2e6',
  },
  sectionHeaderText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6c757d',
    textTransform: 'uppercase',
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#dee2e6',
    backgroundColor: '#fff',
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#2575FC',
  },
  tabText: {
    fontSize: 14,
    color: '#666',
  },
  activeTabText: {
    color: '#2575FC',
    fontWeight: '600',
  },
  searchContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#dee2e6',
    backgroundColor: '#fff',
  },
  searchInput: {
    height: 40,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
  },
  noResults: {
    padding: 16,
    alignItems: 'center',
  },
  noResultsText: {
    color: '#666',
    fontSize: 14,
  },
  selectedItem: {
    backgroundColor: '#f0f7ff',
    borderRadius: 8,
  },
  selectedItemText: {
    color: '#2575FC',
    fontWeight: '500',
  },
  appBar: {
    backgroundColor: '#0F96BB'
  },
  modalContainer: {
    flex: 1,
    justifyContent: "flex-end",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContent: {
    backgroundColor: 'white',
    height: Dimensions.get('window').height * 0.5,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  pickerModalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: 'auto',
  },
  pickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#f8f9fa',
  },
  pickerHeaderButton: {
    padding: 8,
  },
  pickerHeaderButtonText: {
    fontSize: 16,
    color: '#0077B6',
  },
  doneButton: {
    fontWeight: '600',
  },
  pickerHeaderTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  picker: {
    height: 200,
    backgroundColor: '#fff',
  },
  helperText: {
    fontSize: 12,
    color: '#777',
    marginTop: 4,
  },
  debugText: {
    fontSize: 12,
    color: 'red',
    marginTop: 4,
    fontWeight: 'bold',
  },
  debugButton: {
    backgroundColor: '#0F96BB',
    padding: 10,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 5,
    marginBottom: 10,
  },
  debugButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#fff',
  },
});

export default CreateTaskScreen;

