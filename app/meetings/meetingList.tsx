import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  StatusBar,
  ActivityIndicator,
  Alert,
  RefreshControl,
  ScrollView,
  Platform,
  TextInput,
} from 'react-native';
import { SafeAreaView as SafeAreaViewRN } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useRouter, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { useColors } from '@/hooks/useThemeColor';
import { Appbar, Searchbar } from 'react-native-paper';
import { MeetingListItem, MeetingListResponse, formatMeetingDate, formatMeetingTime, getMeetingStatus, isMeetingOverdue } from '../models/model_meeting_list';
import { apiService } from '../../services/ApiService';
import { API_ENDPOINTS } from '../../config/api';
import { showSnackbar, readUserScope } from '../ui/utils';

// Meeting filter types
type MeetingStatus = 'All' | 'Overdue' | 'Upcoming' | 'Completed';

export default function MeetingListScreen() {
  const [activeTab, setActiveTab] = useState<MeetingStatus>('All');
  const [meetings, setMeetings] = useState<MeetingListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [headerTitle, setHeaderTitle] = useState('All Meetings');
  const [showSearch, setShowSearch] = useState(false);
  const [searchText, setSearchText] = useState('');
  const colors = useColors();
  const params = useLocalSearchParams();
  const { contact_id, sales_account_id, deal_id } = params;
  const router = useRouter();
  const [accessScope, setAccessScope] = useState<any>(null);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.primary
    },
    appbar: {
      backgroundColor: '#0F96BB',
      elevation: 2,
      justifyContent: 'center',
      alignItems: 'center',
      height: 64,
      position: 'relative',
      zIndex: 1,
    },
    appbarTitle: {
      fontSize: 20,
      color: '#fff',
      fontWeight: '600',
    },
    searchView: {
      flex: 1,
      marginHorizontal: 16,
    },
    searchbar: {
      elevation: 0,
      backgroundColor: colors.background.transparent,
      borderRadius: 8,
    },
    searchInput: {
      fontSize: 16,
      color: '#FFFFFF',
    },
    plusButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 8,
    },
    tabsContainer: {
      backgroundColor: colors.background.primary,
      borderBottomWidth: 1,
      borderBottomColor: '#eee',
    },
    tabsScrollView: {
      flexDirection: 'row',
      paddingHorizontal: 16,
      paddingVertical: 12,
    },
    tab: {
      paddingVertical: 8,
      paddingHorizontal: 16,
      marginRight: 8,
      borderRadius: 8,
      backgroundColor: '#f0f0f0',
    },
    activeTab: {
      backgroundColor: colors.primary,
    },
    tabText: {
      fontSize: 14,
      color: '#666',
      fontWeight: '500',
    },
    activeTabText: {
      color: '#fff',
    },
    meetingList: {
      paddingHorizontal: 16,
      paddingVertical: 12,
    },
    meetingItem: {
      backgroundColor: '#fff',
      borderRadius: 8,
      padding: 16,
      marginBottom: 12,
      borderWidth: 1,
      borderColor: '#eee',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 6,
    },
    meetingContent: {
      flex: 1,
    },
    meetingHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
    },
    meetingTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: '#333',
      flex: 1,
      marginRight: 8,
    },
    statusBadge: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
    },
    overdueBadge: {
      backgroundColor: '#ffebee',
    },
    upcomingBadge: {
      backgroundColor: '#e3f2fd',
    },
    completedBadge: {
      backgroundColor: '#e8f5e9',
    },
    checkinBadge: {
      backgroundColor: '#e8f5e9',
    },
    statusText: {
      fontSize: 12,
      fontWeight: '500',
    },
    locationContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
    },
    locationText: {
      fontSize: 14,
      color: '#666',
      marginLeft: 4,
    },
    meetingDetails: {
      flexDirection: 'row',
      justifyContent: 'flex-start',
      alignItems: 'center',
    },
    meetingDate: {
      fontSize: 14,
      color: '#666',
    },
    meetingParticipants: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    participantCount: {
      fontSize: 14,
      color: '#666',
      marginLeft: 4,
    },
    dateContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: 16,
    },
    dateText: {
      fontSize: 14,
      color: '#666',
      marginLeft: 4,
    },
    timeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    timeText: {
      fontSize: 14,
      color: '#666',
      marginLeft: 4,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      fontSize: 16,
      color: '#666',
      marginTop: 12,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,

    },
    emptyText: {
      fontSize: 16,
      color: '#666',
      textAlign: 'center',
      textTransform: 'capitalize',
    },

    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    errorText: {
      fontSize: 16,
      color: colors.status.error,
      marginBottom: 16,
      textAlign: 'center',
    },
    retryButton: {
      backgroundColor: colors.primary,
      paddingHorizontal: 20,
      paddingVertical: 10,
      borderRadius: 6,
    },
    retryButtonText: {
      color: colors.button.primary.text,
      fontSize: 16,
      fontWeight: '500',
    },
    goBackButton: {
      backgroundColor: colors.background.tertiary,
      paddingHorizontal: 20,
      paddingVertical: 10,
      borderRadius: 6,
      marginTop: 10,
    },
    backButtonText: {
      color: colors.text.primary,
      fontSize: 16,
      fontWeight: '500',
    },

  });

  useFocusEffect(
    React.useCallback(() => {
      console.log('MeetingList screen is focused, refreshing data...');

      // Set appropriate header title based on context
      if (contact_id) {
        setHeaderTitle('Contact Meetings');
      } else if (sales_account_id) {
        setHeaderTitle('Account Meetings');
      } else if (deal_id) {
        setHeaderTitle('Deal Meetings');
      } else {
        setHeaderTitle('All Meetings');
      }

      // Fetch fresh data
      fetchMeetings();

      return () => {
        // This runs when the screen is unfocused
        console.log('MeetingList screen is unfocused');
      };
    }, [contact_id, sales_account_id, deal_id])
  );

  useEffect(() => {
    const fetchAccessScope = async () => {
      const scope = await readUserScope();

      setAccessScope(scope);
    };
    fetchAccessScope();
  }, []);

  const fetchMeetings = async () => {
    try {
      setLoading(true);
      setError(null);

      let endpoint = API_ENDPOINTS.MEETING_ADD;

      // Add query parameters based on context
      if (contact_id) {
        endpoint = API_ENDPOINTS.CONTACT_MEETING.replace(':id', contact_id as string)
      } else if (sales_account_id) {
        endpoint = API_ENDPOINTS.ACCOUNT_MEETING.replace(':id', sales_account_id as string)
      } else if (deal_id) {
        endpoint = API_ENDPOINTS.DEAL_MEETING.replace(':id', deal_id as string)
      }

      const response = await apiService.get<MeetingListResponse>(endpoint);

      if (response.data?.response) {
        setMeetings(response.data.response);
      } else {
        throw new Error('Failed to fetch meetings');
      }
    } catch (err) {
      console.error('Error fetching meetings:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    fetchMeetings();
  };

  const filteredMeetings = () => {
    let filtered = meetings;

    // First apply search filter if search text exists
    if (searchText.trim()) {
      const query = searchText.toLowerCase();
      filtered = filtered.filter(meeting =>
        meeting.activity_title.toLowerCase().includes(query) ||
        (meeting.description?.toLowerCase().includes(query)) ||
        (meeting.location?.toLowerCase().includes(query)) ||
        (meeting.status?.toLowerCase().includes(query)) ||
        formatMeetingDate(meeting.start_date).toLowerCase().includes(query) ||
        formatMeetingTime(meeting.start_time).toLowerCase().includes(query)
      );
    }

    // Then apply tab filter
    switch (activeTab) {
      case 'Overdue':
        // Show overdue meetings but exclude those with "Closed" status
        // Include "Inprogress" status meetings if they are overdue
        return filtered.filter(meeting => {
          const isOverdue = isMeetingOverdue(meeting);
          return isOverdue && meeting.status !== 'Closed' || meeting.status === 'Inprogress';
        });
      case 'Upcoming':
        return filtered.filter(meeting => !isMeetingOverdue(meeting) && meeting.status === 'Pending');
      case 'Completed':
        // Show meetings with "Closed" status
        return filtered.filter(meeting => meeting.status === 'Closed');
      default:
        // All tab shows the entire list
        return filtered;
    }
  };

  const handleMeetingPress = (meeting: MeetingListItem) => {
    if (accessScope?.appointment?.view === true) {
      if (meeting.status != null) {
        router.push({
          pathname: '/meetings/detailedMeeting' as any,
          params: { data: JSON.stringify(meeting.id) }
        });
      } else {
        showSnackbar('Status is not updated yet');
      }
    } else {
      showSnackbar('You do not have permission to view this meeting');
    }
  };

  const renderMeetingItem = ({ item }: { item: MeetingListItem }) => {
    const isOverdue = isMeetingOverdue(item);
    const status = getMeetingStatus(item);

    return (
      <TouchableOpacity
        style={styles.meetingItem}
        onPress={() => handleMeetingPress(item)}
      >
        <View style={styles.meetingContent}>
          <View style={styles.meetingHeader}>
            <Text style={styles.meetingTitle} numberOfLines={1}>
              {item.activity_title}
            </Text>
            <View style={[
              styles.statusBadge,
              isOverdue ? styles.overdueBadge :
              status === 'Pending' ? styles.upcomingBadge :
              status === 'Completed' ? styles.completedBadge :
              styles.checkinBadge
            ]}>
              <Text style={styles.statusText}>
                {isOverdue ? 'Overdue' : status === null ? '-' : status}
              </Text>
            </View>
          </View>

          {item.location && (
            <View style={styles.locationContainer}>
              <Ionicons name="location-outline" size={16} color="#FF6B35" />
              <Text style={styles.locationText} numberOfLines={1}>
                {item.location}
              </Text>
            </View>
          )}

          <View style={styles.meetingDetails}>
            <View style={styles.dateContainer}>
              <Ionicons name="calendar-outline" size={16} color="#4CAF50" />
              <Text style={styles.dateText}>
                {formatMeetingDate(item.start_date)}
              </Text>
            </View>
            <View style={styles.timeContainer}>
              <Ionicons name="time-outline" size={16} color="#2196F3" />
              <Text style={styles.timeText}>
                {formatMeetingTime(item.start_time)}
              </Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderTabBar = () => {
    const tabs: MeetingStatus[] = ['All', 'Overdue', 'Upcoming', 'Completed'];

    return (
      <View style={styles.tabsContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.tabsScrollView}
        >
          {tabs.map((tab) => {
            const isActive = activeTab === tab;
            const count = tab === 'All' ? meetings.length :
                         tab === 'Overdue' ? meetings.filter(m => isMeetingOverdue(m) && m.status !== 'Closed' || m.status === 'Inprogress').length :
                         tab === 'Completed' ? meetings.filter(m => m.status === 'Closed').length :
                         meetings.filter(m => !isMeetingOverdue(m) && m.status === 'Pending').length;

            return (
              <TouchableOpacity
                key={tab}
                style={[styles.tab, isActive && styles.activeTab]}
                onPress={() => setActiveTab(tab)}
              >
                <Text style={[styles.tabText, isActive && styles.activeTabText]}>
                  {`${tab} (${count})`}
                </Text>
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      </View>
    );
  };

  const renderHeader = () => (
    <Appbar.Header
      style={styles.appbar}
      statusBarHeight={0}
    >
      {!showSearch ? (
        <>
          <Appbar.Action
            icon="arrow-left"
            onPress={() => {
              console.log("Back button pressed, canGoBack:", router.canGoBack());
              if (router.canGoBack()) {
                router.back();
              } else {
                console.log("No previous page, navigating to home");
                router.push('/(tabs)/' as any);
              }
            }}
            color="#FFFFFF"
          />
          <Appbar.Content
            title={headerTitle}
            titleStyle={styles.appbarTitle}
          />
          <Appbar.Action
            icon="magnify"
            onPress={() => setShowSearch(true)}
            color="#FFFFFF"
          />
          {accessScope?.appointment?.create === true && (
            <TouchableOpacity
              style={styles.plusButton}
              onPress={() => {
                console.log("contact_idsdsdd: " + contact_id);
                console.log("sales_account_id: " + sales_account_id);
                console.log("deal_id: " + deal_id); 
                router.push({
                  pathname: '/meetings/createMeeting' as any,
                 params: {
                mode: 'edit',
                   task: JSON.stringify("task"),
                contact_id: contact_id ? contact_id:null,
                sales_account_id:sales_account_id ? sales_account_id : null,
                 deal_id: deal_id ? deal_id : null,
                callFrom: contact_id ? 'contactDetail' :
                         sales_account_id ? 'accountDetail' :
                         deal_id ? 'dealDetail' : 'taskList'
              }
              },
                
              ) }}
            >
              <Ionicons name="add" size={22} color="#FFFFFF" style={{ fontWeight: 'bold' }} />
            </TouchableOpacity>
          )}
        </>
      ) : (
        <>
          <Appbar.Action
            icon="arrow-left"
            onPress={() => {
              setShowSearch(false);
              setSearchText('');
            }}
            color="#FFFFFF"
          />
          <View style={styles.searchView}>
            <Searchbar
              placeholder="Search meetings..."
              onChangeText={setSearchText}
              value={searchText}
              style={[styles.searchbar, { backgroundColor: colors.background.transparent }]}
              inputStyle={styles.searchInput}
              icon="magnify"
              iconColor="#FFFFFF"
              clearIcon={() => null}
              placeholderTextColor="#FFFFFF"
              selectionColor="#FFFFFF"
              autoFocus
              returnKeyType="search"
              onIconPress={() => {}}
              showDivider={false}
            />
          </View>
          <Appbar.Action
            icon="close"
            onPress={() => {
              setShowSearch(false);
              setSearchText('');
            }}
            color="#FFFFFF"
          />
        </>
      )}
    </Appbar.Header>
  );

  if (loading) {
    return (
      <View style={styles.container}>
        <StatusBar backgroundColor="transparent" barStyle="dark-content" translucent={true} />
        <SafeAreaViewRN
          style={{ flex: 1, backgroundColor: colors.background.primary }}
          edges={['top', 'left', 'right']}
        >
          {renderHeader()}
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={styles.loadingText}>Loading meetings...</Text>
          </View>
        </SafeAreaViewRN>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <StatusBar backgroundColor="transparent" barStyle="dark-content" translucent={true} />
        <SafeAreaViewRN
          style={{ flex: 1, backgroundColor: colors.background.primary }}
          edges={['top', 'left', 'right']}
        >
          {renderHeader()}
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity style={styles.retryButton} onPress={fetchMeetings}>
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.goBackButton}
              onPress={() => router.back()}
            >
              <Text style={styles.backButtonText}>Go Back</Text>
            </TouchableOpacity>
          </View>
        </SafeAreaViewRN>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor="transparent" barStyle="dark-content" translucent={true} />
      <SafeAreaViewRN
        style={{ flex: 1, backgroundColor: colors.background.primary }}
        edges={['top', 'left', 'right']}
      >
        {renderHeader()}
        {renderTabBar()}

        {filteredMeetings().length === 0 ? (
          <View style={styles.emptyContainer}>
            <Ionicons name="calendar-outline" size={60} color="#ccc" />
            <Text style={styles.emptyText}>
              {searchText ? 'No meetings found matching your search' :
               `No ${activeTab !== 'All' ? activeTab.toLowerCase() : ''} meetings ${contact_id ? ' for this contact' :
               sales_account_id ? ' for this account' :
               deal_id ? ' for this deal' : ''}`}
            </Text>
          </View>
        ) : (
          <FlatList
            data={filteredMeetings()}
            keyExtractor={(item) => item.id.toString()}
            renderItem={renderMeetingItem}
            contentContainerStyle={styles.meetingList}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={[colors.primary]}
              />
            }
          />
        )}
      </SafeAreaViewRN>
    </View>
  );
}

