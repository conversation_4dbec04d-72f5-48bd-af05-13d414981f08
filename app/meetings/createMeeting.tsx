import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, ScrollView, Alert, Platform, StyleSheet, TextInput, Dimensions, ActivityIndicator, Modal as RNModal } from 'react-native';
import { PaperProvider, Button, Portal, Divider, Appbar, Switch } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { log, logAPIRespose, showSnackbar } from '../ui/utils';
import LinearGradient from 'react-native-linear-gradient';
import { TaskOutcome, Owner, Contact, Account, Deal, RelatedTo as ApiRelatedTo, TaskFieldsResponse, getAllRelatedItems } from '../models/model_task_type_outcome';
import { apiService } from '../../services/ApiService';
import { API_ENDPOINTS } from '../../config/api';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { parseISO } from 'date-fns';
import { format } from 'date-fns';

// Time Zone Interface
interface TimeZone {
  id: string;
  name: string;
  offset: number;
  abbreviation: string;
  region?: string;
}

// Comprehensive Time Zone List
const TIME_ZONES: TimeZone[] = [
  { id: 'utc', name: 'UTC (Coordinated Universal Time)', offset: 0, abbreviation: 'UTC', region: 'UTC' },
  { id: 'gmt', name: 'Greenwich Mean Time', offset: 0, abbreviation: 'GMT', region: 'UTC' },

  // Americas
  { id: 'est', name: 'Eastern Time', offset: -5, abbreviation: 'ET', region: 'Americas' },
  { id: 'cst', name: 'Central Time', offset: -6, abbreviation: 'CT', region: 'Americas' },
  { id: 'mst', name: 'Mountain Time', offset: -7, abbreviation: 'MT', region: 'Americas' },
  { id: 'pst', name: 'Pacific Time', offset: -8, abbreviation: 'PT', region: 'Americas' },

  // Europe & Africa
  { id: 'cet', name: 'Central European Time', offset: 1, abbreviation: 'CET', region: 'Europe' },
  { id: 'wet', name: 'Western European Time', offset: 0, abbreviation: 'WET', region: 'Europe' },
  { id: 'eet', name: 'Eastern European Time', offset: 2, abbreviation: 'EET', region: 'Europe' },

  // Asia & Pacific
  { id: 'ist', name: 'India Standard Time', offset: 5.5, abbreviation: 'IST', region: 'Asia' },
  { id: 'jst', name: 'Japan Standard Time', offset: 9, abbreviation: 'JST', region: 'Asia' },
  { id: 'aest', name: 'Australian Eastern Time', offset: 10, abbreviation: 'AEST', region: 'Pacific' },
  { id: 'nzst', name: 'New Zealand Standard Time', offset: 12, abbreviation: 'NZST', region: 'Pacific' }
];

// Time Zone Utility Functions
// const convertToTimeZone = (date: Date, fromTimeZone: TimeZone | null, toTimeZone: TimeZone | null): Date => {
//   if (!fromTimeZone || !toTimeZone) return date;
//
//   const utc = date.getTime() + (date.getTimezoneOffset() * 60000);
//   const offsetDiff = (toTimeZone.offset - fromTimeZone.offset) * 3600000;
//   return new Date(utc + offsetDiff);
// };

// Set default timezone to IST
const DEFAULT_TIMEZONE: TimeZone = {
  id: 'ist',
  name: 'India Standard Time',
  offset: 5.5,
  abbreviation: 'IST',
  region: 'Asia'
};

// Simplify the formatTimeWithTimeZone function to not use timezone conversions
const formatTimeWithTimeZone = (date: Date): string => {
  // Format time in 12-hour format with AM/PM without timezone conversion
  console.log('Formatting time for display:', {
    date: date.toISOString(),
    hours: date.getHours(),
    minutes: date.getMinutes()
  });

  // Ensure we're working with a valid Date object
  const validDate = new Date(date);

  return validDate.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  }).replace(/\s+/g, ' ');
};

// Add a helper function to parse time strings
const parseTime = (timeString: string): Date => {
  const timeMatch = timeString.match(/(\d+):(\d+)\s*(AM|PM)?/i);
  if (timeMatch) {
    let [, hours, minutes, period] = timeMatch;
    let parsedHours = parseInt(hours, 10);
    const parsedMinutes = parseInt(minutes, 10);

    if (period) {
      // Convert 12-hour format to 24-hour format
      if (period.toUpperCase() === 'PM' && parsedHours < 12) parsedHours += 12;
      if (period.toUpperCase() === 'AM' && parsedHours === 12) parsedHours = 0;
    }

    const parsedTime = new Date();
    parsedTime.setHours(parsedHours, parsedMinutes, 0, 0);
    return parsedTime;
  }

  // If the time string is in 24-hour format, handle it directly
  const [hours, minutes] = timeString.split(':').map(Number);
  if (!isNaN(hours) && !isNaN(minutes)) {
    const parsedTime = new Date();
    parsedTime.setHours(hours, minutes, 0, 0);
    return parsedTime;
  }

  console.warn("Unable to parse time:", timeString);
  return new Date();
};

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollViewContent: {
    padding: 16,
  },
  dropdownLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
    marginTop: 16,
  },
  dateTimeContainer: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 16,
  },
  dateTimeBox: {
    flex: 1,
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#fff',
  },
  dateText: {
    fontSize: 16,
    color: '#333',
  },
  errorBorder: {
    borderColor: '#dc3545',
  },
  errorText: {
    color: '#dc3545',
  },
  errorMessage: {
    color: '#dc3545',
    fontSize: 12,
    marginTop: 4,
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  appbar: {
    elevation: 0,
    backgroundColor: '#0F96BB',
  },
  appbarContent: {
    alignItems: 'center',
  },
  appbarTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  inputContainer: {
    marginBottom: 16,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  inputError: {
    borderColor: 'red',
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  requiredStar: {
    color: 'red',
    fontSize: 16,
  },
  modalContainer: {
    backgroundColor: 'white',
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: Dimensions.get('window').height * 0.7,
    paddingTop: 8,
    paddingBottom: Platform.OS === 'ios' ? 34 : 24,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  pickerModalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: 'auto',
  },
  pickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#f8f9fa',
  },
  pickerHeaderButton: {
    padding: 8,
  },
  pickerHeaderButtonText: {
    fontSize: 16,
    color: '#0077B6',
  },
  doneButton: {
    fontWeight: '600',
  },
  pickerHeaderTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  picker: {
    height: 200,
    backgroundColor: '#fff',
  },
  buttonContainer: {
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#dee2e6',
  },
  button: {
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    fontSize: 18,
    fontWeight: "600",
    color: "#fff",
    textAlign: 'center',
  },
  selectedItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    marginTop: 8,
  },
  selectedItemText: {
    color: '#2575FC',
    fontWeight: '500',
  },
  removeButton: {
    padding: 4,
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#fff',
  },
  dropdownText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  bottomSheet: {
    backgroundColor: 'white',
    padding: 16,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  bottomSheetHeader: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  dragIndicator: {
    width: 40,
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: 16,
    marginBottom: 16,
  },
  closeButton: {
    position: 'absolute',
    right: 16,
    top: 16,
    zIndex: 1,
    backgroundColor: '#F5F5F5',
    borderRadius: 20,
    padding: 6,
  },
  bottomSheetTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212529',
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 16,
    paddingHorizontal: 40,
  },
  searchContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#dee2e6',
    backgroundColor: '#fff',
  },
  searchInput: {
    height: 40,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  bottomSheetContent: {
    maxHeight: '100%',
  },
  listItem: {
    paddingVertical: 16,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#fff',
  },
  selectedListItem: {
    backgroundColor: '#f0f7ff',
  },
  bottomSheetItemText: {
    fontSize: 16,
    color: '#333',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
    paddingVertical: 8,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  timeZoneRegionHeader: {
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#dee2e6',
  },
  timeZoneRegionText: {
    fontSize: 13,
    fontWeight: '600',
    color: '#6c757d',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  timeZoneItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  timeZoneMainText: {
    flex: 1,
  },
  timeZoneName: {
    fontSize: 16,
    color: '#212529',
    marginBottom: 4,
  },
  timeZoneOffset: {
    fontSize: 14,
    color: '#6c757d',
  },
  selectedTimeZoneItem: {
    backgroundColor: '#f0f7ff',
  },
  timeZoneCheckIcon: {
    marginLeft: 12,
  },
  sectionHeader: {
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#dee2e6',
  },
  sectionHeaderText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6c757d',
    textTransform: 'uppercase',
  },
  sectionHeaderButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingRight: 16,
    backgroundColor: '#f8f9fa',
  },
  sectionHeaderContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionItemCount: {
    fontSize: 12,
    color: '#6c757d',
    marginLeft: 8,
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#dee2e6',
    backgroundColor: '#fff',
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#2575FC',
  },
  tabText: {
    fontSize: 14,
    color: '#666',
  },
  activeTabText: {
    color: '#2575FC',
    fontWeight: '600',
  },
  expandedModalContainer: {
    backgroundColor: 'white',
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: Dimensions.get('window').height * 0.9,
    paddingTop: 8,
    paddingBottom: Platform.OS === 'ios' ? 34 : 24,
  },
  confirmButtonContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#dee2e6',
    backgroundColor: '#fff',
  },
  confirmButton: {
    backgroundColor: '#2575FC',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  confirmButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

// Add MeetingResponse interface
interface MeetingResponse {
  id: number;
  title: string;
  description: string;
  from_date: string;
  to_date: string;
  status: string;
  location: string;
  outcome: string;
  // Add other fields as needed
}

const CreateMeetingScreen = () => {
  const router = useRouter();
  const params = useLocalSearchParams();

  // Parse meeting data if available (edit mode)
  const meetingData = React.useMemo(() => {
    try {
      if (params.data) {
        const data = JSON.parse(params.data as string);
        log('Parsed meeting data:', data);
        return data;
      }
      return null;
    } catch (error) {
      console.error('Error parsing meeting data:', error);
      return null;
    }
  }, [params.data]);

  const isEditMode = Boolean(meetingData);

  // Redirect to EditMeeting page if in edit mode
  useEffect(() => {
    if (isEditMode && meetingData) {
      console.log('🔄 Redirecting to EditMeeting page for meeting:', meetingData.id);
      router.replace({
        pathname: '/meetings/editMeeting',
        params: { data: JSON.stringify(meetingData) }
      });
    }
  }, [isEditMode, meetingData]);

  // Add loading state
  const [isSubmitting, setIsSubmitting] = useState(false);

  // API Data states
  const [outcomes, setOutcomes] = useState<TaskOutcome[]>([]);
  const [owners, setOwners] = useState<Owner[]>([]);
  const [relatedToItems, setRelatedToItems] = useState<ApiRelatedTo>({
    contacts: [],
    accounts: [],
    deals: []
  });

  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [location, setLocation] = useState('');
  const [userId, setUserId] = useState<string>('');

  // Time zone and related options state
  const [selectedTimeZone, setSelectedTimeZone] = useState<TimeZone>(DEFAULT_TIMEZONE);
  const [selectedRelatedTo, setSelectedRelatedTo] = useState<{ id: number; name: string; type: string } | null>(null);
  const [selectedOutcome, setSelectedOutcome] = useState<TaskOutcome | null>(null);
  const [selectedAttendees, setSelectedAttendees] = useState<number[]>([]);

  // Add loading state for task fields
  const [isLoadingTaskFields, setIsLoadingTaskFields] = useState(true);

  // Add a state to track if the user has changed the time values
  const [timeValuesChanged, setTimeValuesChanged] = useState({
    fromTime: false,
    toTime: false
  });

  // Add state for details data
  const [accountDetailsData, setAccountDetailsData] = useState<any>(null);
  const [contactDetailsData, setContactDetailsData] = useState<any>(null);
  const [dealDetailsData, setDealDetailsData] = useState<any>(null);

  const callFrom = params.callFrom as string;
  const salesAccountId = params.sales_account_id as string;
  const contactId = params.contact_id as string;
  const dealId = params.deal_id as string;

  // Add a state for form errors
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  // Parse details if available
  useEffect(() => {
    if (params.accountDetails) {
      try {
        const parsedDetails = JSON.parse(params.accountDetails as string);
        setAccountDetailsData(parsedDetails);
        console.log('Parsed account details:', parsedDetails);
      } catch (error) {
        console.error('Error parsing account details:', error);
      }
    }

    if (params.contactDetails) {
      try {
        const parsedDetails = JSON.parse(params.contactDetails as string);
        setContactDetailsData(parsedDetails);
        console.log('Parsed contact details:', parsedDetails);
      } catch (error) {
        console.error('Error parsing contact details:', error);
      }
    }

    if (params.dealDetails) {
      try {
        const parsedDetails = JSON.parse(params.dealDetails as string);
        setDealDetailsData(parsedDetails);
        console.log('Parsed deal details:', parsedDetails);
      } catch (error) {
        console.error('Error parsing deal details:', error);
      }
    }
  }, [params.accountDetails, params.contactDetails, params.dealDetails]);

  // Set related items if coming from detail pages
  useEffect(() => {
    // Handle account detail
    if (callFrom === 'accountDetail' && salesAccountId && !isEditMode) {
      console.log('Setting related account from account detail page');

      // Wait for relatedToItems to be loaded
      if (relatedToItems.accounts && relatedToItems.accounts.length > 0) {
        // Try to find the account in the related items
        const account = relatedToItems.accounts.find(a =>
          a && a.id && a.id.toString() === salesAccountId
        );

        if (account) {
          console.log('Found account in related items:', account);
          setSelectedRelatedTo({
            id: account.id,
            name: account.name,
            type: 'Account'
          });
        } else if (accountDetailsData && accountDetailsData.account) {
          // If account not found in related items but we have account details
          console.log('Using account from accountDetailsData:', accountDetailsData.account);
          setSelectedRelatedTo({
            id: parseInt(salesAccountId),
            name: accountDetailsData.account.name,
            type: 'Account'
          });
        }
      }
    }

    // Handle contact detail
    if (callFrom === 'contactDetail' && contactId && !isEditMode) {
      console.log('Setting related contact from contact detail page');

      // Wait for relatedToItems to be loaded
      if (relatedToItems.contacts && relatedToItems.contacts.length > 0) {
        // Try to find the contact in the related items
        const contact = relatedToItems.contacts.find(c =>
          c && c.id && c.id.toString() === contactId
        );

        if (contact) {
          console.log('Found contact in related items:', contact);
          setSelectedRelatedTo({
            id: contact.id,
            name: contact.name,
            type: 'Contact'
          });
        } else if (contactDetailsData && contactDetailsData.contact) {
          // If contact not found in related items but we have contact details
          console.log('Using contact from contactDetailsData:', contactDetailsData.contact);
          setSelectedRelatedTo({
            id: parseInt(contactId),
            name: contactDetailsData.contact.name,
            type: 'Contact'
          });
        }
      }
    }

    // Handle deal detail
    if (callFrom === 'dealDetail' && dealId && !isEditMode) {
      console.log('Setting related deal from deal detail page');

      // Wait for relatedToItems to be loaded
      if (relatedToItems.deals && relatedToItems.deals.length > 0) {
        // Try to find the deal in the related items
        const deal = relatedToItems.deals.find(d =>
          d && d.id && d.id.toString() === dealId
        );

        if (deal) {
          console.log('Found deal in related items:', deal);
          setSelectedRelatedTo({
            id: deal.id,
            name: deal.name,
            type: 'Deal'
          });
        } else if (dealDetailsData && dealDetailsData.deal) {
          // If deal not found in related items but we have deal details
          console.log('Using deal from dealDetailsData:', dealDetailsData.deal);
          setSelectedRelatedTo({
            id: parseInt(dealId),
            name: dealDetailsData.deal.name,
            type: 'Deal'
          });
        }
      }
    }
  }, [callFrom, salesAccountId, contactId, dealId, isEditMode, relatedToItems, accountDetailsData, contactDetailsData, dealDetailsData]);

  // Fetch user data and set owner
  const fetchUserData = async () => {
    try {
      const userDataString = await AsyncStorage.getItem('user_data');
      console.log('Fetched user data:', JSON.stringify(userDataString));
      if (userDataString) {
        const userData = JSON.parse(userDataString);
        setUserData(userData);

        // If not in edit mode, set the current user as the owner
        if (!isEditMode && userData.id) {
          setUserId(userData.id);
        }
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
    }
  };

  // Modify the fetchTaskFields function to set the current user as owner after owners are loaded
  const fetchTaskFields = async () => {
    try {
      setIsLoadingTaskFields(true);
      const response = await apiService.get<TaskFieldsResponse>(API_ENDPOINTS.TASK_TYPE);
      console.log('Fetched task fields response: ',API_ENDPOINTS.TASK_TYPE+" \n", JSON.stringify(response.data, null, 2));
      if (response.data) {
        const { outcomes: allOutcomes, owners: allOwners, related_to: relatedTo } = response.data.tasks;
        console.log('Fetched task fields:', { outcomes: allOutcomes, owners: allOwners, relatedTo });

        // Set the task fields first - make sure they're not null
        setOutcomes(allOutcomes || []);
        setOwners(allOwners || []);
        setRelatedToItems(relatedTo || { contacts: [], accounts: [], deals: [] });

        // Then initialize meeting data if in edit mode
        if (meetingData) {
          initializeMeetingData(meetingData,
                               relatedTo || { contacts: [], accounts: [], deals: [] },
                               allOutcomes || [],
                               allOwners || []);
        } else if (!isEditMode && userData && userData.id) {
          // Set the current user as owner for new meetings
          const currentOwner = (allOwners || []).find(owner =>
            owner && owner.id && userData.id &&
            owner.id.toString() === userData.id.toString()
          );
          if (currentOwner) {
            // Add the current user to attendees
            setSelectedAttendees([currentOwner.id]);
          }
        }

        // Set related items if coming from detail pages
        if (callFrom === 'accountDetail' && salesAccountId && !isEditMode) {
          const account = relatedTo.accounts.find(a => a.id.toString() === salesAccountId);
          if (account) {
            setSelectedRelatedTo({
              id: account.id,
              name: account.name,
              type: 'Account'
            });
          } else if (accountDetailsData && accountDetailsData.account) {
            setSelectedRelatedTo({
              id: parseInt(salesAccountId),
              name: accountDetailsData.account.name,
              type: 'Account'
            });
          }
        }

        if (callFrom === 'contactDetail' && contactId && !isEditMode) {
          const contact = relatedTo.contacts.find(c => c.id.toString() === contactId);
          if (contact) {
            setSelectedRelatedTo({
              id: contact.id,
              name: contact.name,
              type: 'Contact'
            });
          } else if (contactDetailsData && contactDetailsData.contact) {
            setSelectedRelatedTo({
              id: parseInt(contactId),
              name: contactDetailsData.contact.name,
              type: 'Contact'
            });
          }
        }

        if (callFrom === 'dealDetail' && dealId && !isEditMode) {
          const deal = relatedTo.deals.find(d => d.id.toString() === dealId);
          if (deal) {
            setSelectedRelatedTo({
              id: deal.id,
              name: deal.name,
              type: 'Deal'
            });
          } else if (dealDetailsData && dealDetailsData.deal) {
            setSelectedRelatedTo({
              id: parseInt(dealId),
              name: dealDetailsData.deal.name,
              type: 'Deal'
            });
          }
        }
      }
    } catch (error) {
      console.error('Error fetching task fields:', error);
      showSnackbar('Failed to load data');
    } finally {
      setIsLoadingTaskFields(false);
    }
  };

  // Function to initialize meeting data
  const initializeMeetingData = (
    meetingData: any,
    relatedTo: ApiRelatedTo,
    taskOutcomes: TaskOutcome[],
    taskOwners: Owner[]
  ) => {
    console.log('Full meeting data:', JSON.stringify(meetingData, null, 2));

    setTitle(meetingData.activity_title || '');
    setDescription(meetingData.description || '');
    setLocation(meetingData.location || '');
    setIsAllDay(meetingData.all_day === "true");
    setSelectedTimeZone(DEFAULT_TIMEZONE);

    // Set start date - handle timezone issues to prevent date shift
    if (meetingData.start_date) {
      try {
        // Parse the date parts directly to avoid timezone issues
        const dateParts = meetingData.start_date.split('-');

        // Validate date parts before using them
        if (dateParts.length === 3) {
          const year = parseInt(dateParts[0], 10);
          const month = parseInt(dateParts[1], 10);
          const day = parseInt(dateParts[2], 10);

          // Validate that the date components are valid numbers
          if (!isNaN(year) && !isNaN(month) && !isNaN(day) &&
              year > 0 && month >= 1 && month <= 12 && day >= 1 && day <= 31) {
            // Create date with local timezone (months are 0-indexed in JS)

            const startDate = new Date(year, month - 1, day);
            const dateObject = parseISO(meetingData.start_date);
            console.log('Start date:', startDate.toISOString(), 'Original:', meetingData.start_date);
            setFromDate(dateObject);
          } else {
            console.error('Invalid date components:', { year, month, day });
            setFromDate(new Date());
          }
        } else {
          console.error('Invalid date format:', meetingData.start_date);
          setFromDate(new Date());
        }
      } catch (error) {
        console.error('Error parsing start_date:', error);
        setFromDate(new Date());
      }
    }

    // Set end date - handle timezone issues to prevent date shift
    if (meetingData.end_date) {
      try {
        // Parse the date parts directly to avoid timezone issues
        const dateParts = meetingData.end_date.split('-');

        // Validate date parts before using them
        if (dateParts.length === 3) {
          const year = parseInt(dateParts[0], 10);
          const month = parseInt(dateParts[1], 10);
          const day = parseInt(dateParts[2], 10);

          // Validate that the date components are valid numbers
          if (!isNaN(year) && !isNaN(month) && !isNaN(day) &&
              year > 0 && month >= 1 && month <= 12 && day >= 1 && day <= 31) {
            // Create date with local timezone (months are 0-indexed in JS)
            const endDate = new Date(year, month - 1, day);
            const dateObject = parseISO(meetingData.end_date);
            console.log('End date:', endDate.toISOString(), 'Original:', meetingData.end_date);
            setToDate(dateObject);
          } else {
            console.error('Invalid date components:', { year, month, day });
            setToDate(new Date());
          }
        } else {
          console.error('Invalid date format:', meetingData.end_date);
          setToDate(new Date());
        }
      } catch (error) {
        console.error('Error parsing end_date:', error);
        setToDate(new Date());
      }
    }

    // Set start time
    if (meetingData.start_time && !meetingData.all_day) {
      try {
        const startTime = parseTime(meetingData.start_time);
        console.log('Parsed start time:', startTime.toLocaleTimeString());
        setFromTime(startTime);
      } catch (error) {
        console.error('Error parsing start_time:', error);
        setFromTime(new Date());
      }
    }

    // Set end time
    if (meetingData.end_time && !meetingData.all_day) {
      try {
        const endTime = parseTime(meetingData.end_time);
        console.log('Parsed end time:', endTime.toLocaleTimeString());
        setToTime(endTime);
      } catch (error) {
        console.error('Error parsing end_time:', error);
        // Set end time to 30 minutes after start time
        const thirtyMinutesLater = new Date(fromTime);
        thirtyMinutesLater.setMinutes(fromTime.getMinutes() + 30);
        setToTime(thirtyMinutesLater);
      }
    }

    // Set Related To
    if (meetingData.targetable_id && meetingData.targetable_type) {
      const type = meetingData.targetable_type.toLowerCase();
      const allItems = getAllRelatedItems(relatedTo);

      const relatedItem = allItems.find((item) =>
        item &&
        item.id !== undefined &&
        item.id !== null &&
        item.id.toString() === meetingData.targetable_id.toString() &&
        item.type &&
        typeof item.type === 'string' &&
        item.type.toLowerCase() === type.replace('s', '')
      );

      if (relatedItem) {
        setSelectedRelatedTo({
          id: relatedItem.id,
          name: relatedItem.name,
          type: relatedItem.type
        });
      }
    }

    // Set Outcome
    if (meetingData.outcome && taskOutcomes.length > 0) {
      const outcome = taskOutcomes.find(o =>
        o && o.outcome && typeof o.outcome === 'string' &&
        meetingData.outcome && typeof meetingData.outcome === 'string' &&
        o.outcome.toLowerCase() === meetingData.outcome.toLowerCase()
      );
      if (outcome) {
        setSelectedOutcome(outcome);
      }
    }

    // Set Attendees - check both attendees and attendees_details
    console.log('Setting attendees from meeting data:', {
      attendees: meetingData.attendees,
      attendees_details: meetingData.attendees_details
    });

    let attendeesArray = null;

    // First check attendees_details (from detailed meeting API response)
    if (meetingData.attendees_details && Array.isArray(meetingData.attendees_details)) {
      attendeesArray = meetingData.attendees_details;
      console.log('Using attendees_details:', attendeesArray);
    }
    // Fallback to attendees (from other sources)
    else if (meetingData.attendees && Array.isArray(meetingData.attendees)) {
      attendeesArray = meetingData.attendees;
      console.log('Using attendees:', attendeesArray);
    }

    if (attendeesArray) {
      const attendeeIds = attendeesArray
        .filter((attendee: any) => attendee && (attendee.id || attendee.user_id))
        .map((attendee: any) => {
          const id = attendee.user_id || attendee.id;
          return typeof id === 'string' ? parseInt(id, 10) : id;
        })
        .filter((id: number) => id && !isNaN(id) && taskOwners.some(owner => owner && owner.id === id));

      console.log('Processed attendee IDs:', attendeeIds);
      setSelectedAttendees(attendeeIds);
    } else {
      console.log('No attendees found in meeting data');
    }
  };

  // Initialize form data and fetch task fields
  useEffect(() => {
    const now = new Date();
    console.log('Current date/time:', now.toLocaleString());

    // Set default dates and times only if not in edit mode
    if (!meetingData) {
      setFromDate(now);
      setFromTime(now);

      // Set end time to 30 minutes after current time
      const thirtyMinutesLater = new Date(now);
      thirtyMinutesLater.setMinutes(now.getMinutes() + 30);

      setToDate(now);
      setToTime(thirtyMinutesLater);
      setSelectedTimeZone(DEFAULT_TIMEZONE);
    }

    fetchUserData();
    fetchTaskFields();
  }, [meetingData]);

  // Remove the second useEffect that was handling meetingData initialization

  // Remove the initial state values from useState declarations
  const [fromDate, setFromDate] = useState(new Date());
  const [fromTime, setFromTime] = useState(new Date());
  const [toDate, setToDate] = useState(new Date());
  const [toTime, setToTime] = useState(new Date());

  const [showFromDatePicker, setShowFromDatePicker] = useState(false);
  const [showFromTimePicker, setShowFromTimePicker] = useState(false);
  const [showToDatePicker, setShowToDatePicker] = useState(false);
  const [showToTimePicker, setShowToTimePicker] = useState(false);
  const [fromDateError, setFromDateError] = useState<string>("");
  const [fromTimeError, setFromTimeError] = useState<string>("");
  const [toDateError, setToDateError] = useState<string>("");
  const [toTimeError, setToTimeError] = useState<string>("");

  // Add state for modals
  const [showTimeZoneModal, setShowTimeZoneModal] = useState(false);
  const [showRelatedToModal, setShowRelatedToModal] = useState(false);
  const [showOutcomeModal, setShowOutcomeModal] = useState(false);
  const [showAttendeesModal, setShowAttendeesModal] = useState(false);

  // Remove static data references
  const timeZones = TIME_ZONES;

  const [activeTab, setActiveTab] = useState<'contacts' | 'accounts' | 'deals'>('contacts');
  const [searchQuery, setSearchQuery] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);

  // Add this state for bottom sheet search
  const [bottomSheetSearchQuery, setBottomSheetSearchQuery] = useState('');

  const [userData, setUserData] = useState<any>(null);

  // Add state for all day switch
  const [isAllDay, setIsAllDay] = useState(meetingData?.all_day === "true");

  // Add new states for temporary date and time values
  const [tempFromDate, setTempFromDate] = useState(new Date());
  const [tempFromTime, setTempFromTime] = useState(new Date());
  const [tempToDate, setTempToDate] = useState(new Date());
  const [tempToTime, setTempToTime] = useState(new Date());

  const isToDateTimeValid = (selectedDate: Date, selectedTime: Date): boolean => {
    const fromDateTime = new Date(fromDate);
    fromDateTime.setHours(fromTime.getHours(), fromTime.getMinutes());

    const toDateTime = new Date(selectedDate);
    toDateTime.setHours(selectedTime.getHours(), selectedTime.getMinutes());

    return toDateTime > fromDateTime;
  };

  const onFromDateChange = (event: any, selectedDate?: Date) => {
    // On Android, the picker is automatically dismissed after selection
    if (Platform.OS === 'android') {
    setShowFromDatePicker(false);
    if (selectedDate) {
        setTempFromDate(selectedDate);

        // Directly apply the date on Android
        const now = new Date();
        now.setHours(0, 0, 0, 0);
        const selected = new Date(selectedDate);
        selected.setHours(0, 0, 0, 0);

        // For new meetings, validate the date against today
        if (!isEditMode && selected < now) {
          setFromDateError("Please select today or a future date");
          Alert.alert("Invalid Date", "Please select today or a future date");
          return;
        }

        setFromDate(selectedDate);

        // If to date is before from date, adjust it
        if (toDate < selectedDate) {
          setToDate(selectedDate);
        }
        }
      } else {
      // On iOS, just update the temp value
      if (selectedDate) {
        setTempFromDate(selectedDate);
      }
    }
  };

  const handleFromDateConfirm = () => {
    setShowFromDatePicker(false);
    setFromDateError("");

    if (tempFromDate) {
      const now = new Date();
      now.setHours(0, 0, 0, 0);
      const selected = new Date(tempFromDate);
      selected.setHours(0, 0, 0, 0);

      // For new meetings, validate the date against today
      if (!isEditMode && selected < now) {
        setFromDateError("Please select today or a future date");
        Alert.alert("Invalid Date", "Please select today or a future date");
        return;
      }

      setFromDate(tempFromDate);

      // If to date is before from date, adjust it
      if (toDate < tempFromDate) {
        setToDate(tempFromDate);
      }
    }
  };

  const onFromTimeChange = (event: any, selectedTime?: Date) => {
    // On Android, the picker is automatically dismissed after selection
    if (Platform.OS === 'android') {
    setShowFromTimePicker(false);
    if (selectedTime) {
        setTempFromTime(selectedTime);

        // Directly apply the time on Android
        const newTime = new Date(fromDate);
        newTime.setHours(selectedTime.getHours());
        newTime.setMinutes(selectedTime.getMinutes());
        newTime.setSeconds(0);

        // Mark that the user has changed the from time
        setTimeValuesChanged(prev => ({ ...prev, fromTime: true }));

        setFromTime(newTime);

        // If all-day is true, set it to false since a specific time was selected
        if (isAllDay) {
          setIsAllDay(false);
        }

        // If to time is before from time on the same day, adjust it
        if (
          toDate.getDate() === fromDate.getDate() &&
          toDate.getMonth() === fromDate.getMonth() &&
          toDate.getFullYear() === fromDate.getFullYear() &&
          toTime.getHours() < newTime.getHours() ||
          (toTime.getHours() === newTime.getHours() && toTime.getMinutes() < newTime.getMinutes())
        ) {
          // Set to time to be 30 minutes after from time
          const newToTime = new Date(newTime);
          newToTime.setMinutes(newTime.getMinutes() + 30);
          setToTime(newToTime);
        }
        }
      } else {
      // On iOS, just update the temp value
      if (selectedTime) {
        setTempFromTime(selectedTime);
      }
    }
  };

  const onToDateChange = (event: any, selectedDate?: Date) => {
    // On Android, the picker is automatically dismissed after selection
    if (Platform.OS === 'android') {
    setShowToDatePicker(false);
      if (selectedDate) {
        setTempToDate(selectedDate);

        // Directly apply the date on Android
        setToDate(selectedDate);
    setToDateError("");

        // If the to date is different from the from date, reset the to time
        if (
          selectedDate.getDate() !== fromDate.getDate() ||
          selectedDate.getMonth() !== fromDate.getMonth() ||
          selectedDate.getFullYear() !== fromDate.getFullYear()
        ) {
          // Set to time to be the same time as from time
          const newToTime = new Date(selectedDate);
          newToTime.setHours(fromTime.getHours(), fromTime.getMinutes(), 0);
          setToTime(newToTime);
        }
      }
    } else {
      // On iOS, just update the temp value
    if (selectedDate) {
        setTempToDate(selectedDate);
      }
    }
  };

  const handleToDateConfirm = () => {
    setShowToDatePicker(false);
    setToDateError("");

    if (tempToDate) {
      if (tempToDate >= fromDate) {
        setToDate(tempToDate);
      } else {
        setToDateError("End date must be after start date");
        Alert.alert("Invalid Date", "End date must be after start date");
      }
    }
  };

  const onToTimeChange = (event: any, selectedTime?: Date) => {
    // On Android, the picker is automatically dismissed after selection
    if (Platform.OS === 'android') {
    setShowToTimePicker(false);
    if (selectedTime) {
        setTempToTime(selectedTime);

        // Directly apply the time on Android
        const newTime = new Date(toDate);
        newTime.setHours(selectedTime.getHours());
        newTime.setMinutes(selectedTime.getMinutes());
        newTime.setSeconds(0);

        // Mark that the user has changed the to time
        setTimeValuesChanged(prev => ({ ...prev, toTime: true }));

        // Validate the time
        if (
          toDate.getDate() === fromDate.getDate() &&
          toDate.getMonth() === fromDate.getMonth() &&
          toDate.getFullYear() === fromDate.getFullYear() &&
          selectedTime.getHours() < fromTime.getHours() ||
          (selectedTime.getHours() === fromTime.getHours() && selectedTime.getMinutes() < fromTime.getMinutes())
        ) {
        setToTimeError("End time must be after start time");
        Alert.alert("Invalid Time", "End time must be after start time");
          return;
        }

        setToTime(newTime);
        setToTimeError("");

        // If all-day is true, set it to false since a specific time was selected
        if (isAllDay) {
          setIsAllDay(false);
        }
      }
    } else {
      // On iOS, just update the temp value
      if (selectedTime) {
        setTempToTime(selectedTime);
      }
    }
  };

  const isDateValid = (date: Date): boolean => {
    const now = new Date();
    now.setHours(0, 0, 0, 0);
    const compareDate = new Date(date);
    compareDate.setHours(0, 0, 0, 0);

    // In edit mode, allow the existing date if it matches the meeting data
    if (meetingData && meetingData.start_date) {
      const meetingStartDate = new Date(meetingData.start_date);
      meetingStartDate.setHours(0, 0, 0, 0);
      if (compareDate.getTime() === meetingStartDate.getTime()) {
        return true;
      }
    }

    return compareDate >= now;
  };

  const isTimeValid = (time: Date): boolean => {
    // In edit mode, always allow the time if it matches the meeting data time
    if (isEditMode && meetingData) {
      if (meetingData.start_time) {
        const [meetingHours, meetingMinutes] = meetingData.start_time.split(':').map(Number);
        const meetingTime = new Date();
        meetingTime.setHours(meetingHours, meetingMinutes, 0, 0);

        const compareTime = new Date(time);
        compareTime.setSeconds(0, 0);

        if (compareTime.getHours() === meetingTime.getHours() &&
            compareTime.getMinutes() === meetingTime.getMinutes()) {
          return true;
        }
      }
    }

    const now = new Date();
    if (fromDate.toDateString() === now.toDateString()) {
      // Only validate against current time if it's today's date and not editing
      return time > now;
    }
    // Allow any time for future dates
    return true;
  };

  const validateForm = (): boolean => {
    let isValid = true;
    const errors: { [key: string]: string } = {};

    // Required field validations
    if (!title) {
      errors.title = 'Title is required';
      isValid = false;
    }

    if (!description) {
      errors.description = 'Description is required';
      isValid = false;
    }

    // Only validate owner if not automatically set
    if (selectedAttendees.length === 0) {
      errors.attendees = 'At least one attendee is required';
      isValid = false;
    }

    // Only validate related to if not coming from detail pages
    if (!selectedRelatedTo &&
        callFrom !== 'accountDetail' &&
        callFrom !== 'contactDetail' &&
        callFrom !== 'dealDetail') {
      errors.relatedTo = 'Related To is required';
      isValid = false;
    }

    // Date and time validations
    if (!fromDate) {
      errors.fromDate = 'From Date is required';
      isValid = false;
    }

    if (!fromTime) {
      errors.fromTime = 'From Time is required';
      isValid = false;
    }

    if (!toDate) {
      errors.toDate = 'To Date is required';
      isValid = false;
    }

    if (!toTime) {
      errors.toTime = 'To Time is required';
      isValid = false;
    }

    // Check if To date/time is after From date/time
    if (fromDate && fromTime && toDate && toTime) {
      const fromDateTime = new Date(
        fromDate.getFullYear(),
        fromDate.getMonth(),
        fromDate.getDate(),
        fromTime.getHours(),
        fromTime.getMinutes()
      );
      const toDateTime = new Date(
        toDate.getFullYear(),
        toDate.getMonth(),
        toDate.getDate(),
        toTime.getHours(),
        toTime.getMinutes()
      );

      if (toDateTime <= fromDateTime) {
        errors.toDateTime = 'End time must be after start time';
        isValid = false;
      }
    }

    setFormErrors(errors);
    return isValid;
  };

  const handleSubmit = async () => {
    // Log the current state for debugging
    console.log('Submitting meeting with data:', {
      title,
      description,
      location,
      fromDate: fromDate.toISOString(),
      fromTime: fromTime.toISOString(),
      toDate: toDate.toISOString(),
      toTime: toTime.toISOString(),
      isAllDay,
      selectedTimeZone,
      selectedRelatedTo,
      selectedOutcome,
      selectedAttendees,
      isEditMode,
      meetingId: meetingData?.id || 'new'
    });

    if (!validateForm()) {
      // Display errors in UI instead of alerts
      return;
    }

    try {
      setIsSubmitting(true);

      // Format dates for API
      const fromDateTime = new Date(
        fromDate.getFullYear(),
        fromDate.getMonth(),
        fromDate.getDate(),
        fromTime.getHours(),
        fromTime.getMinutes()
      );

      const toDateTime = new Date(
        toDate.getFullYear(),
        toDate.getMonth(),
        toDate.getDate(),
        toTime.getHours(),
        toTime.getMinutes()
      );

      // Format date and time in the required format
      const formatDate = (date: Date) => {
        return format(date, "yyyy-MM-dd");
      };

      const formatTime = (date: Date) => {
        return format(date, "hh:mm a");
      };

      // Create the payload with the new structure
      interface MeetingPayload {
        activity_title: string;
        activity_type: string;
        all_day: string;
        salesactivities_id: string;
        description: string;
        location: string;
        start_date: string;
        start_time: string;
        end_date: string;
        end_time: string;
        notes: string;
        outcome: string;
        owner_id: string | undefined;
        targetable_id: string;
        targetable_type: string;
        timezone: string;
        attendees: string;
        //attendees: { user_id: number }[];
        id?: string | number; // Optional id property
      }

      const meetingPayload: MeetingPayload = {
        activity_title: title,
        activity_type: 'meeting',
        all_day: isAllDay.toString(),
        salesactivities_id: '3',
        description,
        location,
        start_date: formatDate(fromDateTime),
        start_time: formatTime(fromDateTime),
        end_date: formatDate(toDateTime),
        end_time: formatTime(toDateTime),
        notes: description,
        outcome: selectedOutcome?.outcome || "",
        owner_id: userData?.id,
        targetable_id: selectedRelatedTo?.id?.toString() || "",
        targetable_type: selectedRelatedTo ? selectedRelatedTo.type.toLowerCase()+"s" : "",
        timezone: selectedTimeZone?.name || "",
        attendees: selectedAttendees.join(','), // Convert to comma-separated string
        //attendees: selectedAttendees.map(id => ({ user_id: id })) // Format attendees properly
      };

      // Only include id in the payload if we're in edit mode and have a valid id
      if (isEditMode && meetingData && meetingData.id) {
        meetingPayload.id = meetingData.id;
      }

      console.log('Submitting payload:', meetingPayload);

      let response;
      if (isEditMode && meetingData && meetingData.id) {
        console.log('Editing meeting:', meetingData.id);
        // Make sure we're using the correct endpoint and including the ID in the URL
        response = await apiService.put(
          `${API_ENDPOINTS.MEETING_ADD}/${meetingData.id}`,
          meetingPayload
        );
      } else {
        response = await apiService.post(
          API_ENDPOINTS.MEETING_ADD,
          meetingPayload
        );
      }

      console.log('API Response:', response.data);

      if (response.status === 200 || response.status === 201) {
        //showSnackbar(isEditMode ? 'Meeting updated successfully' : 'Meeting created successfully');

        Alert.alert(
          'Success',
          response.data.message || (isEditMode ? 'Meeting updated successfully' : 'Meeting created successfully'),
          [{
            text: 'OK',
            onPress: () => {
              router.back();
              // Navigate to task details page with the created/updated task data
              const meetingId = response.data.data?.id || (meetingData && meetingData.id);
              if (meetingId) {
                router.replace({
                  pathname: '/meetings/detailedMeeting',
                  params: { data: meetingId }
                });
              }
            }
          }]
        );
      } else {
        showSnackbar('Failed to save meeting');
      }
    } catch (error) {
      console.error('Error saving meeting:', error);
      showSnackbar('Failed to save meeting');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Add state for temp selected attendees at component level
  const [tempSelectedAttendees, setTempSelectedAttendees] = useState<number[]>([]);

  // Update tempSelectedAttendees when attendees modal becomes visible
  useEffect(() => {
    if (showAttendeesModal) {
      console.log('Attendees modal opened, setting tempSelectedAttendees to:', selectedAttendees);
      setTempSelectedAttendees(selectedAttendees);
    }
  }, [showAttendeesModal, selectedAttendees]);

  const renderBottomSheet = (
    visible: boolean,
    onDismiss: () => void,
    title: string,
    items: any[],
    onSelect: (item: any) => void,
    selectedItem?: any,
    multiSelect: boolean = false
  ) => {
    const handleConfirm = () => {
      if (multiSelect) {
        setSelectedAttendees(tempSelectedAttendees);
      }
      setBottomSheetSearchQuery('');
      onDismiss();
    };

    const filteredItems = items.filter(item => {
      const itemName = item?.name;
      const itemOutcome = item?.outcome;
      return (item && ((typeof itemName === 'string' && itemName.toLowerCase().includes(bottomSheetSearchQuery.toLowerCase())) ||
         (typeof itemOutcome === 'string' && itemOutcome.toLowerCase().includes(bottomSheetSearchQuery.toLowerCase()))));
    });

    return (
      <Portal>
        <RNModal
          visible={visible}
          transparent
          animationType="slide"
          onRequestClose={onDismiss}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContainer}>
            <View style={styles.bottomSheetHeader}>
              <View style={styles.dragIndicator} />
              <TouchableOpacity
                style={styles.closeButton}
                  onPress={onDismiss}
              >
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
              <Text style={styles.bottomSheetTitle}>{title}</Text>

              <View style={styles.searchContainer}>
                <TextInput
                  style={styles.searchInput}
                  placeholder="Search attendees..."
                  placeholderTextColor="#666"
                  value={bottomSheetSearchQuery}
                  onChangeText={setBottomSheetSearchQuery}
                />
              </View>
            </View>

            <ScrollView style={[styles.bottomSheetContent, { maxHeight: '80%' }]}>
              {filteredItems.length > 0 ? (
                filteredItems.map((item) => (
                  <TouchableOpacity
                    key={item.id}
                    style={[
                      styles.listItem,
                      (multiSelect ? tempSelectedAttendees.includes(item.id) : selectedItem?.id === item.id) && styles.selectedListItem
                    ]}
                    onPress={() => {
                      if (multiSelect) {
                        setTempSelectedAttendees(prev =>
                          prev.includes(item.id)
                            ? prev.filter(id => id !== item.id)
                            : [...prev, item.id]
                        );
                      } else {
                        onSelect(item);
                        setBottomSheetSearchQuery('');
                        onDismiss();
                      }
                    }}
                  >
                    <Text style={[
                      styles.bottomSheetItemText,
                      (multiSelect ? tempSelectedAttendees.includes(item.id) : selectedItem?.id === item.id) && styles.selectedItemText
                    ]}>
                      {item.name || item.outcome}
                    </Text>
                    {(multiSelect ? tempSelectedAttendees.includes(item.id) : selectedItem?.id === item.id) && (
                      <Ionicons name="checkmark" size={24} color="#2575FC" />
                    )}
                  </TouchableOpacity>
                ))
              ) : (
                <View style={{ padding: 24, alignItems: 'center' }}>
                  <Text style={{ color: '#666', fontSize: 16 }}>
                    {bottomSheetSearchQuery ? 'No outcomes found' : 'No outcomes available'}
                  </Text>
                </View>
              )}
            </ScrollView>

            {multiSelect && (
              <View style={styles.confirmButtonContainer}>
                <TouchableOpacity
                  style={styles.confirmButton}
                  onPress={handleConfirm}
                >
                  <Text style={styles.confirmButtonText}>
                    Confirm Selection ({tempSelectedAttendees.length})
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
          </View>
        </RNModal>
      </Portal>
    );
  };

  const renderTimeZoneBottomSheet = (
    visible: boolean,
    onDismiss: () => void,
  ) => {
    // Group time zones by region
    const groupedTimeZones = TIME_ZONES.reduce((acc, timeZone) => {
      if (timeZone) {
        const region = timeZone.region || 'Other';
        if (!acc[region]) acc[region] = [];
        acc[region].push(timeZone);
      }
      return acc;
    }, {} as Record<string, TimeZone[]>);

    // Sort regions to ensure UTC is first
    const sortedRegions = Object.keys(groupedTimeZones).sort((a, b) =>
      a === 'UTC' ? -1 : b === 'UTC' ? 1 : a.localeCompare(b)
    );

    return (
      <Portal>
        <RNModal
          visible={visible}
          transparent
          animationType="slide"
          onRequestClose={onDismiss}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContainer}>
            <View style={styles.bottomSheetHeader}>
              <View style={styles.dragIndicator} />
              <TouchableOpacity
                style={styles.closeButton}
                onPress={onDismiss}
              >
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
              <Text style={styles.bottomSheetTitle}>Select Time Zone</Text>

              <View style={styles.searchContainer}>
                <TextInput
                  style={styles.searchInput}
                  placeholder="Search time zones..."
                  placeholderTextColor="#666"
                  value={bottomSheetSearchQuery}
                  onChangeText={setBottomSheetSearchQuery}
                />
              </View>
            </View>

            <ScrollView style={styles.bottomSheetContent}>
              {sortedRegions.map((region) => {
                const filteredTimeZones = groupedTimeZones[region].filter(timeZone =>
                  timeZone &&
                  ((timeZone.name && typeof timeZone.name === 'string' &&
                    timeZone.name.toLowerCase().includes(bottomSheetSearchQuery.toLowerCase())) ||
                   (timeZone.abbreviation && typeof timeZone.abbreviation === 'string' &&
                    timeZone.abbreviation.toLowerCase().includes(bottomSheetSearchQuery.toLowerCase())))
                );

                if (filteredTimeZones.length === 0) return null;

                return (
                  <View key={region}>
                    <View style={styles.timeZoneRegionHeader}>
                      <Text style={styles.timeZoneRegionText}>{region}</Text>
                    </View>
                    {filteredTimeZones.map((timeZone) => (
                      <TouchableOpacity
                        key={timeZone.id}
                        style={[
                          styles.timeZoneItem,
                          selectedTimeZone?.id === timeZone.id && styles.selectedListItem
                        ]}
                        onPress={() => {
                          setSelectedTimeZone(timeZone);
                          setBottomSheetSearchQuery('');
                          onDismiss();
                        }}
                      >
                        <View style={styles.timeZoneMainText}>
                          <Text style={[
                            styles.timeZoneName,
                            selectedTimeZone?.id === timeZone.id && styles.selectedItemText
                          ]}>
                            {timeZone.name}
                          </Text>
                          <Text style={styles.timeZoneOffset}>
                            {`UTC${timeZone.offset >= 0 ? '+' : ''}${timeZone.offset}`} ({timeZone.abbreviation})
                          </Text>
                        </View>
                        {selectedTimeZone?.id === timeZone.id && (
                          <View style={styles.timeZoneCheckIcon}>
                            <Ionicons name="checkmark" size={24} color="#2575FC" />
                          </View>
                        )}
                      </TouchableOpacity>
                    ))}
                  </View>
                );
              })}
              {sortedRegions.every(region =>
                groupedTimeZones[region].every(timeZone =>
                  !(timeZone &&
                    ((timeZone.name && typeof timeZone.name === 'string' &&
                      timeZone.name.toLowerCase().includes(bottomSheetSearchQuery.toLowerCase())) ||
                     (timeZone.abbreviation && typeof timeZone.abbreviation === 'string' &&
                      timeZone.abbreviation.toLowerCase().includes(bottomSheetSearchQuery.toLowerCase()))))
                )
              ) && (
                <View style={{ padding: 24, alignItems: 'center' }}>
                  <Text style={{ color: '#666', fontSize: 16 }}>
                    No time zones found
                  </Text>
                </View>
              )}
            </ScrollView>
          </View>
          </View>
        </RNModal>
      </Portal>
    );
  };

  const getFilteredRelatedItems = () => {
    const query = searchQuery.toLowerCase();
    switch (activeTab) {
      case 'contacts':
        return relatedToItems.contacts.filter(item =>
          item && item.name && typeof item.name === 'string' &&
          item.name.toLowerCase().includes(query)
        );
      case 'accounts':
        return relatedToItems.accounts.filter(item =>
          item && item.name && typeof item.name === 'string' &&
          item.name.toLowerCase().includes(query)
        );
      case 'deals':
        return relatedToItems.deals.filter(item =>
          item && item.name && typeof item.name === 'string' &&
          item.name.toLowerCase().includes(query)
        );
      default:
        return [];
    }
  };

  const renderRelatedToBottomSheet = () => {
    const filteredItems = getFilteredRelatedItems();

    return (
      <Portal>
        <RNModal
          visible={showRelatedToModal}
          transparent
          animationType="slide"
          onRequestClose={() => {
            setShowRelatedToModal(false);
            setSearchQuery('');
            setIsExpanded(false);
          }}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContainer}>
              <View style={styles.bottomSheetHeader}>
            <View style={styles.dragIndicator} />
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => {
                setShowRelatedToModal(false);
                setSearchQuery('');
                setIsExpanded(false);
              }}
            >
              <Ionicons name="close" size={24} color="#666" />
            </TouchableOpacity>

            <View style={styles.searchContainer}>
              <TextInput
                style={styles.searchInput}
                placeholder="Search..."
                value={searchQuery}
                onChangeText={setSearchQuery}
                onFocus={() => setIsExpanded(true)}
              />
                </View>
            </View>

            <View style={styles.tabContainer}>
              <TouchableOpacity
                style={[styles.tab, activeTab === 'contacts' && styles.activeTab]}
                onPress={() => setActiveTab('contacts')}
              >
                <Text style={[
                  styles.tabText,
                  activeTab === 'contacts' && styles.activeTabText
                ]}>
                  Contacts ({relatedToItems.contacts.length})
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.tab, activeTab === 'accounts' && styles.activeTab]}
                onPress={() => setActiveTab('accounts')}
              >
                <Text style={[
                  styles.tabText,
                  activeTab === 'accounts' && styles.activeTabText
                ]}>
                  Accounts ({relatedToItems.accounts.length})
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.tab, activeTab === 'deals' && styles.activeTab]}
                onPress={() => setActiveTab('deals')}
              >
                <Text style={[
                  styles.tabText,
                  activeTab === 'deals' && styles.activeTabText
                ]}>
                  Deals ({relatedToItems.deals.length})
                </Text>
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.bottomSheetContent}>
              {filteredItems.length > 0 ? (
                filteredItems.map((item) => (
                  <TouchableOpacity
                    key={`${activeTab}-${item.id}`}
                    style={[
                      styles.listItem,
                      selectedRelatedTo?.id === item.id &&
                      selectedRelatedTo?.type.toLowerCase() === activeTab &&
                      styles.selectedListItem
                    ]}
                    onPress={() => {
                      setSelectedRelatedTo({
                        id: item.id,
                        name: item.name,
                        type: activeTab === 'contacts' ? 'Contact' :
                              activeTab === 'accounts' ? 'Account' : 'Deal'
                      });
                      setShowRelatedToModal(false);
                      setSearchQuery('');
                      setIsExpanded(false);
                    }}
                  >
                    <Text style={[
                      styles.bottomSheetItemText,
                      selectedRelatedTo?.id === item.id &&
                      selectedRelatedTo?.type.toLowerCase() === activeTab &&
                      { color: '#2575FC', fontWeight: '500' }
                    ]}>
                      {item.name}
                    </Text>
                    {selectedRelatedTo?.id === item.id &&
                     selectedRelatedTo?.type.toLowerCase() === activeTab && (
                      <Ionicons name="checkmark" size={20} color="#2575FC" />
                    )}
                  </TouchableOpacity>
                ))
              ) : (
                <View style={{ padding: 16, alignItems: 'center' }}>
                  <Text style={{ color: '#666' }}>
                    {searchQuery ? 'No results found' : `No ${activeTab} available`}
                  </Text>
                </View>
              )}
            </ScrollView>
          </View>
          </View>
        </RNModal>
      </Portal>
    );
  };

  // Add this to the render function to log the current time values
  console.log('Current time values:', {
    fromTime: fromTime.toLocaleTimeString(),
    toTime: toTime.toLocaleTimeString(),
    isEditMode
  });

  // Initialize temp dates
  useEffect(() => {
    // Set temp dates to match current values when the component mounts
    setTempFromDate(fromDate);
    setTempFromTime(fromTime);
    setTempToDate(toDate);
    setTempToTime(toTime);
  }, [fromDate, fromTime, toDate, toTime]);

  // Add an error text component
  const ErrorText = ({ error }: { error?: string }) => {
    if (!error) return null;
    return (
      <Text style={{ color: 'red', fontSize: 12, marginTop: 4 }}>
        {error}
      </Text>
    );
  };

  return (
    <PaperProvider>
      <View style={styles.container}>
        <Appbar.Header style={styles.appbar}>
          <TouchableOpacity onPress={() => router.back()} style={{ padding: 8 }}>
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Appbar.Content
            title={isEditMode ? "Edit Meeting" : "Create Meeting"}
            titleStyle={styles.appbarTitle}
          />
        </Appbar.Header>

        <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollViewContent}>
          {/* Title Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Title<Text style={styles.requiredStar}>*</Text></Text>
            <TextInput
              style={[styles.input, formErrors.title && styles.inputError]}
              placeholder="Enter title"
              value={title}
              onChangeText={setTitle}
            />
            <ErrorText error={formErrors.title} />
          </View>

          {/* Description Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Description<Text style={styles.requiredStar}>*</Text></Text>
            <TextInput
              style={[styles.input, formErrors.description && styles.inputError, { height: 100 }]}
              placeholder="Enter description"
              value={description}
              onChangeText={setDescription}
              multiline
              textAlignVertical="top"
            />
            <ErrorText error={formErrors.description} />
          </View>

          {/* Location Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Location</Text>
            <TextInput
              style={styles.input}
              value={location}
              onChangeText={setLocation}
              placeholder="Enter meeting location"
            />
          </View>

          {/* All Day Switch */}
          <View style={styles.switchContainer}>
            <Text style={styles.switchLabel}>All Day</Text>
            <Switch
              value={isAllDay}
              onValueChange={(value) => setIsAllDay(value)}
              color="#6A11CB"
            />
          </View>

          {/* From Date & Time */}
          <Text style={styles.dropdownLabel}>From Date {!isAllDay && '& Time'}<Text style={styles.requiredStar}>*</Text></Text>
          <View style={styles.dateTimeContainer}>
            <View style={[styles.dateTimeBox, isAllDay && { flex: 1 }]}>
              <TouchableOpacity
                style={[styles.datePickerButton, fromDateError ? styles.errorBorder : null]}
                onPress={() => setShowFromDatePicker(true)}
              >
                <Text style={[styles.dateText, fromDateError ? styles.errorText : null]}>
                  {fromDate.toLocaleDateString('en-US', {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric'
                  })}
                </Text>
                <Ionicons name="calendar" size={20} color={fromDateError ? "#dc3545" : "#777"} />
              </TouchableOpacity>
              {fromDateError && <Text style={styles.errorMessage}>{fromDateError}</Text>}
            </View>

            {!isAllDay && (
              <View style={styles.dateTimeBox}>
                <TouchableOpacity
                  style={[styles.datePickerButton, fromTimeError ? styles.errorBorder : null]}
                  onPress={() => setShowFromTimePicker(true)}
                >
                  <Text style={[styles.dateText, fromTimeError ? styles.errorText : null]}>
                    {isEditMode && meetingData && meetingData.start_time && !timeValuesChanged.fromTime
                      ? meetingData.start_time
                      : formatTimeWithTimeZone(fromTime)}
                  </Text>
                  <Ionicons name="time" size={20} color={fromTimeError ? "#dc3545" : "#777"} />
                </TouchableOpacity>
                {fromTimeError && <Text style={styles.errorMessage}>{fromTimeError}</Text>}
              </View>
            )}
          </View>

          {/* To Date & Time */}
          <Text style={styles.dropdownLabel}>To Date {!isAllDay && '& Time'}<Text style={styles.requiredStar}>*</Text></Text>
          <View style={styles.dateTimeContainer}>
            <View style={[styles.dateTimeBox, isAllDay && { flex: 1 }]}>
              <TouchableOpacity
                style={[styles.datePickerButton, toDateError ? styles.errorBorder : null]}
                onPress={() => setShowToDatePicker(true)}
              >
                <Text style={[styles.dateText, toDateError ? styles.errorText : null]}>
                  {toDate.toLocaleDateString('en-US', {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric'
                  })}
                </Text>
                <Ionicons name="calendar" size={20} color={toDateError ? "#dc3545" : "#777"} />
              </TouchableOpacity>
              {toDateError && <Text style={styles.errorMessage}>{toDateError}</Text>}
            </View>

            {!isAllDay && (
              <View style={styles.dateTimeBox}>
                <TouchableOpacity
                  style={[styles.datePickerButton, toTimeError ? styles.errorBorder : null]}
                  onPress={() => setShowToTimePicker(true)}
                >
                  <Text style={[styles.dateText, toTimeError ? styles.errorText : null]}>
                    {isEditMode && meetingData && meetingData.end_time && !timeValuesChanged.toTime
                      ? meetingData.end_time
                      : formatTimeWithTimeZone(toTime)}
                  </Text>
                  <Ionicons name="time" size={20} color={toTimeError ? "#dc3545" : "#777"} />
                </TouchableOpacity>
                {toTimeError && <Text style={styles.errorMessage}>{toTimeError}</Text>}
              </View>
            )}
          </View>

          {/* Time Zone Selector */}

          <TouchableOpacity style={[styles.inputContainer, {display:'none'}] }>
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Time Zone</Text>
            <TouchableOpacity
              style={styles.dropdownButton}
              onPress={() => setShowTimeZoneModal(true)}
            >
              <Text style={styles.dropdownText} numberOfLines={1}>
                {selectedTimeZone.name}
              </Text>
              <Ionicons name="chevron-down" size={20} color="#666" />
            </TouchableOpacity>
          </View>
          </TouchableOpacity>
          {/* Related To Selector */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Related To<Text style={styles.requiredStar}>*</Text></Text>
            <TouchableOpacity
              style={[styles.dropdownButton, formErrors.relatedTo && styles.inputError]}
              onPress={() => setShowRelatedToModal(true)}
            >
              <Text style={styles.dropdownText} numberOfLines={1}>
                {selectedRelatedTo
                  ? `${selectedRelatedTo.name} (${selectedRelatedTo.type})`
                  : 'Select Related To'}
              </Text>
              <Ionicons name="chevron-down" size={20} color="#666" />
            </TouchableOpacity>
            <ErrorText error={formErrors.relatedTo} />
          </View>

          {/* Outcome Selector - Hidden */}
          <View style={[styles.inputContainer, { display: 'none' }]}>
            <Text style={styles.label}>Outcome</Text>
            <TouchableOpacity
              style={styles.dropdownButton}
              onPress={() => setShowOutcomeModal(true)}
            >
              <Text style={styles.dropdownText} numberOfLines={1}>
                {selectedOutcome?.outcome || 'Select outcome'}
              </Text>
              <Ionicons name="chevron-down" size={20} color="#666" />
            </TouchableOpacity>
          </View>

          {/* Attendees Selector */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Attendees<Text style={styles.requiredStar}>*</Text></Text>
            <TouchableOpacity
              style={[styles.dropdownButton, formErrors.attendees && styles.inputError]}
              onPress={() => setShowAttendeesModal(true)}
            >
              <Text style={styles.dropdownText} numberOfLines={1}>
                {selectedAttendees.length > 0
                  ? `${selectedAttendees.length} attendee(s) selected`
                  : 'Select Attendees'}
              </Text>
              <Ionicons name="chevron-down" size={20} color="#666" />
            </TouchableOpacity>
            <ErrorText error={formErrors.attendees} />
          </View>
        </ScrollView>

        {/* Bottom Button */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[
              styles.button,
              { backgroundColor: '#0F96BB' },
              isSubmitting && { opacity: 0.7 }
            ]}
            onPress={handleSubmit}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.buttonText}>
                {isEditMode ? 'Update Meeting' : 'Create Meeting'}
              </Text>
            )}
          </TouchableOpacity>
        </View>


        {/* Bottom Sheets */}
        {renderTimeZoneBottomSheet(
          showTimeZoneModal,
          () => setShowTimeZoneModal(false)
        )}

        {renderRelatedToBottomSheet()}

        {renderBottomSheet(
          showOutcomeModal,
          () => setShowOutcomeModal(false),
          'Select Outcome',
          outcomes,
          setSelectedOutcome,
          selectedOutcome
        )}

        {renderBottomSheet(
          showAttendeesModal,
          () => setShowAttendeesModal(false),
          'Select Attendees',
          owners,
          (attendee) => {
            setSelectedAttendees(prev =>
              prev.includes(attendee.id)
                ? prev.filter(id => id !== attendee.id)
                : [...prev, attendee.id]
            );
          },
          null,
          true
        )}

        {/* Date Time Pickers with React Native Modal */}
        {Platform.OS === 'ios' ? (
          <RNModal
            visible={showFromDatePicker}
            transparent
            animationType="slide"
            onRequestClose={() => setShowFromDatePicker(false)}
          >
            <View style={styles.modalOverlay}>
              <View style={styles.pickerModalContent}>
                <View style={styles.pickerHeader}>
                  <TouchableOpacity
                    onPress={() => setShowFromDatePicker(false)}
                    style={styles.pickerHeaderButton}
                  >
                    <Text style={styles.pickerHeaderButtonText}>Cancel</Text>
                  </TouchableOpacity>
                  <Text style={styles.pickerHeaderTitle}>Select From Date</Text>
                  <TouchableOpacity
                    onPress={handleFromDateConfirm}
                    style={styles.pickerHeaderButton}
                  >
                    <Text style={[styles.pickerHeaderButtonText, styles.doneButton]}>Done</Text>
                  </TouchableOpacity>
                </View>
                <DateTimePicker
                  testID="fromDatePicker"
                  value={tempFromDate}
                  mode="date"
                  display="spinner"
                  onChange={onFromDateChange}
                  minimumDate={new Date()}
                  style={styles.picker}
                />
              </View>
            </View>
          </RNModal>
        ) : (
          showFromDatePicker && (
            <DateTimePicker
              testID="fromDatePicker"
              value={tempFromDate}
              mode="date"
              display="default"
              onChange={onFromDateChange}
              minimumDate={new Date()}
            />
          )
        )}

        {Platform.OS === 'ios' ? (
          <RNModal
            visible={showFromTimePicker}
            transparent
            animationType="slide"
            onRequestClose={() => setShowFromTimePicker(false)}
          >
            <View style={styles.modalOverlay}>
              <View style={styles.pickerModalContent}>
                <View style={styles.pickerHeader}>
                  <TouchableOpacity
                    onPress={() => setShowFromTimePicker(false)}
                    style={styles.pickerHeaderButton}
                  >
                    <Text style={styles.pickerHeaderButtonText}>Cancel</Text>
                  </TouchableOpacity>
                  <Text style={styles.pickerHeaderTitle}>Select From Time</Text>
                  <TouchableOpacity
                    onPress={() => {
                      setShowFromTimePicker(false);
                      setFromTimeError("");

                      if (tempFromTime) {
                        const newTime = new Date(fromDate);
                        newTime.setHours(tempFromTime.getHours());
                        newTime.setMinutes(tempFromTime.getMinutes());
                        newTime.setSeconds(0);

                        // Mark that the user has changed the from time
                        setTimeValuesChanged(prev => ({ ...prev, fromTime: true }));

                        setFromTime(newTime);

                        // If all-day is true, set it to false since a specific time was selected
                        if (isAllDay) {
                          setIsAllDay(false);
                        }

                        // If to time is before from time on the same day, adjust it
                        if (
                          toDate.getDate() === fromDate.getDate() &&
                          toDate.getMonth() === fromDate.getMonth() &&
                          toDate.getFullYear() === fromDate.getFullYear() &&
                          (toTime.getHours() < newTime.getHours() ||
                          (toTime.getHours() === newTime.getHours() && toTime.getMinutes() < newTime.getMinutes()))
                        ) {
                          // Set to time to be 30 minutes after from time
                          const newToTime = new Date(newTime);
                          newToTime.setMinutes(newTime.getMinutes() + 30);
                          setToTime(newToTime);
                        }
                      }
                    }}
                    style={styles.pickerHeaderButton}
                  >
                    <Text style={[styles.pickerHeaderButtonText, styles.doneButton]}>Done</Text>
                  </TouchableOpacity>
                </View>
                <DateTimePicker
                  testID="fromTimePicker"
                  value={isEditMode && meetingData && meetingData.start_time && !timeValuesChanged.fromTime
                    ? parseTime(meetingData.start_time)
                    : tempFromTime}
                  mode="time"
                  is24Hour={false}
                  display="spinner"
                  onChange={onFromTimeChange}
                  minuteInterval={5}
                  style={styles.picker}
                />
              </View>
            </View>
          </RNModal>
        ) : (
          showFromTimePicker && (
            <DateTimePicker
              testID="fromTimePicker"
              value={isEditMode && meetingData && meetingData.start_time && !timeValuesChanged.fromTime
                ? parseTime(meetingData.start_time)
                : tempFromTime}
              mode="time"
              is24Hour={false}
              display="default"
              onChange={onFromTimeChange}
              minuteInterval={5}
            />
          )
        )}

        {Platform.OS === 'ios' ? (
          <RNModal
            visible={showToDatePicker}
            transparent
            animationType="slide"
            onRequestClose={() => setShowToDatePicker(false)}
          >
            <View style={styles.modalOverlay}>
              <View style={styles.pickerModalContent}>
                <View style={styles.pickerHeader}>
                  <TouchableOpacity
                    onPress={() => setShowToDatePicker(false)}
                    style={styles.pickerHeaderButton}
                  >
                    <Text style={styles.pickerHeaderButtonText}>Cancel</Text>
                  </TouchableOpacity>
                  <Text style={styles.pickerHeaderTitle}>Select To Date</Text>
                  <TouchableOpacity
                    onPress={handleToDateConfirm}
                    style={styles.pickerHeaderButton}
                  >
                    <Text style={[styles.pickerHeaderButtonText, styles.doneButton]}>Done</Text>
                  </TouchableOpacity>
                </View>
                <DateTimePicker
                  testID="toDatePicker"
                  value={tempToDate}
                  mode="date"
                  display="spinner"
                  onChange={onToDateChange}
                  minimumDate={fromDate < new Date() ? new Date() : fromDate}
                  style={styles.picker}
                />
              </View>
            </View>
          </RNModal>
        ) : (
          showToDatePicker && (
            <DateTimePicker
              testID="toDatePicker"
              value={tempToDate}
              mode="date"
              display="default"
              onChange={onToDateChange}
              minimumDate={fromDate < new Date() ? new Date() : fromDate}
            />
          )
        )}

        {Platform.OS === 'ios' ? (
          <RNModal
            visible={showToTimePicker}
            transparent
            animationType="slide"
            onRequestClose={() => setShowToTimePicker(false)}
          >
            <View style={styles.modalOverlay}>
              <View style={styles.pickerModalContent}>
                <View style={styles.pickerHeader}>
                  <TouchableOpacity
                    onPress={() => setShowToTimePicker(false)}
                    style={styles.pickerHeaderButton}
                  >
                    <Text style={styles.pickerHeaderButtonText}>Cancel</Text>
                  </TouchableOpacity>
                  <Text style={styles.pickerHeaderTitle}>Select To Time</Text>
                  <TouchableOpacity
                    onPress={() => {
                      setShowToTimePicker(false);
                      setToTimeError("");

                      if (tempToTime) {
                        const newTime = new Date(toDate);
                        newTime.setHours(tempToTime.getHours());
                        newTime.setMinutes(tempToTime.getMinutes());
                        newTime.setSeconds(0);

                        // Mark that the user has changed the to time
                        setTimeValuesChanged(prev => ({ ...prev, toTime: true }));

                        // Validate the time
                        const fromDateTime = new Date(fromDate);
                        fromDateTime.setHours(fromTime.getHours(), fromTime.getMinutes());

                        if (newTime > fromDateTime) {
                          setToTime(newTime);
                          setToTimeError("");
                        } else {
                          setToTimeError("End time must be after start time");
                          Alert.alert("Invalid Time", "End time must be after start time");
                        }
                      }
                    }}
                    style={styles.pickerHeaderButton}
                  >
                    <Text style={[styles.pickerHeaderButtonText, styles.doneButton]}>Done</Text>
                  </TouchableOpacity>
                </View>
                <DateTimePicker
                  testID="toTimePicker"
                  value={isEditMode && meetingData && meetingData.end_time && !timeValuesChanged.toTime
                    ? parseTime(meetingData.end_time)
                    : tempToTime}
                  mode="time"
                  is24Hour={false}
                  display="spinner"
                  onChange={onToTimeChange}
                  minuteInterval={5}
                  style={styles.picker}
                />
              </View>
            </View>
          </RNModal>
        ) : (
          showToTimePicker && (
            <DateTimePicker
              testID="toTimePicker"
              value={isEditMode && meetingData && meetingData.end_time && !timeValuesChanged.toTime
                ? parseTime(meetingData.end_time)
                : tempToTime}
              mode="time"
              is24Hour={false}
              display="default"
              onChange={onToTimeChange}
              minuteInterval={5}
            />
          )
        )}
      </View>
    </PaperProvider>
  );
};

export default CreateMeetingScreen;