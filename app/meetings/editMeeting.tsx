import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Platform,
  ActivityIndicator,
  Switch,
  Modal as RNModal
} from 'react-native';
import { StyleSheet } from 'react-native';
import { Appbar, Portal, PaperProvider } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { log, logAPIRespose, showSnackbar } from '../ui/utils';
import LinearGradient from 'react-native-linear-gradient';
import { TaskOutcome, Owner, Contact, Account, Deal, RelatedTo as ApiRelatedTo, TaskFieldsResponse, getAllRelatedItems } from '../models/model_task_type_outcome';
import { apiService } from '../../services/ApiService';
import { API_ENDPOINTS } from '../../config/api';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { parseISO } from 'date-fns';
import { format } from 'date-fns';

// Time Zone Interface
interface TimeZone {
  id: string;
  name: string;
  offset: number;
  abbreviation: string;
  region: string;
}

// Time Zones Data
const TIME_ZONES: TimeZone[] = [
  {
    id: 'ist',
    name: 'India Standard Time',
    offset: 5.5,
    abbreviation: 'IST',
    region: 'Asia'
  },
  {
    id: 'utc',
    name: 'Coordinated Universal Time',
    offset: 0,
    abbreviation: 'UTC',
    region: 'UTC'
  },
  {
    id: 'est',
    name: 'Eastern Standard Time',
    offset: -5,
    abbreviation: 'EST',
    region: 'America'
  },
  {
    id: 'pst',
    name: 'Pacific Standard Time',
    offset: -8,
    abbreviation: 'PST',
    region: 'America'
  },
  {
    id: 'gmt',
    name: 'Greenwich Mean Time',
    offset: 0,
    abbreviation: 'GMT',
    region: 'Europe'
  }
];

// Default timezone
const DEFAULT_TIMEZONE: TimeZone = {
  id: 'ist',
  name: 'India Standard Time',
  offset: 5.5,
  abbreviation: 'IST',
  region: 'Asia'
};

// Format time in 12-hour format with AM/PM (hh:mm a format)
const formatTimeWithTimeZone = (date: Date): string => {
  // Format time in 12-hour format with AM/PM without timezone conversion
  console.log('Formatting time for display:', {
    date: date.toISOString(),
    hours: date.getHours(),
    minutes: date.getMinutes()
  });

  // Ensure we're working with a valid Date object
  const validDate = new Date(date);

  // Format to hh:mm a format (e.g., "04:11 PM")
  const formatted = validDate.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  }).replace(/\s+/g, ' ');

  console.log('🎯 Formatted time result:', formatted);
  return formatted;
};

// Add a helper function to parse time strings
const parseTime = (timeString: string): Date => {
  console.log('🕐 parseTime called with:', timeString);

  // First try to match 12-hour format (e.g., "3:20 PM", "3:20:28 pm")
  const timeMatch12 = timeString.match(/(\d+):(\d+)(?::(\d+))?\s*(AM|PM)/i);
  if (timeMatch12) {
    let [, hours, minutes, seconds, period] = timeMatch12;
    let parsedHours = parseInt(hours, 10);
    const parsedMinutes = parseInt(minutes, 10);
    const parsedSeconds = seconds ? parseInt(seconds, 10) : 0;

    console.log('📝 12-hour format components:', { hours, minutes, seconds, period, parsedHours, parsedMinutes });

    // Convert 12-hour format to 24-hour format
    if (period.toUpperCase() === 'PM' && parsedHours < 12) parsedHours += 12;
    if (period.toUpperCase() === 'AM' && parsedHours === 12) parsedHours = 0;
    console.log('🔄 After 12/24 hour conversion:', parsedHours);

    const parsedTime = new Date();
    parsedTime.setHours(parsedHours, parsedMinutes, parsedSeconds, 0);
    console.log('✅ 12-hour format parsed time:', parsedTime.toLocaleTimeString('en-US', { hour12: true }));
    return parsedTime;
  }

  // Then try 24-hour format (e.g., "15:35:00", "15:35")
  const timeMatch24 = timeString.match(/^(\d{1,2}):(\d{2})(?::(\d{2}))?$/);
  if (timeMatch24) {
    const [, hours, minutes, seconds] = timeMatch24;
    const parsedHours = parseInt(hours, 10);
    const parsedMinutes = parseInt(minutes, 10);
    const parsedSeconds = seconds ? parseInt(seconds, 10) : 0;

    console.log('📝 24-hour format components:', { hours, minutes, seconds, parsedHours, parsedMinutes });

    if (parsedHours >= 0 && parsedHours <= 23 && parsedMinutes >= 0 && parsedMinutes <= 59) {
      const parsedTime = new Date();
      parsedTime.setHours(parsedHours, parsedMinutes, parsedSeconds, 0);
      console.log('✅ 24-hour format parsed time:', parsedTime.toLocaleTimeString('en-US', { hour12: true }));
      return parsedTime;
    }
  }

  console.warn("❌ Unable to parse time:", timeString);
  return new Date();
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  appbar: {
    backgroundColor: '#0F96BB',
    elevation: 4,
  },
  appbarTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    padding: 16,
    paddingBottom: 100,
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  requiredStar: {
    color: 'red',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: 'white',
  },
  inputError: {
    borderColor: '#dc3545',
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingVertical: 8,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  dropdownLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  dateTimeContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  dateTimeBox: {
    flex: 1,
  },
  datePickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    backgroundColor: 'white',
  },
  dateText: {
    fontSize: 16,
    color: '#333',
  },
  errorBorder: {
    borderColor: '#dc3545',
  },
  errorText: {
    color: '#dc3545',
  },
  errorMessage: {
    color: '#dc3545',
    fontSize: 12,
    marginTop: 4,
  },
  dropdownButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    backgroundColor: 'white',
  },
  dropdownText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  button: {
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  bottomSheetHeader: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  dragIndicator: {
    width: 40,
    height: 4,
    backgroundColor: '#ccc',
    borderRadius: 2,
    alignSelf: 'center',
    marginBottom: 16,
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    zIndex: 1,
  },
  bottomSheetTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  searchContainer: {
    marginTop: 8,
  },
  searchInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  bottomSheetContent: {
    maxHeight: 400,
  },
  listItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  selectedListItem: {
    backgroundColor: '#e3f2fd',
  },
  bottomSheetItemText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  selectedItemText: {
    color: '#2575FC',
    fontWeight: '500',
  },
  confirmButtonContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  confirmButton: {
    backgroundColor: '#2575FC',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  tab: {
    flex: 1,
    padding: 16,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#2575FC',
  },
  tabText: {
    fontSize: 14,
    color: '#666',
  },
  activeTabText: {
    color: '#2575FC',
    fontWeight: '500',
  },
  pickerModalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
  },
  pickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  pickerHeaderButton: {
    padding: 8,
  },
  pickerHeaderButtonText: {
    fontSize: 16,
    color: '#2575FC',
  },
  pickerHeaderTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  doneButton: {
    fontWeight: 'bold',
  },
  picker: {
    backgroundColor: 'white',
  },
  timeZoneRegionHeader: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  timeZoneRegionText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6c757d',
    textTransform: 'uppercase',
  },
  timeZoneItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  timeZoneMainText: {
    flex: 1,
  },
  timeZoneName: {
    fontSize: 16,
    color: '#333',
    marginBottom: 4,
  },
  timeZoneOffset: {
    fontSize: 14,
    color: '#666',
  },
  timeZoneCheckIcon: {
    marginLeft: 12,
  },
});

export default function EditMeeting() {
  const router = useRouter();
  const params = useLocalSearchParams();

  // Parse meeting data from params
  const meetingData = React.useMemo(() => {
    try {
      if (params.data && typeof params.data === 'string') {
        const data = JSON.parse(params.data as string);
        log('Parsed meeting data:', data);
        return data;
      }
      return null;
    } catch (error) {
      console.error('Error parsing meeting data:', error);
      return null;
    }
  }, [params.data]);

  // State declarations
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [location, setLocation] = useState('');
  const [userId, setUserId] = useState<string>('');

  // Date and time state
  const [fromDate, setFromDate] = useState(new Date());
  const [fromTime, setFromTime] = useState(new Date());
  const [toDate, setToDate] = useState(new Date());
  const [toTime, setToTime] = useState(new Date());

  const [showFromDatePicker, setShowFromDatePicker] = useState(false);
  const [showFromTimePicker, setShowFromTimePicker] = useState(false);
  const [showToDatePicker, setShowToDatePicker] = useState(false);
  const [showToTimePicker, setShowToTimePicker] = useState(false);
  const [fromDateError, setFromDateError] = useState<string>("");
  const [fromTimeError, setFromTimeError] = useState<string>("");
  const [toDateError, setToDateError] = useState<string>("");
  const [toTimeError, setToTimeError] = useState<string>("");

  // Add new states for temporary date and time values
  const [tempFromDate, setTempFromDate] = useState(new Date());
  const [tempFromTime, setTempFromTime] = useState(new Date());
  const [tempToDate, setTempToDate] = useState(new Date());
  const [tempToTime, setTempToTime] = useState(new Date());

  // Form state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [selectedTimeZone, setSelectedTimeZone] = useState<TimeZone>(DEFAULT_TIMEZONE);
  const [selectedRelatedTo, setSelectedRelatedTo] = useState<{ id: number; name: string; type: string } | null>(null);
  const [selectedOutcome, setSelectedOutcome] = useState<TaskOutcome | null>(null);
  const [selectedAttendees, setSelectedAttendees] = useState<number[]>([]);

  // Add loading state for task fields
  const [isLoadingTaskFields, setIsLoadingTaskFields] = useState(true);

  // Add a state to track if the user has changed the time values
  const [timeValuesChanged, setTimeValuesChanged] = useState({
    fromTime: false,
    toTime: false
  });

  // Task fields state
  const [outcomes, setOutcomes] = useState<TaskOutcome[]>([]);
  const [owners, setOwners] = useState<Owner[]>([]);
  const [relatedToItems, setRelatedToItems] = useState<ApiRelatedTo>({
    contacts: [],
    accounts: [],
    deals: []
  });

  // Additional state declarations
  const [showTimeZoneModal, setShowTimeZoneModal] = useState(false);
  const [showRelatedToModal, setShowRelatedToModal] = useState(false);
  const [showOutcomeModal, setShowOutcomeModal] = useState(false);
  const [showAttendeesModal, setShowAttendeesModal] = useState(false);

  const [activeTab, setActiveTab] = useState<'contacts' | 'accounts' | 'deals'>('contacts');
  const [searchQuery, setSearchQuery] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);

  // Add this state for bottom sheet search
  const [bottomSheetSearchQuery, setBottomSheetSearchQuery] = useState('');

  const [userData, setUserData] = useState<any>(null);

  // Add state for all day switch
  const [isAllDay, setIsAllDay] = useState(meetingData?.all_day === "true");

  // Fetch user data
  const fetchUserData = async () => {
    try {
      const userDataString = await AsyncStorage.getItem('userData');
      if (userDataString) {
        const userData = JSON.parse(userDataString);
        setUserData(userData);
        setUserId(userData.id);
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
    }
  };

  // Fetch task fields
  const fetchTaskFields = async () => {
    try {
      setIsLoadingTaskFields(true);
      const response = await apiService.get<TaskFieldsResponse>(API_ENDPOINTS.TASK_TYPE);
      console.log('Fetched task fields response: ', API_ENDPOINTS.TASK_TYPE + " \n", JSON.stringify(response.data, null, 2));
      if (response.data) {
        const { outcomes: allOutcomes, owners: allOwners, related_to: relatedTo } = response.data.tasks;
        console.log('Fetched task fields:', { outcomes: allOutcomes, owners: allOwners, relatedTo });

        // Set the task fields first
        setOutcomes(allOutcomes || []);
        setOwners(allOwners || []);
        setRelatedToItems(relatedTo || { contacts: [], accounts: [], deals: [] });

        // Then initialize meeting data
        if (meetingData) {
          initializeMeetingData(meetingData,
                               relatedTo || { contacts: [], accounts: [], deals: [] },
                               allOutcomes || [],
                               allOwners || []);
        }
      }
    } catch (error) {
      console.error('Error fetching task fields:', error);
      showSnackbar('Failed to load data');
    } finally {
      setIsLoadingTaskFields(false);
    }
  };

  // Function to initialize meeting data
  const initializeMeetingData = (
    meetingData: any,
    relatedTo: ApiRelatedTo,
    taskOutcomes: TaskOutcome[],
    taskOwners: Owner[]
  ) => {
    console.log('🔄 Initializing meeting data:', JSON.stringify(meetingData, null, 2));

    setTitle(meetingData.activity_title || '');
    setDescription(meetingData.description || '');
    setLocation(meetingData.location || '');

    // Set dates
    if (meetingData.start_date) {
      try {
        const startDate = new Date(meetingData.start_date);
        if (!isNaN(startDate.getTime())) {
          setFromDate(startDate);
        } else {
          console.error('Invalid date format:', meetingData.start_date);
          setFromDate(new Date());
        }
      } catch (error) {
        console.error('Error parsing start_date:', error);
        setFromDate(new Date());
      }
    }

    if (meetingData.end_date) {
      try {
        const endDate = new Date(meetingData.end_date);
        if (!isNaN(endDate.getTime())) {
          setToDate(endDate);
        } else {
          console.error('Invalid date format:', meetingData.end_date);
          setToDate(new Date());
        }
      } catch (error) {
        console.error('Error parsing end_date:', error);
        setToDate(new Date());
      }
    }

    // Set start time
    console.log('🕐 Processing start_time:', meetingData.start_time, 'all_day:', meetingData.all_day);
    if (meetingData.start_time && meetingData.all_day !== "true") {
      try {
        const startTime = parseTime(meetingData.start_time);
        console.log('✅ Parsed start time:', startTime.toLocaleTimeString('en-US', { hour12: true }), 'from:', meetingData.start_time);

        // Set the time with proper format
        const formattedStartTime = new Date();
        formattedStartTime.setHours(startTime.getHours(), startTime.getMinutes(), 0, 0);
        setFromTime(formattedStartTime);

        console.log('🎯 Set fromTime to:', formattedStartTime.toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        }));
      } catch (error) {
        console.error('❌ Error parsing start_time:', error);
        setFromTime(new Date());
      }
    } else {
      console.log('⏰ Skipping start_time - either missing or all_day meeting');
    }

    // Set end time
    console.log('🕐 Processing end_time:', meetingData.end_time, 'all_day:', meetingData.all_day);
    if (meetingData.end_time && meetingData.all_day !== "true") {
      try {
        const endTime = parseTime(meetingData.end_time);
        console.log('✅ Parsed end time:', endTime.toLocaleTimeString('en-US', { hour12: true }), 'from:', meetingData.end_time);

        // Parse start time for comparison
        const startTime = parseTime(meetingData.start_time);

        // Check if parsed start and end times are the same (in minutes), and if so, add 1 hour to end time
        if (startTime.getHours() === endTime.getHours() && startTime.getMinutes() === endTime.getMinutes()) {
          console.log('⚠️ Parsed start and end times are the same, adding 1 hour to end time');
          console.log('  Original start:', startTime.toLocaleTimeString('en-US', { hour12: true }));
          console.log('  Original end:', endTime.toLocaleTimeString('en-US', { hour12: true }));

          const adjustedEndTime = new Date();
          adjustedEndTime.setHours(endTime.getHours() + 1, endTime.getMinutes(), 0, 0);
          console.log('  Adjusted end:', adjustedEndTime.toLocaleTimeString('en-US', { hour12: true }));
          setToTime(adjustedEndTime);

          console.log('🎯 Set toTime to (adjusted):', adjustedEndTime.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
          }));
        } else {
          console.log('✅ Start and end times are different, using as-is');
          console.log('  Start:', startTime.toLocaleTimeString('en-US', { hour12: true }));
          console.log('  End:', endTime.toLocaleTimeString('en-US', { hour12: true }));

          // Set the time with proper format
          const formattedEndTime = new Date();
          formattedEndTime.setHours(endTime.getHours(), endTime.getMinutes(), 0, 0);
          setToTime(formattedEndTime);

          console.log('🎯 Set toTime to:', formattedEndTime.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
          }));
        }
      } catch (error) {
        console.error('❌ Error parsing end_time:', error);
        // Set end time to 1 hour after start time
        const oneHourLater = new Date();
        oneHourLater.setHours(fromTime.getHours() + 1, fromTime.getMinutes(), 0, 0);
        setToTime(oneHourLater);

        console.log('🎯 Set toTime to (fallback):', oneHourLater.toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        }));
      }
    } else {
      console.log('⏰ Skipping end_time - either missing or all_day meeting');
    }

    // Set Related To
    if (meetingData.targetable_id && meetingData.targetable_type) {
      const type = meetingData.targetable_type.toLowerCase();
      const allItems = getAllRelatedItems(relatedTo);

      const relatedItem = allItems.find((item) =>
        item &&
        item.id !== undefined &&
        item.id.toString() === meetingData.targetable_id.toString()
      );

      if (relatedItem) {
        setSelectedRelatedTo({
          id: relatedItem.id,
          name: relatedItem.name,
          type: type === 'contact' ? 'Contact' :
                type === 'account' ? 'Account' : 'Deal'
        });
      }
    }

    // Set attendees
    if (meetingData.attendees_details && Array.isArray(meetingData.attendees_details)) {
      const attendeeIds = meetingData.attendees_details.map((attendee: any) => attendee.id);
      setSelectedAttendees(attendeeIds);
    }

    // Set outcome
    if (meetingData.outcome && taskOutcomes.length > 0) {
      const outcome = taskOutcomes.find(o => o.outcome === meetingData.outcome);
      if (outcome) {
        setSelectedOutcome(outcome);
      }
    }

    // Set all day
    setIsAllDay(meetingData.all_day === "true");
  };

  // Initialize data on component mount
  useEffect(() => {
    fetchUserData();
    fetchTaskFields();
  }, [meetingData]);

  // Redirect if no meeting data
  useEffect(() => {
    if (!meetingData) {
      Alert.alert('Error', 'No meeting data found', [
        { text: 'OK', onPress: () => router.back() }
      ]);
    }
  }, [meetingData]);

  // Initialize temp dates
  useEffect(() => {
    setTempFromDate(fromDate);
    setTempFromTime(fromTime);
    setTempToDate(toDate);
    setTempToTime(toTime);
  }, [fromDate, fromTime, toDate, toTime]);

  // Validation function
  const validateForm = (): boolean => {
    console.log('🔍 validateForm called');
    let isValid = true;
    const errors: { [key: string]: string } = {};

    // Required field validations
    if (!title) {
      errors.title = 'Title is required';
      isValid = false;
    }

    if (!description) {
      errors.description = 'Description is required';
      isValid = false;
    }

    if (selectedAttendees.length === 0) {
      errors.attendees = 'At least one attendee is required';
      isValid = false;
    }

    if (!selectedRelatedTo) {
      errors.relatedTo = 'Related To is required';
      isValid = false;
    }

    // Date and time validations
    if (!fromDate) {
      errors.fromDate = 'From Date is required';
      isValid = false;
    }

    if (!fromTime && !isAllDay) {
      errors.fromTime = 'From Time is required';
      isValid = false;
    }

    if (!toDate) {
      errors.toDate = 'To Date is required';
      isValid = false;
    }

    if (!toTime && !isAllDay) {
      errors.toTime = 'To Time is required';
      isValid = false;
    }

    // Enhanced validation for edit mode
    if (!isAllDay && fromDate && fromTime && toDate && toTime) {
      const fromDateTime = new Date(fromDate);
      fromDateTime.setHours(fromTime.getHours(), fromTime.getMinutes());

      const toDateTime = new Date(toDate);
      toDateTime.setHours(toTime.getHours(), toTime.getMinutes());

      console.log('🔍 Edit mode validation - comparing times:');
      console.log('  fromDateTime:', fromDateTime.toLocaleString());
      console.log('  toDateTime:', toDateTime.toLocaleString());
      console.log('  original start_time:', meetingData?.start_time);
      console.log('  original end_time:', meetingData?.end_time);

      // Parse original times for comparison
      let originalStartTime = null;
      let originalEndTime = null;

      if (meetingData?.start_time) {
        originalStartTime = parseTime(meetingData.start_time);
        console.log('  parsed original start_time:', originalStartTime.toLocaleTimeString('en-US', { hour12: true }));
      }

      if (meetingData?.end_time) {
        originalEndTime = parseTime(meetingData.end_time);
        console.log('  parsed original end_time:', originalEndTime.toLocaleTimeString('en-US', { hour12: true }));
      }

      // Check if current times match original times (allowing same start/end if original was same)
      const currentStartMatches = originalStartTime &&
        fromTime.getHours() === originalStartTime.getHours() &&
        fromTime.getMinutes() === originalStartTime.getMinutes();

      const currentEndMatches = originalEndTime &&
        toTime.getHours() === originalEndTime.getHours() &&
        toTime.getMinutes() === originalEndTime.getMinutes();

      console.log('  time matching check:', { currentStartMatches, currentEndMatches });

      // If both times match original and original had same start/end time, allow it
      if (currentStartMatches && currentEndMatches && originalStartTime && originalEndTime &&
          originalStartTime.getHours() === originalEndTime.getHours() &&
          originalStartTime.getMinutes() === originalEndTime.getMinutes()) {
        console.log('⚠️ Edit mode: allowing same start/end time as it matches original meeting data');
      } else {
        // Normal validation: end time must be after start time
        const timeDiff = toDateTime.getTime() - fromDateTime.getTime();
        if (timeDiff <= 0) {
          errors.toDateTime = 'End time must be after start time';
          isValid = false;
          console.log('❌ Edit mode: Date/Time sequence validation failed - time difference:', timeDiff);
        } else {
          console.log('✅ Edit mode: Time validation passed - time difference:', timeDiff, 'ms');
        }
      }
    }

    console.log('📝 Validation result:', { isValid, errors });
    setFormErrors(errors);
    return isValid;
  };

  // Submit function
  const handleSubmit = async () => {
    console.log('🚀 handleSubmit called for Edit Meeting!');
    console.log('🔄 Current isSubmitting state:', isSubmitting);
    console.log('🆔 Meeting ID:', meetingData?.id);

    if (isSubmitting) {
      console.log('⏸️ Already submitting, ignoring duplicate call');
      return;
    }

    setIsSubmitting(true);

    try {
      console.log('🔍 validateForm called');
      if (!validateForm()) {
        console.log('❌ Validation failed');
        Alert.alert('Validation Error', 'Please fill in all required fields correctly.');
        return;
      }
      console.log('✅ Validation passed, proceeding with API call');

      // Create date-time objects
      const fromDateTime = new Date(fromDate);
      fromDateTime.setHours(fromTime.getHours(), fromTime.getMinutes());

      const toDateTime = new Date(toDate);
      toDateTime.setHours(toTime.getHours(), toTime.getMinutes());

      // Format date and time in the required format
      const formatDate = (date: Date) => {
        return format(date, "yyyy-MM-dd");
      };

      const formatTime = (date: Date) => {
        // Use 24-hour format for API consistency
        return format(date, "HH:mm:ss");
      };

      // Create the payload
      interface MeetingPayload {
        activity_title: string;
        activity_type: string;
        all_day: string;
        salesactivities_id: string;
        description: string;
        location: string;
        start_date: string;
        start_time: string;
        end_date: string;
        end_time: string;
        notes: string;
        outcome: string;
        owner_id: string | undefined;
        targetable_id: string;
        targetable_type: string;
        timezone: string;
        attendees: string;
        id?: string | number;
      }

      const meetingPayload: MeetingPayload = {
        activity_title: title,
        activity_type: 'meeting',
        all_day: isAllDay.toString(),
        salesactivities_id: '3',
        description,
        location,
        start_date: formatDate(fromDateTime),
        start_time: formatTime(fromDateTime),
        end_date: formatDate(toDateTime),
        end_time: formatTime(toDateTime),
        notes: description,
        outcome: selectedOutcome?.outcome || "",
        owner_id: userData?.id,
        targetable_id: selectedRelatedTo?.id?.toString() || "",
        targetable_type: selectedRelatedTo ? selectedRelatedTo.type.toLowerCase()+"s" : "",
        timezone: selectedTimeZone?.name || "",
        attendees: selectedAttendees.join(','),
      };

      console.log('📦 Update payload:', JSON.stringify(meetingPayload, null, 2));

      // Log time formatting specifically
      console.log('⏰ Time formatting check:');
      console.log('  start_time formatted:', meetingPayload.start_time);
      console.log('  end_time formatted:', meetingPayload.end_time);
      console.log('  fromDateTime:', fromDateTime.toISOString());
      console.log('  toDateTime:', toDateTime.toISOString());

      console.log('🔄 Updating meeting with ID:', meetingData.id);
      console.log('🔗 Update endpoint:', `${API_ENDPOINTS.MEETING_ADD}/${meetingData.id}`);

      const response = await apiService.put(
        `${API_ENDPOINTS.MEETING_ADD}/${meetingData.id}`,
        meetingPayload
      );

      console.log('✅ Update response status:', response.status);
      console.log('📄 Update response data:', JSON.stringify(response.data, null, 2));

      // Check for successful response
      if (response.status === 200 || response.status === 201 || response.data?.status === 200) {
        const successMessage = response.data?.message || 'Meeting updated successfully';
        console.log('✅ Success:', successMessage);

        Alert.alert(
          'Success',
          successMessage,
          [{
            text: 'OK',
            onPress: () => {
              router.back();
              // Navigate to meeting details page with the meeting ID
              const meetingId = response.data?.data?.id || response.data?.response?.id || response.data?.id || meetingData.id;
              if (meetingId) {
                router.replace({
                  pathname: '/meetings/detailedMeeting',
                  params: { data: meetingId }
                });
              }
            }
          }]
        );
      } else {
        console.error('❌ Unexpected response status:', response.status);
        console.error('Response data:', response.data);
        const errorMessage = response.data?.message || 'Failed to update meeting';
        showSnackbar(errorMessage);
        Alert.alert('Error', errorMessage);
      }
    } catch (error: any) {
      console.error('❌ Error updating meeting:', error);
      console.error('📋 Detailed error info:', {
        status: error?.response?.status,
        statusText: error?.response?.statusText,
        data: error?.response?.data,
        message: error?.message,
        meetingId: meetingData?.id
      });

      let errorMessage = 'Failed to update meeting. Please try again.';

      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      Alert.alert(
        'Update Failed',
        `${errorMessage}\n\nPlease check your internet connection and try again.`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Date/Time picker handlers
  const handleFromDateChange = (event: any, selectedDate?: Date) => {
    setShowFromDatePicker(false);
    if (selectedDate) {
      setFromDate(selectedDate);
      setFromDateError("");
    }
  };

  const handleFromTimeChange = (event: any, selectedTime?: Date) => {
    setShowFromTimePicker(false);
    if (selectedTime) {
      setFromTime(selectedTime);
      setFromTimeError("");
      setTimeValuesChanged(prev => ({ ...prev, fromTime: true }));
    }
  };

  const handleToDateChange = (event: any, selectedDate?: Date) => {
    setShowToDatePicker(false);
    if (selectedDate) {
      setToDate(selectedDate);
      setToDateError("");
    }
  };

  const handleToTimeChange = (event: any, selectedTime?: Date) => {
    setShowToTimePicker(false);
    if (selectedTime) {
      setToTime(selectedTime);
      setToTimeError("");
      setTimeValuesChanged(prev => ({ ...prev, toTime: true }));
    }
  };

  return (
    <PaperProvider>
      <View style={styles.container}>
        <Appbar.Header style={styles.appbar}>
          <TouchableOpacity onPress={() => router.back()} style={{ padding: 8 }}>
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Appbar.Content
            title="Edit Meeting"
            titleStyle={styles.appbarTitle}
          />
        </Appbar.Header>

        <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollViewContent}>
          {/* Title Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Title<Text style={styles.requiredStar}>*</Text></Text>
            <TextInput
              style={[styles.input, formErrors.title && styles.inputError]}
              placeholder="Enter title"
              value={title}
              onChangeText={setTitle}
            />
            {formErrors.title && <Text style={styles.errorMessage}>{formErrors.title}</Text>}
          </View>

          {/* Description Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Description<Text style={styles.requiredStar}>*</Text></Text>
            <TextInput
              style={[styles.input, formErrors.description && styles.inputError, { height: 100 }]}
              placeholder="Enter description"
              value={description}
              onChangeText={setDescription}
              multiline
              textAlignVertical="top"
            />
            {formErrors.description && <Text style={styles.errorMessage}>{formErrors.description}</Text>}
          </View>

          {/* Location Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Location</Text>
            <TextInput
              style={styles.input}
              value={location}
              onChangeText={setLocation}
              placeholder="Enter meeting location"
            />
          </View>

          {/* Validation Status for Edit Mode - HIDDEN */}
          {/*
          <View style={{
            backgroundColor: '#d4edda',
            borderColor: '#c3e6cb',
            borderWidth: 1,
            borderRadius: 8,
            padding: 12,
            marginBottom: 16
          }}>
            <Text style={{
              fontSize: 14,
              fontWeight: '600',
              color: '#155724',
              marginBottom: 8
            }}>
              📝 Edit Meeting Status
            </Text>

            <Text style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>
              Meeting ID: {meetingData?.id || 'Loading...'}
            </Text>
            <Text style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>
              Original Start: {meetingData?.start_time ?
                (meetingData.all_day === "true" ? 'All Day' : formatTimeWithTimeZone(parseTime(meetingData.start_time))) :
                'Not set'}
            </Text>
            <Text style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>
              Original End: {meetingData?.end_time ?
                (meetingData.all_day === "true" ? 'All Day' : formatTimeWithTimeZone(parseTime(meetingData.end_time))) :
                'Not set'}
            </Text>
            <Text style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>
              Current Start: {formatTimeWithTimeZone(fromTime)}
            </Text>
            <Text style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>
              Current End: {formatTimeWithTimeZone(toTime)}
            </Text>
          </View>
          */}

          {/* All Day Switch */}
          <View style={styles.switchContainer}>
            <Text style={styles.switchLabel}>All Day</Text>
            <Switch
              value={isAllDay}
              onValueChange={(value) => setIsAllDay(value)}
              color="#6A11CB"
            />
          </View>

          {/* From Date & Time */}
          <Text style={styles.dropdownLabel}>From Date {!isAllDay && '& Time'}<Text style={styles.requiredStar}>*</Text></Text>
          <View style={styles.dateTimeContainer}>
            <View style={[styles.dateTimeBox, isAllDay && { flex: 1 }]}>
              <TouchableOpacity
                style={[styles.datePickerButton, fromDateError ? styles.errorBorder : null]}
                onPress={() => setShowFromDatePicker(true)}
              >
                <Text style={[styles.dateText, fromDateError ? styles.errorText : null]}>
                  {fromDate.toLocaleDateString('en-US', {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric'
                  })}
                </Text>
                <Ionicons name="calendar" size={20} color={fromDateError ? "#dc3545" : "#777"} />
              </TouchableOpacity>
              {fromDateError && <Text style={styles.errorMessage}>{fromDateError}</Text>}
            </View>

            {!isAllDay && (
              <View style={styles.dateTimeBox}>
                <TouchableOpacity
                  style={[styles.datePickerButton, fromTimeError ? styles.errorBorder : null]}
                  onPress={() => setShowFromTimePicker(true)}
                >
                  <Text style={[styles.dateText, fromTimeError ? styles.errorText : null]}>
                    {formatTimeWithTimeZone(fromTime)}
                  </Text>
                  <Ionicons name="time" size={20} color={fromTimeError ? "#dc3545" : "#777"} />
                </TouchableOpacity>
                {fromTimeError && <Text style={styles.errorMessage}>{fromTimeError}</Text>}
              </View>
            )}
          </View>

          {/* To Date & Time */}
          <Text style={styles.dropdownLabel}>To Date {!isAllDay && '& Time'}<Text style={styles.requiredStar}>*</Text></Text>
          <View style={styles.dateTimeContainer}>
            <View style={[styles.dateTimeBox, isAllDay && { flex: 1 }]}>
              <TouchableOpacity
                style={[styles.datePickerButton, toDateError ? styles.errorBorder : null]}
                onPress={() => setShowToDatePicker(true)}
              >
                <Text style={[styles.dateText, toDateError ? styles.errorText : null]}>
                  {toDate.toLocaleDateString('en-US', {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric'
                  })}
                </Text>
                <Ionicons name="calendar" size={20} color={toDateError ? "#dc3545" : "#777"} />
              </TouchableOpacity>
              {toDateError && <Text style={styles.errorMessage}>{toDateError}</Text>}
            </View>

            {!isAllDay && (
              <View style={styles.dateTimeBox}>
                <TouchableOpacity
                  style={[styles.datePickerButton, toTimeError ? styles.errorBorder : null]}
                  onPress={() => setShowToTimePicker(true)}
                >
                  <Text style={[styles.dateText, toTimeError ? styles.errorText : null]}>
                    {formatTimeWithTimeZone(toTime)}
                  </Text>
                  <Ionicons name="time" size={20} color={toTimeError ? "#dc3545" : "#777"} />
                </TouchableOpacity>
                {toTimeError && <Text style={styles.errorMessage}>{toTimeError}</Text>}
              </View>
            )}
          </View>

          {/* Related To Selector */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Related To<Text style={styles.requiredStar}>*</Text></Text>
            <TouchableOpacity
              style={[styles.dropdownButton, formErrors.relatedTo && styles.inputError]}
              onPress={() => setShowRelatedToModal(true)}
            >
              <Text style={styles.dropdownText} numberOfLines={1}>
                {selectedRelatedTo
                  ? `${selectedRelatedTo.name} (${selectedRelatedTo.type})`
                  : 'Select Related To'}
              </Text>
              <Ionicons name="chevron-down" size={20} color="#666" />
            </TouchableOpacity>
            {formErrors.relatedTo && <Text style={styles.errorMessage}>{formErrors.relatedTo}</Text>}
          </View>

          {/* Attendees Selector */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Attendees<Text style={styles.requiredStar}>*</Text></Text>
            <TouchableOpacity
              style={[styles.dropdownButton, formErrors.attendees && styles.inputError]}
              onPress={() => setShowAttendeesModal(true)}
            >
              <Text style={styles.dropdownText} numberOfLines={1}>
                {selectedAttendees.length > 0
                  ? `${selectedAttendees.length} attendee(s) selected`
                  : 'Select Attendees'}
              </Text>
              <Ionicons name="chevron-down" size={20} color="#666" />
            </TouchableOpacity>
            {formErrors.attendees && <Text style={styles.errorMessage}>{formErrors.attendees}</Text>}
          </View>
        </ScrollView>

        {/* Bottom Button */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[
              styles.button,
              { backgroundColor: '#0F96BB' },
              isSubmitting && { opacity: 0.7 }
            ]}
            onPress={() => {
              console.log('🔘 Update Meeting Button pressed!');
              console.log('🔄 isSubmitting:', isSubmitting);
              console.log('🆔 meetingData?.id:', meetingData?.id);
              handleSubmit();
            }}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.buttonText}>
                Update Meeting
              </Text>
            )}
          </TouchableOpacity>
        </View>

        {/* Date/Time Pickers */}
        {showFromDatePicker && (
          <DateTimePicker
            value={tempFromDate}
            mode="date"
            display="default"
            onChange={handleFromDateChange}
          />
        )}

        {showFromTimePicker && (
          <DateTimePicker
            value={tempFromTime}
            mode="time"
            display="default"
            onChange={handleFromTimeChange}
          />
        )}

        {showToDatePicker && (
          <DateTimePicker
            value={tempToDate}
            mode="date"
            display="default"
            onChange={handleToDateChange}
          />
        )}

        {showToTimePicker && (
          <DateTimePicker
            value={tempToTime}
            mode="time"
            display="default"
            onChange={handleToTimeChange}
          />
        )}

        {/* Placeholder for modals - will be implemented in next step */}
        {showRelatedToModal && (
          <RNModal
            visible={showRelatedToModal}
            transparent={true}
            animationType="slide"
            onRequestClose={() => setShowRelatedToModal(false)}
          >
            <View style={styles.modalOverlay}>
              <View style={styles.modalContainer}>
                <View style={styles.bottomSheetHeader}>
                  <View style={styles.dragIndicator} />
                  <TouchableOpacity
                    style={styles.closeButton}
                    onPress={() => setShowRelatedToModal(false)}
                  >
                    <Ionicons name="close" size={24} color="#666" />
                  </TouchableOpacity>
                  <Text style={styles.bottomSheetTitle}>Select Related To</Text>
                </View>
                <View style={{ padding: 20, alignItems: 'center' }}>
                  <Text style={{ color: '#666', textAlign: 'center' }}>
                    🚧 Related To selector will be implemented in the next step
                  </Text>
                </View>
              </View>
            </View>
          </RNModal>
        )}

        {showAttendeesModal && (
          <RNModal
            visible={showAttendeesModal}
            transparent={true}
            animationType="slide"
            onRequestClose={() => setShowAttendeesModal(false)}
          >
            <View style={styles.modalOverlay}>
              <View style={styles.modalContainer}>
                <View style={styles.bottomSheetHeader}>
                  <View style={styles.dragIndicator} />
                  <TouchableOpacity
                    style={styles.closeButton}
                    onPress={() => setShowAttendeesModal(false)}
                  >
                    <Ionicons name="close" size={24} color="#666" />
                  </TouchableOpacity>
                  <Text style={styles.bottomSheetTitle}>Select Attendees</Text>
                </View>
                <View style={{ padding: 20, alignItems: 'center' }}>
                  <Text style={{ color: '#666', textAlign: 'center' }}>
                    🚧 Attendees selector will be implemented in the next step
                  </Text>
                </View>
              </View>
            </View>
          </RNModal>
        )}
      </View>
    </PaperProvider>
  );
}
