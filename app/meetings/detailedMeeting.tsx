import React, { useMemo, useState, useEffect, useRef, useCallback } from 'react';
import {
  StyleSheet,
  View,
  Text,
  StatusBar,
  Platform,
  ScrollView,
  Image,
  Linking,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  TextInput,
  ActionSheetIOS,
  KeyboardAvoidingView,
  Dimensions,
  Modal as RNModal,
} from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { useColors } from '@/hooks/useThemeColor';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Appbar, Avatar, Portal, Modal, Provider as PaperProvider } from 'react-native-paper';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import * as ImagePicker from 'expo-image-picker';
import { API_ENDPOINTS } from '@/config/api';
import { UPLOADS_BASE_URL } from '@/config/api';
import { apiService } from '@/services/ApiService';
import { DATE_FORMAT, log, logAPIRespose, readUserScope, showSnackbar, getAvatarColor, getContrastingTextColor } from '../ui/utils';
import mime from 'mime';
import { convertDate } from '../ui/utils';
import moment from 'moment';
import { Picker } from '@react-native-picker/picker';
import useAttendanceValidation from '../../hooks/useAttendanceValidation';

// Define interfaces for meeting data
interface SingleMeetingApiResponse {
  status: number;
  message: string;
  response: SingleMeetingResponse;
}

interface SingleMeetingResponse {
  id: number;
  activity_title: string;
  description: string;
  start_date: string;
  start_time: string;
  end_date: string;
  end_time: string;
  status: string;
  location: string;
  outcome: string;
  owner_id: string;
  owner_name: string;
  targetable_id: string;
  targetable_type: string;
  timezone: string;
  isAllDa: string;
  checkin_time?: string;
  checkin_location?: string;
  checkin_latitude?: string;
  checkin_longitude?: string;
  checkout_time?: string;
  checkout_location?: string;
  checkout_latitude?: string;
  checkout_longitude?: string;
  check_in_attachment?: string;
  check_out_attachment?: string;
  target_details?: {
    first_name?: string;
    last_name?: string;
    emails?: string;
    name?: string;
    amount?: string;
  };
  attendees_details?: Array<{
    id: number;
    name: string;
  }>;
}

import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import DateTimePicker from '@react-native-community/datetimepicker';
import Calendar from '../components/Calendar';

// Add helper function to calculate spent time
const calculateSpentTime = (checkInTime: string, checkOutTime: string) => {
  if (!checkInTime || !checkOutTime) return null;

  const checkIn = new Date(checkInTime);
  const checkOut = new Date(checkOutTime);

  const diffInMilliseconds = checkOut.getTime() - checkIn.getTime();
  const hours = Math.floor(diffInMilliseconds / (1000 * 60 * 60));
  const minutes = Math.floor((diffInMilliseconds % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((diffInMilliseconds % (1000 * 60)) / 1000);

  return {
    hours,
    minutes,
    seconds,
    totalMinutes: hours * 60 + minutes
  };
};

// Add this function near the other utility functions
const getStatusColors = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'pending':
      return { bg: '#EC407A', text: 'white' }; // Purple
    case 'inprogress':
    case 'checkin':
      return { bg: '#FF9800', text: 'white' }; // Orange
    case 'completed':
    case 'closed':
      return { bg: '#4CAF50', text: 'white' }; // Green
    default:
      return { bg: '#9E9E9E', text: 'white' }; // Grey
  }
};

// Define interface for StableInput props
interface StableInputProps {
  value: string;
  onChangeText: (text: string) => void;
  style: any;
  placeholder: string;
  placeholderTextColor: string;
  keyboardType?: 'default' | 'number-pad' | 'decimal-pad' | 'numeric' | 'email-address' | 'phone-pad';
  returnKeyType?: 'done' | 'go' | 'next' | 'search' | 'send';
  maxLength?: number;
}

// Add this isolated input component at the top of the file
const StableInput = React.memo(({
  value,
  onChangeText,
  style,
  placeholder,
  placeholderTextColor,
  keyboardType,
  returnKeyType,
  maxLength
}: StableInputProps) => {

  const [localValue, setLocalValue] = useState(value);

  // Update local value when prop value changes
  useEffect(() => {
    if (value !== localValue) {
      setLocalValue(value);
    }
  }, [value, localValue]);

  const handleChangeText = useCallback((text: string) => {
    setLocalValue(text);
    if (onChangeText) {
      onChangeText(text);
    }
  }, [onChangeText]);

  return (
    <TextInput
      style={style}
      value={localValue}
      onChangeText={handleChangeText}
      placeholder={placeholder}
      placeholderTextColor={placeholderTextColor}
      keyboardType={keyboardType}
      returnKeyType={returnKeyType}
      maxLength={maxLength}
    />
  );
}, (prevProps: StableInputProps, nextProps: StableInputProps) => {
  // Custom comparison to prevent unnecessary re-renders
  // Only re-render if important props changed
  const importantPropsChanged =
    prevProps.placeholder !== nextProps.placeholder ||
    prevProps.placeholderTextColor !== nextProps.placeholderTextColor;

  return !importantPropsChanged;
});

export default function DetailedMeetingScreen() {
  const router = useRouter();
  const colors = useColors();
  const params = useLocalSearchParams();
  const styles = useMemo(() => createStyles(colors), [colors]);
  const [isCheckingIn, setIsCheckingIn] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [meetingData, setMeetingData] = useState<SingleMeetingResponse | null>(null);

  // Attendance validation hook
  const { validateAndExecute } = useAttendanceValidation();
  const [showRescheduleModal, setShowRescheduleModal] = useState(false);
  const [fromDate, setFromDate] = useState(new Date());
  const [fromTime, setFromTime] = useState(new Date());
  const [toDate, setToDate] = useState(new Date());
  const [toTime, setToTime] = useState(new Date());
  const [showFromDatePicker, setShowFromDatePicker] = useState(false);
  const [showFromTimePicker, setShowFromTimePicker] = useState(false);
  const [showToDatePicker, setShowToDatePicker] = useState(false);
  const [showToTimePicker, setShowToTimePicker] = useState(false);
  const [fromDateError, setFromDateError] = useState<string>("");
  const [fromTimeError, setFromTimeError] = useState<string>("");
  const [toDateError, setToDateError] = useState<string>("");
  const [toTimeError, setToTimeError] = useState<string>("");
  const [isRescheduling, setIsRescheduling] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [checkInImage, setCheckInImage] = useState<{ uri: string, type: string, name: string } | null>(null);
  const [checkOutImage, setCheckOutImage] = useState<{ uri: string, type: string, name: string } | null>(null);
  const [showCheckoutSheet, setShowCheckoutSheet] = useState(false);
  const [remarks, setRemarks] = useState('');
  const [transportMode, setTransportMode] = useState('');

  const [amount, setAmount] = useState('');
  const [internalAmount, setInternalAmount] = useState(''); // Add this new state
  const [billImage, setBillImage] = useState<{ uri: string, type: string, name: string } | null>(null);
  const notesInputRef = useRef<TextInput>(null);
  const [isNotesFocused, setIsNotesFocused] = useState(false);
  const [transportModes, setTransportModes] = useState<string[]>([]);
  const [isLoadingTransportModes, setIsLoadingTransportModes] = useState(false);
  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false);

  // Description modal states
  const [showDescriptionModal, setShowDescriptionModal] = useState(false);
  const [accessScope, setAccessScope] = useState<any>(null);

  useEffect(() => {
    const fetchAccessScope = async () => {
      const scope = await readUserScope();

      setAccessScope(scope);
    };
    fetchAccessScope();
  }, []);

  // Fetch transport modes from conveyance API
  const fetchTransportModes = async () => {
    try {
      setIsLoadingTransportModes(true);
      const today = new Date();
      const date = today.toISOString().split('T')[0];

      const response = await apiService.post<{
        status: number;
        success: boolean;
        message: string;
        response: {
          transport_modes: Array<{
            mode: string;
            rate: number;
          }>;
        };
      }>(
        `${API_ENDPOINTS.CONVEYANCE}/view`,
        { date }
      );

      if (response.data?.success && response.data.response?.transport_modes) {
        const modes = response.data.response.transport_modes.map((mode) => mode.mode);
        setTransportModes(modes);
      } else {
        // Fallback to default modes if API fails
        setTransportModes(['Car', 'Bike', 'Public Transport', 'Bus']);
        console.warn('Failed to fetch transport modes, using fallback');
      }
    } catch (error) {
      console.error('Error fetching transport modes:', error);
      // Fallback to default modes if API fails
      setTransportModes(['Car', 'Bike', 'Public Transport', 'Bus']);
    } finally {
      setIsLoadingTransportModes(false);
    }
  };

  // Fetch transport modes on component mount
  useEffect(() => {
    fetchTransportModes();
  }, []);

  // Add debounced amount handler
  const updateAmountDebounced = useCallback((text: string) => {
    setInternalAmount(text);
    const timeoutId = setTimeout(() => {
      setAmount(text);
    }, 100);
    return () => clearTimeout(timeoutId);
  }, []);

  // Parse the initial meeting data from params
  const initialMeetingData = useMemo(() => {
    console.log('params.data',""+ params.data);
    try {
      if (params.data) {
        const data = JSON.parse(params.data as string);
        console.log("Received initial meeting data:", data);
        return data;
      }
      return null;
    } catch (error) {
      console.error('Error parsing meeting data:', error);
      return null;
    }
  }, [params.data]);

  // Fetch meeting data from API
  const fetchMeetingData = async () => {
    try {
      console.log('initialMeetingDataAPIPIIII',initialMeetingData);
      setIsLoading(true);
      const response = await apiService.get<SingleMeetingApiResponse>(
        `${API_ENDPOINTS.MEETING_ADD}/${initialMeetingData}`
      );

      if (response.data?.status === 200 && response.data?.response) {
        setMeetingData(response.data.response);
      } else {
        throw new Error('Failed to fetch meeting data');
      }
    } catch (error) {
      console.error('Error fetching meeting data:', error);
      Alert.alert('Error', 'Failed to fetch meeting details');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (initialMeetingData) {
      console.log('initialMeetingData',initialMeetingData);
      fetchMeetingData();
    }
  }, [initialMeetingData]);

  const isPendingStatus = useMemo(() => {
    return meetingData?.status?.toLowerCase() === 'pending';
  }, [meetingData?.status]);



  const handleCheckIn = async () => {
    await validateAndExecute(async () => {
      try {
        Alert.alert(
          'Check In Confirmation',
          'Are you sure you want to check in to this meeting?',
          [
            {
              text: 'Cancel',
              style: 'cancel',
            },
            {
              text: 'Confirm',
              onPress: async () => {
              try {
                setIsCheckingIn(true);

                const { status } = await Location.requestForegroundPermissionsAsync();
                if (status !== 'granted') {
                  alert('Permission to access location was denied');
                  return;
                }

                const location = await Location.getCurrentPositionAsync({});
                const [address] = await Location.reverseGeocodeAsync({
                  latitude: location.coords.latitude,
                  longitude: location.coords.longitude
                });

                const locationString = address
                  ? `${address.street || ''}, ${address.city || ''}, ${address.region || ''}, ${address.country || ''}`
                  : '';

                Alert.alert(
                  'Confirm Location',
                  `Location:\n${locationString}`,
                  [
                    {
                      text: 'Cancel',
                      style: 'cancel',
                    },
                    {
                      text: 'Confirm',
                      onPress: async () => {
                        try {
                          // Create FormData for API request
                          const formData = new FormData();
                          formData.append('meeting_id', meetingData?.id?.toString() || '');
                          formData.append('latitude', location.coords.latitude.toString());
                          formData.append('longitude', location.coords.longitude.toString());
                          formData.append('location', locationString);
                          formData.append('check_in_time', new Date().toISOString());

                          // Attach image if available
                          if (checkInImage) {
                            // Standardize URI format for both platforms
                            let fileUri = checkInImage.uri;

                            // Create a standardized URI with file:/// format
                            const newImageUri = "file:///" + fileUri.split("file:/").join("");

                            // Get the file extension and determine proper MIME type
                            const fileExtension = newImageUri.split('.').pop() || 'jpg';
                            const mimeType = mime.getType(fileExtension) || 'image/jpeg';

                            // Get filename from URI
                            const fileName = newImageUri.split("/").pop() || checkInImage.name;

                            console.log('Check-in image processing:', {
                              originalUri: fileUri,
                              newUri: newImageUri,
                              extension: fileExtension,
                              mimeType: mimeType,
                              fileName: fileName
                            });

                            formData.append('attachment', {
                              uri: newImageUri,
                              name: fileName,
                              type: mimeType
                            } as any);

                            console.log('Check-in image attachment added to form data');
                          }

                          // Make API request with FormData
                          const response = await apiService.putFormData(
                            `${API_ENDPOINTS.MEETING_ADD}/${meetingData?.id}/status`,
                            formData
                          );

                          if (!response.status) {
                            throw new Error('Failed to check in');
                          }

                          // Reset image state
                          setCheckInImage(null);

                          // Refresh meeting data from API
                          fetchMeetingData();
                          Alert.alert('Success', 'Successfully checked in!');
                        } catch (error) {
                          console.error('Check-in error:', error);
                          Alert.alert('Error', 'Failed to check in. Please try again.');
                        }
                      }
                    }
                  ]
                );
              } catch (error) {
                console.error('Location error:', error);
                Alert.alert('Error', 'Failed to get location. Please try again.');
              } finally {
                setIsCheckingIn(false);
              }
            }
          }
        ]
      );
      } catch (error) {
        console.error('Check-in error:', error);
        Alert.alert('Error', 'Failed to check in. Please try again.');
        setIsCheckingIn(false);
      }
    }, 'check in to meeting');
  };

  const handleCheckOut = async () => {
    await validateAndExecute(async () => {
      setShowCheckoutSheet(true);
      setHasAttemptedSubmit(false); // Reset validation state when opening checkout sheet
    }, 'check out from meeting');
  };

  const handleCheckOutSubmit = async () => {
    await validateAndExecute(async () => {
      try {
        if (!checkOutImage) {
          Alert.alert('Error', 'Please attach a check-out image');
          return;
        }

        if (transportMode === 'Public Transport') {
          if (!amount) {
            Alert.alert('Error', 'Please enter the amount for public transport');
            return;
          }
          if (!billImage) {
            Alert.alert('Error', 'Please attach the bill image for public transport');
            return;
          }
        }

        setIsCheckingIn(true);

      Alert.alert(
        'Check Out Confirmation',
        'Are you sure you want to check out from this meeting?',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Confirm',
            onPress: async () => {
              try {
                const { status } = await Location.requestForegroundPermissionsAsync();
                if (status !== 'granted') {
                  alert('Permission to access location was denied');
                  return;
                }

                const location = await Location.getCurrentPositionAsync({});
                const [address] = await Location.reverseGeocodeAsync({
                  latitude: location.coords.latitude,
                  longitude: location.coords.longitude
                });

                const locationString = (address
                  ? `${address.street || ''}, ${address.city || ''}, ${address.region || ''}, ${address.country || ''}`
                  : '');

                // Create FormData for API request
                const formData = new FormData();
                formData.append('meeting_id', meetingData?.id?.toString() || '');
                formData.append('latitude', location.coords.latitude.toString());
                formData.append('longitude', location.coords.longitude.toString());
                formData.append('location', locationString);
                formData.append('check_out_time', new Date().toISOString());
                formData.append('transport_mode', transportMode);
                formData.append('remarks', remarks);

                if (transportMode === 'Public Transport') {
                  formData.append('amount', amount);
                }

                // Attach check-out image
                if (checkOutImage) {
                  let fileUri = checkOutImage.uri;
                  const newImageUri = "file:///" + fileUri.split("file:/").join("");
                  const fileExtension = newImageUri.split('.').pop() || 'jpg';
                  const mimeType = mime.getType(fileExtension) || 'image/jpeg';
                  const fileName = newImageUri.split("/").pop() || checkOutImage.name;

                  formData.append('attachment', {
                    uri: newImageUri,
                    name: fileName,
                    type: mimeType
                  } as any);
                }

                // Attach bill image for Public Transport
                if (transportMode === 'Public Transport' && billImage) {
                  let fileUri = billImage.uri;
                  const newImageUri = "file:///" + fileUri.split("file:/").join("");
                  const fileExtension = newImageUri.split('.').pop() || 'jpg';
                  const mimeType = mime.getType(fileExtension) || 'image/jpeg';
                  const fileName = newImageUri.split("/").pop() || billImage.name;

                  formData.append('bill_attachment', {
                    uri: newImageUri,
                    name: fileName,
                    type: mimeType
                  } as any);
                }

                // Make API request with FormData
                const response = await apiService.putFormData(
                  `${API_ENDPOINTS.MEETING_ADD}/${meetingData?.id}/status`,
                  formData
                );

                if (!response.status) {
                  throw new Error('Failed to check out');
                }

                setCheckOutImage(null);
                setBillImage(null);
                setTransportMode('');
                setAmount('');
                setInternalAmount('');
                setRemarks('');
                setHasAttemptedSubmit(false);
                setShowCheckoutSheet(false);
                fetchMeetingData();
                Alert.alert('Success', 'Successfully checked out!');
              } catch (error) {
                console.error('Check-out error:', error);
                Alert.alert('Error', 'Failed to check out. Please try again.');
              } finally {
                setIsCheckingIn(false);
              }
            }
          }
        ]
      );
      } catch (error) {
        console.error('Check-out error:', error);
        Alert.alert('Error', 'Failed to check out. Please try again.');
        setIsCheckingIn(false);
      }
    }, 'check out from meeting');
  };

  const handleBack = () => {
    console.log("handleBack");
    try {
      // router.back();
       if (router.canGoBack()) {
     router.back();
     } else {
    // Optionally navigate to a fallback screen
    router.replace('/meetings/meetingList'); // or any default screen
  }
    } catch (error) {
      // If back() fails, navigate to meetings index
      router.push('/meetings/index' as any);
    }
  };

  const handleEdit = () => {
   {/*if (!isPendingStatus) {
      alert('Only meetings with Pending status can be edited');
      return;
    }*/}
    log("meetingDatapass",meetingData)

    router.push({
      pathname: '/meetings/createMeeting' as any,
      params: { data: JSON.stringify(meetingData) }
    });
  };

  const handleOpenMap = (address: string) => {
    const url = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(address)}`;
    Linking.openURL(url);
  };



  const pickCheckOutImage = async () => {
    try {
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (permissionResult.granted === false) {
        Alert.alert("Permission Required", "You need to grant camera roll permissions to attach an image.");
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsEditing: false,
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];

        // Get file extension and create a proper filename
        const fileExtension = asset.uri.split('.').pop() || 'jpg';
        const fileName = `checkout_${new Date().getTime()}.${fileExtension}`;

        // Determine proper MIME type using the mime package
        const mimeType = mime.getType(fileExtension) || 'image/jpeg';

        console.log('Selected check-out image:', {
          uri: asset.uri,
          extension: fileExtension,
          mimeType: mimeType,
          fileName: fileName
        });

        setCheckOutImage({
          uri: asset.uri,
          type: mimeType,
          name: fileName
        });
      }
    } catch (error) {
      console.error('Error selecting image:', error);
      Alert.alert('Error', 'Failed to select image. Please try again.');
    }
  };

  const takeCheckOutPhoto = async () => {
    try {
      const permissionResult = await ImagePicker.requestCameraPermissionsAsync();

      if (permissionResult.granted === false) {
        Alert.alert("Permission Required", "You need to grant camera permissions to take a photo.");
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: false,
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];

        // Create a proper filename for the photo
        const fileName = `checkout_photo_${new Date().getTime()}.jpg`;

        // Use proper MIME type for JPEG images
        const mimeType = 'image/jpeg';

        console.log('Captured check-out photo:', {
          uri: asset.uri,
          mimeType: mimeType,
          fileName: fileName
        });

        setCheckOutImage({
          uri: asset.uri,
          type: mimeType,
          name: fileName
        });
      }
    } catch (error) {
      console.error('Error capturing image:', error);
      Alert.alert('Error', 'Failed to capture image. Please try again.');
    }
  };

  const pickBillImage = async () => {
    try {
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (permissionResult.granted === false) {
        Alert.alert("Permission Required", "You need to grant camera roll permissions to attach an image.");
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsEditing: false,
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        const fileExtension = asset.uri.split('.').pop() || 'jpg';
        const fileName = `bill_${new Date().getTime()}.${fileExtension}`;
        const mimeType = mime.getType(fileExtension) || 'image/jpeg';

        setBillImage({
          uri: asset.uri,
          type: mimeType,
          name: fileName
        });
      }
    } catch (error) {
      console.error('Error selecting image:', error);
      Alert.alert('Error', 'Failed to select image. Please try again.');
    }
  };

  const takeBillPhoto = async () => {
    try {
      const permissionResult = await ImagePicker.requestCameraPermissionsAsync();

      if (permissionResult.granted === false) {
        Alert.alert("Permission Required", "You need to grant camera permissions to take a photo.");
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: false,
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        const fileName = `bill_photo_${new Date().getTime()}.jpg`;
        const mimeType = 'image/jpeg';

        setBillImage({
          uri: asset.uri,
          type: mimeType,
          name: fileName
        });
      }
    } catch (error) {
      console.error('Error capturing image:', error);
      Alert.alert('Error', 'Failed to capture image. Please try again.');
    }
  };

  const handleReschedule = async () => {
    try {
      if (!meetingData) {
        Alert.alert('Error', 'Meeting data is not available');
        return;
      }

      const now = new Date();
      const selectedFromDateTime = new Date(fromDate);
      selectedFromDateTime.setHours(fromTime.getHours());
      selectedFromDateTime.setMinutes(fromTime.getMinutes());

      const selectedToDateTime = new Date(toDate);
      selectedToDateTime.setHours(toTime.getMinutes());
      selectedToDateTime.setMinutes(toTime.getMinutes());

      if (selectedFromDateTime <= now) {
        Alert.alert('Invalid Date', 'Please select a future date and time for start');
        return;
      }

      if (selectedToDateTime <= selectedFromDateTime) {
        Alert.alert('Invalid Date', 'End time must be after start time');
        return;
      }

      setIsRescheduling(true);

      const rescheduleData = {
        activity_title: meetingData.activity_title,
        activity_type: 'meeting',
        all_day: meetingData.isAllDa,
        salesactivities_id: '3',
        description: meetingData.description,
        location: meetingData.location,
        start_date: convertDate(fromDate.toISOString(), DATE_FORMAT.SYSTEM_FORMAT, DATE_FORMAT.DATE_FORMAT),
        start_time: convertDate(fromTime.toISOString(), DATE_FORMAT.SYSTEM_FORMAT, DATE_FORMAT.TIME_FORMAT),
        end_date: convertDate(toDate.toISOString(), DATE_FORMAT.SYSTEM_FORMAT, DATE_FORMAT.DATE_FORMAT),
        end_time: convertDate(toTime.toISOString(), DATE_FORMAT.SYSTEM_FORMAT, DATE_FORMAT.TIME_FORMAT),
        notes: meetingData.description,
        outcome: meetingData.outcome || "",
        owner_id: meetingData.owner_id,
        targetable_id: meetingData.targetable_id,
        targetable_type: meetingData.targetable_type.toLowerCase(),
        timezone: meetingData.timezone || "",
        id: meetingData.id || '',
      };

      console.log("rescheduleData",rescheduleData);
      const response = await apiService.put(
        `${API_ENDPOINTS.MEETING_ADD}/${meetingData?.id}`,
        rescheduleData
      );
console.log("rescheduleDataResponse",response);
      if (response.data?.status === 200) {
        Alert.alert('Success', 'Meeting rescheduled successfully', [
          { text: 'OK', onPress: () => {
            setShowRescheduleModal(false);
            fetchMeetingData(); // Refresh meeting data
          }}
        ]);
      } else {
        throw new Error('Failed to reschedule meeting');
      }
    } catch (error) {
      console.error('Reschedule error:', error);
      Alert.alert('Error', 'Failed to reschedule meeting. Please try again.');
    } finally {
      setIsRescheduling(false);
    }
  };



  const isTimeValid = (time: Date, date: Date): boolean => {
    const now = new Date();
    if (date.toDateString() === now.toDateString()) {
      return time > now;
    }
    return true;
  };

  const isToDateTimeValid = (selectedDate: Date, selectedTime: Date): boolean => {
    const fromDateTime = new Date(fromDate);
    fromDateTime.setHours(fromTime.getHours(), fromTime.getMinutes());

    const toDateTime = new Date(selectedDate);
    toDateTime.setHours(selectedTime.getHours(), selectedTime.getMinutes());

    return toDateTime > fromDateTime;
  };



  const onFromTimeChange = (_event: any, selectedTime?: Date) => {
    setShowFromTimePicker(false);
    setFromTimeError("");

    if (selectedTime) {
      if (isTimeValid(selectedTime, fromDate)) {
        setFromTime(selectedTime);
        if (fromDate.toDateString() === toDate.toDateString() && toTime <= selectedTime) {
          const newToTime = new Date(selectedTime);
          newToTime.setHours(selectedTime.getHours() + 1);
          setToTime(newToTime);
        }
      } else {
        setFromTimeError("Please select a future time");
        Alert.alert("Invalid Time", "Please select a future time");
      }
    }
  };



  const onToTimeChange = (_event: any, selectedTime?: Date) => {
    setShowToTimePicker(false);
    setToTimeError("");

    if (selectedTime) {
      if (isToDateTimeValid(toDate, selectedTime)) {
        setToTime(selectedTime);
      } else {
        setToTimeError("End time must be after start time");
        Alert.alert("Invalid Time", "End time must be after start time");
      }
    }
  };

  const handleDelete = async () => {
    if (!meetingData || !meetingData.id) {
      Alert.alert('Error', 'Meeting data is not available');
      return;
    }

    Alert.alert(
      'Delete Meeting',
      'Are you sure you want to delete this meeting? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              setIsDeleting(true);
              const response = await apiService.delete(`${API_ENDPOINTS.MEETING_ADD}/${meetingData.id}`);

              if (response.status === 200 || response.status === 204) {
                showSnackbar('Meeting deleted successfully');
                router.back();
              } else {
                throw new Error(response.data?.message || 'Failed to delete meeting');
              }
            } catch (error) {
              console.error('Error deleting meeting:', error);
              Alert.alert('Error', 'Failed to delete meeting. Please try again.');
            } finally {
              setIsDeleting(false);
            }
          }
        },
      ]
    );
  };

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  if (!meetingData) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Text>No meeting data available</Text>
      </View>
    );
  }

  return (
    <PaperProvider>
      <View style={styles.container}>
        <StatusBar
          backgroundColor="white"
          barStyle="dark-content"
          translucent={true}
        />
        <SafeAreaView style={styles.safeArea} edges={['right', 'left']}>
          <Stack.Screen options={{ headerShown: false }} />

          <Appbar.Header style={styles.appbar}>
            <Appbar.Action
              icon="arrow-left"
              onPress={handleBack}
              color="white"
            />
            <Appbar.Content
              title="Meeting Details"
              titleStyle={styles.appbarTitle}
            />

            {isPendingStatus && (
              <>
                {accessScope?.appointment?.edit === true && (
                <Appbar.Action
                  icon="pencil"
                  color="white"
                  onPress={handleEdit}
                  style={styles.actionButton}
                />
                )}
                {accessScope?.appointment?.delete === true && (
                <Appbar.Action
                  icon="delete"
                  color="white"
                  onPress={handleDelete}
                  disabled={isDeleting}
                  style={styles.actionButton}
                />
                )}
              </>
            )}
          </Appbar.Header>

          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {/* Modern Title Card */}
            <View style={styles.modernCard}>
              <View style={styles.titleHeader}>
                <View style={styles.titleContent}>
                  <Text style={styles.modernTitle} numberOfLines={3}>
                    {meetingData?.activity_title || ''}
                  </Text>
                  {meetingData?.target_details?.first_name && (
                    <Text style={styles.modernSubtitle}>
                      with {meetingData.target_details.first_name} {meetingData.target_details.last_name || ''}
                    </Text>
                  )}
                </View>
                {meetingData?.status && (
                  <View style={[
                    styles.modernStatusBadge,
                    { backgroundColor: getStatusColors(meetingData.status).bg }
                  ]}>
                    <Text style={[styles.modernStatusText, { color: getStatusColors(meetingData.status).text }]}>
                      {meetingData.status.toUpperCase()}
                    </Text>
                  </View>
                )}
              </View>
            </View>

            {/* Description Card */}
            {meetingData?.description && (
              <View style={styles.modernCard}>
                <View style={styles.cardHeader}>
                  <MaterialIcons name="description" size={24} color="#0F96BB" />
                  <Text style={styles.cardTitle}>Description</Text>
                </View>
                <View style={styles.descriptionContainer}>
                  <Text
                    style={styles.descriptionText}
                    numberOfLines={2}
                  >
                    {meetingData.description}
                  </Text>
                  {meetingData.description.length > 80 && (
                    <TouchableOpacity
                      style={styles.readMoreButton}
                      onPress={() => setShowDescriptionModal(true)}
                    >
                      <Text style={styles.readMoreText}>Read More</Text>
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            )}

            {/* Schedule & Timing Card */}
            <View style={styles.modernCard}>
              <View style={styles.cardHeader}>
                <MaterialIcons name="schedule" size={24} color="#0F96BB" />
                <Text style={styles.cardTitle}>Schedule & Timing</Text>
              </View>

              {meetingData && (
                <View style={styles.singleLineContainer}>
                  {/* Scheduled Time Row */}
                  <View style={styles.singleTimeRow}>
                    <View style={styles.timeRowHeader}>
                      <MaterialIcons name="event" size={16} color="#0F96BB" />
                      {/* <Text style={styles.timeRowLabel}>Scheduled</Text> */}
                    </View>
                    <View style={styles.timeRowContent}>
                      <View style={styles.singleTimeItem}>
                        <View style={styles.timeItemContent}>
                          <Text style={styles.timeItemTitle}>From</Text>
                          <Text style={styles.singleTimeText}>
                            {meetingData.start_date ? moment(meetingData.start_date).format('DD/MM') : '--/--'} {meetingData.start_time || '--:--'}
                          </Text>
                        </View>
                      </View>
                      <MaterialIcons name="arrow-forward" size={16} color="#E0E0E0" style={styles.arrowIcon} />
                      <View style={styles.singleTimeItem}>
                        <View style={styles.timeItemContent}>
                          <Text style={styles.timeItemTitle}>To</Text>
                          <Text style={styles.singleTimeText}>
                            {meetingData.end_date ? moment(meetingData.end_date).format('DD/MM') : '--/--'} {meetingData.end_time || '--:--'}
                          </Text>
                        </View>
                      </View>
                    </View>
                  </View>

                  {/* Actual Time Row */}
                  {meetingData.checkin_time && (
                    <View style={styles.singleTimeRow}>
                      <View style={styles.timeRowHeader}>
                        <MaterialIcons name="access-time" size={16} color="#FF9800" />
                        <Text style={styles.timeRowLabel}>Actual</Text>
                      </View>
                      <View style={styles.timeRowContent}>
                        <View style={styles.singleTimeItem}>
                          <View style={styles.timeItemContent}>
                            <Text style={styles.timeItemTitle}>Check In</Text>
                            <Text style={styles.singleTimeText}>
                              {moment(meetingData.checkin_time).format('DD/MM hh:mm A')}
                            </Text>
                          </View>
                        </View>
                        {meetingData.checkout_time && (
                          <>
                            <MaterialIcons name="arrow-forward" size={16} color="#4CAF50" style={styles.arrowIcon} />
                            <View style={styles.singleTimeItem}>
                              <View style={styles.timeItemContent}>
                                <Text style={styles.timeItemTitle}>Check Out</Text>
                                <Text style={styles.singleTimeText}>
                                  {moment(meetingData.checkout_time).format('DD/MM hh:mm A')}
                                </Text>
                              </View>
                            </View>
                          </>
                        )}
                      </View>
                    </View>
                  )}

                  {/* Duration Row */}
                  {meetingData.checkin_time && meetingData.checkout_time && (() => {
                    const spentTime = calculateSpentTime(meetingData.checkin_time, meetingData.checkout_time);
                    if (spentTime) {
                      return (
                        <View style={styles.durationRow}>
                          <View style={styles.timeRowHeader}>
                            <MaterialIcons name="timer" size={16} color="#2196F3" />
                            <Text style={styles.timeRowLabel}>Duration</Text>
                          </View>
                          <View style={styles.timeRowContent}>
                            <Text style={styles.durationText}>
                              {spentTime.hours > 0 ? `${spentTime.hours}h ` : ''}
                              {spentTime.minutes > 0 ? `${spentTime.minutes}m ` : ''}
                              {spentTime.seconds}s
                            </Text>
                          </View>
                        </View>
                      );
                    }
                    return null;
                  })()}
                </View>
              )}
            </View>

            {/* Modern Check-in Card */}
            <View style={styles.modernCard}>
              <View style={styles.cardHeader}>
                <MaterialIcons
                  name="login"
                  size={24}
                  color={meetingData.checkin_time ? getStatusColors("completed").bg : getStatusColors("pending").bg}
                />
                <Text style={styles.cardTitle}>Check In</Text>
                <View style={styles.statusIndicator}>
                  <View style={[
                    styles.statusDot,
                    { backgroundColor: meetingData.checkin_time ? getStatusColors("completed").bg : getStatusColors("pending").bg }
                  ]} />
                  <Text style={[
                    styles.statusLabel,
                    { color: meetingData.checkin_time ? getStatusColors("completed").bg : getStatusColors("pending").bg }
                  ]}>
                    {meetingData.checkin_time ? "Completed" : "Pending"}
                          </Text>
                </View>
              </View>

              {meetingData.status === 'Pending' ? (
                <View style={styles.actionContainer}>
                  {checkInImage && (
                    <View style={styles.modernImagePreview}>
                      <Image
                        source={{ uri: checkInImage.uri }}
                        style={styles.previewImage}
                        resizeMode="cover"
                      />
                      <TouchableOpacity
                        style={styles.modernRemoveButton}
                        onPress={() => setCheckInImage(null)}
                      >
                        <MaterialIcons name="close" size={18} color="white" />
                      </TouchableOpacity>
                    </View>
                  )}

                  <TouchableOpacity
                    style={styles.checkinActionButton}
                    onPress={handleCheckIn}
                    disabled={isCheckingIn}
                  >
                    {isCheckingIn ? (
                      <ActivityIndicator color="#4CAF50" size="small" />
                    ) : (
                      <>
                        <MaterialIcons name="login" size={20} color="#4CAF50" />
                        <Text style={styles.checkinActionText}>Check In Now</Text>
                      </>
                    )}
                  </TouchableOpacity>
                </View>
              ) : (
                <View style={styles.completedContainer}>
                  {meetingData.checkin_latitude && meetingData.checkin_longitude && (
                    <TouchableOpacity
                      onPress={() => handleOpenMap(`${meetingData.checkin_latitude},${meetingData.checkin_longitude}`)}
                      style={styles.modernMapContainer}
                    >
                      <MapView
                        style={styles.modernMap}
                        provider={PROVIDER_GOOGLE}
                        initialRegion={{
                          latitude: parseFloat(meetingData.checkin_latitude) || 0,
                          longitude: parseFloat(meetingData.checkin_longitude) || 0,
                          latitudeDelta: 0.005,
                          longitudeDelta: 0.005,
                        }}
                        scrollEnabled={false}
                        zoomEnabled={false}
                        rotateEnabled={false}
                        pitchEnabled={false}
                      >
                        <Marker
                          coordinate={{
                            latitude: parseFloat(meetingData.checkin_latitude),
                            longitude: parseFloat(meetingData.checkin_longitude),
                          }}
                          title="Check-in Location"
                          description={meetingData.checkin_location || ''}
                        />
                      </MapView>
                      <View style={styles.mapOverlay}>
                        <MaterialIcons name="open-in-new" size={20} color="white" />
                      </View>
                    </TouchableOpacity>
                  )}

                  <View style={styles.detailsContainer}>
                    <TouchableOpacity
                      onPress={() => handleOpenMap(meetingData.checkin_location || '')}
                      style={styles.modernLocationRow}
                    >
                      <MaterialIcons name="location-on" size={20} color="#0F96BB" />
                      <Text style={styles.modernLocationText}>
                        {meetingData.checkin_location || 'Location not available'}
                      </Text>
                    </TouchableOpacity>

                    <View style={styles.modernTimeRow}>
                      <MaterialIcons name="calendar-today" size={16} color="#666" style={{ marginRight: 4 }} />
                      <Text style={styles.modernTimeText}>
                        {meetingData.checkin_time ?
                          moment(meetingData.checkin_time).format('DD/MM, ddd') : 'Date not available'}
                      </Text>
                      <MaterialIcons name="access-time" size={16} color="#666" style={{ marginLeft: 12, marginRight: 4 }} />
                      <Text style={styles.modernTimeText}>
                        {meetingData.checkin_time ?
                          moment(meetingData.checkin_time).format('hh:mm A') : 'Time not available'}
                            </Text>
                          </View>
                        </View>

                  {meetingData.check_in_attachment && (
                    <View style={styles.attachmentSection}>
                      <Text style={styles.attachmentTitle}>Attachment</Text>
                      <TouchableOpacity
                        onPress={() => {
                          router.push({
                            pathname: '/imageViewer' as any,
                            params: { fileName: meetingData.check_in_attachment }
                          });
                        }}
                        style={styles.smallAttachmentContainer}
                      >
                        <Image
                          source={{ uri: UPLOADS_BASE_URL + meetingData.check_in_attachment }}
                          style={styles.smallAttachmentImage}
                          resizeMode="cover"
                        />
                        <View style={styles.smallImageOverlay}>
                          <MaterialIcons name="zoom-in" size={16} color="white" />
                        </View>
                      </TouchableOpacity>
                    </View>
                  )}
                </View>
              )}
            </View>

            {/* Modern Check-out Card */}
            {(meetingData.status === 'Inprogress' ||meetingData.status === 'Checkin'|| meetingData.status === 'Completed' || meetingData.status === 'Closed') && (
              <View style={styles.modernCard}>
                <View style={styles.cardHeader}>
                  <MaterialIcons
                    name="logout"
                    size={24}
                    color={meetingData.checkout_time ? getStatusColors("completed").bg : getStatusColors("pending").bg}
                  />
                  <Text style={styles.cardTitle}>Check Out</Text>
                  <View style={styles.statusIndicator}>
                    <View style={[
                      styles.statusDot,
                      { backgroundColor: meetingData.checkout_time ? getStatusColors("completed").bg : getStatusColors("pending").bg }
                    ]} />
                    <Text style={[
                      styles.statusLabel,
                      { color: meetingData.checkout_time ? getStatusColors("completed").bg : getStatusColors("pending").bg }
                    ]}>
                      {meetingData.checkout_time ? "Completed" : "Pending"}
                          </Text>
                  </View>
                </View>

                {meetingData.status === 'Inprogress' || meetingData.status === 'Checkin' ? (
                  <View style={styles.actionContainer}>
                    {checkOutImage && (
                      <View style={styles.modernImagePreview}>
                        <Image
                          source={{ uri: checkOutImage.uri }}
                          style={styles.previewImage}
                          resizeMode="cover"
                        />
                        <TouchableOpacity
                          style={styles.modernRemoveButton}
                          onPress={() => setCheckOutImage(null)}
                        >
                          <MaterialIcons name="close" size={18} color="white" />
                        </TouchableOpacity>
                      </View>
                    )}

                    <TouchableOpacity
                      style={styles.checkoutActionButton}
                      onPress={handleCheckOut}
                      disabled={isCheckingIn}
                    >
                      {isCheckingIn ? (
                        <ActivityIndicator color="#FF5722" size="small" />
                      ) : (
                        <>
                          <MaterialIcons name="logout" size={20} color="#FF5722" />
                          <Text style={styles.checkoutActionText}>Check Out Now</Text>
                        </>
                      )}
                    </TouchableOpacity>
                  </View>
                ) : (
                  <View style={styles.completedContainer}>
                    {meetingData.checkout_latitude && meetingData.checkout_longitude && (
                      <TouchableOpacity
                        onPress={() => handleOpenMap(`${meetingData.checkout_latitude},${meetingData.checkout_longitude}`)}
                        style={styles.modernMapContainer}
                      >
                        <MapView
                          style={styles.modernMap}
                          provider={PROVIDER_GOOGLE}
                          initialRegion={{
                            latitude: parseFloat(meetingData.checkout_latitude) || 0,
                            longitude: parseFloat(meetingData.checkout_longitude) || 0,
                            latitudeDelta: 0.005,
                            longitudeDelta: 0.005,
                          }}
                          scrollEnabled={false}
                          zoomEnabled={false}
                          rotateEnabled={false}
                          pitchEnabled={false}
                        >
                          <Marker
                            coordinate={{
                              latitude: parseFloat(meetingData.checkout_latitude),
                              longitude: parseFloat(meetingData.checkout_longitude),
                            }}
                            title="Check-out Location"
                            description={meetingData.checkout_location || ''}
                          />
                        </MapView>
                        <View style={styles.mapOverlay}>
                          <MaterialIcons name="open-in-new" size={20} color="white" />
                        </View>
                      </TouchableOpacity>
                    )}

                    <View style={styles.detailsContainer}>
                      <TouchableOpacity
                        onPress={() => handleOpenMap(meetingData.checkout_location || meetingData.location || '')}
                        style={styles.modernLocationRow}
                      >
                        <MaterialIcons name="location-on" size={20} color="#0F96BB" />
                        <Text style={styles.modernLocationText}>
                          {meetingData.checkout_location || meetingData.location || 'Location not available'}
                        </Text>
                      </TouchableOpacity>

                      <View style={styles.modernTimeRow}>
                        <MaterialIcons name="calendar-today" size={16} color="#666" style={{ marginRight: 4 }} />
                        <Text style={styles.modernTimeText}>
                          {meetingData.checkout_time ?
                            moment(meetingData.checkout_time).format('DD/MM, ddd') : 'Date not available'}
                        </Text>
                        <MaterialIcons name="access-time" size={16} color="#666" style={{ marginLeft: 12, marginRight: 4 }} />
                        <Text style={styles.modernTimeText}>
                          {meetingData.checkout_time ?
                            moment(meetingData.checkout_time).format('hh:mm A') : 'Time not available'}
                        </Text>
                      </View>
                    </View>

                    {meetingData.check_out_attachment && (
                      <View style={styles.attachmentSection}>
                        <Text style={styles.attachmentTitle}>Attachment</Text>
                        <TouchableOpacity
                          onPress={() => {
                            router.push({
                              pathname: '/imageViewer' as any,
                              params: { fileName: meetingData.check_out_attachment }
                            });
                          }}
                          style={styles.smallAttachmentContainer}
                        >
                          <Image
                            source={{ uri: UPLOADS_BASE_URL + meetingData.check_out_attachment }}
                            style={styles.smallAttachmentImage}
                            resizeMode="cover"
                          />
                          <View style={styles.smallImageOverlay}>
                            <MaterialIcons name="zoom-in" size={16} color="white" />
                          </View>
                        </TouchableOpacity>
                      </View>
                    )}
                  </View>
                )}
              </View>
            )}

            {/* Modern Outcome Card */}
            <View style={styles.modernCard}>
              <View style={styles.cardHeader}>
                <MaterialIcons name="assignment" size={24} color="#0F96BB" />
                <Text style={styles.cardTitle}>Meeting Outcome</Text>
              </View>
              <View style={styles.outcomeContainer}>
                <Text style={styles.modernOutcomeText}>
                  {meetingData.outcome || 'No outcome recorded yet'}
                </Text>
              </View>
            </View>

            {/* Modern Related Contact Card */}
            <View style={styles.modernCard}>
              <View style={styles.cardHeader}>
                <MaterialIcons name="person" size={24} color="#0F96BB" />
                <Text style={styles.cardTitle}>
                  {meetingData?.targetable_type === 'contacts' ? 'Contact Person' :
                   meetingData?.targetable_type === 'leads' ? 'Lead' :
                   meetingData?.targetable_type === 'deals' ? 'Deal' :
                   meetingData?.targetable_type === 'accounts' ? 'Account' : 'Related Details'}
                </Text>
              </View>

              <View style={styles.modernContactContainer}>
                <View style={styles.modernContactItem}>
                  <Avatar.Text
                    size={32}
                    label={(() => {
                      if (meetingData?.targetable_type === 'deals') {
                        return (meetingData?.target_details?.name?.[0] || '?').toUpperCase();
                      }
                      if (meetingData?.targetable_type === 'accounts') {
                        return (meetingData?.target_details?.name?.[0] || '?').toUpperCase();
                      }
                      return (meetingData?.target_details?.first_name?.[0] || meetingData?.target_details?.last_name?.[0] || '?').toUpperCase();
                    })()}
                    style={styles.compactAttendeeAvatar}
                    labelStyle={styles.compactAttendeeInitial}
                  />
                  <View style={styles.modernContactInfo}>
                    <Text style={[
                      styles.modernContactName,
                      meetingData?.targetable_type === 'deals' && { fontSize: 15, lineHeight: 18, fontWeight: '700', marginBottom: 2 }
                    ]}>
                      {(() => {
                        if (meetingData?.targetable_type === 'deals') {
                          return meetingData?.target_details?.name || 'No deal name';
                        }
                        if (meetingData?.targetable_type === 'accounts') {
                          return meetingData?.target_details?.name || 'No account name';
                        }
                        return meetingData?.target_details?.first_name && meetingData?.target_details?.last_name ?
                          `${meetingData.target_details.first_name} ${meetingData.target_details.last_name}` :
                          meetingData?.target_details?.first_name || meetingData?.target_details?.last_name || 'No name provided';
                      })()}
                    </Text>
                    {meetingData?.target_details?.emails && (
                      <Text style={styles.modernEmailText}>
                        {meetingData.target_details.emails}
                      </Text>
                    )}
                    {/* Removed the modernTypeContainer and modernTypeText to hide the type label below the name */}
                  </View>

                </View>
              </View>
            </View>

            {/* Modern Attendees Card */}
            <View style={styles.modernCard}>
              <View style={styles.cardHeader}>
                <MaterialIcons name="groups" size={24} color="#0F96BB" />
                <Text style={styles.cardTitle}>Meeting Attendees</Text>
              </View>

              <View style={styles.modernAttendeesContainer}>
                {meetingData.attendees_details ? (
                  meetingData.attendees_details.map((attendee: any, index: number) => {
                    const avatarBgColor = getAvatarColor(attendee.name || 'Unknown');
                    const textColor = getContrastingTextColor(avatarBgColor);
                    const isFirst = index === 0;
                    const isLast = index === meetingData.attendees_details!.length - 1;
                    return (
                      <View key={index} style={[
                        styles.modernAttendeeItem,
                        isFirst && styles.firstAttendeeItem,
                        isLast && styles.lastAttendeeItem
                      ]}>
                        <View style={[styles.compactAttendeeAvatar, { backgroundColor: avatarBgColor }]}>
                          <Text style={[styles.compactAttendeeInitial, { color: textColor }]}>
                            {attendee.name?.[0]?.toUpperCase() || "U"}
                          </Text>
                        </View>
                        <View style={styles.modernAttendeeInfo}>
                          <Text style={styles.modernAttendeeName}>{attendee.name}</Text>
                        </View>
                      </View>
                    );
                  })
                ) : (
                  (() => {
                    const ownerName = meetingData.owner_name || "Owner";
                    const avatarBgColor = getAvatarColor(ownerName);
                    const textColor = getContrastingTextColor(avatarBgColor);
                    return (
                      <View style={[styles.modernAttendeeItem, styles.firstAttendeeItem, styles.lastAttendeeItem]}>
                        <View style={[styles.compactAttendeeAvatar, { backgroundColor: avatarBgColor }]}>
                          <Text style={[styles.compactAttendeeInitial, { color: textColor }]}>
                            {ownerName[0]?.toUpperCase() || "O"}
                          </Text>
                        </View>
                        <View style={styles.modernAttendeeInfo}>
                          <Text style={styles.modernAttendeeName}>{ownerName}</Text>
                        </View>
                      </View>
                    );
                  })()
                )}
              </View>
            </View>

            {/* Add padding at the bottom to account for fixed button */}
            <View style={{ height: 80 }} />
          </ScrollView>

          {/* Modern Fixed Reschedule Button at bottom - only show for Pending status */}
          {isPendingStatus && (
            <View style={styles.rescheduleButtonContainer}>
              <TouchableOpacity
                style={styles.modernFixedButton}
                onPress={() => setShowRescheduleModal(true)}
              >
                <MaterialIcons name="schedule" size={18} color="white" style={{ marginRight: 6 }} />
                <Text style={styles.modernFixedButtonText}>Reschedule Meeting</Text>
              </TouchableOpacity>
            </View>
          )}
        </SafeAreaView>

        {/* Reschedule Modal - Centered */}
        <Portal>
          <Modal
            visible={showRescheduleModal}
            onDismiss={() => setShowRescheduleModal(false)}
            contentContainerStyle={styles.rescheduleModalContent}
            dismissable={true}
          >
            {/* Header */}
            <View style={styles.rescheduleModalHeader}>
              <Text style={styles.rescheduleModalTitle}>Reschedule Meeting</Text>
              <TouchableOpacity
                style={styles.modernCloseButton}
                onPress={() => setShowRescheduleModal(false)}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Ionicons name="close" size={20} color={colors.text.primary} />
              </TouchableOpacity>
            </View>

            {/* Content */}
            <View style={styles.rescheduleContent}>
              {/* Meeting Info Card */}
              <View style={styles.meetingInfoCard}>
                <View style={styles.meetingInfoHeader}>
                  <MaterialIcons name="event" size={16} color="#0F96BB" />
                  <Text style={styles.meetingInfoTitle}>Current Schedule</Text>
                </View>
                <Text style={styles.meetingInfoText}>
                  {meetingData?.start_date ? moment(meetingData.start_date).format('DD/MM ddd') : '--'} at {meetingData?.start_time || '--'}
                </Text>
                <Text style={styles.meetingInfoSubtext}>
                  to {meetingData?.end_date ? moment(meetingData.end_date).format('DD/MM ddd') : '--'} at {meetingData?.end_time || '--'}
                </Text>
              </View>

              {/* New Schedule Section */}
              <View style={styles.newScheduleSection}>
                <Text style={styles.sectionTitle}>New Schedule</Text>

                {/* From Date & Time */}
                <View style={styles.modernDateTimeGroup}>
                  <Text style={styles.modernFieldLabel}>From</Text>
                  <View style={styles.modernDateTimeRow}>
                    <TouchableOpacity
                      style={[styles.modernDateTimeButton, fromDateError && styles.modernErrorBorder]}
                      onPress={() => setShowFromDatePicker(true)}
                    >
                      <View style={styles.modernButtonContent}>
                        <Ionicons name="calendar-outline" size={16} color="#0F96BB" />
                        <Text style={[styles.modernButtonText, fromDateError && styles.modernErrorText]}>
                          {moment(fromDate).format('DD/MM/YYYY')}
                        </Text>
                      </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[styles.modernDateTimeButton, fromTimeError && styles.modernErrorBorder]}
                      onPress={() => setShowFromTimePicker(true)}
                    >
                      <View style={styles.modernButtonContent}>
                        <Ionicons name="time-outline" size={16} color="#0F96BB" />
                        <Text style={[styles.modernButtonText, fromTimeError && styles.modernErrorText]}>
                          {fromTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                  {(fromDateError || fromTimeError) && (
                    <Text style={styles.modernErrorMessage}>
                      {fromDateError || fromTimeError}
                    </Text>
                  )}
                </View>

                {/* To Date & Time */}
                <View style={styles.modernDateTimeGroup}>
                  <Text style={styles.modernFieldLabel}>To</Text>
                  <View style={styles.modernDateTimeRow}>
                    <TouchableOpacity
                      style={[styles.modernDateTimeButton, toDateError && styles.modernErrorBorder]}
                      onPress={() => setShowToDatePicker(true)}
                    >
                      <View style={styles.modernButtonContent}>
                        <Ionicons name="calendar-outline" size={16} color="#0F96BB" />
                        <Text style={[styles.modernButtonText, toDateError && styles.modernErrorText]}>
                          {moment(toDate).format('DD/MM/YYYY')}
                        </Text>
                      </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[styles.modernDateTimeButton, toTimeError && styles.modernErrorBorder]}
                      onPress={() => setShowToTimePicker(true)}
                    >
                      <View style={styles.modernButtonContent}>
                        <Ionicons name="time-outline" size={16} color="#0F96BB" />
                        <Text style={[styles.modernButtonText, toTimeError && styles.modernErrorText]}>
                          {toTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                  {(toDateError || toTimeError) && (
                    <Text style={styles.modernErrorMessage}>
                      {toDateError || toTimeError}
                    </Text>
                  )}
                </View>
              </View>
            </View>

            {/* Bottom Action Button */}
            <View style={styles.rescheduleBottomAction}>
              <TouchableOpacity
                style={[
                  styles.modernRescheduleButton,
                  isRescheduling && { opacity: 0.6 }
                ]}
                onPress={handleReschedule}
                disabled={isRescheduling}
              >
                {isRescheduling ? (
                  <ActivityIndicator color="#fff" size="small" />
                ) : (
                  <>
                    <MaterialIcons name="schedule" size={18} color="#fff" />
                    <Text style={styles.modernRescheduleButtonText}>Confirm Reschedule</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          </Modal>
        </Portal>

        {/* Calendar Popup for From Date */}
        <Calendar
          isVisible={showFromDatePicker}
          onClose={() => setShowFromDatePicker(false)}
          onDateSelected={(date) => {
            setFromDate(date);
            setFromDateError('');
            setShowFromDatePicker(false);
            // If to date is before from date, adjust it
            if (toDate < date) {
              setToDate(date);
            }
          }}
          selectedDate={fromDate}
          minDate={new Date()}
          maxDate={new Date(new Date().getFullYear() + 2, 11, 31)} // Allow up to 2 years in future
          title="Select From Date"
          primaryColor="#0F96BB"
        />

        {/* From Time Picker Modal */}
        <RNModal
          transparent={true}
          visible={showFromTimePicker}
          animationType="slide"
          onRequestClose={() => setShowFromTimePicker(false)}
        >
            <View style={styles.timePickerModalOverlay}>
              <View style={styles.timePickerModalContent}>
                <View style={styles.timePickerHeader}>
                  <TouchableOpacity
                    onPress={() => setShowFromTimePicker(false)}
                    style={styles.timePickerHeaderButton}
                  >
                    <Text style={styles.timePickerHeaderButtonText}>Cancel</Text>
                  </TouchableOpacity>
                  <Text style={styles.timePickerHeaderTitle}>Select From Time</Text>
                  <TouchableOpacity
                    onPress={() => {
                      setShowFromTimePicker(false);
                      setFromTimeError('');
                    }}
                    style={styles.timePickerHeaderButton}
                  >
                    <Text style={[styles.timePickerHeaderButtonText, styles.doneButton]}>Done</Text>
                  </TouchableOpacity>
                </View>
                <DateTimePicker
                  value={fromTime}
                  mode="time"
                  is24Hour={false}
                  display="spinner"
                  onChange={onFromTimeChange}
                  minuteInterval={5}
                  style={styles.timePicker}
                />
              </View>
            </View>
        </RNModal>

        {/* Calendar Popup for To Date */}
        <Calendar
          isVisible={showToDatePicker}
          onClose={() => setShowToDatePicker(false)}
          onDateSelected={(date) => {
            if (date >= fromDate) {
              setToDate(date);
              setToDateError('');
              setShowToDatePicker(false);
            } else {
              setToDateError("End date must be after start date");
              Alert.alert("Invalid Date", "End date must be after start date");
            }
          }}
          selectedDate={toDate}
          minDate={fromDate}
          maxDate={new Date(new Date().getFullYear() + 2, 11, 31)} // Allow up to 2 years in future
          title="Select To Date"
          primaryColor="#0F96BB"
        />

        {/* To Time Picker Modal */}
        <RNModal
          transparent={true}
          visible={showToTimePicker}
          animationType="slide"
          onRequestClose={() => setShowToTimePicker(false)}
        >
          <View style={styles.timePickerModalOverlay}>
            <View style={styles.timePickerModalContent}>
              <View style={styles.timePickerHeader}>
                <TouchableOpacity
                  onPress={() => setShowToTimePicker(false)}
                  style={styles.timePickerHeaderButton}
                >
                  <Text style={styles.timePickerHeaderButtonText}>Cancel</Text>
                </TouchableOpacity>
                <Text style={styles.timePickerHeaderTitle}>Select To Time</Text>
                <TouchableOpacity
                  onPress={() => {
                    setShowToTimePicker(false);
                    setToTimeError('');
                  }}
                  style={styles.timePickerHeaderButton}
                >
                  <Text style={[styles.timePickerHeaderButtonText, styles.doneButton]}>Done</Text>
                </TouchableOpacity>
              </View>
              <DateTimePicker
                value={toTime}
                mode="time"
                is24Hour={false}
                display="spinner"
                onChange={onToTimeChange}
                minuteInterval={5}
                style={styles.timePicker}
              />
            </View>
          </View>
        </RNModal>

        {/* Add Bottom Sheet for Checkout */}
        <Portal>
          <Modal
            visible={showCheckoutSheet}
            onDismiss={() => setShowCheckoutSheet(false)}
            contentContainerStyle={[
              styles.modalContainer,
              {
                backgroundColor: colors.background.primary,
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
                maxHeight: '95%',
                height: Platform.OS === 'ios' ? '90%' : '92%',
                borderTopLeftRadius: 20,
                borderTopRightRadius: 20,
                display: 'flex',
                flexDirection: 'column'
              }
            ]}
          >
            <View style={{ flex: 1, position: 'relative', display: 'flex', flexDirection: 'column' }}>
              <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                style={{ flex: 1, display: 'flex', flexDirection: 'column' }}
                keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 40}
              >
              {/* Fixed Header */}
              <View style={{
                backgroundColor: colors.background.primary,
                borderTopLeftRadius: 20,
                borderTopRightRadius: 20,
                borderBottomWidth: 1,
                borderBottomColor: 'rgba(0,0,0,0.1)',
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.1,
                shadowRadius: 3,
                elevation: 4,
                zIndex: 10,
                position: 'relative',
              }}>
                {/* Drag Indicator */}
                <View style={{
                  width: 40,
                  height: 5,
                  backgroundColor: '#DEDEDE',
                  borderRadius: 2.5,
                  alignSelf: 'center',
                  marginTop: 10
                }} />

                {/* Header Content */}
                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  paddingHorizontal: 16,
                  paddingTop: 16,
                  paddingBottom: 16,
                }}>
                  <TouchableOpacity
                    onPress={() => setShowCheckoutSheet(false)}
                    hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                    style={{
                      padding: 8,
                      backgroundColor: 'rgba(0,0,0,0.05)',
                      borderRadius: 20,
                    }}
                  >
                    <Ionicons name="close" size={22} color={colors.text.primary} />
                  </TouchableOpacity>

                  <Text style={{
                    fontSize: 18,
                    fontWeight: '700',
                    color: colors.text.primary,
                    textAlign: 'center',
                  }}>Check Out Details</Text>

                  <View style={{ width: 38 }} />
                </View>
              </View>

              {/* Scrollable Content */}
              <ScrollView
                style={{ flex: 1 }}
                contentContainerStyle={{
                  padding: 20,
                  paddingBottom: 24
                }}
                showsVerticalScrollIndicator={true}
                bounces={true}
                keyboardShouldPersistTaps="handled"
                alwaysBounceVertical={false}
              >
                {/* Transport Mode */}
                <View style={{ marginBottom: 24 }}>
                  <Text style={{
                    fontSize: 16,
                    fontWeight: '600',
                    color: colors.text.primary,
                    marginBottom: 12
                  }}>Mode of Transport<Text style={{ color: 'red' }}>*</Text></Text>

                  {Platform.OS === 'ios' ? (
                    <TouchableOpacity
                      style={{
                        backgroundColor: colors.background.secondary,
                        padding: 16,
                        borderRadius: 8,
                        borderWidth: 1,
                        borderColor: transportMode ? colors.primary : colors.text.secondary,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        shadowColor: '#000',
                        shadowOffset: { width: 0, height: 1 },
                        shadowOpacity: 0.05,
                        shadowRadius: 2,
                        elevation: 1
                      }}
                      onPress={() => {
                        if (isLoadingTransportModes || transportModes.length === 0) {
                          return;
                        }
                        ActionSheetIOS.showActionSheetWithOptions(
                          {
                            options: ['Cancel', ...transportModes],
                            cancelButtonIndex: 0,
                            title: 'Select Transport Mode'
                          },
                          (buttonIndex) => {
                            if (buttonIndex !== 0) {
                              const selectedMode = transportModes[buttonIndex - 1];
                              setTransportMode(selectedMode);
                              if (selectedMode !== 'Public Transport') {
                                setAmount('');
                                setBillImage(null);
                              }
                            }
                          }
                        );
                      }}
                      disabled={isLoadingTransportModes}
                    >
                      {isLoadingTransportModes ? (
                        <ActivityIndicator size="small" color={colors.primary} />
                      ) : (
                        <Text style={{
                          color: transportMode ? colors.text.primary : colors.text.secondary,
                          fontSize: 16,
                          fontWeight: transportMode ? '500' : 'normal'
                        }}>
                          {transportMode || "Select Transport Mode"}
                        </Text>
                      )}
                      {!isLoadingTransportModes && (
                        <MaterialIcons name="keyboard-arrow-down" size={24} color={transportMode ? colors.primary : colors.text.secondary} />
                      )}
                    </TouchableOpacity>
                  ) : (
                    <View style={{
                      borderWidth: 1,
                      borderColor: transportMode ? colors.primary : colors.text.secondary,
                      borderRadius: 8,
                      backgroundColor: colors.background.secondary,
                      shadowColor: '#000',
                      shadowOffset: { width: 0, height: 1 },
                      shadowOpacity: 0.05,
                      shadowRadius: 2,
                      elevation: 1
                    }}>
                      <View style={{ borderRadius: 8, overflow: 'hidden' }}>
                        {isLoadingTransportModes ? (
                          <View style={{
                            height: 50,
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}>
                            <ActivityIndicator size="small" color={colors.primary} />
                          </View>
                        ) : (
                          <Picker
                            selectedValue={transportMode}
                            onValueChange={(itemValue: string) => {
                              setTransportMode(itemValue);
                              if (itemValue !== 'Public Transport') {
                                setAmount('');
                                setBillImage(null);
                              }
                            }}
                            style={{
                              height: 50,
                              color: transportMode ? colors.text.primary : colors.text.secondary,
                              fontWeight: transportMode ? '500' : 'normal'
                            }}
                            dropdownIconColor={transportMode ? colors.primary : colors.text.secondary}
                            enabled={!isLoadingTransportModes}
                          >
                            <Picker.Item label="Select Transport Mode" value="" />
                            {transportModes.map((mode) => (
                              <Picker.Item key={mode} label={mode} value={mode} color={colors.text.primary} />
                            ))}
                          </Picker>
                        )}
                      </View>
                    </View>
                  )}
                </View>

                {/* Public Transport Fields */}
                {transportMode === 'Public Transport' && (
                  <>
                    {/* Amount Field */}
                    <View style={{ marginBottom: 24 }}>
                      <Text style={{
                        fontSize: 16,
                        fontWeight: '600',
                        color: colors.text.primary,
                        marginBottom: 12
                      }}>Amount<Text style={{ color: 'red' }}>*</Text></Text>
                      <View style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        borderWidth: 1,
                        borderColor: amount ? colors.primary : colors.text.secondary,
                        borderRadius: 8,
                        paddingHorizontal: 16,
                        height: 52,
                        backgroundColor: colors.background.secondary,
                        shadowColor: '#000',
                        shadowOffset: { width: 0, height: 1 },
                        shadowOpacity: 0.05,
                        shadowRadius: 2,
                        elevation: 1
                      }}>
                        <Text style={{
                          fontSize: 18,
                          color: colors.text.primary,
                          marginRight: 8,
                          fontWeight: '500'
                        }}>₹</Text>
                        <StableInput
                          style={{
                            flex: 1,
                            fontSize: 16,
                            color: colors.text.primary,
                            padding: 0,
                            height: '100%'
                          }}
                          value={internalAmount}
                          onChangeText={(text) => {
                            // Remove any non-numeric characters except decimal point
                            const sanitizedText = text.replace(/[^0-9.]/g, '');
                            // Ensure only one decimal point
                            const parts = sanitizedText.split('.');
                            const formattedText = parts.length > 2 ? `${parts[0]}.${parts[1]}` : sanitizedText;

                            setInternalAmount(formattedText);
                            // Use a longer timeout to ensure UI updates properly
                            const timeoutId = setTimeout(() => {
                              setAmount(formattedText);
                            }, 300);
                            return () => clearTimeout(timeoutId);
                          }}
                          placeholder="0.00"
                          placeholderTextColor={colors.text.secondary}
                          keyboardType="decimal-pad"
                          returnKeyType="done"
                          maxLength={10}
                        />
                      </View>
                    </View>

                    {/* Bill Image */}
                    <View style={{ marginBottom: 24 }}>
                      <Text style={{
                        fontSize: 16,
                        fontWeight: '600',
                        color: colors.text.primary,
                        marginBottom: 12
                      }}>Bill Image<Text style={{ color: 'red' }}>*</Text></Text>

                      <View style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between'
                      }}>
                        {billImage ? (
                          <View style={{
                            width: 60,
                            height: 60,
                            borderRadius: 8,
                            overflow: 'hidden',
                            position: 'relative'
                          }}>
                            <Image
                              source={{ uri: billImage.uri }}
                              style={{
                                width: '100%',
                                height: '100%'
                              }}
                              resizeMode="cover"
                            />
                            <TouchableOpacity
                              style={{
                                position: 'absolute',
                                top: -10,
                                right: -10,
                                backgroundColor: colors.primary,
                                borderRadius: 15,
                                width: 30,
                                height: 30,
                                alignItems: 'center',
                                justifyContent: 'center',
                                shadowColor: '#000',
                                shadowOffset: { width: 0, height: 2 },
                                shadowOpacity: 0.25,
                                shadowRadius: 3.84,
                                elevation: 5,
                              }}
                              onPress={() => setBillImage(null)}
                            >
                              <MaterialIcons name="close" size={20} color="white" />
                            </TouchableOpacity>
                          </View>
                        ) : (
                          <TouchableOpacity
                            style={{
                              width: 60,
                              height: 60,
                              borderRadius: 30,
                              borderWidth: 1,
                              borderStyle: 'dashed',
                              borderColor: colors.text.secondary,
                              alignItems: 'center',
                              justifyContent: 'center',
                              backgroundColor: 'rgba(15, 150, 187, 0.1)'
                            }}
                            onPress={() => {
                              if (Platform.OS === 'ios') {
                                ActionSheetIOS.showActionSheetWithOptions(
                                  {
                                    options: ['Cancel', 'Take Photo', 'Choose from Library'],
                                    cancelButtonIndex: 0,
                                  },
                                  buttonIndex => {
                                    if (buttonIndex === 1) {
                                      takeBillPhoto();
                                    } else if (buttonIndex === 2) {
                                      pickBillImage();
                                    }
                                  }
                                );
                              } else {
                                Alert.alert(
                                  'Select Image',
                                  'Choose an option',
                                  [
                                    { text: 'Cancel', style: 'cancel' },
                                    { text: 'Take Photo', onPress: takeBillPhoto },
                                    { text: 'Choose from Library', onPress: pickBillImage }
                                  ]
                                );
                              }
                            }}
                          >
                            <MaterialIcons name="add-circle-outline" size={32} color={colors.primary} />
                          </TouchableOpacity>
                        )}
                      </View>
                    </View>
                  </>
                )}

                {/* Check-out Image */}
                <View style={{ marginBottom: 24 }}>
                  <Text style={{
                    fontSize: 16,
                    fontWeight: '600',
                    color: colors.text.primary,
                    marginBottom: 12
                  }}>Check-out Image<Text style={{ color: 'red' }}>*</Text></Text>

                  <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between'
                  }}>
                    {checkOutImage ? (
                      <View style={{
                        width: 60,
                        height: 60,
                        borderRadius: 8,
                        overflow: 'hidden',
                        position: 'relative'
                      }}>
                        <Image
                          source={{ uri: checkOutImage.uri }}
                          style={{
                            width: '100%',
                            height: '100%'
                          }}
                          resizeMode="cover"
                        />
                        <TouchableOpacity
                          style={{
                            position: 'absolute',
                            top: -10,
                            right: -10,
                            backgroundColor: colors.primary,
                            borderRadius: 15,
                            width: 30,
                            height: 30,
                            alignItems: 'center',
                            justifyContent: 'center',
                            shadowColor: '#000',
                            shadowOffset: { width: 0, height: 2 },
                            shadowOpacity: 0.25,
                            shadowRadius: 3.84,
                            elevation: 5,
                          }}
                          onPress={() => setCheckOutImage(null)}
                        >
                          <MaterialIcons name="close" size={20} color="white" />
                        </TouchableOpacity>
                      </View>
                    ) : (
                      <TouchableOpacity
                        style={{
                          width: 60,
                          height: 60,
                          borderRadius: 30,
                          borderWidth: 1,
                          borderStyle: 'dashed',
                          borderColor: colors.text.secondary,
                          alignItems: 'center',
                          justifyContent: 'center',
                          backgroundColor: 'rgba(15, 150, 187, 0.1)'
                        }}
                        onPress={() => {
                          if (Platform.OS === 'ios') {
                            ActionSheetIOS.showActionSheetWithOptions(
                              {
                                options: ['Cancel', 'Take Photo', 'Choose from Library'],
                                cancelButtonIndex: 0,
                              },
                              buttonIndex => {
                                if (buttonIndex === 1) {
                                  takeCheckOutPhoto();
                                } else if (buttonIndex === 2) {
                                  pickCheckOutImage();
                                }
                              }
                            );
                          } else {
                            Alert.alert(
                              'Select Image',
                              'Choose an option',
                              [
                                { text: 'Cancel', style: 'cancel' },
                                { text: 'Take Photo', onPress: takeCheckOutPhoto },
                                { text: 'Choose from Library', onPress: pickCheckOutImage }
                              ]
                            );
                          }
                        }}
                      >
                        <MaterialIcons name="add-circle-outline" size={32} color={colors.primary} />
                      </TouchableOpacity>
                    )}
                  </View>
                </View>

                {/* Notes */}
                <View style={{ marginBottom: 24 }}>
                  <Text style={{
                    fontSize: 16,
                    fontWeight: '600',
                    color: colors.text.primary,
                    marginBottom: 12
                  }}>Notes</Text>

                  <View style={{
                    borderWidth: 1,
                    borderColor: isNotesFocused ? colors.primary : (remarks ? colors.primary : colors.text.secondary),
                    borderRadius: 8,
                    backgroundColor: colors.background.secondary
                  }}>
                    <TextInput
                      ref={notesInputRef}
                      style={{
                        padding: 16,
                        minHeight: 120,
                        textAlignVertical: 'top',
                        fontSize: 16,
                        color: colors.text.primary,
                        width: '100%',
                        ...(Platform.OS === 'ios' ? {
                          height: 120
                        } : {})
                      }}
                      value={remarks}
                      onChangeText={setRemarks}
                      placeholder="Enter notes about your meeting here..."
                      placeholderTextColor={colors.text.secondary}
                      multiline={true}
                      numberOfLines={Platform.OS === 'ios' ? undefined : 4}
                      onFocus={() => setIsNotesFocused(true)}
                      onBlur={() => setIsNotesFocused(false)}
                      autoCapitalize="sentences"
                      returnKeyType="default"
                    />
                  </View>
                </View>

                {/* Extra padding for keyboard */}
                <View style={{ height: 20 }} />
              </ScrollView>
              </KeyboardAvoidingView>

              {/* Fixed Bottom Button Container */}
              <View style={{
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
                backgroundColor: colors.background.primary,
                paddingHorizontal: 20,
                paddingTop: 12,
                paddingBottom: Platform.OS === 'ios' ? 34 : 24,
                borderTopWidth: 1,
                borderTopColor: 'rgba(0,0,0,0.1)',
                shadowColor: '#000',
                shadowOffset: { width: 0, height: -3 },
                shadowOpacity: 0.1,
                shadowRadius: 3,
                elevation: 10,
              }}>
                {/* Error Messages - Only show after user has attempted to submit */}
                {hasAttemptedSubmit && (!transportMode ||
                  !checkOutImage ||
                  (transportMode === 'Public Transport' && (!amount || !billImage))) && (
                  <Text style={{
                    color: '#dc3545',
                    fontSize: 14,
                    marginBottom: 8,
                    textAlign: 'center'
                  }}>
                    {!transportMode ? 'Please select transport mode' :
                     !checkOutImage ? 'Please add a check-out image' :
                     !amount ? 'Please enter amount' :
                     !billImage ? 'Please add bill image' : ''}
                  </Text>
                )}

                {/* Check Out Button */}
                <TouchableOpacity
                  style={{
                    backgroundColor: '#0F96BB',
                    borderRadius: 8,
                    padding: 18,
                    alignItems: 'center',
                    justifyContent: 'center',
                    opacity: (!transportMode || !checkOutImage ||
                      (transportMode === 'Public Transport' && (!amount || !billImage)) ||
                      isCheckingIn) ? 0.5 : 1,
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: 2 },
                    shadowOpacity: 0.15,
                    shadowRadius: 5,
                    elevation: 5,
                    minHeight: 56
                  }}
                  onPress={() => {
                    setHasAttemptedSubmit(true); // Set flag when user attempts to submit

                    if (!transportMode) {
                      Alert.alert('Error', 'Please select transport mode');
                      return;
                    }
                    if (!checkOutImage) {
                      Alert.alert('Error', 'Please add a check-out image');
                      return;
                    }
                    if (transportMode === 'Public Transport') {
                      if (!amount) {
                        Alert.alert('Error', 'Please enter amount');
                        return;
                      }
                      if (!billImage) {
                        Alert.alert('Error', 'Please add bill image');
                        return;
                      }
                    }
                    handleCheckOutSubmit();
                  }}
                  disabled={isCheckingIn}
                >
                  {isCheckingIn ? (
                    <ActivityIndicator color="#fff" size="small" />
                  ) : (
                    <Text style={{
                      color: '#fff',
                      fontSize: 18,
                      fontWeight: '700',
                      textAlign: 'center',
                      letterSpacing: 0.5
                    }}>CHECK OUT</Text>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </Modal>
        </Portal>

        {/* Description Modal */}
        <Portal>
          <Modal
            visible={showDescriptionModal}
            onDismiss={() => setShowDescriptionModal(false)}
            contentContainerStyle={styles.descriptionModalContent}
            dismissable={true}
          >
            {/* Modal Header */}
            <View style={styles.descriptionModalHeader}>
              <Text style={styles.descriptionModalTitle}>Description</Text>
              <TouchableOpacity
                style={styles.descriptionModalCloseButton}
                onPress={() => setShowDescriptionModal(false)}
                accessibilityLabel="Close description"
                accessibilityRole="button"
              >
                <MaterialIcons name="close" size={20} color="#666" />
              </TouchableOpacity>
            </View>

            {/* Modal Content */}
            <ScrollView
              style={styles.descriptionModalScrollContent}
              contentContainerStyle={styles.descriptionModalScrollContainer}
              showsVerticalScrollIndicator={true}
              bounces={true}
            >
              <Text style={styles.descriptionModalText}>
                {meetingData?.description || 'No description available'}
              </Text>
            </ScrollView>
          </Modal>
        </Portal>
      </View>
    </PaperProvider>
  );
}

const createStyles = (colors: ReturnType<typeof useColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  safeArea: {
    flex: 1,
  },
  appbar: {
    backgroundColor: '#0F96BB',
  },
  appbarTitle: {
    color: 'white',
    fontWeight: '600',
    textAlign: 'center',
  },
  actionButton: {
    marginHorizontal: -4,
  },
  content: {
    padding: 16,
  },
  titleSection: {
    marginBottom: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleWrapper: {
    flex: 1,
  },
  titleText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  subtitleText: {
    fontSize: 16,
    color: colors.text.secondary,
  },
  statusBadge: {
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 6,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
  },
  statusText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  dateSection: {
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  icon: {
    marginRight: 8,
    marginTop: 2,
  },
  dateTimeInfo: {
    flexDirection: 'column',
    flex: 1,
  },
  dateTimeHeader: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  dateRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    marginRight: 8,
  },
  dateText: {
    fontSize: 15,
    fontWeight: '500',
    color: colors.text.primary,
  },
  actualTimeContainer: {
    marginBottom: 16,
  },
  spentTimeRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  spentTimeText: {
    fontSize: 14,
  },
  section: {
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationDetails: {
    flex: 1,
  },
  checkInOptionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  imageAttachmentContainer: {
    marginRight: 8,
  },
  selectedImageContainer: {
    marginTop: 12,
    height: 120,
    borderRadius: 8,
    overflow: 'hidden',
  },
  selectedImage: {
    width: '100%',
    height: '100%',
  },
  removeImageButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 12,
    padding: 4,
  },
  checkInButton: {
    padding: 12,
    backgroundColor: colors.primary,
    borderRadius: 4,
  },
  checkInText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
  },
  mapContainer: {
    width: '100%',
    height: 150,
  },
  map: {
    width: '100%',
    height: '100%',
  },
  uploadedImageContainer: {
    marginBottom: 16,
  },
  attachmentLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  uploadedImage: {
    width: '100%',
    height: 150,
    borderRadius: 4,
  },
  locationTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  locationIcon: {
    marginRight: 8,
  },
  locationText: {
    fontSize: 14,
  },
  timeText: {
    fontSize: 14,
  },
  modalContainer: {
    backgroundColor: colors.background.primary,
    ...(Platform.OS === 'ios' ? {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      maxHeight: '95%',
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: -3,
      },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    } : {
      flex: 1,
      margin: 0,
    })
  },
  modalContent: {
    flex: 1,
  },
  modalHeader: {
    paddingTop: 12,
    paddingBottom: 16,
    paddingHorizontal: 20,
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.text.secondary,
  },
  dragIndicator: {
    width: 40,
    height: 4,
    backgroundColor: '#DEDEDE',
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: 12,
    marginBottom: 8,
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  modalTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: colors.text.primary,
    textAlign: 'center',
    marginTop: 8,
  },
  checkoutContent: {
    flex: 1,
  },
  checkoutContentContainer: {
    padding: 16,
    paddingBottom: 34,
  },
  inputSection: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 17,
    fontWeight: '600',
    marginBottom: 10,
    color: colors.text.primary,
  },
  iosDropdownButton: {
    backgroundColor: '#0F96BB',
    borderRadius: 10,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  iosDropdownButtonText: {
    color: '#fff',
    fontSize: 17,
    fontWeight: '500',
  },
  imageButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    gap: 12,
  },
  imageButton: {
    backgroundColor: '#0F96BB',
    borderRadius: 8,
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    minWidth: 120,
  },
  imageButtonText: {
    color: '#fff',
    fontSize: 15,
    fontWeight: '500',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: colors.text.secondary,
    borderRadius: 8,
    marginBottom: 8,
  },
  picker: {
    width: '100%',
    height: 50,
  },
  remarksInput: {
    borderWidth: 1,
    borderColor: colors.text.secondary,
    borderRadius: 8,
    padding: 12,
    minHeight: 100,
    backgroundColor: '#fff',
    textAlignVertical: 'top',
    fontSize: 15,
  },
  submitButton: {
    backgroundColor: '#0F96BB',
    borderRadius: 10,
    padding: 16,
    alignItems: 'center',
    marginTop: 24,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 17,
    fontWeight: '600',
  },
  disabledButton: {
    opacity: 0.5,
  },
  fixedRescheduleContainer: {
    padding: 16,
    backgroundColor: colors.primary,
    borderRadius: 4,
    alignItems: 'center',
  },
  fixedRescheduleText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
  },
  modalOverlay: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  bottomSheetHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  bottomSheetTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text.primary,
    textAlign: 'center',
    flex: 1,
  },
  rescheduleContent: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 12,
    paddingBottom: 12,
    backgroundColor: colors.background.primary,
    justifyContent: 'space-between',
  },
  dateTimeSection: {
    marginBottom: 20,
  },
  dateTimeLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  dateTimeLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginLeft: 8,
  },
  dateTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    backgroundColor: '#F8F9FA',
    minHeight: 48,
  },
  errorBorder: {
    borderColor: '#dc3545',
  },
  errorText: {
    color: '#dc3545',
  },
  errorMessage: {
    color: '#dc3545',
    fontSize: 12,
    marginTop: 4,
  },
  rescheduleBottomContainer: {
    backgroundColor: colors.background.primary,
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 24,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
  },

  // Modern Reschedule Modal Styles - Compact Height
  rescheduleModalContent: {
    backgroundColor: colors.background.primary,
    borderRadius: 16,
    marginHorizontal: 16,
    marginVertical: 'auto',
    height: Dimensions.get('window').height * 0.5, // 50% of device height (reduced from 60%)
    width: '92%',
    maxWidth: 420,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 12,
    elevation: 16,
    display: 'flex',
    flexDirection: 'column',
  },
  modernDragIndicator: {
    width: 40,
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: 12,
    marginBottom: 8,
  },
  rescheduleModalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 12,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.08)',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  modernCloseButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0,0,0,0.05)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  rescheduleModalTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text.primary,
    flex: 1,
  },
  meetingInfoCard: {
    backgroundColor: '#F8FFFE',
    borderRadius: 10,
    padding: 12,
    marginBottom: 16,
    borderLeftWidth: 3,
    borderLeftColor: '#0F96BB',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  meetingInfoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  meetingInfoTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#0F96BB',
    marginLeft: 6,
  },
  meetingInfoText: {
    fontSize: 15,
    fontWeight: '500',
    color: colors.text.primary,
    marginBottom: 3,
  },
  meetingInfoSubtext: {
    fontSize: 13,
    color: colors.text.secondary,
  },
  newScheduleSection: {
    marginBottom: 12,
  },
  modernDateTimeGroup: {
    marginBottom: 16,
  },
  modernFieldLabel: {
    fontSize: 13,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 4,
  },
  modernDateTimeRow: {
    flexDirection: 'row',
    gap: 8,
  },
  modernDateTimeButton: {
    flex: 1,
    backgroundColor: colors.background.secondary,
    borderRadius: 6,
    borderWidth: 1.5,
    borderColor: '#E5E5E5',
    paddingVertical: 8,
    paddingHorizontal: 10,
    minHeight: 36,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  modernButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
  },
  modernButtonText: {
    fontSize: 13,
    fontWeight: '500',
    color: colors.text.primary,
  },
  modernErrorBorder: {
    borderColor: '#dc3545',
    backgroundColor: '#fff5f5',
  },
  modernErrorText: {
    color: '#dc3545',
  },
  modernErrorMessage: {
    fontSize: 12,
    color: '#dc3545',
    marginTop: 6,
    marginLeft: 4,
  },
  rescheduleBottomAction: {
    backgroundColor: colors.background.primary,
    paddingHorizontal: 16,
    paddingTop: 12,
    paddingBottom: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.08)',
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  modernRescheduleButton: {
    backgroundColor: '#0F96BB',
    borderRadius: 10,
    paddingVertical: 12,
    paddingHorizontal: 16,
    minHeight: 44,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 5,
    shadowColor: '#0F96BB',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  modernRescheduleButtonText: {
    fontSize: 15,
    fontWeight: '600',
    color: '#fff',
  },
  rescheduleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    backgroundColor: '#0F96BB',
    borderRadius: 12,
    shadowColor: '#0F96BB',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 6,
  },
  rescheduleButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: 'white',
    marginLeft: 8,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  outcomeText: {
    fontSize: 14,
    lineHeight: 20,
  },
  relatedContainer: {
    marginTop: 8,
  },
  subTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  contactCard: {
    backgroundColor: colors.background.secondary,
    borderRadius: 8,
    marginBottom: 8,
    padding: 12,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    marginRight: 12,
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  emailText: {
    fontSize: 14,
    color: colors.text.secondary,
  },
  typeText: {
    fontSize: 12,
    color: colors.text.secondary,
    marginTop: 4,
  },
  emailButton: {
    padding: 8,
  },
  attendeesContainer: {
    marginTop: 8,
  },
  attendeesList: {
    marginTop: 8,
  },
  attendeeCard: {
    backgroundColor: colors.background.secondary,
    borderRadius: 8,
    marginBottom: 8,
    padding: 12,
  },
  attendeeItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  attendeeInfo: {
    flex: 1,
    marginLeft: 12,
  },
  attendeeName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  attendeeRole: {
    fontSize: 14,
    color: colors.text.secondary,
  },
  amountInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.text.secondary,
    height: 44,
    paddingHorizontal: 12,
  },
  currencySymbol: {
    fontSize: 17,
    fontWeight: '500',
    color: colors.text.primary,
    marginRight: 8,
  },
  amountInput: {
    flex: 1,
    fontSize: 17,
    color: colors.text.primary,
    height: '100%',
    padding: 0,
  },
  selectedImagePreview: {
    marginTop: 12,
    height: 120,
    borderRadius: 8,
    overflow: 'hidden',
  },
  previewImage: {
    width: '100%',
    height: '100%',
  },

  // Modern UI Styles
  modernCard: {
    backgroundColor: colors.background.primary,
    borderRadius: 16,
    marginBottom: 10,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 6,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },

  titleHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },

  titleContent: {
    flex: 1,
    marginRight: 16,
  },

  modernTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text.primary,
    lineHeight: 28,
    marginBottom: 8,
  },

  modernSubtitle: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },

  modernStatusBadge: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignSelf: 'flex-start',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },

  modernStatusText: {
    fontSize: 12,
    fontWeight: '700',
    letterSpacing: 0.5,
  },

  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
    paddingBottom: 5,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.08)',
  },

  cardTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.text.primary,
    marginLeft: 12,
    flex: 1,
  },

  timeSection: {
    marginBottom: 20,
  },

  timeSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 12,
  },

  timeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },

  timeItem: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'rgba(15, 150, 187, 0.05)',
    borderRadius: 12,
    marginHorizontal: 4,
  },

  timeDivider: {
    width: 2,
    height: 40,
    backgroundColor: 'rgba(15, 150, 187, 0.2)',
    marginHorizontal: 8,
  },

  timeLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#666',
    marginBottom: 4,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },

  timeValue: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.text.primary,
    textAlign: 'center',
  },

  timeSubValue: {
    fontSize: 14,
    color: '#0F96BB',
    fontWeight: '600',
    marginTop: 4,
  },

  spentTimeContainer: {
    marginTop: 16,
    padding: 16,
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },

  spentTimeLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4CAF50',
    marginBottom: 4,
  },

  spentTimeValue: {
    fontSize: 18,
    fontWeight: '700',
    color: '#4CAF50',
  },

  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },

  statusLabel: {
    fontSize: 12,
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },

  actionContainer: {
    marginTop: 8,
  },

  modernImagePreview: {
    width: '100%',
    height: 120,
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
    position: 'relative',
  },

  modernRemoveButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(0,0,0,0.7)',
    borderRadius: 15,
    width: 30,
    height: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },

  modernActionButton: {
    backgroundColor: '#0F96BB',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#0F96BB',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },

  modernActionText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '700',
    marginLeft: 8,
    letterSpacing: 0.5,
  },

  // Check-in Button Styles
  checkinActionButton: {
    backgroundColor: 'transparent',
    borderRadius: 12,
    paddingVertical: 8,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    maxWidth: 160,
    alignSelf: 'center',
  },

  checkinActionText: {
    color: '#4CAF50',
    fontSize: 14,
    fontWeight: '700',
    marginLeft: 6,
    letterSpacing: 0.3,
    textTransform: 'uppercase',
  },

  // Check-out Button Styles
  checkoutActionButton: {
    backgroundColor: 'transparent',
    borderRadius: 12,
    paddingVertical: 8,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    maxWidth: 160,
    alignSelf: 'center',
  },

  checkoutActionText: {
    color: '#FF5722',
    fontSize: 14,
    fontWeight: '700',
    marginLeft: 6,
    letterSpacing: 0.3,
    textTransform: 'uppercase',
  },

  completedContainer: {
    marginTop: 16,
  },

  modernMapContainer: {
    width: '100%',
    height: 140, // reduced by 20px
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
    position: 'relative',
  },

  modernMap: {
    width: '100%',
    height: '100%', // remains full container height
  },

  mapOverlay: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(0,0,0,0.7)',
    borderRadius: 20,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },

  detailsContainer: {
    marginBottom: 16,
  },

  modernLocationRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
    padding: 12,
    backgroundColor: 'rgba(15, 150, 187, 0.05)',
    borderRadius: 8,
  },

  modernLocationText: {
    flex: 1,
    fontSize: 14,
    color: colors.text.primary,
    marginLeft: 8,
    lineHeight: 20,
  },

  modernTimeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: 'rgba(0,0,0,0.03)',
    borderRadius: 8,
  },

  modernTimeText: {
    fontSize: 14,
    color: colors.text.primary,
    marginLeft: 8,
    fontWeight: '500',
  },

  modernAttachmentContainer: {
    width: '100%',
    height: 160,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
  },

  modernAttachmentImage: {
    width: '100%',
    height: '100%',
  },

  imageOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },

  outcomeContainer: {
    marginTop: 8,
  },

  modernOutcomeText: {
    fontSize: 16,
    lineHeight: 24,
    color: colors.text.primary,
  },

  modernContactContainer: {
    marginTop: 8,
  },

  modernContactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8, // match attendee
    backgroundColor: 'rgba(15, 150, 187, 0.05)',
    borderRadius: 8, // match attendee
    marginBottom: 6, // match attendee
  },

  modernAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },

  avatarLabel: {
    fontSize: 13,
    fontWeight: '700',
    textAlign: 'center',
  },

  modernContactInfo: {
    flex: 1,
  },

  modernContactName: {
    fontSize: 15, // match attendee
    fontWeight: '600', // match attendee
    color: colors.text.primary,
    marginBottom: 2, // match attendee
  },

  modernEmailText: {
    fontSize: 14,
    color: '#0F96BB',
    marginBottom: 8,
    fontWeight: '500',
  },

  modernTypeContainer: {
    alignSelf: 'flex-start',
  },

  modernTypeText: {
    fontSize: 12,
    color: '#666',
    backgroundColor: 'rgba(0,0,0,0.05)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },

  modernEmailButton: {
    padding: 12,
    backgroundColor: 'rgba(15, 150, 187, 0.1)',
    borderRadius: 24,
  },

  modernAttendeesContainer: {
    marginTop: 8,
  },

  modernAttendeeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    backgroundColor: 'rgba(15, 150, 187, 0.05)',
    borderRadius: 8,
    marginBottom: 6,
  },

  firstAttendeeItem: {
    marginTop: -4,
  },

  lastAttendeeItem: {
    marginBottom: 0,
  },

  attachmentSection: {
    marginTop: 6,
  },

  attachmentTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 8,
  },

  smallAttachmentContainer: {
    width: 80,
    height: 80,
    borderRadius: 8,
    overflow: 'hidden',
    position: 'relative',
    alignSelf: 'flex-start',
  },

  smallAttachmentImage: {
    width: '100%',
    height: '100%',
  },

  smallImageOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },

  // Single Line Schedule & Timing Card Styles
  singleLineContainer: {
    marginTop: 8,
  },

  singleTimeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 14,
    paddingHorizontal: 8,
    marginBottom: 10,
    backgroundColor: 'rgba(15, 150, 187, 0.04)',
    borderRadius: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#0F96BB',
    minHeight: 50,
  },

  timeRowHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: 90,
    maxWidth: 90,
  },

  timeRowLabel: {
    fontSize: 12,
    fontWeight: '700',
    color: colors.text.primary,
    marginLeft: 8,
    textTransform: 'uppercase',
    letterSpacing: 0.8,
  },

  timeRowContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'flex-end',
    paddingLeft: 10,
  },

  singleTimeItem: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginHorizontal: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.08)',
    minWidth: 100,
  },

  timeItemContent: {
    alignItems: 'center',
  },

  timeItemTitle: {
    fontSize: 10,
    fontWeight: '700',
    color: '#666',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    marginBottom: 4,
  },

  singleTimeText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.text.primary,
    textAlign: 'center',
  },

  arrowIcon: {
    marginHorizontal: 10,
  },

  durationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 14,
    paddingHorizontal: 8,
    marginBottom: 10,
    backgroundColor: 'rgba(33, 150, 243, 0.06)',
    borderRadius: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
    minHeight: 50,
  },

  durationText: {
    fontSize: 13,
    fontWeight: '700',
    color: '#2196F3',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(33, 150, 243, 0.2)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },

  modernAttendeeAvatar: {
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  attendeeAvatarLabel: {
    fontSize: 16,
    fontWeight: '700',
  },

  modernAttendeeInfo: {
    flex: 1,
  },

  modernAttendeeName: {
    fontSize: 15,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 2,
  },

  modernAttendeeRole: {
    fontSize: 13,
    color: '#666',
    fontWeight: '500',
  },

  rescheduleButtonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    paddingTop: 12,
    paddingBottom: 20,
    paddingHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
  },

  modernFixedButton: {
    backgroundColor: '#0F96BB',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#0F96BB',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 6,
  },

  modernFixedButtonText: {
    color: 'white',
    fontSize: 15,
    fontWeight: '600',
    letterSpacing: 0.3,
  },

  // Compact attendee avatar styles (similar to contacts page)
  compactAttendeeAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },

  compactAttendeeInitial: {
    fontSize: 13,
    fontWeight: '700',
    textAlign: 'center',
  },

  dealContactItemCompact: {
    paddingVertical: 4,
    paddingTop: 4,
    paddingBottom: 4,
    minHeight: 0,
    height: 40,
  },

  // Description styles
  descriptionContainer: {
    marginTop: 8,
  },

  descriptionText: {
    fontSize: 16,
    color: colors.text.primary,
    lineHeight: 24,
    textAlign: 'left',
  },

  readMoreButton: {
    marginTop: 8,
    alignSelf: 'flex-start',
  },

  readMoreText: {
    fontSize: 14,
    color: '#0F96BB',
    fontWeight: '600',
  },

  // Description modal styles
  descriptionModalContent: {
    backgroundColor: colors.background.primary,
    borderRadius: 20,
    marginHorizontal: 20,
    marginVertical: 'auto',
    maxHeight: '80%',
    minHeight: 300,
    width: '90%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 12,
    elevation: 16,
  },

  descriptionModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.08)',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },

  descriptionModalTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text.primary,
    flex: 1,
  },

  descriptionModalCloseButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0,0,0,0.05)',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 16,
  },

  descriptionModalScrollContent: {
    flex: 1,
    paddingHorizontal: 24,
  },

  descriptionModalScrollContainer: {
    paddingTop: 20,
    paddingBottom: 24,
    flexGrow: 1,
  },

  descriptionModalText: {
    fontSize: 16,
    color: colors.text.primary,
    lineHeight: 24,
    textAlign: 'left',
    letterSpacing: 0.3,
  },

  // Date Time Picker Modal Styles
  dateTimePickerModal: {
    backgroundColor: colors.background.primary,
    borderRadius: 16,
    marginHorizontal: 20,
    marginVertical: 'auto',
    width: '90%',
    maxWidth: 350,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 12,
    elevation: 16,
  },

  dateTimePickerContent: {
    backgroundColor: colors.background.primary,
    borderRadius: 16,
    overflow: 'hidden',
  },

  dateTimePickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.08)',
    backgroundColor: colors.background.primary,
  },

  dateTimePickerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    flex: 1,
  },

  dateTimePickerCloseButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: 'rgba(0,0,0,0.05)',
    alignItems: 'center',
    justifyContent: 'center',
  },

  dateTimePicker: {
    backgroundColor: colors.background.primary,
    marginHorizontal: 10,
    marginVertical: 10,
  },

  // Time Picker Modal Styles
  timePickerModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },

  timePickerModalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: 'auto',
  },

  timePickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#f8f9fa',
  },

  timePickerHeaderButton: {
    padding: 8,
  },

  timePickerHeaderButtonText: {
    fontSize: 16,
    color: '#0077B6',
  },

  doneButton: {
    fontWeight: '600',
  },

  timePickerHeaderTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },

  timePicker: {
    height: 200,
    backgroundColor: '#fff',
  },
});