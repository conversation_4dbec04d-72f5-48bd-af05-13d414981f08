import { useState, useEffect, useCallback } from 'react';
import ConveyanceService from '../services/ConveyanceService';
import { EnhancedConveyanceResponse, ConveyanceRecord } from '../models/Conveyance';

interface UseConveyanceReturn {
  isLoading: boolean;
  error: Error | null;
  conveyanceData: ConveyanceRecord[] | null;
  fetchConveyanceHistory: (filterType: string, month?: string, year?: string) => Promise<void>;
  refreshData: () => Promise<void>;
  errorMessage: string | null;
}

/**
 * Hook for fetching and managing conveyance history data
 */
export default function useConveyance(): UseConveyanceReturn {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [conveyanceData, setConveyanceData] = useState<ConveyanceRecord[] | null>(null);
  const [currentParams, setCurrentParams] = useState<{ filterType: string; month?: string; year?: string } | null>(null);

  /**
   * Fetch conveyance history data
   */
  const fetchConveyanceHistory = useCallback(async (filterType: string, month?: string, year?: string) => {
    setIsLoading(true);
    setError(null);
    setErrorMessage(null);
    setCurrentParams({ filterType, month, year });
    
    try {
      console.log(`Fetching conveyance data with filter: ${filterType}...`);
      const response = await ConveyanceService.getConveyanceHistory(filterType, month, year);
      
      if (response.status) {
        console.log('Successfully fetched conveyance data.');
        setConveyanceData(response.data.conveyance_data);
      } else {
        const msg = response.message || 'Failed to fetch conveyance data';
        console.error(msg);
        setErrorMessage(msg);
        throw new Error(msg);
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error('An unknown error occurred');
      console.error('Error in fetchConveyanceHistory:', error.message);
      setError(error);
      setErrorMessage(error.message);
      setConveyanceData(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Refresh data using current parameters
   */
  const refreshData = useCallback(async () => {
    if (currentParams) {
      await fetchConveyanceHistory(
        currentParams.filterType, 
        currentParams.month, 
        currentParams.year
      );
    }
  }, [currentParams, fetchConveyanceHistory]);

  return {
    isLoading,
    error,
    conveyanceData,
    fetchConveyanceHistory,
    refreshData,
    errorMessage,
  };
} 