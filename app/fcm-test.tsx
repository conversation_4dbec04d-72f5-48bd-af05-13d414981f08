import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Button, SafeAreaView } from 'react-native';
import { Stack } from 'expo-router';
import messaging from '@react-native-firebase/messaging';
import { initializeFirebase } from '../utils/firebaseConfig';
import firebase from '@react-native-firebase/app';

export default function FCMTestScreen() {
  const [logs, setLogs] = useState<string[]>([]);
  const [token, setToken] = useState<string | null>(null);

  const addLog = (message: string) => {
    console.log(message);
    setLogs(prev => [...prev, message]);
  };

  const testFirebaseInit = async () => {
    try {
      addLog('Initializing Firebase...');
      const app = await initializeFirebase();
      addLog(`Firebase initialized: ${app.name}`);
    } catch (error) {
      addLog(`Firebase init error: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  const testPermissions = async () => {
    try {
      addLog('Requesting permissions...');
      const authStatus = await messaging().requestPermission();
      addLog(`Permission status: ${authStatus}`);
    } catch (error) {
      addLog(`Permission error: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  const testFCMToken = async () => {
    try {
      addLog('Requesting FCM token...');
      const fcmToken = await messaging().getToken();
      if (fcmToken) {
        addLog('FCM token obtained!');
        setToken(fcmToken);
      } else {
        addLog('Failed to get FCM token - empty result');
      }
    } catch (error) {
      addLog(`FCM token error: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  const runCompleteTest = async () => {
    try {
      // Clear previous logs
      setLogs([]);
      setToken(null);
      
      addLog('🔄 Starting complete Firebase test...');
      
      // Step 1: Initialize Firebase with explicit config
      addLog('Step 1: Initializing Firebase...');
      await initializeFirebase();
      addLog('✅ Firebase initialized');
      
      // Step 2: Check Firebase initialization
      if (firebase.apps.length > 0) {
        const app = firebase.app();
        addLog(`✅ Firebase app name: ${app.name}`);
        
        // Log options if available
        try {
          const options = app.options;
          if (options) {
            addLog('✅ Firebase options available');
            addLog(`Project ID: ${options.projectId || 'unavailable'}`);
            addLog(`API Key: ${options.apiKey ? 'present' : 'missing'}`);
          } else {
            addLog('❌ No Firebase options available');
          }
        } catch (err) {
          addLog('❌ Error accessing Firebase options');
        }
      } else {
        addLog('❌ No Firebase apps initialized');
      }
      
      // Step 3: Request permissions
      addLog('\nStep 2: Requesting permissions...');
      const authStatus = await messaging().requestPermission();
      addLog(`✅ Permission status: ${authStatus}`);
      
      // Step 4: Get FCM token
      addLog('\nStep 3: Requesting FCM token...');
      const fcmToken = await messaging().getToken();
      if (fcmToken) {
        addLog('✅ FCM token obtained successfully!');
        setToken(fcmToken);
      } else {
        addLog('❌ Failed to get FCM token - empty result');
      }
      
      addLog('\n🏁 Complete test finished');
    } catch (error) {
      addLog(`❌ Test error: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ title: 'FCM Token Test' }} />
      
      <View style={styles.buttonContainer}>
        <Button title="Init Firebase" onPress={testFirebaseInit} />
        <Button title="Request Permissions" onPress={testPermissions} />
        <Button title="Get FCM Token" onPress={testFCMToken} />
      </View>
      
      <Button 
        title="🔄 Run Complete Test" 
        onPress={runCompleteTest} 
        color="#4630EB"
      />
      
      {token ? (
        <View style={styles.tokenContainer}>
          <Text style={styles.tokenTitle}>FCM Token:</Text>
          <ScrollView style={styles.tokenScroll}>
            <Text selectable={true} style={styles.tokenText}>{token}</Text>
          </ScrollView>
        </View>
      ) : (
        <Text style={styles.noToken}>No FCM token available</Text>
      )}
      
      <Text style={styles.logsTitle}>Logs:</Text>
      <ScrollView style={styles.logs}>
        {logs.map((log, index) => (
          <Text key={index} style={styles.logText}>{log}</Text>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  tokenContainer: {
    backgroundColor: '#e6f7ff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
  },
  tokenTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  tokenScroll: {
    maxHeight: 100,
    backgroundColor: '#fff',
    padding: 8,
    borderRadius: 4,
  },
  tokenText: {
    fontFamily: 'monospace',
  },
  noToken: {
    textAlign: 'center',
    marginVertical: 20,
    color: '#999',
  },
  logsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  logs: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 8,
  },
  logText: {
    fontFamily: 'monospace',
    marginBottom: 4,
  },
}); 