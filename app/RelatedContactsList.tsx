import React, { useState, useCallback, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  ScrollView,
  Platform,
  Alert,
} from 'react-native';
import { Stack, useRouter, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { readUserScope, showSnackbar } from './ui/utils';

interface Contact {
  id: string;
  name: string;
  email: string;
  phone: string;
}

interface DealRelationsResponse {
  success: boolean;
  data: {
    deal_id: number;
    contact: Contact;
  };
  message: string;
}

interface AccountRelationsResponse {
  success: boolean;
  message: string;
  data: {
    contacts: {
      total_contacts: number;
      contacts: Contact[];
    };
    deals: {
      summary: {
        total_deals: number;
        total_value: number;
        open_deals: string;
        open_value: number;
        won_deals: string;
        won_value: number;
      };
      deals: Array<{
        id: number;
        name: string;
        amount: string;
        probability: string | null;
        close_date: string | null;
        stage: {
          id: string | null;
          name: string | null;
        };
        contact_name: string | null;
        created_at: string | null;
      }>;
    };
  };
}

const STATUS_BAR_CONFIG = {
  barStyle: "light-content" as const,
  backgroundColor: "#0F96BB",
  translucent: Platform.OS === 'android',
};

export default function RelatedContactsList() {
  const insets = useSafeAreaInsets();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [accountDetails, setAccountDetails] = useState<any>(null);

  const router = useRouter();
  const params = useLocalSearchParams();
  const dealId = params.dealId as string;
  const accountId = params.accountId as string;
  const source = dealId ? 'deal' : 'account';

  const fetchAccountDetails = async () => {
    if (source === 'account' && accountId) {
      try {
        console.log('Fetching account details for ID:', accountId);
        const response = await apiService.get(`${API_ENDPOINTS.SALES_ACCOUNTS}/${accountId}`);

        if (response.data.success) {
          setAccountDetails(response.data.data);
          console.log('Account details fetched:', response.data.data);

          // Temporary alert for debugging
          setTimeout(() => {
            Alert.alert(
              'Debug: Account Details Fetched',
              `Account Name: ${response.data.data?.name || response.data.data?.account_name || 'Unknown'}\nAccount ID: ${response.data.data?.id || 'Unknown'}`,
              [{ text: 'OK' }]
            );
          }, 500);
        } else {
          console.log('Failed to fetch account details:', response.data.message);
        }
      } catch (err) {
        console.error('Error fetching account details:', err);
      }
    }
  };

  const fetchRelatedContacts = async () => {
    try {
      console.log('Fetching related contacts...');
      setIsLoading(true);

      if (source === 'deal') {
        // Fetch deal related contacts
        console.log('🔍 Fetching deal contacts for deal ID:', dealId);
        const response = await apiService.get<DealRelationsResponse>(`${API_ENDPOINTS.DEALS}/${dealId}/relations`);
        console.log('📋 Deal relations API response:', JSON.stringify(response.data, null, 2));

        if(response.status){
          if (response.data.success) {
            const contactData = response.data.data.contact;
            console.log('📞 Contact data from relations:', contactData);

            if (contactData) {
              console.log('✅ Found contact in deal relations:', {
                id: contactData.id,
                name: contactData.name,
                email: contactData.email,
                phone: contactData.phone
              });
              setContacts([contactData]);
            } else {
              console.log('❌ No contact found in deal relations');
              setContacts([]);
            }
          } else {
            console.log('❌ Deal relations API failed:', response.data.message);
            setError(response.data.message);
          }
        } else {
          console.log('❌ Deal relations API request failed:', response.data.message);
          setError(response.data.message);
        }
      } else {
        // Fetch account related contacts
        const response = await apiService.get<AccountRelationsResponse>(`${API_ENDPOINTS.SALES_ACCOUNTS}/${accountId}/relations`);

        if (response.data.success) {
          setContacts(response.data.data.contacts.contacts);
        } else {
          setError(response.data.message);
        }
      }

      // Fetch account details if we're in account mode
      await fetchAccountDetails();
    } catch (err) {
      console.error('Error fetching related contacts:', err);
      setError('Failed to load related contacts');
    } finally {
      setIsLoading(false);
    }
  };

  // Use useFocusEffect instead of useEffect to refresh data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      console.log('Screen focused, fetching contacts...');
      fetchRelatedContacts();
    }, [dealId, accountId])
  );

  const handleBack = () => {
    router.back();
  };

  const handleAddNew = () => {
    // Navigate to add new contact page with the correct source parameter
    console.log('Navigate to add new contact');
    console.log('Current source:', source);
    console.log('Account ID:', accountId);
    console.log('Account Details:', accountDetails);

    const navigationParams: any = {
      dealId: dealId || undefined,
      accountId: accountId || undefined,
      source: source
    };

    // If we're coming from an account and have account details, pass them
    if (source === 'account' && accountId) {
      navigationParams.callFrom = 'accountDetail';
      navigationParams.id = accountId;

      if (accountDetails) {
        navigationParams.accountDetails = JSON.stringify(accountDetails);
        console.log('Passing account details to contact creation:', accountDetails);
      } else {
        console.log('No account details available, but passing account ID');
      }
    }

    console.log('Final navigation params:', navigationParams);

    router.push({
      pathname: '/contact' as any,
      params: navigationParams
    });
  };

  const handleAddExistingContact = () => {
    // Navigate to select existing contact page
    const navigationParams: any = {
      callFrom: 'RelatedContactsList'
    };

    if (source === 'account' && accountId) {
      navigationParams.account_id = accountId;
    } else if (source === 'deal' && dealId) {
      navigationParams.deal_id = dealId;
    }

    router.push({
      pathname: '/SelectContactForAccount' as any,
      params: navigationParams
    });
  };

  const navigateToContactDetails = (contactId: string) => {

 //   if(accessScope?.contact?.view === true){
    router.push({
      pathname: '/contactDetail' as any,
      params: {
        id: contactId,
        source: source,
        parentId: source === 'deal' ? dealId : accountId
      }
    });
  //}else{
    //showSnackbar('You do not have permission to view this contact');
  //}
  };

  // Calculate contact count
  const contactCount = contacts.length;
const [accessScope, setAccessScope] = useState<any>(null);

  useEffect(() => {
    const fetchAccessScope = async () => {
      const scope = await readUserScope();
      console.log("Access_Scope___meeting: " + JSON.stringify(scope));
      setAccessScope(scope);
    };
    fetchAccessScope();
  }, []);

  if (isLoading) {
    return (
      <SafeAreaView style={[styles.safeAreaContainer, { paddingTop: insets.top }]}>
        <StatusBar {...STATUS_BAR_CONFIG} />
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>

          <View style={styles.headerContent}>
            <Text style={styles.headerTitleText}>Related Contacts</Text>
          </View>


          <View style={styles.headerRight} />

        </View>
        <View style={styles.mainContainer}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#0F96BB" />
            <Text style={styles.loadingText}>Loading contacts...</Text>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.safeAreaContainer, { paddingTop: insets.top }]}>
      <StatusBar {...STATUS_BAR_CONFIG} />
      <Stack.Screen options={{ headerShown: false }} />

      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitleText}>Related Contacts</Text>
        </View>
         {accessScope?.contact?.create === true && (
        <TouchableOpacity onPress={handleAddExistingContact} style={styles.plusButton}>
          <Ionicons name="add" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          )}
      </View>

      <View style={styles.mainContainer}>
        {error ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
          </View>
        ) : contacts.length === 0 ? (
          <View style={styles.emptyContainer}>
            <View style={styles.emptyIconContainer}>
              <Ionicons name="people-outline" size={80} color="#C7C7CC" />
            </View>
            <Text style={styles.emptyTitle}>No contacts found</Text>
            <Text style={styles.emptySubtitle}>
              {source === 'deal'
                ? 'Use the + button above to add contacts to this deal'
                : 'Use the + button above to add contacts to this account'
              }
            </Text>
          </View>
        ) : (
          <ScrollView style={styles.container}>
            {/* Contact Section */}
            <View style={styles.sectionContainer}>
              {contacts.map((contact, index) => (
                <TouchableOpacity
                  key={contact.id || index}
                  style={[
                    styles.contactCard,
                    index < contacts.length - 1 && styles.contactCardBorder
                  ]}
                  onPress={() => navigateToContactDetails(contact.id)}
                >
                  <View style={styles.contactInitials}>
                    <Text style={styles.initialsText}>
                      {contact.name?contact.name.substring(0, 2).toUpperCase():''}
                    </Text>
                  </View>
                  <View style={styles.contactInfo}>
                    <Text style={styles.contactName}>{contact.name}</Text>
                    {contact.email && (
                      <Text style={styles.contactDetail}>{contact.email}</Text>
                    )}
                    {contact.phone && (
                      <Text style={styles.contactDetail}>{contact.phone}</Text>
                    )}
                  </View>
                  <Ionicons name="chevron-forward" size={24} color="#888" />
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeAreaContainer: {
    flex: 1,
    backgroundColor: '#0F96BB',
  },
  mainContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#0F96BB',
    height: Platform.OS === 'ios' ? 44 : 56,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
    zIndex: 1000,
    paddingHorizontal: Platform.OS === 'ios' ? 8 : 0,
  },
  backButton: {
    padding: 8,
    marginLeft: 8,
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: Platform.OS === 'ios' ? 4 : 16,
  },
  headerRight: {
    width: Platform.OS === 'ios' ? 44 : 56,
    height: Platform.OS === 'ios' ? 44 : 56,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Platform.OS === 'ios' ? -8 : 0,
  },
  plusButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  headerTitleText: {
    color: '#FFFFFF',
    fontSize: Platform.OS === 'ios' ? 17 : 20,
    fontWeight: Platform.OS === 'ios' ? '600' : '500',
    textAlign: 'center',
  },
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#d32f2f',
    textAlign: 'center',
    maxWidth: '80%',
  },
  sectionContainer: {
    backgroundColor: 'white',
    marginTop: 16,
    borderRadius: 8,
    overflow: 'hidden',
    marginHorizontal: 16,
  },
  contactCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
  },
  contactInitials: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#0F96BB',
    alignItems: 'center',
    justifyContent: 'center',
  },
  initialsText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  contactInfo: {
    flex: 1,
    marginLeft: 16,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  contactDetail: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    backgroundColor: 'white',
  },
  emptyIconContainer: {
    marginBottom: 24,
    opacity: 0.6,
  },
  emptyTitle: {
    fontSize: 22,
    fontWeight: '600',
    color: '#1C1C1E',
    textAlign: 'center',
    marginBottom: 12,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
    lineHeight: 22,
    maxWidth: 280,
    marginBottom: 32,
  },
  addContactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#0F96BB',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 25,
    ...Platform.select({
      ios: {
        shadowColor: '#0F96BB',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  addContactText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
    marginLeft: 8,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  contactCardBorder: {
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
});