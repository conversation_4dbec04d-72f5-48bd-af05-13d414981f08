import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
  Platform,
  TextInput,
  Modal,
  FlatList,
  KeyboardAvoidingView,
  Alert,
} from 'react-native';
import { Appbar } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { showSnackbar } from './ui/utils';
import DateTimePicker from '@react-native-community/datetimepicker';

interface QuoteFieldChoice {
  id: number;
  quotationfield_id: number;
  value?: string;
  custom_option: string | number;
  created_at?: string;
  updated_at?: string;
}

interface QuoteField {
  id: number;
  custom_field: number;
  field_label: string;
  field_name: string;
  field_type: string;
  required: string | null;
  quick_add: string | null;
  tool_tip: string;
  placeholder: string;
  choices: QuoteFieldChoice[];
  read_only: string | null;
  has_dependent: string | null;
  quotationgroups_id: number;
  active: number;
  order: number | null;
  lookup_type: string | null;
  lookup_column: string | null;
  created_at: string;
  updated_at: string;
  field_value: string | null;
}

interface QuoteData {
  id: number;
  quotation_name: string;
  quotation_no: string;
  fields: QuoteField[];
  stages: {
    id: number;
    stage_name: string;
    is_current: boolean;
  }[];
}

export default function DealsQuoteEdit() {
  const [loading, setLoading] = useState(true);
  const [fields, setFields] = useState<QuoteField[]>([]);
  const [formData, setFormData] = useState<{ [key: string]: any }>({});
  const [submitting, setSubmitting] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [currentDropdownField, setCurrentDropdownField] = useState<QuoteField | null>(null);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [currentDateField, setCurrentDateField] = useState<string>('');
  const [tempDate, setTempDate] = useState(new Date());
  const [searchQuery, setSearchQuery] = useState('');
  const [invalidFields, setInvalidFields] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);

  const router = useRouter();
  const params = useLocalSearchParams();
  const quoteId = params.quoteId as string;

  // Group fields by quotationgroups_id for better organization
  const groupedFields = React.useMemo(() => {
    const groups: { [key: number]: QuoteField[] } = {};
    fields.forEach(field => {
      if (!groups[field.quotationgroups_id]) {
        groups[field.quotationgroups_id] = [];
      }
      groups[field.quotationgroups_id].push(field);
    });
    return groups;
  }, [fields]);

  // First fetch the quote fields structure
  useEffect(() => {
    fetchQuoteFields();
  }, []);

  // Then fetch quote details when fields are loaded
  useEffect(() => {
    if (fields.length > 0) {
      fetchQuoteDetails();
    }
  }, [fields]);

  const fetchQuoteFields = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.get(`${API_ENDPOINTS.QUOTATION_FIELDS}`);
      
      if (response.data.status) {
        // Filter active fields and sort by order if available
        const activeFields = response.data.response
          .filter((field: QuoteField) => field.active === 1)
          .sort((a: QuoteField, b: QuoteField) => {
            if (a.order === null) return 1;
            if (b.order === null) return -1;
            return (a.order || 0) - (b.order || 0);
          });
        
        setFields(activeFields);
        
        // Initialize form data with field_value from fields response
        const initialFormData: { [key: string]: any } = {};
        activeFields.forEach((field: QuoteField) => {
          // Use field_value from the field definition if available
          initialFormData[field.field_name] = field.field_value;
          
          // For lookup/dropdown fields, initialize display value if choices are available
          if ((field.field_type.toLowerCase() === 'lookup' || field.field_type.toLowerCase() === 'dropdown') && field.choices?.length > 0) {
            const choice = field.choices.find(c => c.id.toString() === field.field_value);
            if (choice) {
              initialFormData[`${field.field_name}_display`] = field.field_type.toLowerCase() === 'lookup' 
                ? choice.value 
                : choice.custom_option;
            }
          }
        });
        setFormData(initialFormData);
      } else {
        throw new Error(response.data.message || 'Failed to fetch quote fields');
      }
    } catch (err) {
      console.error('Error fetching quote fields:', err);
      setError('Failed to load quote fields. Please try again.');
    }
  };

  const fetchQuoteDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.get(`${API_ENDPOINTS.QUOTATIONS}/${quoteId}`);
      
      if (response.data.status) {
        const quoteData = response.data.response;
        const updatedFormData = { ...formData };
        
        // First update from the main quote data
        fields.forEach((field: QuoteField) => {
          if (quoteData[field.field_name] !== undefined) {
            updatedFormData[field.field_name] = quoteData[field.field_name];
          }
        });

        // Then handle nested fields data if present
        if (quoteData.fields && Array.isArray(quoteData.fields)) {
          quoteData.fields.forEach((quoteField: { field_name: string; field_value: any }) => {
            if (quoteField.field_name) {
              updatedFormData[quoteField.field_name] = quoteField.field_value;
              
              // Update display values for lookup/dropdown fields
              const field = fields.find(f => f.field_name === quoteField.field_name);
              if (field && (field.field_type.toLowerCase() === 'lookup' || field.field_type.toLowerCase() === 'dropdown')) {
                const choice = field.choices?.find(c => c.id.toString() === quoteField.field_value);
                if (choice) {
                  updatedFormData[`${field.field_name}_display`] = field.field_type.toLowerCase() === 'lookup' 
                    ? choice.value 
                    : choice.custom_option;
                }
              }
            }
          });
        }

        setFormData(updatedFormData);
      } else {
        throw new Error(response.data.message || 'Failed to fetch quote details');
      }
    } catch (err) {
      console.error('Error fetching quote details:', err);
      setError('Failed to load quote details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatDateForDisplay = (dateString: string): string => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return date.toISOString().split('T')[0];
    } catch (e) {
      return dateString;
    }
  };

  const formatDateForApi = (dateString: string): string => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return date.toISOString();
    } catch (e) {
      return dateString;
    }
  };

  const handleInputChange = (fieldName: string, value: string) => {
    setFormData(prevData => {
      const newData = { ...prevData };
      
      // Find the field definition
      const field = fields.find(f => f.field_name === fieldName);
      if (field?.field_type.toLowerCase() === 'datepicker') {
        // Format date for API
        newData[fieldName] = formatDateForApi(value);
      } else {
        newData[fieldName] = value;
      }
      
      if (invalidFields.includes(fieldName) && value) {
        setInvalidFields(prev => prev.filter(field => field !== fieldName));
      }
      
      return newData;
    });
  };

  const handleBack = () => {
    router.back();
  };

  const handleDropdownSelect = (field: QuoteField, choice: QuoteFieldChoice) => {
    const value = field.field_type.toLowerCase() === 'lookup' ? choice.value : choice.custom_option;
    setFormData(prevData => ({
      ...prevData,
      [field.field_name]: choice.id.toString(),
      [`${field.field_name}_display`]: value
    }));
    setShowDropdown(false);
  };

  const onDateChange = (event: any, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      setShowDatePicker(false);
      if (event.type !== 'dismissed' && selectedDate) {
        handleInputChange(currentDateField, selectedDate.toISOString().split('T')[0]);
      }
    } else {
      if (selectedDate) {
        setTempDate(selectedDate);
      }
    }
  };

  const handleDateConfirm = () => {
    handleInputChange(currentDateField, tempDate.toISOString().split('T')[0]);
    setShowDatePicker(false);
  };

  const handleUpdateQuote = async () => {
    try {
      // Validate required fields
      const requiredFields = fields.filter(field => field.required === 'on');
      const missingFields = requiredFields.filter(field => {
        const value = formData[field.field_name];
        return !value || value.toString().trim() === '';
      });
      
      if (missingFields.length > 0) {
        setInvalidFields(missingFields.map(field => field.field_name));
        Alert.alert('Validation Error', `Please fill in the following required fields: ${missingFields.map(f => f.field_label).join(', ')}`);
        return;
      }

      setSubmitting(true);

      // Prepare data for submission - only include fields that have values or are required
      const requestData = fields
        .filter(field => {
          const value = formData[field.field_name];
          return field.required === 'on' || (value !== null && value !== undefined && value !== '');
        })
        .map(field => ({
          id: field.id,
          custom_field: field.custom_field,
          field_label: field.field_label,
          field_name: field.field_name,
          field_type: field.field_type,
          required: field.required,
          field_value: formData[field.field_name]?.toString() || null
        }));

      console.log('Updating quote with data:', JSON.stringify(requestData, null, 2));

      const response = await apiService.put(
        `${API_ENDPOINTS.QUOTATIONS}/${quoteId}`,
        requestData,
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
      
      if (response.data?.status) {
        showSnackbar(response.data.message || 'Quote updated successfully');
        router.back();
      } else {
        throw new Error(response.data?.message || 'Failed to update quote');
      }
    } catch (err) {
      console.error('Error updating quote:', err);
      Alert.alert(
        'Error',
        (err as Error)?.message || 'Failed to update quote. Please try again.'
      );
    } finally {
      setSubmitting(false);
    }
  };

  // Get display value for a field
  const getFieldDisplayValue = (field: QuoteField, value: string | number | null) => {
    if (!value) return '';
    
    if (field.field_type.toLowerCase() === 'lookup' || field.field_type.toLowerCase() === 'dropdown') {
      const selectedChoice = field.choices?.find(c => c.id.toString() === value.toString());
      if (selectedChoice) {
        return field.field_type.toLowerCase() === 'lookup' 
          ? selectedChoice.value 
          : selectedChoice.custom_option;
      }
      // If we have a display value stored, use that
      if (formData[`${field.field_name}_display`]) {
        return formData[`${field.field_name}_display`];
      }
    }
    return value.toString();
  };

  // Render form field based on field type
  const renderFormField = (field: QuoteField) => {
    if (field.active !== 1) return null;
    
    const fieldValue = formData[field.field_name];
    const isRequired = field.required === 'on';
    const isReadOnly = field.read_only === 'on';
    const showRedBorder = isRequired && invalidFields.includes(field.field_name);
    
    let displayValue = fieldValue;
    if (field.field_type.toLowerCase() === 'datepicker' && fieldValue) {
      displayValue = formatDateForDisplay(fieldValue);
    } else if ((field.field_type.toLowerCase() === 'lookup' || field.field_type.toLowerCase() === 'dropdown') && fieldValue) {
      displayValue = formData[`${field.field_name}_display`] || getFieldDisplayValue(field, fieldValue);
    }
    
    let fieldContent;
    
    switch (field.field_type.toLowerCase()) {
      case 'text field':
      case 'number':
        fieldContent = (
          <TextInput
            style={[
              styles.input,
              isReadOnly && styles.readOnlyInput,
              showRedBorder && styles.errorInput
            ]}
            placeholder={field.placeholder || `Enter ${field.field_label.toLowerCase()}`}
            placeholderTextColor="#999"
            value={displayValue?.toString() || ''}
            onChangeText={(text) => handleInputChange(field.field_name, text)}
            editable={!isReadOnly}
            keyboardType={field.field_type.toLowerCase() === 'number' ? 'numeric' : 'default'}
          />
        );
        break;
        
      case 'dropdown':
      case 'lookup':
        fieldContent = (
          <TouchableOpacity
            style={[
              styles.dropdownField,
              isReadOnly && styles.readOnlyInput,
              showRedBorder && styles.errorInput
            ]}
            onPress={() => {
              if (!isReadOnly) {
                setCurrentDropdownField(field);
                setShowDropdown(true);
                setSearchQuery('');
              }
            }}
            disabled={isReadOnly}
          >
            <Text style={[
              styles.dropdownText,
              !displayValue && styles.placeholderText
            ]}>
              {displayValue || field.placeholder || 'Click to select'}
            </Text>
            <Ionicons name="chevron-down" size={20} color="#888" />
          </TouchableOpacity>
        );
        break;
        
      case 'datepicker':
        fieldContent = (
          <TouchableOpacity
            style={[
              styles.dropdownField,
              isReadOnly && styles.readOnlyInput,
              showRedBorder && styles.errorInput
            ]}
            onPress={() => {
              if (!isReadOnly) {
                setCurrentDateField(field.field_name);
                setTempDate(fieldValue ? new Date(fieldValue) : new Date());
                setShowDatePicker(true);
              }
            }}
            disabled={isReadOnly}
          >
            <Text style={[
              styles.dropdownText,
              !displayValue && styles.placeholderText
            ]}>
              {displayValue || field.placeholder || 'Pick a date'}
            </Text>
            <Ionicons name="calendar-outline" size={20} color="#888" />
          </TouchableOpacity>
        );
        break;
        
      default:
        fieldContent = (
          <TextInput
            style={[
              styles.input,
              showRedBorder && styles.errorInput
            ]}
            placeholder={field.placeholder || `Enter ${field.field_label.toLowerCase()}`}
            placeholderTextColor="#999"
            value={displayValue?.toString() || ''}
            onChangeText={(text) => handleInputChange(field.field_name, text)}
          />
        );
    }
    
    return (
      <View key={field.id} style={styles.fieldContainer}>
        <View style={styles.labelContainer}>
          <Text style={styles.fieldLabel}>{field.field_label}</Text>
          {isRequired && <Text style={styles.requiredStar}>*</Text>}
        </View>
        {fieldContent}
      </View>
    );
  };

  // Render group of fields
  const renderFieldGroup = (groupId: number, groupFields: QuoteField[]) => {
    let groupName = 'Basic information';
    
    switch (groupId) {
      case 2:
        groupName = 'Basic information';
        break;
      case 3:
        groupName = 'Shipping information';
        break;
      case 4:
        groupName = 'Billing information';
        break;
      case 5:
        groupName = 'Additional information';
        break;
      default:
        groupName = `Group ${groupId}`;
    }
    
    return (
      <View key={groupId} style={styles.sectionContainer}>
        <Text style={styles.sectionHeader}>{groupName}</Text>
        {groupFields.map(field => renderFormField(field))}
      </View>
    );
  };

  // Render dropdown options
  const renderDropdownOptions = () => {
    if (!currentDropdownField) return null;
    
    const isLookup = currentDropdownField.field_type.toLowerCase() === 'lookup';
    const displayProperty = isLookup ? 'value' : 'custom_option';
    
    const filteredChoices = currentDropdownField.choices?.filter(choice => {
      if (!searchQuery) return true;
      const searchValue = choice[displayProperty];
      if (typeof searchValue === 'string') {
        return searchValue.toLowerCase().includes(searchQuery.toLowerCase());
      }
      return false;
    });
    
    return (
      <Modal
        visible={showDropdown}
        transparent={true}
        animationType="slide"
        onRequestClose={() => {
          setShowDropdown(false);
          setSearchQuery('');
        }}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.dropdownContainer}>
            <View style={styles.dropdownHeader}>
              <Text style={styles.dropdownTitle}>{currentDropdownField.field_label}</Text>
              <TouchableOpacity 
                onPress={() => {
                  setShowDropdown(false);
                  setSearchQuery('');
                }}
                hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
              >
                <Ionicons name="close" size={24} color="#000" />
              </TouchableOpacity>
            </View>
            
            <View style={styles.searchContainer}>
              <Ionicons name="search" size={20} color="#888" />
              <TextInput
                style={styles.searchInput}
                placeholder="Search..."
                value={searchQuery}
                onChangeText={setSearchQuery}
                autoFocus={true}
              />
            </View>
            
            <FlatList
              data={filteredChoices || []}
              keyExtractor={(item) => item.id.toString()}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.dropdownItem}
                  onPress={() => handleDropdownSelect(currentDropdownField, item)}
                >
                  <Text style={styles.dropdownItemText}>
                    {item[displayProperty]}
                  </Text>
                </TouchableOpacity>
              )}
              ListEmptyComponent={
                <View style={styles.emptyList}>
                  <Text style={styles.emptyListText}>No options found</Text>
                </View>
              }
            />
          </View>
        </SafeAreaView>
      </Modal>
    );
  };

  // Render date picker
  const renderDatePicker = () => {
    if (!showDatePicker) return null;

    return Platform.OS === 'ios' ? (
      <Modal
        visible={showDatePicker}
        transparent={true}
        animationType="slide"
      >
        <View style={styles.modalContainer}>
          <View style={styles.datePickerContainer}>
            <View style={styles.datePickerHeader}>
              <TouchableOpacity onPress={() => setShowDatePicker(false)}>
                <Text style={styles.datePickerButton}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={handleDateConfirm}>
                <Text style={[styles.datePickerButton, styles.datePickerDoneButton]}>Done</Text>
              </TouchableOpacity>
            </View>
            <DateTimePicker
              value={tempDate}
              mode="date"
              display="spinner"
              onChange={onDateChange}
            />
          </View>
        </View>
      </Modal>
    ) : (
      <DateTimePicker
        value={tempDate}
        mode="date"
        display="default"
        onChange={onDateChange}
      />
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#0F96BB" />
        <Appbar.Header style={styles.header}>
          <Appbar.BackAction onPress={handleBack} color="#FFFFFF" />
          <Appbar.Content 
            title="Edit Quote" 
            titleStyle={styles.headerTitleText}
          />
        </Appbar.Header>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0F96BB" />
          <Text style={styles.loadingText}>Loading quote details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#0F96BB" />
        <Appbar.Header style={styles.header}>
          <Appbar.BackAction onPress={handleBack} color="#FFFFFF" />
          <Appbar.Content 
            title="Edit Quote" 
            titleStyle={styles.headerTitleText}
          />
        </Appbar.Header>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={fetchQuoteFields}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#0F96BB" />
      <Stack.Screen options={{ headerShown: false }} />

      <Appbar.Header style={styles.header}>
        <Appbar.BackAction onPress={handleBack} color="#FFFFFF" />
        <Appbar.Content 
          title="Edit Quote" 
          titleStyle={styles.headerTitleText}
        />
        <Appbar.Action 
          icon="check"
          color="#FFFFFF"
          onPress={handleUpdateQuote}
          disabled={submitting}
        />
      </Appbar.Header>

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
      >
        <ScrollView 
          style={styles.content} 
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {fields.length > 0 && (
            <View style={styles.formContainer}>
              {/* Render grouped fields */}
              {Object.entries(groupedFields).map(([groupIdStr, groupFields]) => {
                const groupId = parseInt(groupIdStr, 10);
                return renderFieldGroup(groupId, groupFields);
              })}
            </View>
          )}
          {/* Add bottom padding for save button */}
          <View style={styles.bottomPadding} />
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Fixed Save Button */}
      <View style={styles.saveButtonContainer}>
        <TouchableOpacity 
          style={[styles.fixedSaveButton, submitting && styles.disabledButton]} 
          onPress={handleUpdateQuote}
          disabled={submitting}
        >
          {submitting ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <Text style={styles.fixedSaveButtonText}>Save Quote</Text>
          )}
        </TouchableOpacity>
      </View>

      {/* Render dropdowns and date pickers */}
      {renderDropdownOptions()}
      {renderDatePicker()}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#0F96BB',
    elevation: Platform.OS === 'android' ? 4 : 0,
    shadowColor: Platform.OS === 'ios' ? '#000000' : 'transparent',
    shadowOffset: { 
      width: 0, 
      height: Platform.OS === 'ios' ? 2 : 0 
    },
    shadowOpacity: Platform.OS === 'ios' ? 0.25 : 0,
    shadowRadius: Platform.OS === 'ios' ? 3.84 : 0,
    height: Platform.OS === 'android' ? 56 : 44,
    justifyContent: 'center',
    zIndex: 1000,
  },
  headerTitleText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginLeft: Platform.OS === 'ios' ? -16 : 0, // Adjust title position on iOS
  },
  content: {
    flex: 1,
  },
  formContainer: {
    padding: 16,
  },
  sectionContainer: {
    backgroundColor: 'white',
    borderRadius: 8,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionHeader: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0F96BB',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  fieldContainer: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  fieldLabel: {
    fontSize: 14,
    color: '#444',
  },
  requiredStar: {
    color: 'red',
    fontSize: 14,
    marginLeft: 2,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    backgroundColor: '#fff',
    color: '#333',
  },
  readOnlyInput: {
    backgroundColor: '#f5f5f5',
    borderColor: '#e0e0e0',
  },
  errorInput: {
    borderColor: 'red',
  },
  dropdownField: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    backgroundColor: '#fff',
  },
  dropdownText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  placeholderText: {
    color: '#999',
    fontSize: 16,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  dropdownContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    maxHeight: '80%',
  },
  dropdownHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  dropdownTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
  },
  dropdownItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  dropdownItemText: {
    fontSize: 16,
    color: '#333',
  },
  emptyList: {
    padding: 16,
    alignItems: 'center',
  },
  emptyListText: {
    fontSize: 16,
    color: '#666',
  },
  datePickerContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  datePickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  datePickerButton: {
    fontSize: 16,
    color: '#0F96BB',
  },
  datePickerDoneButton: {
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: '#0F96BB',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  scrollContent: {
    flexGrow: 1,
  },
  bottomPadding: {
    height: 100,
  },
  saveButtonContainer: {
    position: 'absolute',
    bottom: 15,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  fixedSaveButton: {
    backgroundColor: '#0F96BB',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Platform.OS === 'ios' ? 8 : 4,
  },
  fixedSaveButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '500',
  },
  disabledButton: {
    opacity: 0.7,
  },
}); 