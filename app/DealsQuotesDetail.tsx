import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  StatusBar,
  Platform,
  Dimensions,
  Modal,
  FlatList,
  Alert,
  ToastAndroid,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter, useLocalSearchParams, Href, useFocusEffect } from 'expo-router';
import { useColors } from '@/hooks/useThemeColor';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS, API_BASE_URL } from '../config/api';
import { showSnackbar } from './ui/utils';
import WebView from 'react-native-webview';
import Checkbox from 'expo-checkbox';

interface QuoteDetails {
  id: number;
  quotation_name: string;
  quotation_no: string;
  status: string;
  pdf_url: string;
  pdf_full_url: string;
  quotation_date: string;
  total_amount: string;
  total_price: string;
  currency: string;
  account_name: string;
  deal_name: string;
  deal_id: number;
  formatted_create_at: string;
  stages: QuoteStage[];
  is_synced: boolean;
  synced?: string;
}

interface QuoteStage {
  id: number;
  stage_name: string;
  is_current: boolean;
}

import { Linking } from 'react-native';
import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';

export default function DealsQuotesDetail() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [quoteDetails, setQuoteDetails] = useState<QuoteDetails | null>(null);
  const [quoteStage, setQuoteStage] = useState<string>('Draft');
  const [currentStage, setCurrentStage] = useState<string>('Draft');
  const [isSyncing, setIsSyncing] = useState(false);
  const [isChecked, setIsChecked] = useState(false);
  const [syncResponseMessage, setSyncResponseMessage] = useState<string | null>(null);
  const [isStageDropdownOpen, setIsStageDropdownOpen] = useState(false);
  const [stages, setStages] = useState<QuoteStage[]>([]);
  const [isUpdatingStage, setIsUpdatingStage] = useState(false);
  const [downloadLoading, setDownloadLoading] = useState(false);

  const router = useRouter();
  const params = useLocalSearchParams();
  const quoteId = params.quoteId as string;
  const colors = useColors();

  useEffect(() => {
    fetchQuoteDetails();
  }, [quoteId]);

  // Refresh data when screen comes into focus (e.g., returning from edit page)
  useFocusEffect(
    useCallback(() => {
      if (quoteId) {
        console.log('Screen focused, refreshing quote details...');
        fetchQuoteDetails();
      }
    }, [quoteId])
  );

  const fetchQuoteDetails = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiService.get(`${API_ENDPOINTS.QUOTATIONS}/${quoteId}`);
      
      if (response.data.status) {
        setQuoteDetails(response.data.response);
        setQuoteStage(response.data.response.status || 'Draft');
        
        // Check if synced equals "synced" to determine checkbox state
        const syncedValue = response.data.response.synced;
        const isSyncedFlag = response.data.response.is_synced;
        const isSynced = syncedValue === "synced" || isSyncedFlag === true || false;

        console.log('Sync status check:', {
          syncedValue,
          isSyncedFlag,
          finalIsSynced: isSynced
        });

        setIsChecked(isSynced);
        
        if (response.data.response.stages) {
          setStages(response.data.response.stages);
          
          // Find current stage
          const current = response.data.response.stages.find((stage: QuoteStage) => stage.is_current);
          if (current) {
            setCurrentStage(current.stage_name);
            // Also set as the selected stage in dropdown
            setQuoteStage(current.stage_name);
          }
        }
      } else {
        throw new Error(response.data.message || 'Failed to fetch quote details');
      }
    } catch (err) {
      console.error('Error fetching quote details:', err);
      setError('Failed to load quote details. Please try again.');
      showSnackbar('Failed to load quote details');
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  const testDownloadURL = async () => {
    if (!quoteDetails?.pdf_full_url) return;

    try {
      console.log('Testing PDF URL accessibility...');
      const response = await fetch(quoteDetails.pdf_full_url, { method: 'HEAD' });
      console.log('URL test response:', {
        status: response.status,
        headers: Object.fromEntries(response.headers.entries()),
        url: response.url
      });

      if (response.status === 200) {
        console.log('✅ PDF URL is accessible');
        return true;
      } else {
        console.log('❌ PDF URL returned status:', response.status);
        return false;
      }
    } catch (error) {
      console.log('❌ PDF URL test failed:', error);
      return false;
    }
  };

  const handleSync = async (checked: boolean) => {
    if (!quoteDetails?.deal_id) {
      const errorMessage = 'No associated deal found for this quote';
      showSnackbar(errorMessage);
      setSyncResponseMessage(errorMessage);
      return;
    }

    try {
      setIsSyncing(true);
      // Clear previous response message
      setSyncResponseMessage(null);
      // Optimistically update UI state
      setIsChecked(checked);

      if (checked) {
        // Sync quote to deal
        const response = await apiService.post('products/deal/sync-quote', {
          dealid: quoteDetails.deal_id,
          quoteid: parseInt(quoteId)
        });

        if (response.data.status) {
          const successMessage = response.data.message || 'Quote successfully synced to deal';
          showSnackbar(successMessage);
          setSyncResponseMessage(successMessage);

          // Clear message after 5 seconds
          setTimeout(() => {
            setSyncResponseMessage(null);
          }, 5000);

          // Update quote details with synced state
          if (quoteDetails) {
            setQuoteDetails({
              ...quoteDetails,
              is_synced: true,
              synced: "synced"
            });
          }
          // Ensure checkbox reflects the synced state
          setIsChecked(true);

          // Refresh quote details to get updated sync status from server
          setTimeout(() => {
            fetchQuoteDetails();
          }, 1000);
        } else {
          setIsChecked(!checked); // Revert on error
          const errorMessage = response.data.message || 'Failed to sync quote';
          setSyncResponseMessage(errorMessage);
          throw new Error(errorMessage);
        }
      } else {
        // Remove sync
        const response = await apiService.post('products/deal/remove-sync', {
          dealid: quoteDetails.deal_id
        });

        if (response.data.status) {
          const successMessage = response.data.message || 'Quote successfully unsynced from deal';
          showSnackbar(successMessage);
          setSyncResponseMessage(successMessage);

          // Clear message after 5 seconds
          setTimeout(() => {
            setSyncResponseMessage(null);
          }, 5000);

          // Update quote details with unsynced state
          if (quoteDetails) {
            setQuoteDetails({
              ...quoteDetails,
              is_synced: false,
              synced: "unsynced"
            });
          }
          // Ensure checkbox reflects the unsynced state
          setIsChecked(false);

          // Refresh quote details to get updated sync status from server
          setTimeout(() => {
            fetchQuoteDetails();
          }, 1000);
        } else {
          setIsChecked(!checked); // Revert on error
          const errorMessage = response.data.message || 'Failed to unsync quote';
          setSyncResponseMessage(errorMessage);
          throw new Error(errorMessage);
        }
      }
    } catch (error: any) {
      console.error('Error syncing quote:', error);
      const errorMessage = error.message || 'Failed to sync quote';
      showSnackbar(errorMessage);
      setSyncResponseMessage(errorMessage);
      setIsChecked(!checked); // Revert checkbox state on error
    } finally {
      setIsSyncing(false);
    }
  };

  const handleAddProducts = () => {
    router.push(`/DealsQuoteAddProduct?quoteId=${quoteId}` as Href);
  };

  const handleEditQuote = () => {
    // Navigate to edit quote page
    router.push(`/DealsQuoteEdit?quoteId=${quoteId}` as Href);
  };

  const handleDeleteQuote = () => {
    Alert.alert(
      'Delete Quote',
      'Are you sure you want to delete this quote? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: confirmDeleteQuote
        }
      ]
    );
  };

  const confirmDeleteQuote = async () => {
    try {
      setLoading(true);

      // Use the same delete API as the quotes list page
      const response = await apiService.post(`${API_ENDPOINTS.QUOTATIONS}/${quoteId}/delete`);

      if (response.data.status) {
        showSnackbar('Quote deleted successfully');

        // Show success alert and navigate back
        Alert.alert(
          'Quote Deleted',
          'The quote has been successfully deleted.',
          [
            {
              text: 'OK',
              onPress: () => router.back()
            }
          ]
        );
      } else {
        throw new Error(response.data.message || 'Failed to delete quote');
      }
    } catch (error: any) {
      console.error('Error deleting quote:', error);
      showSnackbar('Failed to delete quote. Please try again.');

      Alert.alert(
        'Delete Failed',
        error.message || 'Unable to delete the quote. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadPDF = async () => {
    if (!quoteDetails?.pdf_full_url) {
      Alert.alert('Error', 'No PDF URL available for download');
      return;
    }

    // Test URL accessibility first
    const isUrlAccessible = await testDownloadURL();
    if (!isUrlAccessible) {
      Alert.alert(
        'Download Error',
        'The PDF file is not accessible. Please check your internet connection or try again later.',
        [
          { text: 'OK' },
          {
            text: 'Open in Browser',
            onPress: () => Linking.openURL(quoteDetails.pdf_full_url)
          }
        ]
      );
      return;
    }

    // First, try simple browser download for better compatibility
    if (Platform.OS === 'android') {
      try {
        setDownloadLoading(true);
        showSnackbar('Opening download in browser...');

        // Use browser download which is more reliable on Android
        await Linking.openURL(quoteDetails.pdf_full_url);

        showSnackbar('PDF opened in browser. Use browser download option to save.');
        return;
      } catch (error) {
        console.log('Browser download failed, trying file system download');
      } finally {
        setDownloadLoading(false);
      }
    }

    try {
      setDownloadLoading(true);
      console.log('Starting PDF download for URL:', quoteDetails.pdf_full_url);

      // Show loading feedback
      showSnackbar('Starting PDF download...');

      // Generate filename with quote name and timestamp
      const timestamp = new Date().getTime();
      const quoteName = quoteDetails.quotation_name?.replace(/[^a-zA-Z0-9]/g, '_') || 'quote';
      const fileName = `${quoteName}_${timestamp}.pdf`;

      // Ensure the URL is properly encoded
      const encodedUrl = encodeURI(quoteDetails.pdf_full_url);
      console.log('Encoded URL:', encodedUrl);

      // Create the file path
      const fileUri = `${FileSystem.documentDirectory}${fileName}`;
      console.log('File path:', fileUri);

      // Download the file
      console.log('Starting download...');
      const downloadResult = await FileSystem.downloadAsync(encodedUrl, fileUri);
      console.log('Download result:', downloadResult);

      if (downloadResult.status === 200) {
        console.log('Download successful, file saved to:', downloadResult.uri);

        // Check if file exists
        const fileInfo = await FileSystem.getInfoAsync(downloadResult.uri);
        console.log('File info:', fileInfo);

        if (!fileInfo.exists) {
          throw new Error('Downloaded file not found');
        }

        // For Android, try to save to Downloads folder
        if (Platform.OS === 'android') {
          try {
            // Request media library permissions
            const { status } = await MediaLibrary.requestPermissionsAsync();
            if (status === 'granted') {
              const asset = await MediaLibrary.createAssetAsync(downloadResult.uri);
              console.log('Asset created:', asset);

              // Try to add to Downloads album
              try {
                let album = await MediaLibrary.getAlbumAsync('Downloads');
                if (!album) {
                  album = await MediaLibrary.createAlbumAsync('Downloads', asset, false);
                } else {
                  await MediaLibrary.addAssetsToAlbumAsync([asset], album, false);
                }
                console.log('Added to Downloads album');
              } catch (albumError) {
                console.log('Could not add to Downloads album:', albumError);
                // Continue anyway, file is still downloaded
              }
            } else {
              console.log('Media library permission not granted');
            }
          } catch (mediaError) {
            console.log('Media library error:', mediaError);
            // Continue anyway, file is still downloaded to app directory
          }
        }

        showSnackbar('PDF downloaded successfully!');

        Alert.alert(
          'Download Complete',
          `PDF has been saved as "${fileName}"\n\nFile size: ${(fileInfo.size / 1024 / 1024).toFixed(2)} MB`,
          [
            { text: 'OK' },
            {
              text: 'Open',
              onPress: () => {
                console.log('Opening file:', downloadResult.uri);
                Linking.openURL(downloadResult.uri).catch(err => {
                  console.error('Error opening file:', err);
                  // Fallback to opening original URL
                  Linking.openURL(quoteDetails.pdf_full_url);
                });
              }
            },
            {
              text: 'Share',
              onPress: () => {
                // Try to share the file
                if (Platform.OS === 'android') {
                  Linking.openURL(`content://com.android.externalstorage.documents/document/primary:Download/${fileName}`).catch(() => {
                    Linking.openURL(downloadResult.uri);
                  });
                } else {
                  Linking.openURL(downloadResult.uri);
                }
              }
            }
          ]
        );
      } else {
        throw new Error(`Download failed with status: ${downloadResult.status}`);
      }
    } catch (error: any) {
      console.error('Error downloading PDF:', error);
      showSnackbar('Failed to download PDF. Please try again.');

      // Provide detailed error information
      const errorMessage = error.message || 'Unknown error occurred';

      Alert.alert(
        'Download Failed',
        `Unable to download PDF.\n\nError: ${errorMessage}\n\nWould you like to open it in your browser instead?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Open in Browser',
            onPress: () => {
              console.log('Opening in browser:', quoteDetails.pdf_full_url);
              Linking.openURL(quoteDetails.pdf_full_url).catch(browserError => {
                console.error('Error opening in browser:', browserError);
                Alert.alert('Error', 'Unable to open PDF in browser');
              });
            }
          },
          {
            text: 'Retry Download',
            onPress: () => handleDownloadPDF()
          }
        ]
      );
    } finally {
      setDownloadLoading(false);
    }
  };

  const handleStageSelect = async (stageName: string) => {
    try {
      setIsUpdatingStage(true);
      setIsStageDropdownOpen(false);

      const response = await apiService.put(`${API_ENDPOINTS.QUOTATIONS}/stage/update`, {
        quoteid: quoteId,
        quotestage: stageName
      });

      if (response.data && response.data.status) {
        setQuoteStage(stageName);
        setCurrentStage(stageName);

        // Update the is_current flag in the stages array
        const updatedStages = stages.map(stage => ({
          ...stage,
          is_current: stage.stage_name === stageName
        }));
        setStages(updatedStages);

        showSnackbar('Quote stage updated successfully');

        // Add more visible alert for stage change
        if (Platform.OS === 'ios') {
          Alert.alert(
            'Stage Updated',
            `Quote stage has been updated to "${stageName}"`,
            [{ text: 'OK' }]
          );
        } else {
          // On Android, use Toast for better visibility alongside snackbar
          ToastAndroid.show(`Quote stage updated to "${stageName}"`, ToastAndroid.LONG);
        }
      } else {
        showSnackbar(response.data?.message || 'Failed to update quote stage');
        throw new Error(response.data?.message || 'Failed to update quote stage');
      }
    } catch (error) {
      console.error('Error updating quote stage:', error);
      showSnackbar('Failed to update quote stage. Please try again.');
    } finally {
      setIsUpdatingStage(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#0F96BB" />
        <View style={[styles.header, { backgroundColor: '#0F96BB' }]}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Quote Details</Text>
          <View style={styles.headerActions} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0F96BB" />
          <Text style={styles.loadingText}>Loading quote details...</Text>
        </View>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#0F96BB" />
        <View style={[styles.header, { backgroundColor: '#0F96BB' }]}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Quote Details</Text>
          <View style={styles.headerActions} />
        </View>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={fetchQuoteDetails}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={{ flex: 1 }}>
        <StatusBar barStyle="light-content" backgroundColor="#0F96BB" />
        <Stack.Screen options={{ headerShown: false }} />

        <View style={[styles.header, { backgroundColor: '#0F96BB' }]}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{quoteDetails?.quotation_name || 'Quote Details'}</Text>
          <View style={styles.headerActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={handleEditQuote}
              hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
            >
              <Ionicons name="pencil" size={24} color="white" />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={handleDeleteQuote}
              hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
            >
              <Ionicons name="trash-outline" size={24} color="white" />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.content}>
          {/* Quote Actions */}
          <View style={styles.actionsContainer}>
            {/* Quote Stage */}
            <View style={styles.actionItem}>
              <Text style={styles.actionLabel}>Quote stage</Text>
              <TouchableOpacity 
                style={styles.stageButton}
                onPress={() => !isUpdatingStage && setIsStageDropdownOpen(true)}
                disabled={isUpdatingStage}
              >
                <Text style={styles.stageText}>{currentStage}</Text>
                {isUpdatingStage ? (
                  <ActivityIndicator size="small" color="#0F96BB" style={styles.stageLoadingIndicator} />
                ) : (
                  <Ionicons name="chevron-down" size={20} color="#0F96BB" />
                )}
              </TouchableOpacity>
            </View>

            {/* Sync Option */}
            <View style={styles.actionItem}>
              <View style={styles.syncContainer}>
                <Checkbox
                  value={isChecked}
                  onValueChange={(checked) => !isSyncing && handleSync(checked)}
                  disabled={isSyncing}
                  color={isChecked ? '#0F96BB' : undefined}
                  style={[styles.checkbox, isSyncing && styles.checkboxDisabled]}
                />
                <View style={styles.actionTitleContainer}>
                  <Text style={styles.actionLabel}>Sync to Deal</Text>
                  <Text style={styles.actionDescription}>Sync this quote with the associated deal</Text>
                </View>
              </View>
              {isSyncing && (
                <ActivityIndicator size="small" color="#0F96BB" />
              )}

              {/* Response Message Display */}
              {syncResponseMessage && (
                <Text style={styles.simpleResponseMessage}>
                  {syncResponseMessage}
                </Text>
              )}
            </View>
          </View>

          {/* PDF Viewer */}
          <View style={styles.pdfContainer}>
            {Platform.OS === 'ios' ? (
              <View style={styles.iosPdfContainer}>
                <WebView
                  source={quoteDetails?.pdf_full_url ? { uri: quoteDetails.pdf_full_url } : undefined}
                  style={styles.pdfView}
                  renderLoading={() => (
                    <View style={styles.pdfLoadingContainer}>
                      <ActivityIndicator size="large" color="#0F96BB" />
                    </View>
                  )}
                  startInLoadingState={true}
                />
                {quoteDetails?.pdf_full_url && (
                  <TouchableOpacity
                    style={[styles.downloadButton, downloadLoading && styles.downloadButtonDisabled]}
                    onPress={handleDownloadPDF}
                    disabled={downloadLoading}
                  >
                    {downloadLoading ? (
                      <ActivityIndicator size="small" color="white" />
                    ) : (
                      <Ionicons name="download-outline" size={24} color="white" />
                    )}
                    <Text style={styles.downloadButtonText}>
                      {downloadLoading ? 'Downloading...' : 'Download PDF'}
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            ) : (
              <View style={styles.androidPdfContainer}>
                {quoteDetails?.pdf_full_url ? (
                  <>
                    <WebView
                      source={{ uri: `https://docs.google.com/gview?embedded=true&url=${quoteDetails.pdf_full_url}`, baseUrl: '' }}
                      style={styles.pdfView}
                      renderLoading={() => (
                        <View style={styles.pdfLoadingContainer}>
                          <ActivityIndicator size="large" color="#0F96BB" />
                        </View>
                      )}
                      startInLoadingState={true}
                      javaScriptEnabled={true}
                      domStorageEnabled={true}
                      allowFileAccess={true}
                      allowUniversalAccessFromFileURLs={true}
                      mixedContentMode="always"
                    />
                    <TouchableOpacity
                      style={[styles.downloadButton, downloadLoading && styles.downloadButtonDisabled]}
                      onPress={handleDownloadPDF}
                      disabled={downloadLoading}
                    >
                      {downloadLoading ? (
                        <ActivityIndicator size="small" color="white" />
                      ) : (
                        <Ionicons name="download-outline" size={24} color="white" />
                      )}
                      <Text style={styles.downloadButtonText}>
                        {downloadLoading ? 'Downloading...' : 'Download PDF'}
                      </Text>
                    </TouchableOpacity>
                  </>
                ) : (
                  <View style={styles.pdfLoadingContainer}>
                    <Text style={styles.errorText}>No PDF available</Text>
                  </View>
                )}
              </View>
            )}
          </View>

          {/* Bottom Button */}
          <View style={styles.bottomButtonContainer}>
            <TouchableOpacity 
              style={styles.addProductsButton}
              onPress={handleAddProducts}
            >
              <Ionicons name="cube-outline" size={24} color="white" />
              <Text style={styles.addProductsText}>Add/Edit Products</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Stage Dropdown Modal */}
        <Modal
          visible={isStageDropdownOpen}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setIsStageDropdownOpen(false)}
        >
          <TouchableOpacity 
            style={styles.modalOverlay}
            activeOpacity={1}
            onPress={() => setIsStageDropdownOpen(false)}
          >
            <View style={styles.stageDropdownContainer}>
              <View style={styles.stageDropdownHeader}>
                <View style={styles.dropdownTitleContainer}>
                  <Text style={styles.stageDropdownTitle}>Select Quote Stage</Text>
                  <View style={styles.currentStageContainer}>
                    <Text style={styles.currentStageLabel}>Current:</Text>
                    <Text style={styles.currentStageValue}>{currentStage}</Text>
                  </View>
                </View>
                <TouchableOpacity 
                  style={styles.closeButton}
                  onPress={() => setIsStageDropdownOpen(false)}
                >
                  <Ionicons name="close" size={24} color="#666" />
                </TouchableOpacity>
              </View>
              <FlatList
                data={stages}
                keyExtractor={(item) => item.id.toString()}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={[
                      styles.stageItem,
                      quoteStage === item.stage_name && styles.stageItemActive,
                    ]}
                    onPress={() => handleStageSelect(item.stage_name)}
                  >
                    <View style={styles.stageItemContent}>
                      <Text 
                        style={[
                          styles.stageItemText,
                          quoteStage === item.stage_name && styles.stageItemTextActive,
                        ]}
                      >
                        {item.stage_name}
                      </Text>
                    </View>
                    {quoteStage === item.stage_name && (
                      <Ionicons name="checkmark" size={20} color="#0F96BB" />
                    )}
                  </TouchableOpacity>
                )}
                ItemSeparatorComponent={() => <View style={styles.stageDivider} />}
              />
            </View>
          </TouchableOpacity>
        </Modal>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 47 : (StatusBar.currentHeight || 0),
    height: Platform.OS === 'ios' ? 90 : (56 + (StatusBar.currentHeight || 0)),
    paddingHorizontal: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
    textAlign: 'left',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  actionButton: {
    padding: 8,
    borderRadius: 20,
  },
  content: {
    flex: 1,
  },
  actionsContainer: {
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  actionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  actionTitleContainer: {
    flex: 1,
  },
  actionLabel: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
    marginBottom: 4,
  },
  actionDescription: {
    fontSize: 14,
    color: '#666',
  },
  stageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  stageText: {
    fontSize: 16,
    color: '#0F96BB',
    fontWeight: '600',
    marginRight: 4,
  },
  syncContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  checkbox: {
    marginRight: 12,
  },
  checkboxDisabled: {
    opacity: 0.6,
  },
  pdfContainer: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  pdfView: {
    flex: 1,
  },
  pdfLoadingContainer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#f8f9fa',
    alignItems: 'center',
    justifyContent: 'center',
  },
  bottomButtonContainer: {
    padding: 16,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  addProductsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#0F96BB',
    padding: 16,
    borderRadius: 8,
  },
  addProductsText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
    marginLeft: 8,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#dc3545',
    marginBottom: 12,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#0F96BB',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 16,
    color: 'white',
    fontWeight: '500',
  },
  detailText: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  stageDropdownContainer: {
    width: '80%',
    maxHeight: 300,
    backgroundColor: 'white',
    borderRadius: 8,
    overflow: 'hidden',
    padding: 8,
  },
  stageDropdownHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    marginBottom: 8,
  },
  dropdownTitleContainer: {
    flex: 1,
  },
  stageDropdownTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  currentStageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  currentStageLabel: {
    fontSize: 14,
    color: '#666',
    marginRight: 4,
  },
  currentStageValue: {
    fontSize: 14,
    fontWeight: '700',
    color: '#0F96BB',
  },
  closeButton: {
    padding: 4,
    borderRadius: 20,
  },
  stageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  stageItemActive: {
    backgroundColor: '#f0f8ff',
  },
  stageItemContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  stageItemText: {
    fontSize: 16,
    color: '#333',
  },
  stageItemTextActive: {
    color: '#0F96BB',
    fontWeight: '600',
  },
  stageDivider: {
    height: 1,
    backgroundColor: '#f0f0f0',
  },
  stageLoadingIndicator: {
    marginLeft: 8,
  },
  iosPdfContainer: {
    flex: 1,
  },
  androidPdfContainer: {
    flex: 1,
  },
  downloadButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    backgroundColor: '#0F96BB',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 24,
    elevation: 4,
  },
  downloadButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  downloadButtonDisabled: {
    opacity: 0.6,
  },
  simpleResponseMessage: {
    marginTop: 8,
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
});
