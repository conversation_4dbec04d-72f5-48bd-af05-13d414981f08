import React, { useState, useCallback } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,

  StatusBar,
  Modal,
  Alert,
  Platform,
  Image,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { SafeAreaView, Edge } from 'react-native-safe-area-context';
import { Appbar } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { useFocusEffect } from '@react-navigation/native';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { showSnackbar } from './ui/utils';
import { 
  DealsProductsListModel, 
  DealProduct, 
  DealValue,
  Quotation
} from './models/DealsProductsListModel';


const CustomHeader = ({ title, onBack, onAdd }: { 
  title: string; 
  onBack: () => void; 
  onAdd: () => void;
}) => (
  <View style={{
    backgroundColor: '#0F96BB',
    paddingTop: Platform.OS === 'ios' ? 50 : 40,
    paddingBottom: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  }}>
    <TouchableOpacity 
      onPress={() => {
        console.log("Custom header back pressed");
        onBack();
      }}
      style={{ padding: 8 }}
    >
      <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
    </TouchableOpacity>
    
    <Text style={{
      color: '#FFFFFF',
      fontSize: 18,
      fontWeight: '600',
    }}>
      {title}
    </Text>
    
    <TouchableOpacity 
      onPress={() => {
        console.log("Custom header add pressed");
        onAdd();
      }}
      style={{ padding: 8 }}
    >
      <Ionicons name="add" size={24} color="#FFFFFF" />
    </TouchableOpacity>
  </View>
);

export default function DealsProductList() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [products, setProducts] = useState<DealProduct[]>([]);
  const [dealValue, setDealValue] = useState<DealValue | null>(null);
  const [showSummary, setShowSummary] = useState(false);
  const [optionsVisible, setOptionsVisible] = useState(false);
  const [selectedProductId, setSelectedProductId] = useState<number | null>(null);

  const router = useRouter();
  const params = useLocalSearchParams();
  const dealId = params.dealId as string;


  const loadProducts = useCallback(async () => {
    let isSubscribed = true;

    if (!dealId) {
      console.error('No dealId found in navigation params');
      Alert.alert(
        'Navigation Error',
        'No deal ID was provided. Please go back and try again.',
        [{ text: 'Go Back', onPress: () => router.back() }]
      );
      return;
    }

    // Validate dealId format
    const dealIdNumber = parseInt(dealId);
    if (isNaN(dealIdNumber)) {
      console.error('Invalid dealId format:', dealId);
      Alert.alert(
        'Error',
        'Invalid deal ID format. Please go back and try again.',
        [{ text: 'Go Back', onPress: () => router.back() }]
      );
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      
      // Define endpoint and payload for the API request
      const endpoint = `${API_ENDPOINTS.DEALS_PRODUCTS_LIST}`;
      const payload = {
        dealid: dealIdNumber
      };
      
      console.log('Fetching products for deal ID:', dealId);
      console.log('Endpoint:', endpoint);
      console.log('Payload:', payload);
      
      // Make the POST API request
      const response = await apiService.post<DealsProductsListModel>(endpoint, payload);
     
      // Only update state if component is still mounted
      if (isSubscribed) {
        if (response.data.status) {
          console.log('Products found:', response.data.response.dealproducts?.length || 0);
          setProducts(response.data.response.dealproducts);
          setDealValue(response.data.response.dealvalue);
        } else {
          console.error('API returned status false:', response.data.message);
          setError(response.data.message || 'Failed to load products');
          // Show empty product list instead of error
          setProducts([]);
        }
      }else{
        setError(response.data.message || 'Failed to load products');
        setProducts([]);
      }
    } catch (error) {
      console.error('Error fetching deal products:', error);
      // Only update state if component is still mounted
      if (isSubscribed) {
        setError('Failed to load products. Please try again.');
        // Show empty product list instead of error
        setProducts([]);
      }
    } finally {
      // Only update state if component is still mounted
      if (isSubscribed) {
        setIsLoading(false);
      }
    }

    return () => {
      isSubscribed = false;
    };
  }, [dealId, router]);

  // Use useFocusEffect to refresh data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      console.log('Screen focused - refreshing data');
      loadProducts();

      return () => {
        // Cleanup when screen loses focus
        console.log('Screen unfocused - cleanup');
      };
    }, [loadProducts])
  );

  const handleBack = () => {
    console.log("Back button pressed, canGoBack:", router.canGoBack());
    if (router.canGoBack()) {
      router.back();
    } else {
      console.log("No previous page, navigating to home");
      router.push('/(tabs)/' as any);
    }
  };

  const handleAddProduct = () => {
    console.log('kjasdgkd:', dealId);
    if (!dealId) {
      showSnackbar('Deal ID is required');
      return;
    }
    console.log("handleAddProduct else");
    // Navigate to DealsAddProduct screen with dealId
    router.push({
      pathname: '/DealsAddProduct' as any,
      params: { 
        dealId,
        mode: 'add',
        callFrom: 'dealDetail'
      }
    });
  };

  const handleShowOptions = (productId: number) => {
    setSelectedProductId(productId);
    setOptionsVisible(true);
  };

  const handleOptionPress = (option: string) => {
    setOptionsVisible(false);
    
    if (!selectedProductId) return;
    
    switch (option) {
      case 'Edit':
        console.log('Edit product:', selectedProductId);
        // Find the selected product from the products array
        const productToEdit = products.find(p => p.id === selectedProductId);
        if (productToEdit) {
          // Navigate to DealsAddProduct with edit mode and product ID only
          router.push({
            pathname: 'DealsAddProduct' as any,
            params: { 
              dealId,
              editMode: 'true',
              productId: selectedProductId.toString()
            }
          });
        } else {
          Alert.alert('Error', 'Product not found. Please try again.');
        }
        break;
      case 'Delete':
        Alert.alert(
          'Delete Product',
          'Are you sure you want to remove this product from the deal?',
          [
            { text: 'Cancel', style: 'cancel' },
            { 
              text: 'Delete', 
              style: 'destructive',
              onPress: () => handleDeleteProduct(selectedProductId)
            }
          ]
        );
        break;
      default:
        break;
    }
    
    // Reset selected product
    setSelectedProductId(null);
  };

  const handleDeleteProduct = async (productId: number) => {
    try {
      console.log('Deleting product from deal:', productId);
      
      // Use the correct endpoint for deleting product from deal
      const endpoint = `${API_ENDPOINTS.DEALS_PRODUCTS_REMOVE}`;
      
      // Make delete request with payload
      const payload = {
        dealid: dealId,
        productid: productId.toString()
      };
      
      const response = await apiService.post(endpoint, payload);
      
      if (response.data.status) {
        // Remove product from local state
        setProducts(prevProducts => prevProducts.filter(p => p.id !== productId));
        showSnackbar(response.data.message || 'Product removed successfully');
        
        // Refetch deal value data to get updated totals
        fetchDealProducts();
      } else {
        throw new Error(response.data.message || 'Failed to remove product');
      }
    } catch (error) {
      console.error('Error removing product:', error);
      Alert.alert(
        'Error',
        error instanceof Error ? error.message : 'Failed to remove product. Please try again.'
      );
    }
  };

  const toggleSummary = () => {
    setShowSummary(!showSummary);
  };

  const renderEmptyState = () => {
    return (
      <View style={styles.emptyContainer}>
        <View style={styles.emptyIconContainer}>
          <Ionicons name="cube-outline" size={80} color="#0F96BB" />
        </View>
        <Text style={styles.emptyTitle}>No Products Added</Text>
        <Text style={styles.emptyText}>
          Add products to track what's included in this deal
        </Text>
        <TouchableOpacity 
          style={styles.addButton} 
          onPress={handleAddProduct}
        >
          <Text style={styles.addButtonText}>Add Product</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderProductItem = ({ item }: { item: DealProduct }) => {
    // Format currency values for display
    const unitPrice = parseFloat(item.unitprice);
    const totalPrice = parseFloat(item.totalprice);
    const setupFee = item.setupfree ? parseFloat(item.setupfree) : 0;
    const discount = item.discount ? parseFloat(item.discount) : 0;
    
    const handleProductPress = () => {
      router.push({
        pathname: 'ui/product/ProductDetailsPage',
        params: { productId: item.product_id.toString() }
      } as any);
    };
    
    return (
      <TouchableOpacity onPress={handleProductPress} activeOpacity={0.8}>
        <View style={styles.productCard}>
          <View style={styles.productHeader}>
            <Text style={styles.productName}>{item.product_name}</Text>
            <TouchableOpacity 
              onPress={() => handleShowOptions(item.id)}
              hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
            >
              <Ionicons name="ellipsis-vertical" size={20} color="#777" />
            </TouchableOpacity>
          </View>
          
          <View style={styles.priceRow}>
            <Text style={styles.priceLabel}>Unit Price:</Text>
            <Text style={styles.priceValue}>₹ {unitPrice.toFixed(2)}</Text>
          </View>
          
          <View style={styles.priceRow}>
            <Text style={styles.priceLabel}>Quantity:</Text>
            <Text style={styles.priceValue}>{item.quantity}</Text>
          </View>

          {/* Always show discount information */}
          <View style={styles.priceRow}>
            <Text style={styles.priceLabel}>
              Discount ({item.discount_type === 'percentage' ? '%' : 'Amount'}):
            </Text>
            <Text style={[
              styles.priceValue,
              discount > 0 && styles.discountValue
            ]}>
              {item.discount_type === 'percentage' 
                ? `${discount}%` 
                : `₹ ${discount.toFixed(2)}`
              }
            </Text>
          </View>
          
          {setupFee > 0 && (
            <View style={styles.priceRow}>
              <Text style={styles.priceLabel}>Setup Fee:</Text>
              <Text style={styles.priceValue}>₹ {setupFee.toFixed(2)}</Text>
            </View>
          )}
          
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Total:</Text>
            <Text style={styles.totalValue}>₹ {totalPrice.toFixed(2)}</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderSummary = () => {
    if (!dealValue) return null;
    
    // Parse values for display
    const subtotal = parseFloat(dealValue.subtotal);
    const salesTax = parseFloat(dealValue.salestax);
    const salesTaxAmount = parseFloat(dealValue.salestaxamount);
    const totalValue = parseFloat(dealValue.dealvalue);
    
    // Use the correct currency symbol
    const currencySymbol = '₹ ';
    
    return (
      <View style={styles.summaryContainer}>
        {showSummary && (
          <View style={styles.summaryContent}>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Sub total</Text>
              <Text style={styles.summaryValue}>{currencySymbol}{subtotal.toFixed(2)}</Text>
            </View>
            
            {salesTax > 0 && (
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Sales Tax ({salesTax}%)</Text>
                <Text style={styles.summaryValue}>{currencySymbol}{salesTaxAmount.toFixed(2)}</Text>
              </View>
            )}
            
            {dealValue.quotations && dealValue.quotations.length > 0 && (
              <View style={styles.quotationSection}>
                <Text style={styles.quotationLabel}>Linked Quotations:</Text>
                {dealValue.quotations.map((quotation: Quotation) => (
                  <Text key={quotation.id} style={styles.quotationName}>
                    • {quotation.quotation_name}
                  </Text>
                ))}
              </View>
            )}
          </View>
        )}
        
        <TouchableOpacity 
          style={styles.totalRow}
          onPress={toggleSummary}
          activeOpacity={0.7}
        >
          <Text style={styles.totalDealLabel}>Total Deal value</Text>
          <View style={styles.totalValueContainer}>
            <Text style={styles.totalDealValue}>{currencySymbol}{totalValue.toFixed(2)}</Text>
            <Ionicons 
              name={showSummary ? "chevron-down" : "chevron-up"} 
              size={24} 
              color="#333" 
              style={styles.chevronIcon}
            />
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  const fetchDealProducts = () => {
    // Call loadProducts directly for manual refresh
    loadProducts();
  };

  if (isLoading) {
    return (
       <SafeAreaView style={styles.container} edges={['top'] as Edge[]}>
      <StatusBar barStyle="light-content" backgroundColor='#0F96BB' />
        <View style={[styles.header, { backgroundColor: '#0F96BB' }]}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Ionicons name="arrow-back" size={24} color={ '#FFFFFF'} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Deal Products</Text>
        <TouchableOpacity
                style={styles.headerButton}
                onPress={handleAddProduct}

              >
                <Ionicons name="add" size={24} color="#FFFFFF" />

              </TouchableOpacity>
      </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0F96BB" />
          <Text style={styles.loadingText}>Loading products...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top'] as Edge[]}>
      <StatusBar barStyle="light-content" backgroundColor='#0F96BB' />
      <Stack.Screen 
        options={{ 
          headerShown: false // Hide the default header
        }} 
      />
       <View style={[styles.header, { backgroundColor: '#0F96BB' }]}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Ionicons name="arrow-back" size={24} color={ '#FFFFFF'} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Deal Products</Text>
        <TouchableOpacity
                style={styles.headerButton}
                onPress={handleAddProduct}

              >
                <Ionicons name="add" size={24} color="#FFFFFF" />

              </TouchableOpacity>
      </View>
    
      
      <View style={styles.container}>
        {products.length === 0 ? (
          renderEmptyState()
        ) : (
          <>
            <FlatList
              data={products}
              renderItem={renderProductItem}
              keyExtractor={item => item.id.toString()}
              contentContainerStyle={[
                styles.listContent,
                showSummary ? styles.listContentWithExpandedSummary : styles.listContentWithCollapsedSummary
              ]}
            />
            {renderSummary()}
          </>
        )}
      </View>
      
      {/* Options Modal */}
      <Modal
        visible={optionsVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setOptionsVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setOptionsVisible(false)}
        >
          <View style={styles.optionsContainer}>
            <View style={styles.optionsHeader}>
              <Text style={styles.optionsTitle}>Options</Text>
              <TouchableOpacity 
                onPress={() => setOptionsVisible(false)}
                hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
              >
                <Ionicons name="close" size={24} color="#555" />
              </TouchableOpacity>
            </View>
            {[
              { name: 'Edit', icon: 'pencil' },
              { name: 'Delete', icon: 'trash-outline' }
            ].map((option) => (
              <TouchableOpacity
                key={option.name}
                style={styles.optionItem}
                onPress={() => handleOptionPress(option.name)}
              >
                <Ionicons name={option.icon as any} size={22} color="#555" style={styles.optionIcon} />
                <Text style={styles.optionText}>{option.name}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>
    </SafeAreaView>
  );
}

// Define styles type
type Styles = {
  container: ViewStyle;
  header: ViewStyle;
  backButton: ViewStyle;
  headerTitle: TextStyle;
  headerActions: ViewStyle;
  headerButton: ViewStyle;
  listContent: ViewStyle;
  loadingContainer: ViewStyle;
  loadingText: TextStyle;
  emptyContainer: ViewStyle;
  emptyIconContainer: ViewStyle;
  emptyTitle: TextStyle;
  emptyText: TextStyle;
  addButton: ViewStyle;
  addButtonText: TextStyle;
  productCard: ViewStyle;
  productHeader: ViewStyle;
  productName: TextStyle;
  skuText: TextStyle;
  descriptionText: TextStyle;
  priceRow: ViewStyle;
  priceLabel: TextStyle;
  priceValue: TextStyle;
  totalRow: ViewStyle;
  totalLabel: TextStyle;
  totalValue: TextStyle;
  summaryContainer: ViewStyle;
  summaryHeader: ViewStyle;
  summaryHeaderExpanded: ViewStyle;
  summaryHeaderLeft: ViewStyle;
  summaryTitle: TextStyle;
  summaryContent: ViewStyle;
  summaryRow: ViewStyle;
  summaryLabel: TextStyle;
  summaryValue: TextStyle;
  totalSummaryValue: TextStyle;
  quotationSection: ViewStyle;
  quotationLabel: TextStyle;
  quotationName: TextStyle;
  modalOverlay: ViewStyle;
  optionsContainer: ViewStyle;
  optionItem: ViewStyle;
  optionText: TextStyle;
  optionIcon: TextStyle;
  listContentWithExpandedSummary: ViewStyle;
  listContentWithCollapsedSummary: ViewStyle;
  optionsHeader: ViewStyle;
  optionsTitle: TextStyle;
  discountValue: TextStyle;
  totalDealLabel: TextStyle;
  totalValueContainer: ViewStyle;
  totalDealValue: TextStyle;
  chevronIcon: TextStyle;
};

const styles = StyleSheet.create<Styles>({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    borderTopLeftRadius: Platform.OS === 'ios' ? 15 : 0,
    borderTopRightRadius: Platform.OS === 'ios' ? 15 : 0,
  },
   header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: '#2E74BB',
    zIndex: 10,
    elevation: 4,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color:  '#FFFFFF',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    padding: 4,
  },
  listContent: {
    padding: 16,
    paddingBottom: 16, // Reduced bottom padding since summary is now fixed
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    borderTopLeftRadius: Platform.OS === 'ios' ? 15 : 0,
    borderTopRightRadius: Platform.OS === 'ios' ? 15 : 0,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#888',
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    backgroundColor: 'white',
  },
  emptyIconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#f0f8ff',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  emptyText: {
    fontSize: 16,
    color: '#777',
    textAlign: 'center',
    marginBottom: 30,
  },
  addButton: {
    backgroundColor: '#0F96BB',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 6,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  productCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  productName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  skuText: {
    fontSize: 14,
    color: '#777',
    marginBottom: 8,
  },
  descriptionText: {
    fontSize: 14,
    color: '#555',
    marginBottom: 12,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 6,
  },
  priceLabel: {
    fontSize: 14,
    color: '#777',
  },
  priceValue: {
    fontSize: 14,
    color: '#333',
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  totalValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0F96BB',
  },
  summaryContainer: {
    backgroundColor: '#f5f5f5',
    overflow: 'hidden',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
  },
  summaryHeaderExpanded: {
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  summaryHeaderLeft: {
    flex: 1,
  },
  summaryTitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 4,
  },
  summaryContent: {
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  summaryLabel: {
    fontSize: 16,
    color: '#333',
    fontWeight: '400',
  },
  summaryValue: {
    fontSize: 16,
    color: '#333',
    fontWeight: '400',
  },
  totalSummaryValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#0F96BB',
  },
  quotationSection: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  quotationLabel: {
    fontSize: 15,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  quotationName: {
    fontSize: 14,
    color: '#777',
    marginBottom: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'flex-end',
  },
  optionsContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    overflow: 'hidden',
  },
  optionsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  optionsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  optionText: {
    fontSize: 16,
    color: '#333',
  },
  optionIcon: {
    marginRight: 16,
    width: 24,
    textAlign: 'center',
  },
  listContentWithExpandedSummary: {
    paddingBottom: 160,
  },
  listContentWithCollapsedSummary: {
    paddingBottom: 70,
  },
  discountValue: {
    color: '#2E7D32', // Green color for discount values
    fontWeight: '500',
  },
  totalDealLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  totalValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  totalDealValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginRight: 4,
  },
  chevronIcon: {
    marginLeft: 8,
  },
} as const); 