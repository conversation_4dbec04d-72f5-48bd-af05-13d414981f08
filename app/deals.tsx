import React, { useState, useRef, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  SafeAreaView,
  Dimensions,
  Modal,
  Animated,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';

// Static data for saved filters
const SAVED_FILTERS = [
  // Default Views
  { id: '1', name: 'Opportunities', isDefault: true, category: 'Default Views' },
  { id: '2', name: 'My Opportunities', category: 'Default Views' },
  { id: '3', name: 'Recently Viewed Opportunities', category: 'Default Views' },

  // Opportunity Status
  { id: '4', name: 'New Opportunities', category: 'Opportunity Status' },
  { id: '5', name: 'In Progress Opportunities', category: 'Opportunity Status' },
  { id: '6', name: 'Won Opportunities', category: 'Opportunity Status' },
  { id: '7', name: 'Lost Opportunities', category: 'Opportunity Status' },

  // Opportunity Value
  { id: '8', name: 'High Value Opportunities (>$10k)', category: 'Opportunity Value' },
  { id: '9', name: 'Medium Value Opportunities ($5k-$10k)', category: 'Opportunity Value' },
  { id: '10', name: 'Low Value Opportunities (<$5k)', category: 'Opportunity Value' },

  // Time Based
  { id: '11', name: 'Created This Week', category: 'Time Based' },
  { id: '12', name: 'Created This Month', category: 'Time Based' },
  { id: '13', name: 'Closing This Month', category: 'Time Based' },
  { id: '14', name: 'Closing Next Month', category: 'Time Based' },
];

// Mock data for opportunities
const OPPORTUNITIES_DATA = [
  {
    id: '60',
    name: 'Kambaa test',
    account: 'Sample Account',
    status: 'New',
    amount: '1000',
  },
  {
    id: '61',
    name: 'Second Opportunity',
    account: 'Another Account',
    status: 'In Progress',
    amount: '2500',
  }
];

export default function OpportunitiesScreen() {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [drawerSearchQuery, setDrawerSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState(SAVED_FILTERS[0]);
  const [filteredSavedFilters, setFilteredSavedFilters] = useState(SAVED_FILTERS);
  const slideAnim = useRef(new Animated.Value(-Dimensions.get('window').width * 0.8)).current;

  const router = useRouter();
  const params = useLocalSearchParams();
  const contactId = params.contactId as string;

  // Filter saved filters when search query changes
  useEffect(() => {
    if (drawerSearchQuery.trim() === '') {
      setFilteredSavedFilters(SAVED_FILTERS);
    } else {
      const query = drawerSearchQuery.toLowerCase();
      const filtered = SAVED_FILTERS.filter(filter => 
        filter.name.toLowerCase().includes(query)
      );
      setFilteredSavedFilters(filtered);
    }
  }, [drawerSearchQuery]);

  // Drawer animation
  useEffect(() => {
    if (isDrawerOpen) {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: -Dimensions.get('window').width * 0.8,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [isDrawerOpen]);

  const handleCloseDrawer = () => {
    Animated.timing(slideAnim, {
      toValue: -Dimensions.get('window').width * 0.8,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setIsDrawerOpen(false);
    });
  };

  const renderSavedFilterItem = ({ item }: { item: typeof SAVED_FILTERS[0] }) => (
    <TouchableOpacity
      style={[
        styles.savedFilterItem,
        selectedFilter.id === item.id && styles.selectedSavedFilterItem
      ]}
      onPress={() => {
        setSelectedFilter(item);
        handleCloseDrawer();
      }}
    >
      <Text style={[
        styles.savedFilterText,
        selectedFilter.id === item.id && styles.selectedSavedFilterText
      ]}>
        {item.name}
      </Text>
      {selectedFilter.id === item.id && (
        <Ionicons name="checkmark" size={20} color="#0F96BB" />
      )}
    </TouchableOpacity>
  );

  const renderCategoryHeader = (category: string) => (
    <View style={styles.categoryHeader}>
      <Text style={styles.categoryTitle}>{category}</Text>
    </View>
  );

  // Group filters by category
  const groupedFilters = React.useMemo(() => {
    const grouped = filteredSavedFilters.reduce((acc, filter) => {
      const category = filter.category || 'Other';
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(filter);
      return acc;
    }, {} as Record<string, typeof SAVED_FILTERS>);

    return Object.entries(grouped);
  }, [filteredSavedFilters]);

  const handleBack = () => {
    router.back();
  };

  const handleAddNew = () => {
    // Handle add new opportunity
    console.log('Add new opportunity for contact: ', contactId);

    // Navigate to create opportunity page
    try {
      console.log('Navigating to create opportunity page');
      router.push('/CreateDeals');
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };

  const renderOpportunityItem = ({ item }: { item: typeof OPPORTUNITIES_DATA[0] }) => (
    <TouchableOpacity
      style={styles.opportunityItemContainer}
      onPress={() => handleOpportunityPress(item)}
      activeOpacity={0.7}
      accessibilityRole="button"
      accessibilityLabel={`View details for opportunity ${item.name}`}
    >
      <View style={styles.opportunityItemMainContent}>
        <View>
          <Text style={styles.opportunityName}>{item.name}</Text>
          <Text style={styles.opportunityAccount}>{item.account}</Text>
          <Text style={styles.opportunityStatus}>{item.status}</Text>
        </View>
        <View style={styles.opportunityAmountContainer}>
          <Text style={styles.opportunityAmount}>$ {item.amount}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const handleOpportunityPress = (opportunity: typeof OPPORTUNITIES_DATA[0]) => {
    // Log the opportunity being tapped
    console.log('Opportunity tapped:', opportunity);

    // Check if opportunity ID is valid
    if (!opportunity.id) {
      console.error('Opportunity is missing ID');
      return;
    }

    // Navigate to opportunity details page with dealId (keeping same route for backend compatibility)
    try {
      console.log('Navigating to opportunity details with ID:', opportunity.id);

      // Use direct URL approach - this consistently works
      router.push(`/dealDetails?dealId=${opportunity.id}`);
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      {/* Custom Header */}
      <View style={[styles.header, { backgroundColor: '#0F96BB' }]}>
        <View style={styles.headerLeft}>
          <TouchableOpacity 
            style={styles.headerTitleContainer}
            onPress={() => setIsDrawerOpen(true)}
          >
            <Text style={[styles.headerTitle, { color: '#FFFFFF' }]}>Opportunities</Text>
            <Ionicons name="chevron-down" size={20} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
        <TouchableOpacity onPress={handleAddNew} style={styles.addButton}>
          <Text style={[styles.addButtonText, { color: '#FFFFFF' }]}>Add new</Text>
        </TouchableOpacity>
      </View>

      {/* Side Navigation Drawer */}
      <Modal
        visible={isDrawerOpen}
        transparent={true}
        onRequestClose={handleCloseDrawer}
      >
        <TouchableOpacity
          style={styles.drawerOverlay}
          activeOpacity={1}
          onPress={handleCloseDrawer}
        >
          <Animated.View 
            style={[
              styles.drawer,
              {
                transform: [{ translateX: slideAnim }]
              }
            ]}
          >
            <TouchableOpacity activeOpacity={1} onPress={e => e.stopPropagation()}>
              <View style={styles.drawerContent}>
                <View style={[styles.drawerHeader, { backgroundColor: '#0F96BB' }]}>
                  <Text style={[styles.drawerTitle, { color: '#FFFFFF' }]}>Opportunities</Text>
                  <TouchableOpacity onPress={handleCloseDrawer}>
                    <Ionicons name="close" size={24} color="#FFFFFF" />
                  </TouchableOpacity>
                </View>

                <View style={styles.drawerSearchContainer}>
                  <Ionicons name="search" size={20} color="#666" style={styles.drawerSearchIcon} />
                  <TextInput
                    style={styles.drawerSearchInput}
                    placeholder="Search saved filters..."
                    value={drawerSearchQuery}
                    onChangeText={setDrawerSearchQuery}
                    placeholderTextColor="#666"
                  />
                </View>

                <FlatList
                  data={groupedFilters}
                  renderItem={({ item: [category, filters] }) => (
                    <View>
                      {renderCategoryHeader(category)}
                      {filters.map((filter) => (
                        <View key={filter.id}>
                          {renderSavedFilterItem({ item: filter })}
                        </View>
                      ))}
                    </View>
                  )}
                  keyExtractor={([category]) => category}
                  contentContainerStyle={styles.savedFiltersList}
                  showsVerticalScrollIndicator={false}
                />
              </View>
            </TouchableOpacity>
          </Animated.View>
        </TouchableOpacity>
      </Modal>

      {/* Opportunities List */}
      <FlatList
        data={OPPORTUNITIES_DATA}
        keyExtractor={item => item.id}
        renderItem={renderOpportunityItem}
        contentContainerStyle={styles.listContainer}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 15,
    paddingHorizontal: 15,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  addButton: {
    padding: 5,
  },
  addButtonText: {
    fontSize: 16,
    color: '#3068E1',
    fontWeight: '500',
  },
  listContainer: {
    paddingBottom: 20,
  },
  opportunityItemContainer: {
    backgroundColor: 'white',
    paddingVertical: 15,
    paddingHorizontal: 15,
  },
  opportunityItemMainContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  opportunityName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 5,
  },
  opportunityAccount: {
    fontSize: 14,
    color: '#666',
    marginBottom: 3,
  },
  opportunityStatus: {
    fontSize: 14,
    color: '#999',
  },
  opportunityAmountContainer: {
    backgroundColor: '#f0f0f0',
    paddingVertical: 5,
    paddingHorizontal: 10,
    borderRadius: 5,
  },
  opportunityAmount: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  separator: {
    height: 1,
    backgroundColor: '#e1e1e1',
  },
  drawerOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  drawer: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: Dimensions.get('window').width * 0.8,
    backgroundColor: '#fff',
    elevation: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  drawerContent: {
    flex: 1,
    backgroundColor: '#fff',
    height: '100%',
  },
  drawerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
    height: 56,
  },
  drawerTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  drawerSearchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f5f5f5',
    margin: 16,
    borderRadius: 8,
  },
  drawerSearchIcon: {
    marginRight: 8,
  },
  drawerSearchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    padding: 0,
  },
  savedFiltersList: {
    paddingBottom: 24,
  },
  savedFilterItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 2,
  },
  selectedSavedFilterItem: {
    backgroundColor: '#F5FBFD',
  },
  savedFilterText: {
    fontSize: 15,
    color: '#333',
  },
  selectedSavedFilterText: {
    color: '#0F96BB',
    fontWeight: '500',
  },
  categoryHeader: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#f5f5f5',
    marginTop: 8,
    marginBottom: 4,
  },
  categoryTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
}); 