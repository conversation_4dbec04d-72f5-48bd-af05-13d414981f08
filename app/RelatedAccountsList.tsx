import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  RefreshControl,
  StatusBar,
  SafeAreaView,
  ScrollView,
  Platform,
} from 'react-native';
import { Stack, useRouter, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { Ionico<PERSON>, Feather } from '@expo/vector-icons';
import { useColors } from '@/hooks/useThemeColor';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { readUserScope, showSnackbar } from './ui/utils';

interface Account {
  id: number;
  name: string;
  website: string | null;
  phone: string | null;
  created_at: string | null;
  openDealsAmount?: number;
  wonDealsAmount?: number;
}

interface RelationsResponse {
  status: number;
  success: boolean;
  message: string;
  data: {
    account: Account;
    deals: {
      summary: {
        open_amount?: number;
        won_amount?: number;
      };
      deals: any[];
    };
  };
}

const STATUS_BAR_CONFIG = {
  barStyle: "light-content" as const,
  backgroundColor: "#0F96BB",
  translucent: Platform.OS === 'android',
};

export default function RelatedAccountsList() {
  const [account, setAccount] = useState<Account | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const router = useRouter();
  const params = useLocalSearchParams();
  const colors = useColors();
  const contactId = params.contact_id;
  const insets = useSafeAreaInsets();
  const [accessScope, setAccessScope] = useState<any>(null);

  useEffect(() => {
    const fetchAccessScope = async () => {
      const scope = await readUserScope();
      console.log("Access_Scope___meeting: " + JSON.stringify(scope));
      setAccessScope(scope);
    };

    const initializePage = async () => {
      await fetchAccessScope();
      await fetchRelatedAccount();
      // Call additional API when user enters the page
      await handlePageEntry();
    };

    initializePage();
  }, [contactId]);

  const fetchRelatedAccount = async () => {
    if (!contactId) {
      setError('Contact ID is missing');
      setIsLoading(false);
      return;
    }

    try {
      setError(null);
      const response = await apiService.get<RelationsResponse>(
        `${API_ENDPOINTS.CONTACTS}/${contactId}/relations`
      );
console.log('responsesdsd', JSON.stringify(response));
      if (response.data?.success && response.data?.data?.account) {
        const accountData = response.data.data.account;
        const dealsSummary = response.data.data.deals?.summary || {};

        // Merge account data with deal summary
        setAccount({
          ...accountData,
          openDealsAmount: dealsSummary.open_amount || 0,
          wonDealsAmount: dealsSummary.won_amount || 0
        });
      } else {
        setAccount(null);
        if (!response.data?.success) {
          setError(response.data?.message || 'Failed to fetch related account');
        }
      }
    } catch (err) {
      console.error('Error fetching related account:', err);
      setError('Failed to fetch related account. Please try again.');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Refresh data when screen comes into focus (after returning from create/edit)
  useFocusEffect(
    useCallback(() => {
      if (contactId) {
        fetchRelatedAccount();
      }
    }, [contactId])
  );

  // Handle page entry - call API when user enters the page
  const handlePageEntry = async () => {
    if (!contactId) {
      console.log('No contact ID available for page entry API call');
      return;
    }

    try {
      console.log('Calling page entry API for contact:', contactId);

      // Example API call - you can customize this based on your needs
      // This could be for logging page visits, initializing data, etc.
      const response = await apiService.post(`${API_ENDPOINTS.CONTACTS}/${contactId}/page-entry`, {
        page: 'RelatedAccountsList',
        timestamp: new Date().toISOString(),
        action: 'view_related_accounts'
      });

      console.log('Page entry API response:', response.data);

      // Handle response if needed
      if (response.data?.success) {
        console.log('Page entry logged successfully');
      }
    } catch (err) {
      // Don't show error to user for page entry API - it's not critical
      console.warn('Page entry API call failed:', err);
    }
  };

  const handleRefresh = () => {
    setIsRefreshing(true);
    fetchRelatedAccount();
  };

  const handleBack = () => {
    router.back();
  };

  const handleAddNew = () => {
    // Navigate to add new account page with contact_id for relationship
    router.push({
      pathname: '/CreateAccounts' as any,
      params: {
        contact_id: contactId,
        callFrom: 'RelatedAccountsList'
      }
    });
  };

  const handleAddExistingAccount = () => {
    // Navigate to select existing account page
    router.push({
      pathname: '/SelectAccountForContact' as any,
      params: {
        contact_id: contactId,
        callFrom: 'RelatedAccountsList'
      }
    });
  };

  const handleEditAccount = (account: Account) => {
    // Check if user has edit permission
    // if (accessScope?.account?.update !== true) {
    //   showSnackbar('You do not have permission to edit accounts');
    //   return;
    // }

    // Navigate to edit account page
    router.push({
      pathname: '/UpdateAccounts' as any,
      params: {
        id: account.id,
        callFrom: 'RelatedAccountsList',
        contact_id: contactId
      }
    });
  };

  const handleAccountPress = (account: Account) => {
    console.log('Navigating to account details with ID:', account.id);
    // Navigate to account details page
    router.push({
      pathname: '/accountDetail' as any,
      params: {
        id: account.id.toString(),
        name: account.name || '',
        phone: account.phone || '',
        website: account.website || ''
      }
    });
  };

  const renderAccountItem = ({ item }: { item: Account }) => (
    <View style={styles.accountContainer}>
      <View style={styles.accountHeader}>
        <TouchableOpacity onPress={() => handleAccountPress(item)}>
          <Text style={styles.accountName}>{item.name}</Text>
        </TouchableOpacity>
        {/* {accessScope?.account?.update === true && ( */}
          <TouchableOpacity onPress={() => handleEditAccount(item)} style={styles.editButton}>
            <Feather name="edit-2" size={20} color="#666" />
          </TouchableOpacity>
        {/* )} */}
      </View>

      <View style={styles.financialDetails}>
        <View style={styles.amountRow}>
          <Text style={styles.amountLabel}>Open deals amount</Text>
          <Text style={styles.amountValue}>$ {item.openDealsAmount?.toFixed(2) || '0.00'}</Text>
        </View>

        <View style={styles.amountRow}>
          <Text style={styles.amountLabel}>Won deals amount</Text>
          <Text style={styles.amountValue}>$ {item.wonDealsAmount?.toFixed(2) || '0.00'}</Text>
        </View>
      </View>
    </View>
  );

  // Content based on loading/error state
  const renderContent = () => {
    if (isLoading) {
      return (
        <View style={styles.centerContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      );
    }

    if (error) {
      return (
        <View style={styles.centerContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={fetchRelatedAccount}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      );
    }

    // Create an array with the single account for FlatList
    const accountsArray = account ? [account] : [];
    const totalAccounts = accountsArray.length;

    return (
      <FlatList
        data={accountsArray}
        renderItem={renderAccountItem}
        keyExtractor={(item) => item.id.toString()}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
        }
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No accounts found</Text>
          </View>
        }
      />
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={[styles.safeAreaContainer, { paddingTop: insets.top }]}>
        <StatusBar {...STATUS_BAR_CONFIG} />
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Ionicons name={Platform.OS === 'ios' ? 'chevron-back' : 'arrow-back'}  size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <View style={styles.headerContent}>
            <Text style={styles.headerTitleText}>Related Accounts</Text>
          </View>
          <View style={styles.headerRight} />
        </View>
        <View style={styles.mainContainer}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#0F96BB" />
            <Text style={styles.loadingText}>Loading accounts...</Text>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.safeAreaContainer, { paddingTop: insets.top }]}>
      <StatusBar {...STATUS_BAR_CONFIG} />
      <Stack.Screen options={{ headerShown: false }} />

      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Ionicons name={Platform.OS === 'ios' ? 'chevron-back' : 'arrow-back'}  size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitleText}>Related Accounts</Text>
        </View>
         {accessScope?.sales_account?.create === true && ( 
        <TouchableOpacity onPress={handleAddExistingAccount} style={styles.headerRight}>
          <Ionicons name="add" size={28} color="#FFFFFF" />
        </TouchableOpacity>
         )}
      </View>

      <View style={styles.mainContainer}>
        {error ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
          </View>
        ) : account ? (
          <View style={styles.accountsContainer}>
            <ScrollView style={styles.container}>
              <View style={styles.sectionContainer}>
                {renderAccountItem({ item: account })}
              </View>
            </ScrollView>
            {/* Floating Action Button for adding existing account */}
            {accessScope?.sales_account?.create === true && (
              <TouchableOpacity onPress={handleAddExistingAccount} style={styles.floatingActionButton}>
                <Ionicons name="add" size={28} color="#FFFFFF" />
              </TouchableOpacity>
            )}
          </View>
        ) : (
          <View style={styles.emptyContainer}>
            <View style={styles.emptyIconContainer}>
              <Ionicons name="business-outline" size={80} color="#ccc" />
            </View>
            <Text style={styles.emptyTitle}>No Related Accounts</Text>
            <Text style={styles.emptySubtitle}>
              Add an existing account or create a new one to establish a business relationship with this contact.
            </Text>
            {accessScope?.sales_account?.create === true && (
              <View style={styles.buttonContainer}>
                <TouchableOpacity onPress={handleAddExistingAccount} style={styles.addAccountButton}>
                  <Ionicons name="business" size={24} color="#FFFFFF" />
                  <Text style={styles.addAccountButtonText}>Add Existing Account</Text>
                </TouchableOpacity>
                <TouchableOpacity onPress={handleAddNew} style={styles.createAccountButton}>
                  <Ionicons name="add-circle" size={24} color="#FFFFFF" />
                  <Text style={styles.createAccountButtonText}>Create New Account</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeAreaContainer: {
    flex: 1,
    backgroundColor: '#0F96BB',
  },
  mainContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#0F96BB',
    height: Platform.OS === 'ios' ? 44 : 56,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
    zIndex: 1000,
    paddingHorizontal: Platform.OS === 'ios' ? 8 : 0,
  },
  backButton: {
    width: Platform.OS === 'ios' ? 44 : 56,
    height: Platform.OS === 'ios' ? 44 : 56,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: Platform.OS === 'ios' ? -8 : 0,
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: Platform.OS === 'ios' ? 4 : 16,
  },
  headerRight: {
    width: Platform.OS === 'ios' ? 44 : 56,
    height: Platform.OS === 'ios' ? 44 : 56,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Platform.OS === 'ios' ? -8 : 0,
  },
  headerTitleText: {
    color: '#FFFFFF',
    fontSize: Platform.OS === 'ios' ? 17 : 20,
    fontWeight: Platform.OS === 'ios' ? '600' : '500',
    textAlign: 'center',
  },
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  accountsContainer: {
    flex: 1,
    position: 'relative',
  },
  floatingActionButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#0F96BB',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
  },
  content: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  listContent: {
    flexGrow: 1,
    paddingTop: 10,
    paddingBottom: 20,
  },
  accountContainer: {
    marginVertical: 8,
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  accountHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  accountName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#4285F4',
  },
  editButton: {
    padding: 5,
  },
  financialDetails: {
    marginTop: 8,
  },
  amountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  amountLabel: {
    fontSize: 16,
    color: '#666',
  },
  amountValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  accountIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#0F96BB',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  accountInitial: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  accountInfo: {
    flex: 1,
  },
  accountDetail: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyIconContainer: {
    marginBottom: 20,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 30,
    paddingHorizontal: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
  },
  buttonContainer: {
    flexDirection: 'column',
    gap: 12,
    width: '100%',
    paddingHorizontal: 20,
  },
  addAccountButton: {
    backgroundColor: '#4CAF50',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  addAccountButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  createAccountButton: {
    backgroundColor: '#0F96BB',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  createAccountButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  errorText: {
    fontSize: 16,
    color: '#ff0000',
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#0F96BB',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#666',
    fontSize: 16,
    marginTop: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sectionContainer: {
    padding: 16,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});