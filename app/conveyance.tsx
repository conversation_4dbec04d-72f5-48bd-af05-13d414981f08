import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  Modal,
  SafeAreaView,
  ActivityIndicator,
  Alert,
  RefreshControl,
  Pressable,
  ScrollView,
  StatusBar,
  Platform,
} from 'react-native';
import MarqueeView from 'react-native-marquee-view';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter } from 'expo-router';
import {
  ConveyanceRecord,
  EnhancedConveyanceResponse,
  formatDistance,
  formatCharge,
  calculateTotalTimeSpent
} from './models/Conveyance';
import ConveyanceService from './services/ConveyanceService';

// Get status bar height for Android
const STATUSBAR_HEIGHT = Platform.OS === 'android' ? StatusBar.currentHeight || 0 : 0;

// Custom ConditionalMarquee component that only scrolls text longer than 12 characters
const ConditionalMarquee = ({
  text,
  style,
  minLength = 12, // Only apply marquee if text length exceeds this value
  speed = 0.1
}: {
  text: string,
  style?: any,
  minLength?: number,
  speed?: number
}) => {
  // Check if text length exceeds the minimum length for scrolling
  const shouldScroll = text.length > minLength;

  if (!shouldScroll) {
    // If text is short enough, just display it normally
    return <Text style={style} numberOfLines={1} ellipsizeMode="tail">{text}</Text>;
  }

  // If text is long, use MarqueeView for scrolling
  return (
    <MarqueeView
      style={{ flex: 1, overflow: 'hidden' }}
      speed={speed}
    >
      <Text style={style}>{text}</Text>
    </MarqueeView>
  );
};

// Interface for transformed data that matches our UI
interface ConveyanceListItem {
  id: string;
  date: string;
  distance: string;
  timeSpent: string;
  charge: string;
}

export default function ConveyanceScreen() {
  const [isFilterVisible, setFilterVisible] = useState(false);
  const [isMonthYearVisible, setMonthYearVisible] = useState(false);
  const [filterType, setFilterType] = useState('Monthly');
  const [conveyanceData, setConveyanceData] = useState<ConveyanceListItem[]>([]);
  const [originalApiData, setOriginalApiData] = useState<ConveyanceRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Month and year selection state
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1); // Current month (1-12)
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear()); // Current year
  const [tempMonth, setTempMonth] = useState(selectedMonth);
  const [tempYear, setTempYear] = useState(selectedYear);

  // Convert to string format for API
  const [currentMonth, setCurrentMonth] = useState(
    new Date().getMonth() + 1 > 9
    ? `${new Date().getMonth() + 1}`
    : `0${new Date().getMonth() + 1}`
  );
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear().toString());
  const [filterPeriod, setFilterPeriod] = useState('');
  const router = useRouter();

  // Generate arrays for month and year selection
  const months = [
    { value: 1, label: 'January' },
    { value: 2, label: 'February' },
    { value: 3, label: 'March' },
    { value: 4, label: 'April' },
    { value: 5, label: 'May' },
    { value: 6, label: 'June' },
    { value: 7, label: 'July' },
    { value: 8, label: 'August' },
    { value: 9, label: 'September' },
    { value: 10, label: 'October' },
    { value: 11, label: 'November' },
    { value: 12, label: 'December' }
  ];

  // Generate years (current year and previous 2 years)
  const currentYearValue = new Date().getFullYear();
  const years = [currentYearValue - 2, currentYearValue - 1, currentYearValue];

  // Function to fetch conveyance data from API
  const fetchConveyanceData = useCallback(async (showLoader = true) => {
    if (showLoader) setIsLoading(true);
    setError(null);

    try {
      // Use the ConveyanceService to fetch data based on filter type
      const response = await ConveyanceService.getConveyanceHistory(
        filterType,
        currentMonth,
        currentYear
      );

      // Check if the response is valid
      if (!response.data || !response.data.conveyance_data) {
        throw new Error('Invalid response format from server');
      }

      // Save the original API data
      setOriginalApiData(response.data.conveyance_data);

      // Transform API response to match our UI format
      const transformedData = transformApiData(response.data.conveyance_data);
      setConveyanceData(transformedData);

      // Update filter period from response
      setFilterPeriod(response.data.period);
    } catch (err) {
      console.error('Fetch error:', err);
      setError(err instanceof Error ? err.message : 'Failed to load conveyance data');
      Alert.alert('Error', 'Failed to load conveyance data');
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  }, [filterType, currentMonth, currentYear]);

  // Function to transform API data into UI format
  const transformApiData = (apiData: ConveyanceRecord[]): ConveyanceListItem[] => {
    return apiData.map((item, index) => {
      // Parse date from API format (DD/MM/YYYY) to get day of week
      const dateParts = item.conveyance_date.split('/');
      const date = new Date(parseInt(currentYear), parseInt(dateParts[1]) - 1, parseInt(dateParts[0]));

      // Get day of week abbreviation
      const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
      const dayOfWeek = dayNames[date.getDay()];

      // Format date to match UI format (DD/MM DayOfWeek)
      const formattedDate = `${dateParts[0]}/${dateParts[1]} ${dayOfWeek}`;

      return {
        id: (index + 1).toString(),
        date: formattedDate,
        distance: formatDistance(item.distance),
        timeSpent: calculateTotalTimeSpent(item.activity),
        charge: formatCharge(item.charge),
      };
    });
  };

  // Fetch data when component mounts or filter changes
  useEffect(() => {
    fetchConveyanceData();
  }, [fetchConveyanceData]);

  const handleRefresh = () => {
    setRefreshing(true);
    fetchConveyanceData(false);
  };

  const handleBack = () => {
    router.back();
  };

  const handleFilter = () => {
    setFilterVisible(true);
  };

  const handleFilterSelect = (type: string) => {
    setFilterType(type);
    setFilterVisible(false);

    // Show month/year picker for monthly filter
    if (type === 'Monthly') {
      // Reset temp values to current selections
      setTempMonth(selectedMonth);
      setTempYear(selectedYear);

      // Use setTimeout to ensure the first modal is fully closed before opening the second
      setTimeout(() => {
        setMonthYearVisible(true);
      }, 300);
    }
  };

  // Handle month selection (updates temporary state only)
  const handleMonthSelect = useCallback((month: number) => {
    // If selected year is current year, validate month selection
    if (tempYear === currentYearValue && month > (new Date().getMonth() + 1)) {
      // Don't allow future months for current year
      return;
    }

    // Update temp month
    setTempMonth(month);
  }, [tempYear, currentYearValue]);

  // Handle year selection (updates temporary state only)
  const handleYearSelect = useCallback((year: number) => {
    // Update temp year
    setTempYear(year);

    // If changing to current year, validate the currently selected month
    if (year === currentYearValue && tempMonth > (new Date().getMonth() + 1)) {
      // Reset to current month if previously selected month is in the future
      setTempMonth(new Date().getMonth() + 1);
    }
  }, [tempMonth, currentYearValue]);

  // Apply month/year filter - this is the only place where we update the actual state
  const applyMonthYearFilter = () => {
    // Apply temp values to actual state
    setSelectedMonth(tempMonth);
    setSelectedYear(tempYear);

    // Update the string formats for API
    setCurrentMonth(tempMonth > 9 ? `${tempMonth}` : `0${tempMonth}`);
    setCurrentYear(tempYear.toString());

    // Close the modal
    setMonthYearVisible(false);
    // Data will be fetched automatically through the useEffect
  };

  const handleRowPress = (item: ConveyanceListItem) => {
    // Extract DD/MM from "DD/MM Day" format
    const dateParts = item.date.split(' ')[0].split('/');
    const dateToMatch = dateParts.join('/');

    // Find the matching record from the original API data
    const originalRecord = originalApiData.find(record =>
      record.conveyance_date.includes(dateToMatch)
    );

    // Navigate to conveyance details page
    router.push({
      pathname: 'conveyanceDetails' as any,
      params: {
        date: item.date,
        record: originalRecord ? JSON.stringify(originalRecord) : undefined
      }
    });
  };

  const TableHeader = () => (
    <View style={styles.tableHeader}>
      <View style={{ flex: 0.3, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={styles.headerCell}>#</Text>
      </View>
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={styles.headerCell}>Date</Text>
      </View>
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={styles.headerCell}>Distance</Text>
      </View>
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={styles.headerCell}>Time spent</Text>
      </View>
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={styles.headerCell}>Charge</Text>
      </View>
    </View>
  );

  const renderItem = ({ item }: { item: ConveyanceListItem }) => (
    <TouchableOpacity onPress={() => handleRowPress(item)}>
      <View style={styles.tableRow}>
        {/* Serial number - no marquee needed */}
        <Text style={[styles.tableCell, { flex: 0.3 }]}>{item.id}</Text>

        {/* Date with conditional marquee */}
        <View style={[{ flex: 1, justifyContent: 'center' }]}>
          <ConditionalMarquee
            text={item.date}
            style={styles.tableCell}
            minLength={12}
            speed={0.1}
          />
        </View>

        {/* Distance with conditional marquee */}
        <View style={[{ flex: 1, justifyContent: 'center' }]}>
          <ConditionalMarquee
            text={item.distance}
            style={styles.tableCell}
            minLength={12}
            speed={0.1}
          />
        </View>

        {/* Time spent with conditional marquee */}
        <View style={[{ flex: 1, justifyContent: 'center' }]}>
          <ConditionalMarquee
            text={item.timeSpent}
            style={styles.tableCell}
            minLength={12}
            speed={0.1}
          />
        </View>

        {/* Charge with conditional marquee */}
        <View style={[{ flex: 1, justifyContent: 'center' }]}>
          <ConditionalMarquee
            text={item.charge}
            style={styles.tableCell}
            minLength={12}
            speed={0.1}
          />
        </View>
      </View>
    </TouchableOpacity>
  );

  // Loading state
  if (isLoading && !refreshing) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar backgroundColor="#0F96BB" barStyle="light-content" />
        <View style={styles.statusBarPlaceholder} />
        <Stack.Screen options={{ headerShown: false }} />

        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Conveyance</Text>
          <TouchableOpacity onPress={handleFilter} style={styles.filterButton}>
            <Ionicons name="funnel-outline" size={24} color="white" />
          </TouchableOpacity>
        </View>

        {/* Filter Period Section */}
        <View style={styles.filterIndicator}>
          <Text style={styles.filterLabel}>Showing:</Text>
          <View style={styles.filterPill}>
            <Text style={styles.filterPillText}>{filterPeriod || 'All Conveyance'}</Text>
            <TouchableOpacity onPress={handleFilter}>
              <Ionicons name="chevron-down" size={16} color="#0F96BB" />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0F96BB" />
          <Text style={styles.loadingText}>Loading conveyance data...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor="#0F96BB" barStyle="light-content" />
      <View style={styles.statusBarPlaceholder} />
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      {/* Custom Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Conveyance</Text>
        <TouchableOpacity onPress={handleFilter} style={styles.filterButton}>
          <Ionicons name="funnel-outline" size={24} color="white" />
        </TouchableOpacity>
      </View>

      {/* Filter Period Section */}
      <View style={styles.filterIndicator}>
        <Text style={styles.filterLabel}>Showing:</Text>
        <View style={styles.filterPill}>
          <Text style={styles.filterPillText}>{filterPeriod || 'All Conveyance'}</Text>
          <TouchableOpacity onPress={handleFilter}>
            <Ionicons name="chevron-down" size={16} color="#0F96BB" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Error message */}
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => fetchConveyanceData()}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Table */}
      {!error && (
        conveyanceData.length > 0 ? (
          <FlatList
            data={conveyanceData}
            renderItem={renderItem}
            keyExtractor={item => item.id}
            ListHeaderComponent={TableHeader}
            stickyHeaderIndices={[0]}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={['#0066cc']}
              />
            }
          />
        ) : (
          <ScrollView
            style={styles.emptyStateWrapper}
            contentContainerStyle={styles.emptyScrollContent}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={['#0066cc']}
              />
            }
          >
            <View style={styles.emptyContainer}>
              <Ionicons name="car-outline" size={64} color="#ccc" />
              <Text style={styles.emptyText}>No conveyance records found</Text>
              <Text style={styles.emptySubtext}>
                {filterType === 'Today' && 'No conveyance data for today'}
                {filterType === 'Weekly' && 'No conveyance data for this week'}
                {filterType === 'Monthly' && `No conveyance data for ${months.find(m => m.value === selectedMonth)?.label} ${selectedYear}`}
                {!filterType && 'Try adjusting your filter settings'}
              </Text>
              <TouchableOpacity
                style={styles.refreshButton}
                onPress={handleRefresh}
              >
                <Ionicons name="refresh-outline" size={20} color="#0F96BB" />
                <Text style={styles.refreshButtonText}>Refresh</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        )
      )}

      {/* Filter Modal */}
      <Modal
        visible={isFilterVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setFilterVisible(false)}
      >
        <Pressable style={styles.modalOverlay} onPress={() => setFilterVisible(false)}>
          <Pressable style={styles.modalContent} onPress={e => e.stopPropagation()}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Filter</Text>
              <TouchableOpacity
                onPress={() => setFilterVisible(false)}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color="black" />
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              style={[styles.filterOption, filterType === 'Today' && styles.activeFilterOption]}
              onPress={() => handleFilterSelect('Today')}
            >
              <Text style={[styles.filterOptionText, filterType === 'Today' && styles.activeFilterOptionText]}>Today</Text>
              {filterType === 'Today' && (
                <Ionicons name="checkmark" size={20} color="#0F96BB" />
              )}
            </TouchableOpacity>

            <View style={styles.divider} />

            <TouchableOpacity
              style={[styles.filterOption, filterType === 'Weekly' && styles.activeFilterOption]}
              onPress={() => handleFilterSelect('Weekly')}
            >
              <Text style={[styles.filterOptionText, filterType === 'Weekly' && styles.activeFilterOptionText]}>Weekly</Text>
              {filterType === 'Weekly' && (
                <Ionicons name="checkmark" size={20} color="#0F96BB" />
              )}
            </TouchableOpacity>

            <View style={styles.divider} />

            <TouchableOpacity
              style={[styles.filterOption, filterType === 'Monthly' && styles.activeFilterOption]}
              onPress={() => handleFilterSelect('Monthly')}
            >
              <Text style={[styles.filterOptionText, filterType === 'Monthly' && styles.activeFilterOptionText]}>Monthly</Text>
              {filterType === 'Monthly' && (
                <Ionicons name="checkmark" size={20} color="#0F96BB" />
              )}
            </TouchableOpacity>
          </Pressable>
        </Pressable>
      </Modal>

      {/* Month/Year Selection Modal */}
      <Modal
        visible={isMonthYearVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setMonthYearVisible(false)}
      >
        <Pressable style={styles.modalOverlay} onPress={e => e.stopPropagation()}>
          <Pressable style={styles.modalContent} onPress={e => e.stopPropagation()}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Month & Year</Text>
              <TouchableOpacity
                onPress={() => setMonthYearVisible(false)}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color="black" />
              </TouchableOpacity>
            </View>

            <View style={styles.calendarContainer}>
              {/* Month Selection */}
              <Text style={styles.pickerLabel}>Month</Text>
              {tempYear === currentYearValue && (
                <Text style={styles.pickerHelperText}>
                  Only months up to {months[new Date().getMonth()].label} are available for {currentYearValue}
                </Text>
              )}
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.pickerScrollContent}
              >
                {months.map((month) => {
                  // Determine if this month should be disabled (future month in current year)
                  const isDisabled = tempYear === currentYearValue && month.value > (new Date().getMonth() + 1);

                  return (
                    <TouchableOpacity
                      key={month.value}
                      style={[
                        styles.pickerItem,
                        tempMonth === month.value && styles.pickerItemSelected,
                        isDisabled && styles.pickerItemDisabled
                      ]}
                      onPress={() => !isDisabled && handleMonthSelect(month.value)}
                      disabled={isDisabled}
                      activeOpacity={0.7}
                    >
                      <Text
                        style={[
                          styles.pickerItemText,
                          tempMonth === month.value && styles.pickerItemTextSelected,
                          isDisabled && styles.pickerItemTextDisabled
                        ]}
                      >
                        {month.label}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
              </ScrollView>

              {/* Year Selection */}
              <Text style={[styles.pickerLabel, {marginTop: 20}]}>Year</Text>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.pickerScrollContent}
              >
                {years.map((year) => (
                  <TouchableOpacity
                    key={year}
                    style={[
                      styles.pickerItem,
                      tempYear === year && styles.pickerItemSelected
                    ]}
                    onPress={() => handleYearSelect(year)}
                    activeOpacity={0.7}
                  >
                    <Text
                      style={[
                        styles.pickerItemText,
                        tempYear === year && styles.pickerItemTextSelected
                      ]}
                    >
                      {year}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            <TouchableOpacity
              style={styles.applyButton}
              onPress={applyMonthYearFilter}
            >
              <Text style={styles.applyButtonText}>Apply</Text>
            </TouchableOpacity>
          </Pressable>
        </Pressable>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  statusBarPlaceholder: {
    height: STATUSBAR_HEIGHT,
    backgroundColor: '#0F96BB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#0F96BB',
    paddingVertical: 15,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#0284A8',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  filterButton: {
    padding: 5,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#f0f0f0',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
    paddingVertical: 12,
  },
  headerCell: {
    flex: 1,
    fontWeight: 'bold',
    textAlign: 'center',
    color: '#555',
    fontSize: 14,
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
    paddingVertical: 12,
    backgroundColor: 'white',
  },
  tableCell: {
    flex: 1,
    textAlign: 'center',
    color: '#333',
    fontSize: 13,
  },
  marqueeContainer: {
    flex: 1,
    overflow: 'hidden',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#555',
  },
  errorContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#d32f2f',
    marginBottom: 15,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#0066cc',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
  },
  emptyStateWrapper: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  emptyScrollContent: {
    flexGrow: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 20,
    minHeight: 400, // Ensure minimum height for proper centering
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#555',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#888',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  refreshButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f0f8ff',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#0F96BB',
    gap: 8,
  },
  refreshButtonText: {
    fontSize: 16,
    color: '#0F96BB',
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: '#f0f8ff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 5,
  },
  filterOption: {
    paddingVertical: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  filterOptionText: {
    fontSize: 16,
    color: '#333',
  },
  divider: {
    height: 1,
    backgroundColor: '#e1e1e1',
  },
  activeFilterOption: {
    backgroundColor: 'rgba(15, 150, 187, 0.1)',
    borderRadius: 8,
    paddingHorizontal: 10,
  },
  activeFilterOptionText: {
    color: '#0F96BB',
    fontWeight: '500',
  },
  filterIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f7f7f7',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
  },
  filterLabel: {
    fontSize: 14,
    color: '#666',
    marginRight: 8,
  },
  filterPill: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e6f7fb',
    paddingHorizontal: 12,
    paddingVertical: 5,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#cce7f0',
  },
  filterPillText: {
    fontSize: 14,
    color: '#0F96BB',
    fontWeight: '500',
    marginRight: 4,
  },
  calendarContainer: {
    marginVertical: 15,
  },
  pickerLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  pickerHelperText: {
    fontSize: 12,
    color: '#777',
    marginBottom: 8,
    fontStyle: 'italic',
  },
  pickerScrollContent: {
    paddingBottom: 10,
  },
  pickerItem: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 8,
    backgroundColor: '#e6e6e6',
  },
  pickerItemSelected: {
    backgroundColor: '#0F96BB',
  },
  pickerItemDisabled: {
    backgroundColor: '#f0f0f0',
    opacity: 0.5,
  },
  pickerItemText: {
    fontSize: 14,
    color: '#333',
  },
  pickerItemTextSelected: {
    color: 'white',
    fontWeight: 'bold',
  },
  pickerItemTextDisabled: {
    color: '#999',
  },
  applyButton: {
    backgroundColor: '#0F96BB',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginTop: 15,
  },
  applyButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});