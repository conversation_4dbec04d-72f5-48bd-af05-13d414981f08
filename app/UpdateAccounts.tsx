import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  ScrollView,
  ActivityIndicator,
  Alert,
  Platform,
  Keyboard,
  KeyboardAvoidingView,
  Modal,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { AccountField, AccountFieldsResponse, AccountFormData, AccountFieldChoice } from './models/AccountField';
import { useColors } from '@/hooks/useThemeColor';
import { DynamicFormField } from './dynamic_fields/DynamicFormField';
import { FIELD_TYPES } from './models/contact_fields_model';

const UpdateAccounts = () => {
  const [fields, setFields] = useState<AccountField[]>([]);
  const [formData, setFormData] = useState<AccountFormData>({} as AccountFormData);
  const [loading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [currentDropdownField, setCurrentDropdownField] = useState<AccountField | null>(null);

  const router = useRouter();
  const params = useLocalSearchParams();
  const accountId = params.id as string;
  const callFrom = params.callFrom as string;
  const contactId = params.contact_id as string;
  const colors = useColors();

  // Fetch account fields and account details on component mount
  useEffect(() => {
    if (accountId) {
      loadAccountData();
    } else {
      setError('No account ID provided');
      setIsLoading(false);
    }
  }, [accountId]);

  // Load account data - both field definitions and existing values
  const loadAccountData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch fields structure from the fields endpoint
      console.log('Fetching account fields...');
      const fieldsResponse = await apiService.get<AccountFieldsResponse>(API_ENDPOINTS.ACCOUNT_FIELDS);

      if (!fieldsResponse.data || !fieldsResponse.data.accountFields) {
        throw new Error('Failed to fetch account fields');
      }

      // Sort fields by order property, with null values at the end
      const sortedFields = [...fieldsResponse.data.accountFields].sort((a, b) => {
        if (a.order === null) return 1;
        if (b.order === null) return -1;
        return a.order - b.order;
      });

      console.log(`Fetched ${sortedFields.length} account fields`);
      setFields(sortedFields);

      // Fetch account details to get existing values
      const endpoint = `${API_ENDPOINTS.SALES_ACCOUNTS}/${accountId}`;
      console.log(`Fetching account details from: ${endpoint}`);

      const accountResponse = await apiService.get(endpoint);

      if (!accountResponse.data || !accountResponse.data.account) {
        throw new Error('Failed to fetch account details');
      }

      const accountData = accountResponse.data.account;
      const accountFields = accountData.accounts || [];

      // Initialize form data with existing values from account data
      const initialFormData: AccountFormData = { name: '' };

      // First add all field definitions
      sortedFields.forEach(field => {
        initialFormData[field.field_name] = '';
      });

      // Then override with actual values from the account
      accountFields.forEach((field: AccountField) => {
        if (field.field_name) {
          initialFormData[field.field_name] = field.field_value || '';

          // For owner fields, if we have an ID value, find the corresponding display text
          const isOwnerField = field.field_name.toLowerCase().includes('owner') ||
                              field.field_label.toLowerCase().includes('owner') ||
                              field.lookup_type === 'users';

          if (isOwnerField && field.field_value && field.choices) {
            const selectedChoice = field.choices.find(choice => choice.id.toString() === field.field_value);
            if (selectedChoice) {
              initialFormData[`${field.field_name}_display`] = selectedChoice.custom_option;
            }
          }
        }
      });

      console.log('Form data initialized with account values');
      setFormData(initialFormData);
    } catch (err) {
      console.error('Error loading account data:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle text input changes
  const handleInputChange = (fieldName: string, value: string) => {
    setFormData(prevData => ({
      ...prevData,
      [fieldName]: value
    }));
  };

  const handleChange = (fieldId: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [fieldId]: value
    }));
  };

  // Handle dropdown selection
  const handleDropdownSelect = (field: AccountField, choice: AccountFieldChoice) => {
    // For owner fields, we need to store the ID, not the email
    const isOwnerField = field.field_name.toLowerCase().includes('owner') ||
                        field.field_label.toLowerCase().includes('owner') ||
                        field.lookup_type === 'users';

    if (isOwnerField) {
      // Store both the ID for API submission and display text for UI
      setFormData(prevData => ({
        ...prevData,
        [field.field_name]: choice.id.toString(), // Store ID for API
        [`${field.field_name}_display`]: choice.custom_option // Store display text for UI
      }));
    } else {
      // For other fields, store the custom_option as before
      setFormData(prevData => ({
        ...prevData,
        [field.field_name]: choice.custom_option
      }));
    }

    // Close dropdown after selection
    setShowDropdown(false);
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      // Validate required fields
      const requiredFields = fields.filter(field => field.required === 'on');
      const missingFields = requiredFields.filter(field => !formData[field.field_name]);

      if (missingFields.length > 0) {
        Alert.alert('Validation Error', `Please fill in the following required fields: ${missingFields.map(f => f.field_label).join(', ')}`);
        return;
      }

      setSubmitting(true);

      // Prepare data using the exact same format as CreateAccounts
      // Update field values with user input from the fields we have on the form
      const sales_account = fields.map(field => {
        return {
          ...field,
          field_value: formData[field.field_name] || null
        };
      });

      // Create the request payload with the sales_account array - same format as create
      const requestData = {
        sales_account: sales_account
      };

      console.log('Updating account data with ID:', accountId);

      // Use the same endpoint structure but with PUT method
      const endpoint = API_ENDPOINTS.SALES_ACCOUNTS + '/' + accountId;
      const response = await apiService.put(endpoint, requestData);

      if (!response.data) {
        throw new Error('No response data received');
      }

      if (response.data.status === 200) {
        console.log('Account updated successfully:', response.data);

        Alert.alert(
          'Success',
          'Account updated successfully!',
          [{
            text: 'OK',
            onPress: () => {
              // Navigate back based on where we came from
              if (callFrom === 'RelatedAccountsList') {
                router.back(); // Go back to RelatedAccountsList
              } else {
                router.back(); // Default behavior - go back to previous screen
              }
            }
          }]
        );
      } else {
        throw new Error(response.data.message || 'Failed to update account');
      }
    } catch (err) {
      console.error('Error updating account:', err);
      Alert.alert(
        'Error',
        err instanceof Error
          ? err.message
          : 'Failed to update account. Please try again.'
      );
    } finally {
      setSubmitting(false);
    }
  };

  // Render dropdown options
  const renderDropdownOptions = () => {
    if (!currentDropdownField) return null;

    return (
      <Modal
        visible={showDropdown}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowDropdown(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowDropdown(false)}
        >
          <View style={styles.dropdownContainer}>
            <View style={styles.dropdownHeader}>
              <Text style={styles.dropdownTitle}>{currentDropdownField.field_label}</Text>
              <TouchableOpacity onPress={() => setShowDropdown(false)}>
                <Ionicons name="close" size={24} color={colors.text.primary} />
              </TouchableOpacity>
            </View>

            <FlatList
              data={currentDropdownField.choices}
              keyExtractor={(item) => item.id.toString()}
              renderItem={({ item }) => {
                // Check if this is an owner field
                const isOwnerField = currentDropdownField.field_name.toLowerCase().includes('owner') ||
                                    currentDropdownField.field_label.toLowerCase().includes('owner') ||
                                    currentDropdownField.lookup_type === 'users';

                // Determine if this item is selected
                const isSelected = isOwnerField
                  ? formData[currentDropdownField.field_name] === item.id.toString()
                  : formData[currentDropdownField.field_name] === item.custom_option;

                return (
                  <TouchableOpacity
                    style={styles.dropdownItem}
                    onPress={() => handleDropdownSelect(currentDropdownField, item)}
                  >
                    <Text style={[
                      styles.dropdownOptionText,
                      isSelected && styles.selectedOptionText
                    ]}>
                      {item.custom_option}
                    </Text>
                    {isSelected && (
                      <Ionicons name="checkmark" size={20} color={colors.primary} />
                    )}
                  </TouchableOpacity>
                );
              }}
              ItemSeparatorComponent={() => <View style={styles.separator} />}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    );
  };

  // Render form fields based on field type
  const renderFormField = (field: AccountField) => {
    const isRequired = field.required === 'on';

    switch (field.field_type.toLowerCase()) {
      case 'dropdown':
      case 'lookup':
        return (
          <View key={field.id} style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>
              {field.field_label} {isRequired && <Text style={styles.requiredStar}>*</Text>}
            </Text>
            <TouchableOpacity
              activeOpacity={1}
              style={styles.dropdownField}
              onPress={() => {
                setCurrentDropdownField(field);
                setShowDropdown(true);
              }}
            >
              <Text style={[
                styles.dropdownText,
                !formData[field.field_name] && styles.placeholderText
              ]}>
                {(() => {
                  // For owner fields, show the display text instead of the ID

                  const selectedChoice = field.choices.find(c => c.id.toString() === formData[field.field_name]?.toString());
                  var displayText = formData[field.field_name];
                  if(selectedChoice){
                     displayText = selectedChoice.custom_option;
                    }
                  return displayText || field.placeholder;
                })()}
              </Text>
              <Ionicons name="chevron-down" size={20} color={colors.text.secondary} />
            </TouchableOpacity>
          </View>
        );

      case 'text field':
        return (
          <View key={field.id} style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>
              {field.field_label} {isRequired && <Text style={styles.requiredStar}>*</Text>}
            </Text>
            <TextInput
              style={styles.textInput}
              placeholder={field.placeholder}
              value={formData[field.field_name]}
              onChangeText={(text) => handleInputChange(field.field_name, text)}
            />
          </View>
        );

      case 'number':
        return (
          <TouchableOpacity style={[ {display:field.active === 1 ? 'flex' : 'none'}]}>
            <View key={field.id} style={styles.fieldContainer}>
              <Text style={styles.fieldLabel}>
                {field.field_label} {isRequired && <Text style={styles.requiredStar}>*</Text>}
              </Text>
              <TextInput
                style={styles.textInput}
                maxLength={getFieldMaxLength(field)}
                placeholder={field.placeholder}
                value={formData[field.field_name]}
                onChangeText={(text) => handleInputChange(field.field_name, text)}
                keyboardType="numeric"
              />
            </View>
          </TouchableOpacity>
        );

      default:
        return (
          <View key={field.id} style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>
              {field.field_label} {isRequired && <Text style={styles.requiredStar}>*</Text>}
            </Text>
            <TextInput
              style={styles.textInput}
              placeholder={field.placeholder}
              value={formData[field.field_name]}
              onChangeText={(text) => handleInputChange(field.field_name, text)}
            />
          </View>
        );
    }
  };

  const getFieldMaxLength = (field: AccountField): number => {
    switch (field.field_type) {
      case FIELD_TYPES.NUMBER:
        if(field.field_label.includes("Telephone")||field.field_label.includes("Mobile")||field.field_label.includes("Phone")||field.field_label.includes("contact")){
          return 10;
        }else{
          return 255;
        };
      case FIELD_TYPES.TEXTAREA:
        return 1000;
      case FIELD_TYPES.TEXT:
      default:
        return 255;
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0F96BB" />
        <Text style={styles.loadingText}>Loading account data...</Text>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={loadAccountData}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color={colors.text.inverse} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Update Account</Text>
          <View style={{ width: 40 }} />
        </View>

        {/* Form Content */}
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.contentContainer}
          keyboardShouldPersistTaps="handled"
        >
          {fields.map(field => renderFormField(field))}

         
        </ScrollView>
         <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.submitButton, submitting && styles.submittingButton]}
            onPress={handleSubmit}
            disabled={submitting}
          >
            {submitting ? (
              <ActivityIndicator color="#fff" size="small" />
            ) : (
              <Text style={styles.submitButtonText}>Update Account</Text>
            )}
          </TouchableOpacity>
        </View>
        {/* Dropdown Options Modal */}
        {renderDropdownOptions()}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#555',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: 'red',
    marginBottom: 20,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#0F96BB',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#0F96BB',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 100,
  },
  fieldContainer: {
    marginBottom: 20,
  },
  fieldLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    color: '#333',
  },
  requiredStar: {
    color: 'red',
  },
  textInput: {
    backgroundColor: '#fff',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#ddd',
    fontSize: 16,
  },
  dropdownField: {
    backgroundColor: '#fff',
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#ddd',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dropdownText: {
    fontSize: 16,
    color: '#333',
  },
  placeholderText: {
    color: '#999',
  },
  
  buttonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  submitButton: {
    backgroundColor: '#0F96BB',
    paddingVertical: 15,
    borderRadius: 5,
    alignItems: 'center',
  
  },
  submittingButton: {
    backgroundColor: '#0F96BB',
    opacity: 0.7,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  dropdownContainer: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    maxHeight: '80%',
  },
  dropdownHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  dropdownTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  dropdownItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  dropdownOptionText: {
    fontSize: 16,
    color: '#333',
  },
  selectedOptionText: {
    color: '#0F96BB',
    fontWeight: '500',
  },
  separator: {
    height: 1,
    backgroundColor: '#eee',
  },
});

export default UpdateAccounts;