import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  RefreshControl,
  StatusBar,
  SafeAreaView,
  Platform,
  Alert,
  TextInput,
} from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { showSnackbar } from './ui/utils';
import { Account, AccountsResponse, DisplayAccount } from './models/Account';

const STATUS_BAR_CONFIG = {
  barStyle: "light-content" as const,
  backgroundColor: "#0F96BB",
  translucent: Platform.OS === 'android',
};

export default function SelectAccountForContact() {
  const [accounts, setAccounts] = useState<DisplayAccount[]>([]);
  const [filteredAccounts, setFilteredAccounts] = useState<DisplayAccount[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);

  const router = useRouter();
  const params = useLocalSearchParams();
  const contactId = params.contact_id;
  const insets = useSafeAreaInsets();

  // Capitalize function
  const capitalizeWords = (str: string): string => {
    return str
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  useEffect(() => {
    fetchAccounts();
  }, []);

  // Filter accounts when search query changes
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredAccounts(accounts);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = accounts.filter(account => 
        (account.name && account.name.toLowerCase().includes(query)) ||
        (account.phone && account.phone.toLowerCase().includes(query)) ||
        (account.website && account.website.toLowerCase().includes(query))
      );
      setFilteredAccounts(filtered);
    }
  }, [searchQuery, accounts]);

  const fetchAccounts = async () => {
    try {
      setError(null);

      // Use the same API as distributors page to get all accounts
      const filterPayload = {
        title: "All Accounts",
        filter: [],
        share_with: "selected_users",
        selected_users: ["1"],
        accessibility: "Public",
        prevent_sharing: false
      };

      console.log('Fetching accounts with payload:', JSON.stringify(filterPayload, null, 2));
      console.log('Accounts API endpoint:', API_ENDPOINTS.ACCOUNT_LIST_WITH_FILTERS);

      const response = await apiService.post<AccountsResponse>(
        API_ENDPOINTS.ACCOUNT_LIST_WITH_FILTERS,
        filterPayload
      );

      console.log('Accounts API response:', JSON.stringify(response.data, null, 2));

      if (!response.data || !response.data.sales_accounts) {
        throw new Error('No data received from the server');
      }

      // Transform the data to include display fields
      const transformedData: DisplayAccount[] = response.data.sales_accounts
        .map((account: Account) => {
          const capitalizedName = account.name ? capitalizeWords(account.name) : 'A';
          return {
            ...account,
            name: capitalizedName,
            initial: capitalizedName[0],
          };
        });

      // Sort by name
      const sortedData = transformedData.sort((a, b) =>
        (a.name || '').localeCompare(b.name || '')
      );

      console.log(`Successfully loaded ${sortedData.length} accounts`);
      console.log('First few accounts:', sortedData.slice(0, 3).map(a => ({ id: a.id, name: a.name })));

      setAccounts(sortedData);
      setFilteredAccounts(sortedData);
    } catch (err) {
      console.error('Error fetching accounts:', err);
      setError('Failed to fetch accounts. Please try again.');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setIsRefreshing(true);
    fetchAccounts();
  };

  const handleBack = () => {
    router.back();
  };

  const handleAccountSelect = async (account: Account) => {
    if (!contactId) {
      showSnackbar('Contact ID is missing');
      return;
    }

    try {
      setIsUpdating(true);

      // First, get the contact details to get the current field structure
      console.log('Fetching contact details from:', `${API_ENDPOINTS.CONTACTS}/${contactId}`);
      const contactResponse = await apiService.get(`${API_ENDPOINTS.CONTACTS}/${contactId}`);

      console.log('Contact details API response:', JSON.stringify(contactResponse.data, null, 2));

      if (!contactResponse.data?.contact?.contacts) {
        console.error('Invalid contact response structure:', contactResponse.data);
        throw new Error('Failed to get contact details');
      }

      const contactFields = contactResponse.data.contact.contacts;
      console.log('Contact fields found:', contactFields.length);
      
      // Find the sales_accounts field and update it with the selected account ID
      console.log('Looking for sales_accounts field in contact fields...');
      let salesAccountsFieldFound = false;

      const updatedFields = contactFields.map((field: any) => {
        console.log(`Field: ${field.field_name}, Type: ${field.field_type}, Current value: ${field.field_value}`);

        if (field.field_name === 'sales_accounts') {
          salesAccountsFieldFound = true;
          console.log(`Found sales_accounts field! Type: ${field.field_type}`);

          // Handle different field types properly
          let newFieldValue;
          let newValue;

          if (field.field_type === 'Dropdown' && field.choices) {
            // For dropdown fields, find the choice that matches our account
            const accountChoice = field.choices.find((choice: any) =>
              choice.custom_option === account.name || choice.id.toString() === account.id.toString()
            );

            if (accountChoice) {
              newFieldValue = accountChoice.custom_option;
              newValue = accountChoice.custom_option;
              console.log(`Setting dropdown field to choice: ${accountChoice.custom_option} (ID: ${accountChoice.id})`);
            } else {
              // If no matching choice found, use account name as fallback
              newFieldValue = account.name;
              newValue = account.name;
              console.log(`No matching choice found, using account name: ${account.name}`);
            }
          } else {
            // For other field types (text, number, etc.), use account ID
            newFieldValue = account.id.toString();
            newValue = account.id.toString();
            console.log(`Setting non-dropdown field to account ID: ${account.id}`);
          }

          console.log(`Updating from "${field.field_value}" to "${newFieldValue}"`);

          return {
            ...field,
            field_value: newFieldValue,
            value: newValue // Include both field_value and value like the edit page does
          };
        }
        return field;
      });

      if (!salesAccountsFieldFound) {
        console.warn('sales_accounts field not found in contact fields!');
        console.log('Available field names:', contactFields.map((f: any) => f.field_name));
      }

      // Prepare the update payload (same structure as contact edit page)
      const updatePayload = {
        contact: updatedFields
      };

      console.log('Updating contact with account:', {
        contactId,
        accountId: account.id,
        accountName: account.name
      });

      console.log('Contact update API request body:', JSON.stringify(updatePayload, null, 2));
      console.log('Contact update API endpoint:', `${API_ENDPOINTS.CONTACTS}/${contactId}`);

      // Update the contact
      const updateResponse = await apiService.put(
        `${API_ENDPOINTS.CONTACTS}/${contactId}`,
        updatePayload
      );

      console.log('Contact update API response:', JSON.stringify(updateResponse.data, null, 2));

      if (updateResponse.status === 200 || updateResponse.status === 201) {
        showSnackbar(`Account "${account.name}" has been added to the contact successfully`);
        router.back(); // Go back to RelatedAccountsList
      } else {
        throw new Error(updateResponse.data?.message || 'Failed to update contact');
      }
    } catch (err) {
      console.error('Error updating contact with account:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to add account to contact';
      showSnackbar(errorMessage);
      Alert.alert('Error', errorMessage);
    } finally {
      setIsUpdating(false);
    }
  };

  const renderAccountItem = ({ item }: { item: DisplayAccount }) => (
    <TouchableOpacity 
      style={styles.accountContainer}
      onPress={() => handleAccountSelect(item)}
      disabled={isUpdating}
    >
      <View style={styles.accountContent}>
        <View style={[styles.accountAvatar, { backgroundColor: '#0F96BB' }]}>
          <Text style={styles.accountInitial}>{item.initial}</Text>
        </View>
        <View style={styles.accountInfo}>
          <Text style={styles.accountName}>{item.name}</Text>
          {item.phone && <Text style={styles.accountDetail}>📞 {item.phone}</Text>}
          {item.website && <Text style={styles.accountDetail}>🌐 {item.website}</Text>}
        </View>
        <Ionicons name="chevron-forward" size={20} color="#666" />
      </View>
    </TouchableOpacity>
  );

  if (isLoading) {
    return (
      <SafeAreaView style={[styles.safeAreaContainer, { paddingTop: insets.top }]}>
        <StatusBar {...STATUS_BAR_CONFIG} />
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Ionicons name={Platform.OS === 'ios' ? 'chevron-back' : 'arrow-back'}  size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <View style={styles.headerContent}>
            <Text style={styles.headerTitleText}>Select Account</Text>
          </View>
          <View style={styles.headerRight} />
        </View>
        <View style={styles.mainContainer}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#0F96BB" />
            <Text style={styles.loadingText}>Loading accounts...</Text>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.safeAreaContainer, { paddingTop: insets.top }]}>
      <StatusBar {...STATUS_BAR_CONFIG} />
      <Stack.Screen options={{ headerShown: false }} />

      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Ionicons name={Platform.OS === 'ios' ? 'chevron-back' : 'arrow-back'}  size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitleText}>Select Account</Text>
        </View>
        <View style={styles.headerRight} />
      </View>

      <View style={styles.mainContainer}>
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search accounts..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor="#999"
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery('')} style={styles.clearButton}>
                <Ionicons name="close-circle" size={20} color="#666" />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {error ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity style={styles.retryButton} onPress={fetchAccounts}>
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <FlatList
            data={filteredAccounts}
            renderItem={renderAccountItem}
            keyExtractor={(item) => item.id.toString()}
            refreshControl={
              <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
            }
            contentContainerStyle={styles.listContent}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="business-outline" size={80} color="#ccc" />
                <Text style={styles.emptyText}>
                  {searchQuery ? 'No accounts found matching your search' : 'No accounts available'}
                </Text>
              </View>
            }
          />
        )}

        {isUpdating && (
          <View style={styles.updatingOverlay}>
            <View style={styles.updatingContainer}>
              <ActivityIndicator size="large" color="#0F96BB" />
              <Text style={styles.updatingText}>Adding account...</Text>
            </View>
          </View>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeAreaContainer: {
    flex: 1,
    backgroundColor: '#0F96BB',
  },
  mainContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#0F96BB',
    height: Platform.OS === 'ios' ? 44 : 56,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
    zIndex: 1000,
    paddingHorizontal: Platform.OS === 'ios' ? 8 : 0,
  },
  backButton: {
    width: Platform.OS === 'ios' ? 44 : 56,
    height: Platform.OS === 'ios' ? 44 : 56,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: Platform.OS === 'ios' ? -8 : 0,
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: Platform.OS === 'ios' ? 4 : 16,
  },
  headerRight: {
    width: Platform.OS === 'ios' ? 44 : 56,
    height: Platform.OS === 'ios' ? 44 : 56,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Platform.OS === 'ios' ? -8 : 0,
  },
  headerTitleText: {
    color: '#FFFFFF',
    fontSize: Platform.OS === 'ios' ? 17 : 20,
    fontWeight: Platform.OS === 'ios' ? '600' : '500',
    textAlign: 'center',
  },
  searchContainer: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 40,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  clearButton: {
    marginLeft: 8,
  },
  listContent: {
    flexGrow: 1,
    paddingTop: 8,
  },
  accountContainer: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginVertical: 4,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  accountContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  accountAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  accountInitial: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  accountInfo: {
    flex: 1,
  },
  accountName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  accountDetail: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#666',
    fontSize: 16,
    marginTop: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#ff0000',
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#0F96BB',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 16,
  },
  updatingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  updatingContainer: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  updatingText: {
    color: '#333',
    fontSize: 16,
    marginTop: 10,
  },
});
