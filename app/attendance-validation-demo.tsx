/**
 * Attendance Validation Demo Page
 * Demonstrates the attendance validation functionality for tasks and meetings
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  RefreshControl
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useColors } from '../hooks/useThemeColor';
import useAttendanceValidation from '../hooks/useAttendanceValidation';
import AttendanceStatusIndicator from '../components/AttendanceStatusIndicator';
import { attendanceStorageService } from '../services/AttendanceStorageService';
import { convertDate, DATE_FORMAT } from './ui/utils';

export default function AttendanceValidationDemoScreen() {
  const [refreshing, setRefreshing] = useState(false);
  const colors = useColors();
  const router = useRouter();
  
  const { 
    validateAndExecute, 
    canPerformActions, 
    attendanceStatus,
    validationResult,
    refreshAttendanceStatus
  } = useAttendanceValidation();

  const onRefresh = async () => {
    setRefreshing(true);
    await refreshAttendanceStatus();
    setRefreshing(false);
  };

  // Demo functions for different actions
  const demoTaskComplete = async () => {
    await validateAndExecute(async () => {
      Alert.alert('Success', 'Task marked as complete!');
    }, 'mark task as complete');
  };

  const demoTaskIncomplete = async () => {
    await validateAndExecute(async () => {
      Alert.alert('Success', 'Task marked as incomplete!');
    }, 'mark task as incomplete');
  };

  const demoMeetingCheckIn = async () => {
    await validateAndExecute(async () => {
      Alert.alert('Success', 'Checked in to meeting!');
    }, 'check in to meeting');
  };

  const demoMeetingCheckOut = async () => {
    await validateAndExecute(async () => {
      Alert.alert('Success', 'Checked out from meeting!');
    }, 'check out from meeting');
  };

  const changeAttendanceStatus = async (newStatus: string) => {
    try {
      const today = convertDate(new Date().toISOString(), DATE_FORMAT.SYSTEM_FORMAT, DATE_FORMAT.DATE_FORMAT);
      
      await attendanceStorageService.updateAttendanceStatus(today, newStatus, {
        checkin_time: newStatus === 'Inprogress' ? '09:00 AM' : null,
        checkout_time: newStatus === 'Completed' ? '05:30 PM' : null,
        location: 'Demo Location'
      });
      
      await refreshAttendanceStatus();
      Alert.alert('Success', `Attendance status changed to ${newStatus}`);
    } catch (error) {
      console.error('Error changing attendance status:', error);
      Alert.alert('Error', 'Failed to change attendance status');
    }
  };

  const actionButtons = [
    {
      title: 'Complete Task',
      icon: 'checkmark-circle-outline',
      action: demoTaskComplete,
      color: '#4CD964'
    },
    {
      title: 'Mark Task Incomplete',
      icon: 'close-circle-outline',
      action: demoTaskIncomplete,
      color: '#FF9500'
    },
    {
      title: 'Meeting Check-In',
      icon: 'log-in-outline',
      action: demoMeetingCheckIn,
      color: '#007AFF'
    },
    {
      title: 'Meeting Check-Out',
      icon: 'log-out-outline',
      action: demoMeetingCheckOut,
      color: '#FF3B30'
    }
  ];

  const statusButtons = [
    {
      title: 'Set Pending',
      status: 'Pending',
      color: '#FF9500'
    },
    {
      title: 'Set In Progress',
      status: 'Inprogress',
      color: '#4CD964'
    },
    {
      title: 'Set Completed',
      status: 'Completed',
      color: '#007AFF'
    }
  ];

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <Stack.Screen 
        options={{ 
          title: 'Attendance Validation Demo',
          headerShown: true,
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: 'white',
          headerTitleStyle: { fontWeight: 'bold' }
        }} 
      />
      
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Current Status Card */}
        <View style={[styles.card, { backgroundColor: colors.background.secondary }]}>
          <Text style={[styles.cardTitle, { color: colors.text.primary }]}>
            Current Attendance Status
          </Text>
          <AttendanceStatusIndicator 
            showDetails={true}
            onPress={() => router.push('/(tabs)/' as any)}
          />
          
          {attendanceStatus && (
            <View style={styles.statusDetails}>
              <Text style={[styles.detailText, { color: colors.text.secondary }]}>
                Check-in: {attendanceStatus.checkin_time || 'Not checked in'}
              </Text>
              <Text style={[styles.detailText, { color: colors.text.secondary }]}>
                Check-out: {attendanceStatus.checkout_time || 'Not checked out'}
              </Text>
              <Text style={[styles.detailText, { color: colors.text.secondary }]}>
                Location: {attendanceStatus.location || 'No location'}
              </Text>
            </View>
          )}
        </View>

        {/* Validation Rules Card */}
        <View style={[styles.card, { backgroundColor: colors.background.secondary }]}>
          <Text style={[styles.cardTitle, { color: colors.text.primary }]}>
            Validation Rules
          </Text>
          <View style={styles.rulesContainer}>
            <View style={styles.ruleItem}>
              <Ionicons name="close-circle" size={16} color="#FF3B30" />
              <Text style={[styles.ruleText, { color: colors.text.secondary }]}>
                Pending: Actions blocked - Punch in required
              </Text>
            </View>
            <View style={styles.ruleItem}>
              <Ionicons name="checkmark-circle" size={16} color="#4CD964" />
              <Text style={[styles.ruleText, { color: colors.text.secondary }]}>
                In Progress: All actions allowed
              </Text>
            </View>
            <View style={styles.ruleItem}>
              <Ionicons name="close-circle" size={16} color="#FF3B30" />
              <Text style={[styles.ruleText, { color: colors.text.secondary }]}>
                Completed: Actions blocked - Attendance closed
              </Text>
            </View>
          </View>
        </View>

        {/* Change Status Card */}
        <View style={[styles.card, { backgroundColor: colors.background.secondary }]}>
          <Text style={[styles.cardTitle, { color: colors.text.primary }]}>
            Change Attendance Status (Demo)
          </Text>
          <View style={styles.buttonGrid}>
            {statusButtons.map((button, index) => (
              <TouchableOpacity
                key={index}
                style={[styles.statusButton, { backgroundColor: button.color }]}
                onPress={() => changeAttendanceStatus(button.status)}
              >
                <Text style={styles.statusButtonText}>{button.title}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Test Actions Card */}
        <View style={[styles.card, { backgroundColor: colors.background.secondary }]}>
          <Text style={[styles.cardTitle, { color: colors.text.primary }]}>
            Test Actions
          </Text>
          <Text style={[styles.cardSubtitle, { color: colors.text.secondary }]}>
            Try these actions to see attendance validation in action
          </Text>
          
          <View style={styles.actionGrid}>
            {actionButtons.map((button, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.actionButton,
                  { 
                    backgroundColor: canPerformActions ? button.color : '#E0E0E0',
                    opacity: canPerformActions ? 1 : 0.6
                  }
                ]}
                onPress={button.action}
              >
                <Ionicons 
                  name={button.icon as any} 
                  size={24} 
                  color={canPerformActions ? 'white' : '#999'} 
                />
                <Text style={[
                  styles.actionButtonText,
                  { color: canPerformActions ? 'white' : '#999' }
                ]}>
                  {button.title}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Current Validation Result */}
        {validationResult && (
          <View style={[styles.card, { backgroundColor: colors.background.secondary }]}>
            <Text style={[styles.cardTitle, { color: colors.text.primary }]}>
              Validation Result
            </Text>
            <View style={styles.validationResult}>
              <Text style={[styles.validationText, { color: colors.text.secondary }]}>
                Can Perform Actions: {canPerformActions ? 'Yes' : 'No'}
              </Text>
              <Text style={[styles.validationText, { color: colors.text.secondary }]}>
                Status: {validationResult.status || 'Unknown'}
              </Text>
              <Text style={[styles.validationText, { color: colors.text.secondary }]}>
                Message: {validationResult.message}
              </Text>
            </View>
          </View>
        )}

        {/* Navigation Card */}
        <View style={[styles.card, { backgroundColor: colors.background.secondary }]}>
          <Text style={[styles.cardTitle, { color: colors.text.primary }]}>
            Navigation
          </Text>
          <TouchableOpacity
            style={[styles.navButton, { backgroundColor: colors.primary }]}
            onPress={() => router.push('/(tabs)/' as any)}
          >
            <Ionicons name="home-outline" size={20} color="white" />
            <Text style={styles.navButtonText}>Go to Home Page</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.navButton, { backgroundColor: '#007AFF' }]}
            onPress={() => router.push('/attendance-storage-demo' as any)}
          >
            <Ionicons name="archive-outline" size={20} color="white" />
            <Text style={styles.navButtonText}>View Storage Demo</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  card: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  cardSubtitle: {
    fontSize: 14,
    marginBottom: 16,
    lineHeight: 20,
  },
  statusDetails: {
    marginTop: 12,
    gap: 4,
  },
  detailText: {
    fontSize: 12,
  },
  rulesContainer: {
    gap: 12,
  },
  ruleItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  ruleText: {
    fontSize: 14,
    flex: 1,
  },
  buttonGrid: {
    flexDirection: 'row',
    gap: 8,
    flexWrap: 'wrap',
  },
  statusButton: {
    flex: 1,
    minWidth: 100,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  statusButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  actionGrid: {
    gap: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 8,
    gap: 12,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  validationResult: {
    gap: 8,
  },
  validationText: {
    fontSize: 14,
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
    gap: 8,
  },
  navButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
});
