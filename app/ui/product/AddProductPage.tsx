import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Modal,
} from 'react-native';
import { Appbar } from 'react-native-paper';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { apiService } from '../../../services/ApiService';
import { showSnackbar } from '../utils';
import { 
  ProductField, 
  ProductFieldsResponse, 
  validateFieldValue,
  getActiveFields 
} from '../../models/ModelProductFileOption';
import { ContactField } from '@/app/models/contact_fields_model';
import { DynamicFormField } from '@/app/dynamic_fields/DynamicFormField';
import { API_ENDPOINTS } from '@/config/api';
import { ProductDetails } from '@/app/models/ProductDetailsModel';
// Mock categories data - replace with your API data
const CATEGORIES = [
  { id: 1, name: 'Electronics' },
  { id: 2, name: 'Clothing' },
  { id: 3, name: 'Books' },
  { id: 4, name: 'Home & Garden' },
  { id: 5, name: 'Sports' },
  { id: 6, name: 'Toys' },
];
interface Category {
  id: number;
  name: string;
}
interface ProductForm {
  name: string;
  category: Category | null;
}
interface ProductData {
  product: {
    id: string;
    fields: Array<{
      field_name: string;
      field_value: string;
      field_type: string;
      field_label: string;
      value: string;
    }>;
  };
}
export default function AddProductPage() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const productId = params.productId as string;
  const [productDetails, setProductDetails] = useState<ProductDetails | null>(null);
  
  useEffect(() => {
    // Parse and set product details when component mounts
    try {
      if (params.productDetails) {
        const details = typeof params.productDetails === 'string' 
          ? JSON.parse(decodeURIComponent(params.productDetails))
          : params.productDetails;
        console.log('detailssldkfjdsklf', details.products.length);
        setProductDetails(details);
      }
    } catch (error) {
      console.error('Error parsing product details:', error);
    }
  }, [params.productDetails]);
  const isEditing = !!productId;
  const [fields, setFields] = useState<ContactField[]>([]);
  const [formValues, setFormValues] = useState<{ [key: string]: any }>({});
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [loading, setLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showCategorySheet, setShowCategorySheet] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [interactedFields, setInteractedFields] = useState<Set<string>>(new Set());
  useEffect(() => {
    fetchProductFields();
  }, [productDetails]); // Add productDetails as dependency
  const fetchProductFields = async () => {
    try {
      const fieldsResponse = await apiService.get<ProductFieldsResponse>(API_ENDPOINTS.PRODUCT_FIELDS);
      
      if (fieldsResponse?.data?.productFields && Array.isArray(fieldsResponse.data.productFields)) {
        const activeFields = getActiveFields(fieldsResponse.data.productFields);
        setFields(activeFields);
        console.log('activeFieldsskdljfdf ', "skdljfdf "+productDetails?.products?.length);
        // Initialize form values
        const initialValues: { [key: string]: any } = {};
        activeFields.forEach(field => {
          if (isEditing ) {
            // Find matching field in product details
            const existingField = productDetails?.products.find(
              f => f.field_name === field.field_name
            );
            
            // Use existing value or default
            initialValues[field.field_name] = existingField?.field_value || field.field_value || '';
          } else {
            // If creating new, use default values
            initialValues[field.field_name] = field.field_value || '';
          }
        });
        
        setFormValues(initialValues);
      } else {
        console.error('Invalid response structure:', fieldsResponse);
        showSnackbar('Failed to load product fields: Invalid response format');
      }
    } catch (error) {
      console.error('Error fetching product data:', error);
      showSnackbar('Failed to load product data');
    } finally {
      setIsLoading(false);
    }
  };
  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};
    let isValid = true;
    fields.forEach(field => {
      if (field.required === 'on') {
        const value = formValues[field.field_name];
        if (!validateFieldValue(field, value)) {
          newErrors[field.field_name] = `${field.field_label} is required`;
          isValid = false;
        }
      }
    });
    setErrors(newErrors);
    return isValid;
  };
  const handleSave = async () => {
    if (!validateForm()) {
      showSnackbar('Please fill in all required fields correctly');
      return;
    }
    setLoading(true);
    try {
      const prepareApiData = () => {
        const activeFields = fields
          .filter(field => field.active)
          .map(field => {
            let value = formValues[field.field_name] || '';
            
            // For dropdown fields, handle the value differently
            if (field.field_type === 'dropdown' && value) {
              const selectedChoice = field.choices?.find(c => c.id.toString() === value);
              return {
                ...field, // Include all field properties
                field_value: selectedChoice ? selectedChoice.custom_option : value,
                value: selectedChoice ? selectedChoice.custom_option : value
              };
            }
            
            return {
              ...field, // Include all field properties
              field_value: value,
              value: value
            };
          });
        return { product: activeFields };
      };
      const apiData = prepareApiData();
      let response;
      
      if (isEditing) {
        response = await apiService.put(`${API_ENDPOINTS.PRODUCTS}/${productId}`, apiData);
      } else {
        response = await apiService.post(API_ENDPOINTS.PRODUCTS, apiData);
      }
      
      if (response.data) {
        showSnackbar(`Product ${isEditing ? 'updated' : 'added'} successfully`);
        router.back();
      } else {
        throw new Error(response.data?.message || `Failed to ${isEditing ? 'update' : 'create'} product`);
      }
    } catch (error) {
      showSnackbar(error instanceof Error ? error.message : `Failed to ${isEditing ? 'update' : 'add'} product`);
    } finally {
      setLoading(false);
    }
  };
  const handleFieldChange = (fieldName: string, value: any) => {
    setFormValues(prev => ({
      ...prev,
      [fieldName]: value
    }));
    // Mark field as interacted
    setInteractedFields(prev => new Set(prev).add(fieldName));
    if (errors[fieldName]) {
      setErrors(prev => ({
        ...prev,
        [fieldName]: ''
      }));
    }
  };
  const filteredCategories = CATEGORIES.filter(category =>
    category.name.toLowerCase().includes(searchQuery.toLowerCase())
  );
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#fff',
    },
    appbar: {
      backgroundColor: '#0F96BB',
      elevation: 0,
      height: 56,
    },
    content: {
      flex: 1,
      paddingLeft: 16,
      paddingRight: 16,
      marginBottom: 16,
      marginTop:16,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    inputContainer: {
      marginBottom: 20,
    },
    label: {
      fontSize: 16,
      color: '#333',
      marginBottom: 8,
    },
    required: {
      color: 'red',
      marginLeft: 4,
    },
    input: {
      height: 48,
      borderWidth: 1,
      borderColor: '#E0E0E0',
      borderRadius: 8,
      paddingHorizontal: 16,
      fontSize: 16,
      color: '#333',
      backgroundColor: '#fff',
    },
    errorInput: {
      borderColor: 'red',
    },
    errorText: {
      color: 'red',
      fontSize: 12,
      marginTop: 4,
    },
    categoryButton: {
      height: 48,
      borderWidth: 1,
      borderColor: '#E0E0E0',
      borderRadius: 8,
      paddingHorizontal: 16,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: '#fff',
    },
    categoryText: {
      fontSize: 16,
      color: '#333',
    },
    placeholderText: {
      color: '#999',
    },
    modalContainer: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'flex-end',
    },
    bottomSheet: {
      backgroundColor: '#fff',
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      maxHeight: '80%',
    },
    bottomSheetHeader: {
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: '#E0E0E0',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    bottomSheetTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: '#333',
    },
    searchContainer: {
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: '#E0E0E0',
    },
    searchInput: {
      height: 40,
      backgroundColor: '#F5F5F5',
      borderRadius: 8,
      paddingHorizontal: 16,
      fontSize: 16,
    },
    categoryList: {
      padding: 16,
    },
    categoryItem: {
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: '#E0E0E0',
    },
    categoryItemText: {
      fontSize: 16,
      color: '#333',
    },
  });
  if (isLoading) {
    return (
      <SafeAreaView style={styles.container} edges={['top']}>
        <Appbar.Header style={styles.appbar} statusBarHeight={0}>
          <Appbar.BackAction color="#fff" onPress={() => router.back()} />
          <Appbar.Content 
            title={isEditing ? "Edit Product" : "Add Product"}
            titleStyle={{ color: '#fff', fontSize: 20, fontWeight: '600' }}
          />
        </Appbar.Header>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0F96BB" />
        </View>
      </SafeAreaView>
    );
  }
  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Appbar.Header style={styles.appbar} statusBarHeight={0}>
        <Appbar.BackAction color="#fff" onPress={() => router.back()} />
        <Appbar.Content 
          title={isEditing ? "Edit Product" : "Add Product"}
          titleStyle={{ color: '#fff', fontSize: 20, fontWeight: '600' }}
        />
        {loading ? (
          <ActivityIndicator color="#fff" style={{ marginRight: 8 }} />
        ) : (
          <Appbar.Action icon="check" color="#fff" onPress={handleSave} />
        )}
      </Appbar.Header>
      <ScrollView style={styles.content}>
        {fields.map(field => (
          <DynamicFormField
            key={field.id}
            field={field}
            value={formValues[field.field_name]}
            onChangeText={(value) => handleFieldChange(field.field_name, value)}
            error={errors[field.field_name]}
            hasInteracted={interactedFields.has(field.field_name)}
          />
        ))}
      </ScrollView>
    </SafeAreaView>
  );
} 