import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  TextInput,
  ScrollView,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Appbar, IconButton, <PERSON>u, Card } from 'react-native-paper';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { apiService } from '../../../services/ApiService';
import { API_ENDPOINTS } from '../../../config/api';
import { CALL_FROM, log, showSnackbar } from '../utils';
import {
  ProductFilterField,
  ProductFilterFieldsResponse,
  getLookupValues,
  isLookupField,
  isDropdownField,
  prepareFilterData,
  isDateField,
  isNumberField,
  isTextField,
} from '@/app/models/ModelProductFilterFields';
import { ModelShareWithUsers, ModelShareWithUsersResponse } from '@/app/models/ModelShareWithUsers';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { DISTRIBUTORS_FILTER_STORAGE_KEY } from '@/app/(tabs)/distributors';
import { CONTACTS_FILTER_STORAGE_KEY } from '@/app/(tabs)/contacts';
import { ORDERS_FILTER_STORAGE_KEY } from '@/app/(tabs)/orders';

interface FilterItem {
  field: ProductFilterField;
  value: any;
  isExpanded?: boolean;
  displayValue?: string;
  operator: string;
  selectedValues?: string[];
}

const getOperatorsByFieldType = (field: ProductFilterField) => {
  //console.log("fieldsdkjfdlf ",field.field_label+" list "+JSON.stringify(field.contains_list));
    return field.contains_list;
};

// Add these static data at the top of the file after imports
const SHARE_OPTIONS = [
  { id: 'just_me', label: 'Just Me' },
  { id: 'everyone', label: 'Everyone' },
  { id: 'selected_users', label: 'Selected users, teams and territories' },
];

const ACCESSIBILITY_OPTIONS = [
  { id: 'View Only', label: 'Can view only' },
  { id: 'Public', label: 'Can view and edit' },
];

// Add storage key constant after imports
const PRODUCTS_FILTER_STORAGE_KEY = '@products_filter';

export default function ProductFiltersPage() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const callFrom = params.callFrom as string;
  const [loading, setLoading] = useState(false);
  const [fields, setFields] = useState<ProductFilterField[]>([]);
  const [showFieldSelector, setShowFieldSelector] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilters, setSelectedFilters] = useState<FilterItem[]>([]);
  const [showValueSelector, setShowValueSelector] = useState(false);
  const [showOperatorSelector, setShowOperatorSelector] = useState(false);
  const [activeFilterIndex, setActiveFilterIndex] = useState<number>(-1);
  const [showSaveView, setShowSaveView] = useState(false);
  const [viewTitle, setViewTitle] = useState('');
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [preventSharing, setPreventSharing] = useState(false);
  const [selectedShareOption, setSelectedShareOption] = useState('just_me');
  const [selectedAccessibility, setSelectedAccessibility] = useState('View Only');
  const [shareWithOptions, setShareWithOptions] = useState<ModelShareWithUsers | null>(null);
  const [loadingShareOptions, setLoadingShareOptions] = useState(false);
  const [filterErrors, setFilterErrors] = useState<{[key: number]: string}>({});

  useEffect(() => {
    fetchFields();
  }, []);

  const fetchFields = async () => {
    console.log("callFrominFilterPage",callFrom);
    try {
      setLoading(true);
      var endpoint =  API_ENDPOINTS.PRODUCT_FILTERS_ADD_LIST;//callFrom ? `${API_ENDPOINTS.FILTERS_LIST}?callFrom=${callFrom}` : API_ENDPOINTS.FILTERS_LIST;
      if(callFrom == CALL_FROM.CONTACT_LIST){
        endpoint = API_ENDPOINTS.CONTACT_FILTERS_ADD_LIST;
      }else if(callFrom == CALL_FROM.PRODUCT_LIST){
        endpoint = API_ENDPOINTS.PRODUCT_FILTERS_ADD_LIST;
      }else if(callFrom == CALL_FROM.DEAL_LIST){
        endpoint = API_ENDPOINTS.DEAL_FILTERS_ADD_LIST;
      }else if(callFrom == CALL_FROM.ACCOUNT_LIST){
        endpoint = API_ENDPOINTS.ACCOUNT_FILTERS_ADD_LIST;
      }
      //const response = await apiService.post<ProductFilterFieldsResponse>(API_ENDPOINTS.FILTERS_LIST);
      const response = await apiService.post<ProductFilterFieldsResponse>(endpoint);
      console.log("responseskdjf",JSON.stringify(response.data));
      if (response?.data?.response) {
        setFields(response.data.response);
      }
    } catch (error) {
      console.error('Error fetching fields:', error);
      showSnackbar('Failed to load filter fields');
    } finally {
      setLoading(false);
    }
  };

  const fetchShareWithOptions = async () => {
    try {
      setLoadingShareOptions(true);
      const response = await apiService.get<ModelShareWithUsersResponse>(API_ENDPOINTS.SARE_WITH_USERS);
      
      if (response?.data) {
        const model = ModelShareWithUsers.fromResponse(response.data);
        if (model) {
          setShareWithOptions(model);
        } else {
          showSnackbar(response.data.message || 'Failed to load share options');
        }
      }
    } catch (error) {
      console.error('Error fetching share options:', error);
      showSnackbar('Failed to load share options');
    } finally {
      setLoadingShareOptions(false);
    }
  };

  const handleAddFilter = () => {
    setShowFieldSelector(true);
  };

  const handleFieldSelect = (field: ProductFilterField) => {
    const newFilter: FilterItem = {
      field: field,
      value: '',
      isExpanded: true,
      displayValue: '',
      operator: ''+field.contains_list[0].name,
      selectedValues: []
    };
    const newIndex = selectedFilters.length;
    setSelectedFilters([...selectedFilters, newFilter]);
    setShowFieldSelector(false);
    
    if (isDropdownField(field)) {
      setActiveFilterIndex(newIndex);
      setShowValueSelector(true);
    }
  };

  const handleValueChange = (value: string, index: number) => {
    const newFilters = [...selectedFilters];
    newFilters[index].value = value;
    newFilters[index].displayValue = value;
    setSelectedFilters(newFilters);
  };

  const handleDropdownSelect = (value: string, displayValue: string) => {
    if (activeFilterIndex >= 0) {
      const newFilters = [...selectedFilters];
      const filter = newFilters[activeFilterIndex];
      const isMultiSelect = filter.operator === 'contains' || filter.operator === 'does not contain';

      if (isMultiSelect) {
        const selectedValues = filter.selectedValues || [];
        const valueIndex = selectedValues.indexOf(value);
        
        if (valueIndex === -1) {
          selectedValues.push(value);
        } else {
          selectedValues.splice(valueIndex, 1);
        }
        
        filter.selectedValues = selectedValues;
        filter.value = selectedValues.join(',');
        filter.displayValue = selectedValues.length > 0 
          ? selectedValues.join(', ')
          : '';
      } else {
        filter.value = value;
        filter.displayValue = displayValue;
        filter.selectedValues = [value];
      }
      
      setSelectedFilters(newFilters);
      if (!isMultiSelect) {
        setShowValueSelector(false);
        setActiveFilterIndex(-1);
      }
    }
  };

  const handleOperatorSelect = (operator: string) => {
    if (activeFilterIndex >= 0) {
      const newFilters = [...selectedFilters];
      const oldOperator = newFilters[activeFilterIndex].operator;
      newFilters[activeFilterIndex].operator = operator;
      
      const wasMultiSelect = oldOperator === 'contains' || oldOperator === 'does not contain';
      const isMultiSelect = operator === 'contains' || operator === 'does not contain';
      
      if (wasMultiSelect !== isMultiSelect) {
        newFilters[activeFilterIndex].value = '';
        newFilters[activeFilterIndex].displayValue = '';
        newFilters[activeFilterIndex].selectedValues = [];
      }
      
      setSelectedFilters(newFilters);
      setShowOperatorSelector(false);
    }
  };

  const toggleExpand = (index: number) => {
    const newFilters = [...selectedFilters];
    newFilters[index].isExpanded = !newFilters[index].isExpanded;
    setSelectedFilters(newFilters);
  };

  const handleApplyFilters = async () => {
    try {
      if (!validateFilters()) {
        return;
      }
 const filter = selectedFilters.map(filter => 
        prepareFilterData(filter.field, filter.value, filter.operator)
      );
      // Prepare filter data matching the FilterType structure
      const filterData = {
        accessibility: "public",
        active: 1,
        contains: selectedFilters[0]?.operator || "contains",
        created_at: null,
        field_id: selectedFilters[0]?.field.field_name || "",
        fileds: filter,
        filter:filter, //JSON.stringify(selectedFilters[0]?.selectedValues || [selectedFilters[0]?.value]),
        filterid: null,
        id: null,
        name: "Custom Filter",
        share_with: "Just Me",
        teams: null,
        territories: null,
        updated_at: null,
        users: null,
        view_id: null
      };

      console.log("filterData", "callFrom " + callFrom + " \n " + JSON.stringify(filterData));

      
    console.log('📱 Saving filter to storage:', filterData);
      // Navigate back to the root tab with filters
      if (callFrom === CALL_FROM.PRODUCT_LIST) {
         await AsyncStorage.setItem(PRODUCTS_FILTER_STORAGE_KEY, JSON.stringify(filterData));
        router.replace('/ui/product/ProductListPage' as any);
      } else if (callFrom === CALL_FROM.CONTACT_LIST) {
        await AsyncStorage.setItem(CONTACTS_FILTER_STORAGE_KEY, JSON.stringify(filterData));
        router.replace('/contacts' as any);
      } else if (callFrom === CALL_FROM.DEAL_LIST) {
        await AsyncStorage.setItem(ORDERS_FILTER_STORAGE_KEY, JSON.stringify(filterData));
        router.replace('/orders' as any);
      } else if (callFrom === CALL_FROM.ACCOUNT_LIST) {
        await AsyncStorage.setItem(DISTRIBUTORS_FILTER_STORAGE_KEY, JSON.stringify(filterData));
        router.replace('/distributors' as any);
      }

      // Store the filters in AsyncStorage for the destination page to pick up
      await AsyncStorage.setItem('selectedFilters', JSON.stringify(filterData));
      
    } catch (error) {
      console.error('Error preparing filters:', error);
      showSnackbar('Failed to prepare filters');
    }
  };

const getApplyEndPoint = (callFrom: string) => {
  console.log('callFromFiledsd', callFrom);
  if(callFrom == CALL_FROM.CONTACT_LIST){
    return API_ENDPOINTS.CONTACTS_FILTERS;
  }
  else if(callFrom == CALL_FROM.PRODUCT_LIST){   
    return API_ENDPOINTS.SAVE_VIEW;
  }
  else if(callFrom == CALL_FROM.DEAL_LIST){
    return API_ENDPOINTS.DEALS_FILTERS;
  }
  else if(callFrom == CALL_FROM.ACCOUNT_LIST){
    return API_ENDPOINTS.SALES_ACCOUNTS_FILTERS;
  }
}
  const handleSaveView = async () => {
    try {
      if (!viewTitle.trim()) {
        showSnackbar('Please enter a title for the view');
        return;
      }

      if (selectedShareOption === 'selected_users' && selectedUsers.length === 0) {
        showSnackbar('Please select users or territories');
        return;
      }

      setLoading(true);
      
      // Prepare filter data
      const filterData = selectedFilters.map(filter => 
        prepareFilterData(filter.field, filter.value, filter.operator)
      );

      // Prepare view data
      const viewData = {
        title: viewTitle,
        filter: filterData,
        share_with: selectedShareOption,
        selected_users: selectedShareOption === 'selected_users' ? selectedUsers : [],
        accessibility: selectedAccessibility,
        prevent_sharing: preventSharing,
        callFrom: callFrom
      };
      const endpoints = getApplyEndPoint(callFrom);
      //const endpoint = callFrom ? `${API_ENDPOINTS.SAVE_VIEW}?callFrom=${callFrom}` : API_ENDPOINTS.SAVE_VIEW;
      const response = await apiService.post( endpoints || '', viewData);
      
      if (response.status == 200) {
        Alert.alert('Update Status',
          response.data.message || 'Filters saved successfully',
          [ 
            {
              text: 'OK',
              onPress: () => {
                setShowSaveView(false);
                router.back();
              }
            },
          ]
        );
      }
    } catch (error) {
      console.error('Error saving view:', error);
      showSnackbar('Failed to save view');
    } finally {
      setLoading(false);
    }
  };

  const renderFieldItem = ({ item }: { item: ProductFilterField }) => (
    <TouchableOpacity
      style={styles.listItem}
      onPress={() => handleFieldSelect(item)}
    >
      <Text style={styles.listItemText}>{item.field_label}</Text>
    </TouchableOpacity>
  );

  const renderValueSelector = () => {
    const filter = selectedFilters[activeFilterIndex];
    if (!filter || !isDropdownField(filter.field)) return null;

    const isMultiSelect = filter.operator === 'contains' || filter.operator === 'does not contain';
    const selectedValues = filter.selectedValues || [];

    return (
      <Modal
        visible={showValueSelector}
        animationType="slide"
        transparent={true}
        onRequestClose={() => {
          setShowValueSelector(false);
          setActiveFilterIndex(-1);
        }}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {isMultiSelect ? `Select ${filter.field.field_label} (Multiple)` : `Select ${filter.field.field_label}`}
              </Text>
              <View style={styles.modalHeaderButtons}>
                {isMultiSelect && (
                  <TouchableOpacity
                    style={styles.doneButton}
                    onPress={() => {
                      setShowValueSelector(false);
                      setActiveFilterIndex(-1);
                    }}
                  >
                    <Text style={styles.doneButtonText}>Done</Text>
                  </TouchableOpacity>
                )}
                <IconButton
                  icon="close"
                  size={24}
                  onPress={() => {
                    setShowValueSelector(false);
                    setActiveFilterIndex(-1);
                  }}
                />
              </View>
            </View>
            <FlatList
              data={filter.field.values || []}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.listItem,
                    selectedValues.includes(item.value) && styles.dropdownItemSelected
                  ]}
                  onPress={() => handleDropdownSelect(item.value, item.value)}
                >
                  <Text style={[
                    styles.listItemText,
                    selectedValues.includes(item.value) && styles.dropdownItemTextSelected
                  ]}>
                    {""+item.value}
                  </Text>
                  {selectedValues.includes(item.value) && (
                    <MaterialIcons name="check" size={20} color="#0F96BB" />
                  )}
                </TouchableOpacity>
              )}
              keyExtractor={(item, index) => index.toString()}
            />
          </View>
        </View>
      </Modal>
    );
  };

  const renderOperatorSelector = () => {
    const filter = selectedFilters[activeFilterIndex];
    if (!filter) return null;

    const operators = getOperatorsByFieldType(filter.field);

    return (
      <Modal
        visible={showOperatorSelector}
        animationType="slide"
        transparent={true}
        onRequestClose={() => {
          setShowOperatorSelector(false);
          setActiveFilterIndex(-1);
        }}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Operator for {filter.field.field_label}</Text>
              <IconButton
                icon="close"
                size={24}
                onPress={() => {
                  setShowOperatorSelector(false);
                  setActiveFilterIndex(-1);
                }}
              />
            </View>
            <FlatList
              data={operators}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.listItem}
                  onPress={() => handleOperatorSelect(item.name)}
                >
                  <Text style={styles.listItemText}>{item.name}</Text>
                </TouchableOpacity>
              )}
              keyExtractor={item => item.name}
            />
          </View>
        </View>
      </Modal>
    );
  };

  const validateFilters = () => {
    const errors: {[key: number]: string} = {};
    let hasErrors = false;

    selectedFilters.forEach((filter, index) => {
      const shouldHideValueInput = filter.operator === 'is empty' || filter.operator === 'is not empty';
      if (shouldHideValueInput) return;
      
      if (isDropdownField(filter.field)) {
        if (!filter.selectedValues?.length) {
          errors[index] = `Please select a value for ${filter.field.field_label}`;
          hasErrors = true;
        }
      } else {
        if (!filter.value?.trim()) {
          errors[index] = `Please enter a value for ${filter.field.field_label}`;
          hasErrors = true;
        }
      }
    });

    setFilterErrors(errors);
    return !hasErrors;
  };

  const renderFilterInput = (filter: FilterItem, index: number) => {
    const shouldHideValueInput = filter.operator === 'is empty' || filter.operator === 'is not empty';

    if (isDropdownField(filter.field)) {
      return (
        <View style={styles.filterInputContainer}>
          <TouchableOpacity
            style={styles.dropdownButton}
            onPress={() => {
              setActiveFilterIndex(index);
              setShowOperatorSelector(true);
            }}
          >
            <Text style={styles.dropdownButtonText}>
              {filter.operator || 'Select operator...'}
            </Text>
            <MaterialIcons name="arrow-drop-down" size={24} color="#666" />
          </TouchableOpacity>

          {!shouldHideValueInput && (
            <>
              <TouchableOpacity
                style={[
                  styles.dropdownButton, 
                  filter.selectedValues?.length ? styles.dropdownButtonSelected : {},
                  filterErrors[index] ? styles.inputError : {}
                ]}
                onPress={() => {
                  setActiveFilterIndex(index);
                  setShowValueSelector(true);
                }}
              >
                <Text 
                  style={[
                    styles.dropdownButtonText,
                    filter.selectedValues?.length ? styles.dropdownButtonTextSelected : {},
                    { flex: 1 }
                  ]}
                  numberOfLines={2}
                  ellipsizeMode="tail"
                >
                  {filter.displayValue || 'Select value...'}
                </Text>
                <MaterialIcons name="arrow-drop-down" size={24} color="#666" />
              </TouchableOpacity>
              {filterErrors[index] && (
                <Text style={styles.errorText}>{filterErrors[index]}</Text>
              )}
            </>
          )}
        </View>
      );
    }

    return (
      <View style={styles.filterInputContainer}>
        <TouchableOpacity
          style={styles.dropdownButton}
          onPress={() => {
            setActiveFilterIndex(index);
            setShowOperatorSelector(true);
          }}
        >
          <Text style={styles.dropdownButtonText}>
            {filter.operator || 'Select operator...'}
          </Text>
          <MaterialIcons name="arrow-drop-down" size={24} color="#666" />
        </TouchableOpacity>

        {!shouldHideValueInput && (
          <>
            <TouchableOpacity style={[{display : filter.field.field_type =="Datepicker"?'none':'flex'}]}>
              <TextInput
                style={[styles.filterInput, filterErrors[index] ? styles.inputError : {}]}
                placeholder="Enter value"
                value={filter.displayValue}
                onChangeText={(value) => {
                  handleValueChange(value, index);
                  if (filterErrors[index]) {
                    const newErrors = {...filterErrors};
                    delete newErrors[index];
                    setFilterErrors(newErrors);
                  }
                }}
              />
            </TouchableOpacity>
            {filterErrors[index] && (
              <Text style={styles.errorText}>{filterErrors[index]}</Text>
            )}
          </>
        )}
      </View>
    );
  };

  const renderSaveViewModal = () => {
    const [showShareDropdown, setShowShareDropdown] = useState(false);
    const [showAccessibilityDropdown, setShowAccessibilityDropdown] = useState(false);
    const [showUsersModal, setShowUsersModal] = useState(false);
    const [searchUserQuery, setSearchUserQuery] = useState('');

    const handleShareOptionSelect = async (optionId: string) => {
      setSelectedShareOption(optionId);
      setShowShareDropdown(false);
      
      if (optionId === 'selected_users') {
        try {
          setLoadingShareOptions(true);
          await fetchShareWithOptions();
        } catch (error) {
          console.error('Error loading share options:', error);
        } finally {
          setLoadingShareOptions(false);
        }
      }
    };

    const getShareOptionLabel = () => {
      const option = SHARE_OPTIONS.find(opt => opt.id === selectedShareOption);
      return option?.label || '';
    };

    const getAccessibilityLabel = () => {
      const option = ACCESSIBILITY_OPTIONS.find(opt => opt.id === selectedAccessibility);
      return option?.label || '';
    };

    const getSelectedUsersLabel = () => {
      if (!shareWithOptions) return 'Loading...';
      if (selectedUsers.length === 0) return 'Select users and territories';
      
      const options = shareWithOptions.getAllOptions();
      const selectedItems = options.filter(item => selectedUsers.includes(item.id.toString()));
      const userCount = selectedItems.filter(item => item.type === 'user').length;
      const territoryCount = selectedItems.filter(item => item.type === 'territory').length;
      
      const parts = [];
      if (userCount > 0) parts.push(`${userCount} user${userCount > 1 ? 's' : ''}`);
      if (territoryCount > 0) parts.push(`${territoryCount} ${territoryCount > 1 ? 'territories' : 'territory'}`);
      
      return parts.join(', ');
    };

    const filteredOptions = shareWithOptions
      ? shareWithOptions.getAllOptions()
          .filter(option => 
            option.name.toLowerCase().includes(searchUserQuery.toLowerCase())
          )
      : [];

    const renderUsersModal = () => (
      <Modal
        visible={showUsersModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowUsersModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select users/teams/territories</Text>
              <IconButton
                icon="close"
                size={24}
                onPress={() => setShowUsersModal(false)}
              />
            </View>
            
            <TextInput
              style={styles.searchInput}
              placeholder="Search users and territories..."
              value={searchUserQuery}
              onChangeText={setSearchUserQuery}
            />

            {loadingShareOptions ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#0F96BB" />
              </View>
            ) : (
              <FlatList
                data={filteredOptions}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={[
                      styles.listItem,
                      selectedUsers.includes(item.id.toString()) && styles.dropdownItemSelected
                    ]}
                    onPress={() => {
                      if (selectedUsers.includes(item.id.toString())) {
                        setSelectedUsers(selectedUsers.filter(id => id !== item.id.toString()));
                      } else {
                        setSelectedUsers([...selectedUsers, item.id.toString()]);
                      }
                    }}
                  >
                    <View style={styles.listItemContent}>
                      <Text style={[
                        styles.listItemText,
                        selectedUsers.includes(item.id.toString()) && styles.dropdownItemTextSelected
                      ]}>
                        {item.name}
                      </Text>
                      <Text style={styles.itemType}>
                        {item.type === 'user' ? 'User' : 'Territory'}
                      </Text>
                    </View>
                    {selectedUsers.includes(item.id.toString()) && (
                      <MaterialIcons name="check" size={20} color="#0F96BB" />
                    )}
                  </TouchableOpacity>
                )}
                keyExtractor={item => `${item.type}-${item.id}`}
              />
            )}

            <View style={styles.modalFooter}>
              <TouchableOpacity 
                style={[styles.footerButton, styles.cancelButton]}
                onPress={() => setShowUsersModal(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={[styles.footerButton, styles.saveButton]}
                onPress={() => setShowUsersModal(false)}
              >
                <Text style={styles.saveButtonText}>Done</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );

    return (
      <>
        <Modal
          visible={showSaveView}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowSaveView(false)}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <View style={styles.bottomSheetHeader}>
                <Text style={styles.bottomSheetTitle}>Save as new view</Text>
                <IconButton
                  icon="close"
                  size={24}
                  onPress={() => setShowSaveView(false)}
                />
              </View>

              <ScrollView style={styles.saveViewContent}>
                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>Enter title</Text>
                  <TextInput
                    style={styles.input}
                    value={viewTitle}
                    onChangeText={setViewTitle}
                    placeholder="Enter title"
                  />
                </View>

                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>Share with</Text>
                  <TouchableOpacity 
                    style={[styles.selectBox, showShareDropdown && styles.selectBoxActive]}
                    onPress={() => {
                      setShowShareDropdown(!showShareDropdown);
                      setShowAccessibilityDropdown(false);
                    }}
                  >
                    <Text style={styles.selectText}>{getShareOptionLabel()}</Text>
                    <MaterialIcons 
                      name={showShareDropdown ? "keyboard-arrow-up" : "keyboard-arrow-down"} 
                      size={24} 
                      color={showShareDropdown ? "#0F96BB" : "#666"} 
                    />
                  </TouchableOpacity>
                  {showShareDropdown && (
                    <View style={styles.dropdownList}>
                      {SHARE_OPTIONS.map((option) => (
                        <TouchableOpacity
                          key={option.id}
                          style={[
                            styles.dropdownItem,
                            selectedShareOption === option.id && styles.dropdownItemSelected
                          ]}
                          onPress={() => handleShareOptionSelect(option.id)}
                        >
                          <Text style={[
                            styles.dropdownItemText,
                            selectedShareOption === option.id && styles.dropdownItemTextSelected
                          ]}>
                            {option.label}
                          </Text>
                          {selectedShareOption === option.id && (
                            <MaterialIcons name="check" size={20} color="#0F96BB" />
                          )}
                        </TouchableOpacity>
                      ))}
                    </View>
                  )}
                </View>

                {selectedShareOption === 'selected_users' && (
                  <View style={styles.inputContainer}>
                    <Text style={styles.inputLabel}>Select users/teams/territories</Text>
                    {loadingShareOptions ? (
                      <View style={[styles.selectBox, styles.loadingBox]}>
                        <ActivityIndicator size="small" color="#0F96BB" />
                        <Text style={styles.selectText}>Loading options...</Text>
                      </View>
                    ) : (
                      <TouchableOpacity 
                        style={styles.selectBox}
                        onPress={() => shareWithOptions && setShowUsersModal(true)}
                      >
                        <Text style={[styles.selectText, { flex: 1 }]} numberOfLines={2}>
                          {getSelectedUsersLabel()}
                        </Text>
                        <MaterialIcons name="chevron-right" size={24} color="#666" />
                      </TouchableOpacity>
                    )}
                  </View>
                )}

                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>Accessibility</Text>
                  <TouchableOpacity 
                    style={[styles.selectBox, showAccessibilityDropdown && styles.selectBoxActive]}
                    onPress={() => {
                      setShowAccessibilityDropdown(!showAccessibilityDropdown);
                      setShowShareDropdown(false);
                    }}
                  >
                    <Text style={styles.selectText}>{getAccessibilityLabel()}</Text>
                    <MaterialIcons 
                      name={showAccessibilityDropdown ? "keyboard-arrow-up" : "keyboard-arrow-down"} 
                      size={24} 
                      color={showAccessibilityDropdown ? "#0F96BB" : "#666"} 
                    />
                  </TouchableOpacity>
                  {showAccessibilityDropdown && (
                    <View style={styles.dropdownList}>
                      {ACCESSIBILITY_OPTIONS.map((option) => (
                        <TouchableOpacity
                          key={option.id}
                          style={[
                            styles.dropdownItem,
                            selectedAccessibility === option.id && styles.dropdownItemSelected
                          ]}
                          onPress={() => {
                            setSelectedAccessibility(option.id);
                            setShowAccessibilityDropdown(false);
                          }}
                        >
                          <Text style={[
                            styles.dropdownItemText,
                            selectedAccessibility === option.id && styles.dropdownItemTextSelected
                          ]}>
                            {option.label}
                          </Text>
                          {selectedAccessibility === option.id && (
                            <MaterialIcons name="check" size={20} color="#0F96BB" />
                          )}
                        </TouchableOpacity>
                      ))}
                    </View>
                  )}
                </View>

                <TouchableOpacity 
                  style={styles.checkboxContainer}
                  onPress={() => setPreventSharing(!preventSharing)}
                >
                  <View style={[styles.checkbox, preventSharing && styles.checkboxChecked]}>
                    {preventSharing && <MaterialIcons name="check" size={16} color="#fff" />}
                  </View>
                  <Text style={styles.checkboxLabel}>
                    Prevent users from sharing the view to other people
                  </Text>
                </TouchableOpacity>
              </ScrollView>

              <View style={styles.modalFooter}>
                <TouchableOpacity 
                  style={[styles.footerButton, styles.cancelButton]}
                  onPress={() => setShowSaveView(false)}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity 
                  style={[styles.footerButton, styles.saveButton]}
                  onPress={handleSaveView}
                >
                  <Text style={styles.saveButtonText}>Save</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
        {renderUsersModal()}
      </>
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Appbar.Header style={styles.appbar}>
        <Appbar.BackAction color="#fff" onPress={() => router.back()} />
        <Appbar.Content
          title={`Filters (${selectedFilters.length})`}
          titleStyle={styles.headerTitle}
        />
        <Appbar.Action icon="refresh" color="#fff" onPress={() => setSelectedFilters([])} />
      </Appbar.Header>

      <ScrollView style={styles.content}>
        {selectedFilters.map((filter, index) => (
          <Card key={index} style={styles.filterCard}>
            <TouchableOpacity onPress={() => toggleExpand(index)}>
              <View style={styles.filterHeader}>
                <Text style={styles.filterLabel}>{filter.field.field_label}</Text>
                <IconButton
                  icon={filter.isExpanded ? "chevron-up" : "chevron-down"}
                  size={24}
                />
              </View>
            </TouchableOpacity>
            {filter.isExpanded && (
              <View style={styles.filterContent}>
                {renderFilterInput(filter, index)}
                <IconButton
                  icon="delete-outline"
                  size={24}
                  iconColor="#FF4444"
                  style={styles.deleteButton}
                  onPress={() => {
                    const newFilters = [...selectedFilters];
                    newFilters.splice(index, 1);
                    setSelectedFilters(newFilters);
                  }}
                />
              </View>
            )}
          </Card>
        ))}

        <TouchableOpacity
          style={styles.addFilterButton}
          onPress={handleAddFilter}
        >
          <Text style={styles.addFilterText}>Add filter</Text>
        </TouchableOpacity>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.footerButton, styles.saveViewButton]}
          onPress={() => {
            if (selectedFilters.length === 0) {
              showSnackbar('Please add at least one filter');
              return;
            }

            if (validateFilters()) {
              setShowSaveView(true);
            }
          }}
        >
          <Text style={styles.footerButtonText}>SAVE VIEW</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.footerButton, styles.applyButton]}
          onPress={() => {
            if (selectedFilters.length === 0) {
              showSnackbar('Please add at least one filter');
              return;
            }

            if (validateFilters()) {
              handleApplyFilters();
            }
          }}
        >
          <Text style={[styles.footerButtonText, styles.applyButtonText]}>APPLY</Text>
        </TouchableOpacity>
      </View>

      {/* Field Selector Modal */}
      <Modal
        visible={showFieldSelector}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowFieldSelector(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Field</Text>
              <IconButton
                icon="close"
                size={24}
                onPress={() => setShowFieldSelector(false)}
              />
            </View>
            <TextInput
              style={styles.searchInput}
              placeholder="Search fields..."
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            <FlatList
              data={fields.filter(field => 
                field.field_label.toLowerCase().includes(searchQuery.toLowerCase())
              )}
              renderItem={renderFieldItem}
              keyExtractor={item => item.id.toString()}
            />
          </View>
        </View>
      </Modal>

      

      {/* Operator Selector Modal */}
      {renderOperatorSelector()}

      {/* Value Selector Modal for Dropdowns */}
      
      {renderValueSelector()}
      
      {renderSaveViewModal()}
      
      {loading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color="#0F96BB" />
        </View>
      )}
    </SafeAreaView>
  );
}

const newStyles = StyleSheet.create({
  dropdownButtonSelected: {
    borderColor: '#0F96BB',
  },
  dropdownButtonTextSelected: {
    color: '#0F96BB',
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
  },
  listItemContent: {
    flex: 1,
  },
  itemType: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  loadingBox: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    gap: 10,
  },
  errorText: {
    color: '#FF4444',
    fontSize: 12,
    marginTop: 4,
    marginBottom: 8,
  },
  inputError: {
    borderColor: '#FF4444',
  },
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  appbar: {
    backgroundColor: '#0F96BB',
    elevation: 4,
    marginTop:-50,
  },
  headerTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: '600',
    marginLeft: 16,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  filterCard: {
    marginBottom: 8,
    borderRadius: 8,
    overflow: 'hidden',
  },
  filterHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingLeft: 8,
    paddingRight:8,
    backgroundColor: '#fff',
  },
  filterContent: {
    padding: 16,
    paddingRight: 56,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    position: 'relative',
  },
  filterLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  operatorContainer: {
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#f5f5f5',
    padding: 8,
    borderRadius: 4,
  },
  operatorLabel: {
    fontSize: 14,
    color: '#666',
  },
  filterInputContainer: {
    flex: 1,
    marginRight: 24,
  },
  filterInput: {
    height: 40,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 4,
    paddingHorizontal: 12,
    marginBottom: 8,
    backgroundColor: '#fff',
  },
  dropdownButton: {
    height: 40,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 4,
    paddingHorizontal: 12,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#fff',
  },
  dropdownButtonText: {
    fontSize: 14,
    color: '#333',
  },
  deleteButton: {
    position: 'absolute',
    right: 8,
    top: 16,
  },
  addFilterButton: {
    padding: 16,
    borderRadius: 8,
    marginBottom:24,
    alignItems: 'flex-start',
  },
  addFilterText: {
    color: '#0F96BB',
    fontSize: 16,
    fontWeight: '600',
  },
  footer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  footerButton: {
    flex: 1,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',

  },
  saveViewButton: {
    backgroundColor: '#fff',
    marginRight: 8,
  },
  applyButton: {
    backgroundColor: '#0F96BB',
  },
  footerButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0F96BB',
  },
  applyButtonText: {
    color: '#fff',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  searchInput: {
    margin: 16,
    padding: 8,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  listItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  listItemText: {
    fontSize: 16,
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomSheetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  bottomSheetTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  saveViewContent: {
    padding: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  input: {
    height: 40,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 4,
    paddingHorizontal: 12,
    backgroundColor: '#fff',
  },
  selectBox: {
    height: 40,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 4,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#fff',
  },
  selectText: {
    fontSize: 14,
    color: '#333',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 4,
    marginRight: 8,
  },
  checkboxChecked: {
    backgroundColor: '#0F96BB',
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  cancelButton: {
    backgroundColor: '#fff',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0F96BB',
  },
  saveButton: {
    backgroundColor: '#0F96BB',
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
  checkboxLabel: {
    fontSize: 14,
    color: '#333',
    flex: 1,
    marginLeft: 8,
  },
  selectBoxActive: {
    borderColor: '#0F96BB',
  },
  dropdownList: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#0F96BB',
    borderRadius: 4,
    marginTop: 4,
    maxHeight: 200,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  dropdownItemSelected: {
    backgroundColor: '#F5FBFD',
  },
  dropdownItemText: {
    fontSize: 14,
    color: '#333',
  },
  dropdownItemTextSelected: {
    color: '#0F96BB',
  },
  modalHeaderButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  doneButton: {
    marginRight: 8,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    backgroundColor: '#0F96BB',
  },
  doneButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  ...newStyles,
}); 