import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TextInput,
  Switch,
  Image,
  Platform,
  ScrollView,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import * as ImagePicker from 'expo-image-picker';
import { SafeAreaView } from 'react-native-safe-area-context';

interface FileUploadScreenProps {
  visible: boolean;
  onClose: () => void;
  onUpload: (fileData: any) => void;
}

export default function FileUploadScreen({ visible, onClose, onUpload }: FileUploadScreenProps) {
  const router = useRouter();
  const [selectedFile, setSelectedFile] = useState<any>(null);
  const [fileName, setFileName] = useState('');
  const [shareWithAll, setShareWithAll] = useState(false);

  const handleCamera = async () => {
    try {
      const permissionResult = await ImagePicker.requestCameraPermissionsAsync();
      
      if (permissionResult.granted === false) {
        alert("You've refused to allow this app to access your camera!");
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ['images', 'videos'],
        quality: 1,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        setSelectedFile({
          uri: asset.uri,
          type: asset.type || 'image/jpeg',
          name: `camera_${new Date().getTime()}.jpg`
        });
        setFileName(`Camera_${new Date().getTime()}`);
      }
    } catch (error) {
      console.error('Error capturing image:', error);
      alert('Failed to capture image. Please try again.');
    }
  };

  const handleGallery = async () => {
    try {
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (permissionResult.granted === false) {
        alert("You've refused to allow this app to access your photos!");
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images', 'videos'],
        quality: 1,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        const fileExtension = asset.uri.split('.').pop() || 'jpg';
        const fileName = `file_${new Date().getTime()}.${fileExtension}`;
        
        setSelectedFile({
          uri: asset.uri,
          type: asset.type || 'image/jpeg',
          name: fileName
        });
        setFileName(fileName);
      }
    } catch (error) {
      console.error('Error selecting image:', error);
      alert('Failed to select image. Please try again.');
    }
  };

  const handleUpload = () => {
    if (!fileName.trim()) {
      alert('Please enter a file name');
      return;
    }

    if (!selectedFile) {
      alert('Please select a file first');
      return;
    }

    onUpload({
      file: selectedFile,
      fileName: fileName,
      shareWithAll: shareWithAll
    });
    
    // Reset state
    setSelectedFile(null);
    setFileName('');
    setShareWithAll(false);
    onClose();
  };

  const renderContent = () => (
    <View style={styles.container}>
      <SafeAreaView edges={['top']} style={styles.header}>
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={onClose}>
            <MaterialIcons name="close" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Upload a file</Text>
          {selectedFile ? (
            <TouchableOpacity onPress={handleUpload}>
              <MaterialIcons name="check" size={24} color="#fff" />
            </TouchableOpacity>
          ) : (
            <View style={{ width: 24 }} />
          )}
        </View>
      </SafeAreaView>

      <ScrollView style={styles.content}>
        {selectedFile ? (
          <View style={styles.confirmationContent}>
            <Image 
              source={{ uri: selectedFile.uri }} 
              style={styles.previewImage}
              resizeMode="contain"
            />
            <View style={styles.formContainer}>
              <Text style={styles.label}>Name your file</Text>
              <TextInput
                style={styles.input}
                value={fileName}
                onChangeText={setFileName}
                placeholder="Enter file name"
              />
              <View style={styles.switchContainer}>
                <Text style={styles.switchLabel}>Share this file with all users in the CRM</Text>
                <Switch
                  value={shareWithAll}
                  onValueChange={setShareWithAll}
                  trackColor={{ false: '#e0e0e0', true: '#0F96BB' }}
                  thumbColor="#fff"
                />
              </View>
            </View>
          </View>
        ) : (
          <View style={styles.optionsContainer}>
            <TouchableOpacity style={styles.option} onPress={handleCamera}>
              <View style={styles.iconContainer}>
                <MaterialIcons name="camera-alt" size={32} color="#0F96BB" />
              </View>
              <Text style={styles.optionText}>Take photo</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.option} onPress={handleGallery}>
              <View style={styles.iconContainer}>
                <MaterialIcons name="photo-library" size={32} color="#0F96BB" />
              </View>
              <Text style={styles.optionText}>Choose from gallery</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
    >
      {renderContent()}
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    backgroundColor: '#0F96BB',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#fff',
  },
  content: {
    flex: 1,
  },
  optionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: 40,
  },
  option: {
    alignItems: 'center',
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#F5FBFD',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  optionText: {
    fontSize: 16,
    color: '#0F96BB',
    marginTop: 8,
  },
  confirmationContent: {
    flex: 1,
    padding: 16,
  },
  previewImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 16,
    backgroundColor: '#f5f5f5',
  },
  formContainer: {
    padding: 16,
    backgroundColor: '#fff',
    borderRadius: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    height: 40,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 4,
    paddingHorizontal: 12,
    marginBottom: 16,
    backgroundColor: '#fff',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  switchLabel: {
    flex: 1,
    fontSize: 14,
    color: '#333',
    marginRight: 16,
  },
}); 