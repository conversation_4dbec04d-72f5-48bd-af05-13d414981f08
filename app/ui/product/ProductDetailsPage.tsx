import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  ScrollView,
  TouchableOpacity,
  Switch,
  Platform,
  Alert,
  ActivityIndicator,
  Animated,
  Dimensions,
  Modal,
  Image,
} from 'react-native';
import { Appbar, FAB, Portal, Menu } from 'react-native-paper';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useFocusEffect } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useColors } from '@/hooks/useThemeColor';
import { apiService } from '../../../services/ApiService';
import { API_ENDPOINTS, API_BASE_URL } from '@/config/api';
import { showSnackbar } from '../utils';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  ProductDetailsResponse,
  ProductDetail,
  formatProductFields,
  getFieldValue,
  Currency,
  File as ProductFile,
  ProductCurrency
} from '@/app/models/ModelProductDetails';
import FileUploadScreen from './FileUploadScreen';

type TabType = 'INFO' | 'PRICING' | 'FILES';

interface TabItem {
  key: TabType;
  label: string;
  icon: 'information-outline' | 'currency-inr' | 'file-document-outline';
  badge?: number;
}

interface PricingItem {
  currency: string;
  currency_code: string;
  amount: string;
}

export default function ProductDetailsPage() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const colors = useColors();
  const [activeTab, setActiveTab] = useState<TabType>('INFO');
  const [searchQuery, setSearchQuery] = useState('');
  const [showEmptyFields, setShowEmptyFields] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [basicInformation, setBasicInformation] = useState<ProductDetail[]>([]);
  const [systemInformation, setSystemInformation] = useState<ProductDetail[]>([]);
  const [productName, setProductName] = useState('');
  const [productAmount, setProductAmount] = useState('');
  const productId = params.productId as string;
  const [tabTranslateX] = useState(new Animated.Value(0));
  const [pricingList, setPricingList] = useState<ProductCurrency[]>([]);
  const [menuVisible, setMenuVisible] = useState<string | null>(null);
  const [isBottomSheetVisible, setIsBottomSheetVisible] = useState(false);
  const [productDetails, setProductDetails] = useState<ProductDetailsResponse | null>(null);
  const [selectedCurrency, setSelectedCurrency] = useState<Currency | null>(null);
  const [tabs, setTabs] = useState<TabItem[]>([
    { key: 'INFO', label: 'Information', icon: 'information-outline' as const },
    { key: 'PRICING', label: 'Pricing', icon: 'currency-inr' as const, badge: 0 },
    { key: 'FILES', label: 'Files', icon: 'file-document-outline' as const, badge: 0 },
  ]);
  const [showFileUpload, setShowFileUpload] = useState(false);

  const fetchProductDetails = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await apiService.get<ProductDetailsResponse>(`${API_ENDPOINTS.PRODUCTS}/${productId}`);
      
      if (response.status === 200) {
        setProductDetails(response.data);
        const { basic, system } = formatProductFields(response.data.product);
        setBasicInformation(basic);
        setSystemInformation(system);

        // Set header information - extract from products array using getFieldValue
        const productName = getFieldValue(response.data.product.products, 'product_name') ||
                           response.data.product.product.product_name ||
                           'Product';
        const productAmount = getFieldValue(response.data.product.products, 'base_currency_amount') ||
                            response.data.product.product.base_currency_amount;

        console.log('Product Details Debug:', {
          productName,
          productAmount,
          fromProducts: getFieldValue(response.data.product.products, 'product_name'),
          fromProductObject: response.data.product.product.product_name,
          amountFromProducts: getFieldValue(response.data.product.products, 'base_currency_amount'),
          amountFromProductObject: response.data.product.product.base_currency_amount
        });

        setProductName(productName);
        setProductAmount(productAmount ? `₹ ${productAmount}` : '');

        // Set pricing list from currencies
        const currencies = response.data.product.product.currencies || [];
        setPricingList(currencies);

        // Update badges for both Pricing and Files tabs
        const filesCount = response.data.product.files?.length || 0;
        const pricingCount = currencies.length;
        
        setTabs(prevTabs => 
          prevTabs.map(tab => {
            if (tab.key === 'FILES') return { ...tab, badge: filesCount };
            if (tab.key === 'PRICING') return { ...tab, badge: pricingCount };
            return tab;
          })
        );
      } else {
        throw new Error('Failed to fetch product details');
      }
    } catch (error) {
      console.error('Error fetching product details:', error);
      showSnackbar('Failed to load product details');
      router.back();
    } finally {
      setIsLoading(false);
    }
  }, [productId]);

  
  useEffect(() => {
    if (productId) {
      fetchProductDetails();
    } else {
      showSnackbar('Product ID not found');
      router.back();
    }
  }, [productId]);

  // Refresh data when screen comes into focus (e.g., returning from edit page)
  useFocusEffect(
    useCallback(() => {
      if (productId) {
        console.log('Screen focused, refreshing product details...');
        fetchProductDetails();
      }
    }, [productId, fetchProductDetails])
  );

  const handleDeleteProduct = async () => {
    Alert.alert(
      'Delete Product',
      'Are you sure you want to delete this product? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              setIsDeleting(true);
              const response = await apiService.delete(`${API_ENDPOINTS.PRODUCTS}/${productId}`);
              
              if (response.status === 200) {
                showSnackbar('Product deleted successfully');
                router.back();
              } else {
                throw new Error(response.data?.message || 'Failed to delete product');
              }
            } catch (error) {
              console.error('Error deleting product:', error);
              showSnackbar(error instanceof Error ? error.message : 'Failed to delete product');
            } finally {
              setIsDeleting(false);
            }
          },
        },
      ],
      { cancelable: true }
    );
  };

  const handleTabPress = (tab: TabType) => {
    const index = tabs.findIndex(t => t.key === tab);
    Animated.spring(tabTranslateX, {
      toValue: index * (Dimensions.get('window').width / tabs.length),
      useNativeDriver: true,
      tension: 68,
      friction: 10,
    }).start();
    setActiveTab(tab);
  };

  const handleEditPrice = async (currencyCode: string) => {
    try {
      const priceItem = pricingList.find(item => item.currency === currencyCode);
      if (!priceItem) return;

      // Navigate to add price page in edit mode with the current price data
      router.push({
        pathname: '/ui/product/AddPricePage' as const,
        params: {
          productId,
          currencies: JSON.stringify(productDetails?.product?.currencies || []),
          pricingtypes: JSON.stringify(productDetails?.product?.pricingtypes || []),
          editMode: 'true',
          editCurrencyCode: currencyCode,
          editAmount: priceItem.base_currency_amount,
          editCurrency: priceItem.currency
        }
      });
    } catch (error) {
      console.error('Error navigating to edit price:', error);
      showSnackbar('Failed to open edit price page');
    } finally {
      setMenuVisible(null);
    }
  };

  const handleDeletePrice = (currencyCode: string) => {
    Alert.alert(
      'Delete Price',
      'Are you sure you want to delete this price?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              var data = {
                "productid":productId
              }
              const response = await apiService.post(
                'products/delete-price',
                data
              );
              
              if (response.status === 200) {
                // Remove the price from the local state
                setPricingList(current => 
                  current.filter(item => item.currency !== currencyCode)
                );
                showSnackbar('Price deleted successfully');
              } else {
                throw new Error(response.data?.message || 'Failed to delete price');
              }
            } catch (error) {
              console.error('Error deleting price:', error);
              showSnackbar(error instanceof Error ? error.message : 'Failed to delete price');
            } finally {
              setMenuVisible(null);
            }
          }
        }
      ]
    );
  };

  const handleAddPrice = () => {
    setIsBottomSheetVisible(false);
    router.push({
      pathname: '/ui/product/AddPricePage' as const,
      params: {
        productId: productDetails?.product?.id,
        currencies: JSON.stringify(productDetails?.product?.currencies || []),
        pricingtypes: JSON.stringify(productDetails?.product?.pricingtypes || [])
      }
    });
  };

  const handleFileUpload = async (fileData: any) => {
    try {
      setIsLoading(true);
      
      // Create FormData instance
      const formData = new FormData();
      
      // Get file URI based on platform
      const fileUri = Platform.OS === 'android' ? `file://${fileData.file.uri}` : fileData.file.uri;
      
      // Get file name and type
      const fileName = fileData.fileName || fileData.file.name || `file_${new Date().getTime()}.jpg`;
      const fileType = fileData.file.type || 'image/jpeg';

      // Create file object that matches React Native's FormData format
      const fileObject = {
        uri: fileUri,
        type: fileType,
        name: fileName
      } as any;

      // Log the file data for debugging
      console.log('File Data:', {
        uri: fileUri,
        type: fileType,
        name: fileName,
        productId,
        shareWithAll: fileData.shareWithAll
      });

      // Append file to FormData with the exact field name expected by multer
      formData.append('file', fileObject);
      formData.append('type_id', productId);
      formData.append('share_with_team', fileData.shareWithAll ? '1' : '0');

      // Get auth token
      const authToken = await AsyncStorage.getItem('auth_token');
      if (!authToken) {
        throw new Error('Authentication token not found');
      }

      // Log the complete FormData
      console.log('FormData entries:', Array.from((formData as any).entries()));

      // Use direct fetch with complete URL
      const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.PRODUCT_FILES_ADD}`, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${authToken}`,
        },
        body: formData
      });

      console.log('Upload response status:', response.status);
      console.log('Upload response headers:', response.headers);

      const responseText = await response.text();
      console.log('Raw response:', responseText);

      let responseData;
      try {
        responseData = JSON.parse(responseText);
      } catch (e) {
        console.error('Failed to parse response:', e);
        throw new Error('Invalid response from server');
      }

      console.log('Parsed response:', responseData);

      if (!response.ok) {
        throw new Error(responseData.message || 'Failed to upload file');
      }

      if (responseData.success) {
        // Update the files list in state
        if (productDetails && responseData.file) {
          setProductDetails(prev => prev ? {
            ...prev,
            product: {
              ...prev.product,
              files: [...(prev.product.files || []), responseData.file]
            }
          } : null);

          // Update the files badge count
          setTabs(prevTabs => 
            prevTabs.map(tab => {
              if (tab.key === 'FILES') {
                return { ...tab, badge: (productDetails.product.files?.length || 0) + 1 };
              }
              return tab;
            })
          );

          showSnackbar('File uploaded successfully');
          setShowFileUpload(false);
          
          // Refresh the product details to ensure we have the latest data
          fetchProductDetails();
        }
      } else {
        throw new Error(responseData.message || 'Failed to upload file');
      }
    } catch (error: any) {
      console.error('Error uploading file:', error);
      // More detailed error message
      let errorMessage = 'Failed to upload file';
      if (error.response) {
        console.error('Error response:', error.response);
        errorMessage = error.response.data?.message || 'Server error occurred';
      } else if (error.request) {
        console.error('Error request:', error.request);
        errorMessage = 'No response from server. Please check your connection.';
      } else {
        console.error('Error details:', error);
        errorMessage = error.message || 'Error preparing upload';
      }
      showSnackbar(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const renderInfoSection = (title: string, data: ProductDetail[]) => {
    const filteredData = showEmptyFields 
      ? data.map(item => ({
          ...item,
          value: item.value || '-'
        }))
      : data.filter(item => item.value !== '');

    if (filteredData.length === 0) return null;

    const filteredBySearch = searchQuery
      ? filteredData.filter(item => 
          item.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (item.value !== '-' && item.value.toLowerCase().includes(searchQuery.toLowerCase()))
        )
      : filteredData;

    if (filteredBySearch.length === 0) return null;

    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{title}</Text>
        {filteredBySearch.map((item, index) => (
          <View key={index} style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>{item.label}</Text>
            <Text style={[
              styles.fieldValue,
              item.isReadOnly && styles.readOnlyText,
              item.value === '-' && styles.emptyValue
            ]}>
              {item.value}
              {item.isReadOnly && ' (Read-only)'}
            </Text>
          </View>
        ))}
      </View>
    );
  };
  
  const renderPricingContent = () => (
    <ScrollView style={styles.content}>
      <View style={styles.pricingHeader}>
        <View style={styles.pricingTitleContainer}>
          <MaterialCommunityIcons name="currency-inr" size={20} color="#666" style={styles.pricingIcon} />
          <Text style={styles.pricingTitle}>
            {productDetails?.product.product.pricing_type || 'One-time pricing'}
          </Text>
        </View>
      </View>
      <View style={styles.pricingList}>
        {pricingList.length === 0 ? (
          <View style={styles.emptyPricing}>
            <Text style={styles.emptyText}>No prices added yet</Text>
          </View>
        ) : (
          pricingList.map((item) => (
            <View key={item.currency} style={styles.pricingItem}>
              <View style={styles.pricingInfo}>
                <Text style={styles.currencyLabel}>Currency: {item.currency}</Text>

                <Text style={styles.priceAmount}>
                  Unit Price: {item.base_currency_amount}
                </Text>
              </View>
              <View style={styles.pricingActions}>
              
                <Menu
                  visible={menuVisible === item.currency}
                  onDismiss={() => setMenuVisible(null)}
                  anchor={
                    <TouchableOpacity
                      onPress={() => setMenuVisible(item.currency)}
                      style={styles.actionButton}
                    >
                      <MaterialCommunityIcons name="dots-vertical" size={20} color="#666" />
                    </TouchableOpacity>
                  }
                >
                  <Menu.Item 
                    onPress={() => handleEditPrice(item.currency)} 
                    title="Edit"
                    leadingIcon="pencil"
                  />
                  <Menu.Item 
                    onPress={() => handleDeletePrice(item.currency)}
                    title="Delete"
                    leadingIcon="delete"
                  />
                </Menu>
              </View>
            </View>
          ))
        )}
      </View>
      <TouchableOpacity 
        style={styles.addPriceButton}
        onPress={handleAddPrice}
      >
        <MaterialCommunityIcons name="plus" size={24} color="#0F96BB" />
        <Text style={styles.addPriceText}>Add Price</Text>
      </TouchableOpacity>
    </ScrollView>
  );

  const renderFilesContent = () => {
    const hasFiles = productDetails?.product.files && productDetails.product.files.length > 0;

    if (!hasFiles) {
      // Show centered empty state without header
      return (
        <View style={styles.emptyFilesContainer}>
          <MaterialCommunityIcons
            name="file-document-outline"
            size={64}
            color="#ccc"
            style={styles.emptyFilesIcon}
          />
          <Text style={styles.emptyFilesText}>No files attached</Text>
          <Text style={styles.emptyFilesSubtext}>
            Tap the + button to add files to this product
          </Text>
        </View>
      );
    }

    return (
      <ScrollView style={styles.content}>
        <View style={styles.filesHeader}>
          <View style={styles.filesTitleContainer}>
            <MaterialCommunityIcons name="file-document-outline" size={20} color="#666" style={styles.filesIcon} />
            <Text style={styles.filesTitle}>Attached Files</Text>
          </View>
        </View>
        <View style={styles.filesList}>
          {productDetails.product.files.map((file) => (
            <View key={file.id} style={styles.fileItem}>
              <View style={styles.fileInfo}>
                <Text style={styles.fileName}>{file.file_name}</Text>
                <Text style={styles.fileSize}>
                  {(parseInt(file.file_size) / 1024).toFixed(2)} KB
                </Text>
                <Text style={styles.fileDate}>
                  {new Date(file.created_at).toLocaleDateString()}
                </Text>
              </View>
              <View style={styles.fileActions}>
                <TouchableOpacity
                  onPress={() => handleDeleteFile(file.id)}
                  style={styles.actionButton}
                >
                  <MaterialCommunityIcons name="delete" size={20} color="#666" />
                </TouchableOpacity>
              </View>
            </View>
          ))}
        </View>
      </ScrollView>
    );
  };

  const handleDownloadFile = (file: ProductFile) => {
    // Implement file download logic
    console.log('Downloading file:', file.file_path);
  };

  const handleDeleteFile = (fileId: number) => {
    Alert.alert(
      'Delete File',
      'Are you sure you want to delete this file?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
             var data = {
                "fileid":fileId
            }
            console.log("apiCallingName",""+API_ENDPOINTS.PRODUCT_FILES_DELETE);
              const response = await apiService.post(
                'products/file/delete',
                data
              );
              
              if (response.status === 200) {
                // Update the files list in state
                setProductDetails(prev => prev ? {
                  ...prev,
                  product: {
                    ...prev.product,
                    files: prev.product.files.filter(f => f.id !== fileId)
                  }
                } : null);
                showSnackbar('File deleted successfully');
              } else {
                throw new Error(response.data?.message || 'Failed to delete file');
              }
            } catch (error) {
              console.error('Error deleting file:', error);
              showSnackbar(error instanceof Error ? error.message : 'Failed to delete file');
            }
          }
        }
      ]
    );
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <View style={styles.centeredContent}>
          <ActivityIndicator size="large" color="#0F96BB" />
        </View>
      );
    }

    switch (activeTab) {
      case 'INFO':
        return (
          <ScrollView style={styles.content}>
            <View style={styles.searchContainer}>
              <TextInput
                style={styles.searchInput}
                placeholder="Search fields"
                value={searchQuery}
                onChangeText={setSearchQuery}
                placeholderTextColor="#999"
              />
            </View>

            <View style={styles.toggleContainer}>
              <Text style={styles.toggleLabel}>Show empty fields</Text>
              <Switch
                value={showEmptyFields}
                onValueChange={setShowEmptyFields}
                trackColor={{ false: '#E0E0E0', true: '#0F96BB' }}
                thumbColor="#fff"
              />
            </View>

            {renderInfoSection('BASIC INFORMATION', basicInformation)}
            {renderInfoSection('SYSTEM INFORMATION', systemInformation)}
          </ScrollView>
        );
      case 'PRICING':
        return renderPricingContent();
      case 'FILES':
        return renderFilesContent();
      default:
        return null;
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#fff',
    },
    header: {
      backgroundColor: '#0F96BB',
    },
    appbar: {
      backgroundColor: '#0F96BB',
      elevation: 0,
      height: Platform.OS === 'ios' ? 56 : 64,
      justifyContent: 'center',
      alignItems: 'center',
    },
    titleContainer: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 4,
    },
    title: {
      fontSize: 18,
      color: '#fff',
      fontWeight: '600',
      lineHeight: 22,
      flex: 1,
      marginRight: 8,
    },
    amount: {
      fontSize: 16,
      color: '#fff',
      fontWeight: '500',
      opacity: 0.95,
      lineHeight: 20,
    },
    tabsContainer: {
      flexDirection: 'row',
      backgroundColor: '#fff',
      height: 48,
      position: 'relative',
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
    },
    tabIndicator: {
      position: 'absolute',
      bottom: 0,
      height: 3,
      width: Dimensions.get('window').width / tabs.length,
      backgroundColor: '#0F96BB',
      borderTopLeftRadius: 3,
      borderTopRightRadius: 3,
    },
    tab: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
      paddingVertical: 12,
    },
    tabContent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      paddingHorizontal: 4,
    },
    tabIcon: {
      marginRight: 8,
    },
    tabText: {
      fontSize: 14,
      color: '#666',
      fontWeight: '500',
      marginRight: 20,
    },
    activeTabText: {
      color: '#0F96BB',
      fontWeight: '600',
    },
    tabBadge: {
      position: 'absolute',
      top: -8,
      right: -8,
      backgroundColor: '#FF4444',
      borderRadius: 12,
      minWidth: 18,
      height: 18,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 4,
    },
    tabBadgeText: {
      color: '#fff',
      fontSize: 10,
      fontWeight: '600',
      textAlign: 'center',
    },
    searchContainer: {
      padding: 12,
      backgroundColor: '#f5f5f5',
    },
    searchInput: {
      height: 36,
      backgroundColor: '#fff',
      borderRadius: 8,
      paddingHorizontal: 12,
      fontSize: 14,
      borderWidth: 1,
      borderColor: '#E0E0E0',
    },
    toggleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 12,
      backgroundColor: '#fff',
      borderBottomWidth: 1,
      borderBottomColor: '#E0E0E0',
    },
    toggleLabel: {
      fontSize: 14,
      color: '#333',
    },
    content: {
      flex: 1,
    },
    section: {
      padding: 12,
      backgroundColor: '#f5f5f5',
      marginBottom: 8,
    },
    sectionTitle: {
      fontSize: 13,
      fontWeight: '600',
      color: '#666',
      marginBottom: 12,
    },
    fieldContainer: {
      marginBottom: 12,
    },
    fieldLabel: {
      fontSize: 13,
      color: '#666',
      marginBottom: 2,
    },
    fieldValue: {
      fontSize: 14,
      color: '#333',
    },
    readOnlyText: {
      color: '#666',
      fontStyle: 'italic',
    },
    emptyValue: {
      color: '#999',
      fontStyle: 'italic',
    },
    centeredContent: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 16,
    },
    deleteButton: {
      marginRight: 8,
    },
    deleteIcon: {
      color: '#fff',
    },
    actionButton: {
      marginHorizontal: 4,
      justifyContent: 'center',
      alignItems: 'center',
    },
    pricingHeader: {
      padding: 16,
      backgroundColor: '#f5f5f5',
      borderBottomWidth: 1,
      borderBottomColor: '#E0E0E0',
    },
    pricingTitleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    pricingIcon: {
      marginRight: 8,
    },
    pricingTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: '#333',
    },
    pricingList: {
      backgroundColor: '#fff',
    },
    pricingItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: '#E0E0E0',
    },
    pricingInfo: {
      flex: 1,
    },
    currencyName: {
      fontSize: 16,
      color: '#333',
      fontWeight: '500',
    },
    currencyLabel: {
      fontSize: 16,
      color: '#333',
      fontWeight: '500',
    },
    currencyCode: {
      fontSize: 14,
      color: '#666',
      marginTop: 2,
    },
    priceAmount: {
      fontSize: 16,
      color: '#0F96BB',
      fontWeight: '600',
      marginTop: 4,
    },
    pricingActions: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    addPriceButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      padding: 16,
      backgroundColor: '#fff',
      borderTopWidth: 1,
      borderTopColor: '#E0E0E0',
    },
    addPriceText: {
      fontSize: 16,
      color: '#0F96BB',
      fontWeight: '500',
      marginLeft: 8,
    },
    emptyPricing: {
      padding: 24,
      alignItems: 'center',
      justifyContent: 'center',
    },
    emptyText: {
      fontSize: 16,
      color: '#666',
      textAlign: 'center',
    },
    fab: {
      position: 'absolute',
      right: 16,
      bottom: 16,
      backgroundColor: '#0F96BB',
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'flex-end',
    },
    bottomSheet: {
      backgroundColor: '#fff',
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      padding: 16,
    },
    optionsRow: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingVertical: 8,
    },
    option: {
      alignItems: 'center',
      padding: 16,
    },
    optionText: {
      marginTop: 8,
      color: '#0F96BB',
      fontSize: 14,
    },
    filesHeader: {
      padding: 16,
      backgroundColor: '#f5f5f5',
      borderBottomWidth: 1,
      borderBottomColor: '#E0E0E0',
    },
    filesTitleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    filesIcon: {
      marginRight: 8,
    },
    filesTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: '#333',
    },
    filesList: {
      backgroundColor: '#fff',
    },
    fileItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: '#E0E0E0',
    },
    fileInfo: {
      flex: 1,
    },
    fileName: {
      fontSize: 16,
      color: '#333',
      fontWeight: '500',
    },
    fileSize: {
      fontSize: 14,
      color: '#666',
      marginTop: 2,
    },
    fileDate: {
      fontSize: 14,
      color: '#666',
      marginTop: 2,
    },
    fileActions: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    emptyFiles: {
      padding: 24,
      alignItems: 'center',
      justifyContent: 'center',
    },
    emptyFilesContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 32,
      backgroundColor: '#fff',
    },
    emptyFilesIcon: {
      marginBottom: 16,
    },
    emptyFilesText: {
      fontSize: 18,
      color: '#666',
      textAlign: 'center',
      marginBottom: 8,
      fontWeight: '500',
    },
    emptyFilesSubtext: {
      fontSize: 14,
      color: '#999',
      textAlign: 'center',
      lineHeight: 20,
    },
  });

  const renderTabs = () => (
    <View style={styles.tabsContainer}>
      <Animated.View 
        style={[
          styles.tabIndicator,
          {
            transform: [{ translateX: tabTranslateX }],
          },
        ]} 
      />
      {tabs.map((tab) => (
        <TouchableOpacity
          key={tab.key}
          style={styles.tab}
          onPress={() => handleTabPress(tab.key)}
          activeOpacity={0.7}
        >
          <View style={styles.tabContent}>
            <MaterialCommunityIcons 
              name={tab.icon}
              size={20}
              color={activeTab === tab.key ? '#0F96BB' : '#666'}
              style={styles.tabIcon}
            />
            <Text style={[
              styles.tabText,
              activeTab === tab.key && styles.activeTabText
            ]}>
              {tab.label}
            </Text>
            {tab.badge !== undefined && tab.badge > 0 && (
              <View style={styles.tabBadge}>
                <Text style={styles.tabBadgeText}>
                  {tab.badge > 99 ? '99+' : tab.badge}
                </Text>
              </View>
            )}
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderBottomSheet = () => (
    <>
      <Modal
        visible={isBottomSheetVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setIsBottomSheetVisible(false)}
      >
        <TouchableOpacity 
          style={styles.modalOverlay}
          activeOpacity={1} 
          onPress={() => setIsBottomSheetVisible(false)}
        >
          <View style={styles.bottomSheet}>
            <View style={styles.optionsRow}>
              <TouchableOpacity 
                style={styles.option} 
                onPress={() => {
                  setIsBottomSheetVisible(false);
                  setShowFileUpload(true);
                }}
              >
                <MaterialCommunityIcons name="file-upload" size={24} color="#0F96BB" />
                <Text style={styles.optionText}>File</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.option} onPress={handleAddPrice}>
                <MaterialCommunityIcons name="currency-inr" size={24} color="#0F96BB" />
                <Text style={styles.optionText}>Price</Text>
              </TouchableOpacity>
            </View>
          </View>
        </TouchableOpacity>
      </Modal>
      <FileUploadScreen
        visible={showFileUpload}
        onClose={() => setShowFileUpload(false)}
        onUpload={handleFileUpload}
      />
    </>
  );

  const renderCurrencyList = () => {
    const currencies = productDetails?.product?.currencies || [];
    return currencies.map((currency: Currency) => (
      <Menu.Item
        key={currency.id}
        onPress={() => {
          setSelectedCurrency(currency);
          setMenuVisible(null);
        }}
        title={`${currency.currency} (${currency.currency_code})`}
      />
    ));
  };

  return (
    <>
      <SafeAreaView style={styles.container} edges={['top']}>
        <View style={styles.header}>
          <Appbar.Header style={styles.appbar} statusBarHeight={0}>
            <Appbar.BackAction
              color="#fff"
              onPress={() => router.back()}
              style={{ marginRight: 4 }}
            />
            <Appbar.Content
              title={
                <View style={styles.titleContainer}>
                  <Text style={styles.title} numberOfLines={1}>{productName}</Text>
                  {productAmount ? (
                    <Text style={styles.amount} numberOfLines={1}>{productAmount}</Text>
                  ) : null}
                </View>
              }
              style={{ paddingHorizontal: 8, flex: 1 }}
            />
            <Appbar.Action
              icon="pencil"
              color="#fff"
              onPress={() => router.push({
                pathname: '/ui/product/AddProductPage',
                params: { productDetails: JSON.stringify(productDetails?.product) ,productId: productDetails?.product?.id}
              } as any)}
              style={styles.actionButton}
            />
            <Appbar.Action
              icon="delete"
              color="#fff"
              disabled={isDeleting}
              onPress={handleDeleteProduct}
              style={[styles.actionButton, { marginRight: 4 }]}
            />
          </Appbar.Header>
          {renderTabs()}
        </View>
        {renderContent()}
        {renderBottomSheet()}
        <FAB
          icon="plus"
          style={styles.fab}
          onPress={() => setIsBottomSheetVisible(true)}
          color="#fff"
        />
      </SafeAreaView>
    </>
  );
} 
