import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  TextInput,
  ScrollView,
  Animated,
  Dimensions,
  Modal,
} from 'react-native';
import { <PERSON><PERSON><PERSON>, Chip, Drawer, Portal } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter, useLocalSearchParams, Router } from 'expo-router';
import { useFocusEffect } from '@react-navigation/native';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { apiService } from '../../../services/ApiService';
import { API_ENDPOINTS } from '../../../config/api';
import { useColors } from '@/hooks/useThemeColor';
import { CALL_FROM, showSnackbar } from '../utils';
import { FiltersResponse, SavedFilter } from '@/app/models/Filter';
interface ProductField {
  id: number;
  field_label: string;
  field_name: string;
  field_type: string;
  field_value: string | null;
  choices?: Array<{
    id: number;
    custom_option: string;
  }>;
}
interface ProductInfo {
  id: string;
  products: ProductField[];
  owner_name: string | null;
}
interface FilterField {
  field_id: string;
  contains: string;
  filter: string;
}
interface FilterPayload {
  title: string;
  filter: Array<{
    field_name: string;
    field_label: string;
    value_id: string | string[];
    contains: string;
  }>;
  share_with: string;
  selected_users: string[];
  accessibility: string;
  prevent_sharing: boolean;
}
interface FilterType extends SavedFilter {
  filterid: string | null;
  territories: string | null;
  teams: string | null;
  field_id: string | null;
  contains: string | null;
  filter: string | null;
  view_id: number | null;
  active: number | null;
  fileds: FilterField[];
}
interface ProductListResponse {
  status: number;
  message: string;
  products: {
    productInfo: ProductInfo[];
    filtersType: FilterType[];
  };
}
export default function ProductListPage() {
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [products, setProducts] = useState<ProductInfo[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<ProductInfo[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedSubCategory, setSelectedSubCategory] = useState<string | null>(null);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [filterTypes, setFilterTypes] = useState<SavedFilter[]>([]);
  const [selectedFilter, setSelectedFilter] = useState<SavedFilter | null>(null);
  const [filterSearchQuery, setFilterSearchQuery] = useState('');
  const [filteredFilterTypes, setFilteredFilterTypes] = useState<SavedFilter[]>([]);
  const [activeFilters, setActiveFilters] = useState<any>(null);
  const [savedFilters, setSavedFilters] = useState<SavedFilter[]>([]);
  const [filteredSavedFilters, setFilteredSavedFilters] = useState<SavedFilter[]>([]);
  const [drawerSearchQuery, setDrawerSearchQuery] = useState('');
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [filterToDelete, setFilterToDelete] = useState<SavedFilter | null>(null);
  const slideAnim = useRef(new Animated.Value(-Dimensions.get('window').width * 0.8)).current;
  const router = useRouter();
  const colors = useColors();
  const params = useLocalSearchParams();
  useFocusEffect(
    useCallback(() => {
      console.log('Screen focused, refreshing data...');
      refreshFilters();
    }, [])
  );
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        setLoading(true);
        await refreshFilters();
      } catch (error) {
        console.error('Error loading initial data:', error);
      } finally {
        setLoading(false);
      }
    };
    loadInitialData();
  }, []);
  useEffect(() => {
    const checkForFilters = async () => {
      try {
        const storedFilters = await AsyncStorage.getItem('selectedFilters');
        if (storedFilters) {
          const filterData = JSON.parse(storedFilters);
          console.log('Retrieved filters:', JSON.stringify(filterData));
          
          // Create a properly structured FilterPayload object
          const formattedFilter: FilterPayload = {
            title: filterData.name || "Custom Filter",
            filter: filterData.fileds?.map((field: any) => ({
              field_name: field.field_id,
              field_label: field.name || field.field_id,
              value_id: (field.contains.toLowerCase().includes('contain') || field.contains.toLowerCase().includes('between'))
                ? Array.isArray(field.filter) ? field.filter : [field.filter]
                : field.filter,
              contains: field.contains
            })) || [],
            share_with: filterData.share_with || "Just Me",
            selected_users: filterData.users || [],
            accessibility: filterData.accessibility || "public",
            prevent_sharing: false
          };
          setActiveFilters(formattedFilter);
          fetchProducts(formattedFilter);
          await AsyncStorage.removeItem('selectedFilters');
        } else {
          fetchProducts();
        }
      } catch (error) {
        console.error('Error checking stored filters:', error);
    fetchProducts();
      }
    };
  
    checkForFilters();
  }, []);
  useEffect(() => {
    if (filterTypes.length > 0) {
      const filtered = filterTypes.filter(filter => 
        filter.name?.toLowerCase().includes(filterSearchQuery.toLowerCase()) ||
        filter.accessibility?.toLowerCase().includes(filterSearchQuery.toLowerCase()) ||
        filter.share_with?.toLowerCase().includes(filterSearchQuery.toLowerCase())
      );
      setFilteredFilterTypes(filtered);
    }
  }, [filterSearchQuery, filterTypes]);
  useEffect(() => {
    return () => {
      setActiveFilters(null);
      setFilteredProducts([]);
    };
  }, []);
  useEffect(() => {
    console.log('Filters changed:', savedFilters.length);
    if (savedFilters.length > 0) {
      const filtered = savedFilters.filter(filter => {
        const searchLower = drawerSearchQuery.toLowerCase();
        return (
          filter.name?.toLowerCase().includes(searchLower) ||
          filter.accessibility?.toLowerCase().includes(searchLower) ||
          filter.share_with?.toLowerCase().includes(searchLower) ||
          filter.filters?.some(f => 
            f.name?.toLowerCase().includes(searchLower) ||
            f.filter?.toLowerCase().includes(searchLower)
          )
        );
      });
      console.log('Filtered filters:', filtered.length);
      setFilteredSavedFilters(filtered);
    }
  }, [drawerSearchQuery, savedFilters]);
  const fetchProducts = async (filter?: FilterPayload) => {
    console.log("fetchProductsWhast",filter);
    try {
      setLoading(true);
      const payload = filter ? {
        accessibility: filter.accessibility || "public",
        filter: filter.filter.map(field => ({
          contains: field.contains,
          field_name: field.field_name,
          value_id: field  .value_id
        })),
        share_with: filter.share_with || "Just Me",
        title: filter.title || "filter view"
      } : {
        accessibility: "public",
        filter: [],
        share_with: "Just Me",
        title: "all products"
      };
      const response = await apiService.post<ProductListResponse>(API_ENDPOINTS.PRODUCT_FILTER, payload);
      
      if (response.data?.products?.productInfo) {
        setProducts(response.data.products.productInfo);
        setFilteredProducts(response.data.products.productInfo);
        
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      showSnackbar('Failed to load products');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };
  const getFieldValue = (fields: ProductField[], fieldName: string): string | null => {
    const field = fields.find(f => f.field_name === fieldName);
    if (!field) return null;
    if (field.field_type === 'Dropdown' && field.choices && field.field_value) {
      const choice = field.choices.find(c => c.id.toString() === field.field_value);
      return choice ? choice.custom_option : field.field_value;
    }
    return field.field_value;
  };
  const onRefresh = () => {
    setRefreshing(true);
    fetchProducts();
  };
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    filterProducts(query, selectedCategory, selectedSubCategory);
  };
  const handleCategorySelect = (category: string | null) => {
    setSelectedCategory(category);
    filterProducts(searchQuery, category, selectedSubCategory);
  };
  const handleSubCategorySelect = (subCategory: string | null) => {
    setSelectedSubCategory(subCategory);
    filterProducts(searchQuery, selectedCategory, subCategory);
  };
  const filterProducts = (query: string, category: string | null, subCategory: string | null) => {
    let filtered = [...products];
    if (query) {
      filtered = filtered.filter(product => {
        const name = getFieldValue(product.products, 'product_name')?.toLowerCase();
        return name?.includes(query.toLowerCase());
      });
    }
    if (category) {
      filtered = filtered.filter(product => {
        const productCategory = getFieldValue(product.products, 'category');
        return productCategory === category;
      });
    }
    if (subCategory) {
      filtered = filtered.filter(product => {
        const productSubCategory = getFieldValue(product.products, 'subcategory');
        return productSubCategory === subCategory;
      });
    }
    setFilteredProducts(filtered);
  };
  const getAllCategories = () => {
    const categories = new Set<string>();
    products.forEach(product => {
      const categoryField = product.products.find(f => f.field_name === 'category');
      if (categoryField?.choices) {
        categoryField.choices.forEach(choice => {
          categories.add(choice.custom_option);
        });
      }
    });
    return Array.from(categories);
  };
  const getAllSubCategories = () => {
    const subCategories = new Set<string>();
    products.forEach(product => {
      const subCategoryField = product.products.find(f => f.field_name === 'subcategory');
      if (subCategoryField?.choices) {
        subCategoryField.choices.forEach(choice => {
          subCategories.add(choice.custom_option);
        });
      }
    });
    return Array.from(subCategories);
  };
  const handleFilterSelect = (filter: SavedFilter) => {
    setSelectedFilter(filter);
    setIsDrawerOpen(false);
   
    const filterPayload: FilterPayload = {
      title: filter.name,
      filter: filter.filters.map((f: any) => {
        console.log('📍 Mapping filter field:', {
          name: f.name,
          field_id: f.field_id,
          contains: f.contains,
          filter:  Array.isArray(f.filter) ? f.filter : [f.filter]
        });
        return {
          field_name: f.field_id,
          field_label: f.name,
          value_id:( f.contains.toLowerCase().includes('contain')||f.contains.toLowerCase().includes('between') ) ? Array.isArray(f.filter) ? f.filter : [f.filter] : f.filter  , // Ensure value_id is always an array
          contains: f.contains
        };
      }),
      share_with: filter.share_with || '',
      selected_users: filter.users || [],
      accessibility: filter.accessibility || 'Private',
      prevent_sharing: false
    };
    fetchProducts(filterPayload);
  };
  const applyCustomFilters = (filters: any[]) => {
    if (!filters || filters.length === 0) {
      setFilteredProducts(products);
      return;
    }
    const filtered = products.filter(product => {
      return filters.every(filter => {
        const productField = product.products.find(f => f.field_name === filter.field_name);
        if (!productField) return false;
        const fieldValue = productField.field_value;
        const filterValue = filter.value;
        switch (filter.operator) {
          case 'equals':
            return fieldValue === filterValue;
          case 'contains':
            return filter.selectedValues.some((val: string) => 
              fieldValue?.includes(val)
            );
          case 'does not contain':
            return !filter.selectedValues.some((val: string) => 
              fieldValue?.includes(val)
            );
          case 'is empty':
            return !fieldValue || fieldValue.trim() === '';
          case 'is not empty':
            return fieldValue && fieldValue.trim() !== '';
          default:
            return true;
        }
      });
    });
    setFilteredProducts(filtered);
  };
  const fetchSavedFilters = async () => {
    try {
      console.log('Fetching saved filters...');
      const response = await apiService.get<FiltersResponse>(API_ENDPOINTS.PRODUCT_FILTERS);
      console.log('Filters API Response:', response?.data);
      if (response?.data?.filters) {
        const filters = response.data.filters;
        console.log('Setting filters:', filters.length);
        setSavedFilters(filters);
        setFilteredSavedFilters(filters);
      } else {
        console.log('No filters in response');
        setSavedFilters([]);
        setFilteredSavedFilters([]);
      }
    } catch (error) {
      console.error('Error fetching filters:', error);
      showSnackbar('Failed to fetch filters');
    }
  };
  useEffect(() => {
    Animated.timing(slideAnim, {
      toValue: isDrawerOpen ? 0 : -Dimensions.get('window').width * 0.8,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [isDrawerOpen]);
  const handleDeletePress = (filter: SavedFilter, event: any) => {
    event.stopPropagation();
    setFilterToDelete(filter);
    setShowDeleteConfirm(true);
  };
  const refreshFilters = async () => {
    try {
      await fetchSavedFilters();
      await fetchProducts();
    } catch (error) {
      console.error('Error refreshing filters:', error);
    }
  };
  const handleDeleteFilter = async (filter: SavedFilter) => {
    try {
      console.log('Deleting filter:', filter.id);
      
      await apiService.delete(`${API_ENDPOINTS.PRODUCT_FILTERS}/${filter.id}`);
      console.log('Filter deleted from API');
      
      setSavedFilters(prev => prev.filter(f => f.id !== filter.id));
      setFilteredSavedFilters(prev => prev.filter(f => f.id !== filter.id));
      
      if (selectedFilter?.id === filter.id) {
        setSelectedFilter(null);
      }
      
      showSnackbar('Filter deleted successfully');
      await refreshFilters();
    } catch (error) {
      console.error('Error in handleDeleteFilter:', error);
      showSnackbar('Failed to delete filter');
    } finally {
      setShowDeleteConfirm(false);
      setFilterToDelete(null);
    }
  };
  const handleEditFilter = async (filter: SavedFilter, event: any) => {
    try {
      event.stopPropagation();
      console.log('Editing filter:', filter);
      router.push({
        pathname: 'ui/product/save-view',
        params: { 
          callFrom: CALL_FROM.PRODUCT_LIST,
          filterData: JSON.stringify(filter),
          isEdit: "true"
        }
      } as any);
    } catch (error) {
      console.error('Error in handleEditFilter:', error);
      showSnackbar('Failed to navigate to edit page');
    }
  };
  const renderSideNavigationView = () => (
    <Modal
      visible={isDrawerOpen}
      transparent={true}
      onRequestClose={() => setIsDrawerOpen(false)}
      animationType="none"
    >
      <TouchableOpacity
        style={styles.drawerOverlay}
        activeOpacity={1}
        onPress={() => setIsDrawerOpen(false)}
      >
        <Animated.View 
          style={[
            styles.drawerContent,
            {
              transform: [{ translateX: slideAnim }]
            }
          ]}
        >
          <View style={styles.drawerHeader}>
            <Text style={styles.drawerTitle}>
              Saved Filters {filteredSavedFilters.length > 0 && `(${filteredSavedFilters.length})`}
            </Text>
            <TouchableOpacity 
              onPress={() => setIsDrawerOpen(false)}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Ionicons name="close" size={24} color="#fff" />
            </TouchableOpacity>
          </View>
          <View style={styles.filterSearchContainer}>
            <View style={styles.searchInputContainer}>
              <Ionicons 
                name="search" 
                size={20} 
                color="#999" 
                style={styles.searchIcon} 
              />
              <TextInput
                style={styles.filterSearchInput}
                placeholder="Search filters..."
                value={drawerSearchQuery}
                onChangeText={setDrawerSearchQuery}
                placeholderTextColor="#999"
                autoCapitalize="none"
                autoCorrect={false}
              />
              {drawerSearchQuery.length > 0 && (
                <TouchableOpacity 
                  onPress={() => {
                    setDrawerSearchQuery('');
                    setFilteredSavedFilters(savedFilters);
                  }}
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                >
                  <Ionicons name="close-circle" size={20} color="#999" />
                </TouchableOpacity>
              )}
            </View>
          </View>
          <ScrollView style={styles.filterList} showsVerticalScrollIndicator={false}>
            {filteredSavedFilters.map((filter, index) => (
              <TouchableOpacity
                key={filter.id || index}
                style={[
                  styles.filterItem,
                  selectedFilter?.id === filter.id && styles.selectedFilterItem
                ]}
                onPress={() => handleFilterSelect(filter)}
                activeOpacity={0.7}
              >
                <View style={styles.filterItemContent}>
                  <View style={styles.filterItemHeader}>
                    <Text style={[
                      styles.filterItemText,
                      selectedFilter?.id === filter.id && styles.selectedFilterItemText
                    ]}>
                      {filter.name}
                    </Text>
                    <View style={styles.filterItemActions}>
                      <TouchableOpacity
                        onPress={(e) => handleEditFilter(filter, e)}
                        style={styles.actionButton}
                        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                      >
                        <Ionicons name="pencil" size={18} color={colors.primary} />
                      </TouchableOpacity>
                      <TouchableOpacity
                        onPress={(e) => handleDeletePress(filter, e)}
                        style={styles.actionButton}
                        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                      >
                        <Ionicons name="trash-outline" size={18} color="#FF4444" />
                      </TouchableOpacity>
                    </View>
                  </View>
                  {filter.filters && filter.filters.length > 0 && (
                    <View style={styles.filterDetails}>
                      {filter.filters.map((field, fieldIndex) => (
                        <Text key={fieldIndex} style={styles.filterFieldText}>
                          {field.name}: {field.filter} ({field.contains})
                        </Text>
                      ))}
                    </View>
                  )}
                  <Text style={styles.accessibilityText}>
                    {filter.accessibility || 'Private'} • {filter.share_with || 'Not shared'}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
            {filteredSavedFilters.length === 0 && (
              <View style={styles.emptySearchContainer}>
                <Text style={styles.emptySearchText}>
                  {drawerSearchQuery 
                    ? `No filters found matching "${drawerSearchQuery}"`
                    : 'No saved filters available'}
                </Text>
              </View>
            )}
          </ScrollView>
        </Animated.View>
      </TouchableOpacity>
    </Modal>
  );
  const renderDeleteConfirmation = () => (
    <Modal
      visible={showDeleteConfirm}
      transparent={true}
      onRequestClose={() => setShowDeleteConfirm(false)}
      animationType="fade"
    >
      <View style={styles.confirmOverlay}>
        <View style={styles.confirmBox}>
          <Text style={styles.confirmTitle}>Delete Filter</Text>
          <Text style={styles.confirmMessage}>
            Are you sure you want to delete "{filterToDelete?.name}"?
          </Text>
          <View style={styles.confirmButtons}>
            <TouchableOpacity
              style={[styles.confirmButton, styles.cancelButton]}
              onPress={() => {
                setShowDeleteConfirm(false);
                setFilterToDelete(null);
              }}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.confirmButton, styles.deleteButton]}
              onPress={() => filterToDelete && handleDeleteFilter(filterToDelete)}
            >
              <Text style={styles.deleteButtonText}>Delete</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
  const renderProductItem = ({ item }: { item: ProductInfo }) => {
    const name = getFieldValue(item.products, 'product_name');
    const category = getFieldValue(item.products, 'category');
    const subCategory = getFieldValue(item.products, 'subcategory');
    const cost = getFieldValue(item.products, 'base_currency_amount');
    const available = getFieldValue(item.products, 'available');
    return (
      <TouchableOpacity
        style={styles.productCard}
        onPress={() => router.push({
          pathname: '/ui/product/ProductDetailsPage',
          params: { productId: item.id }
        } as any)}
      >
        <View style={styles.productHeader}>
          <Text style={styles.productName}>{name}</Text>
          <Text style={styles.productCost}>₹{cost || '0'}</Text>
        </View>
        <View style={styles.productDetails}>
          <View style={styles.categoryContainer}>
            {category && (
              <Chip style={styles.categoryChip} textStyle={styles.chipText}>
                {category}
              </Chip>
            )}
            {subCategory && (
              <Chip style={styles.subCategoryChip} textStyle={styles.chipText}>
                {subCategory}
              </Chip>
            )}
          </View>
          <Text style={styles.availableText}>Available: {available || '0'}</Text>
        </View>
      </TouchableOpacity>
    );
  };
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.primary,
    },
    appbar: {
      backgroundColor: colors.primary,
      elevation: 0,
      height: 56,
      borderBottomWidth: 1,
      borderBottomColor: 'rgba(255, 255, 255, 0.1)',
    },
    appbarContent: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    titleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    title: {
      color: '#fff',
      fontSize: 18,
      fontWeight: '500',
    },
    dropdownIcon: {
      marginLeft: 4,
    },
    content: {
      flex: 1,
    },
    searchContainer: {
      padding: 16,
      backgroundColor: '#fff',
      borderBottomWidth: 1,
      borderBottomColor: '#eee',
    },
    searchInput: {
      height: 40,
      backgroundColor: '#f5f5f5',
      borderRadius: 20,
      paddingHorizontal: 16,
      fontSize: 16,
    },
    filterContainer: {
      padding: 16,
      backgroundColor: '#fff',
      borderBottomWidth: 1,
      borderBottomColor: '#eee',
    },
    filterTitle: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 8,
    },
    chipContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
    },
    categoryChip: {
      backgroundColor: '#e3f2fd',
      marginRight: 8,
    },
    subCategoryChip: {
      backgroundColor: '#f3e5f5',
    },
    chipText: {
      fontSize: 12,
    },
    productList: {
      padding: 16,
    },
    productCard: {
      backgroundColor: '#fff',
      borderRadius: 8,
      padding: 16,
      marginBottom: 12,
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.2,
      shadowRadius: 1.41,
    },
    productHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
    },
    productName: {
      fontSize: 16,
      fontWeight: '600',
      color: '#333',
      flex: 1,
    },
    productCost: {
      fontSize: 16,
      fontWeight: '600',
      color: '#0F96BB',
    },
    productDetails: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    categoryContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    availableText: {
      fontSize: 14,
      color: '#666',
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    emptyText: {
      fontSize: 16,
      color: '#666',
      textAlign: 'center',
    },
    drawerOverlay: {
      position: 'absolute',
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    drawerContent: {
      backgroundColor: 'white',
      height: '100%',
      width: '80%',
      position: 'absolute',
      top: 0,
      left: 0,
      bottom: 0,
      borderRightWidth: 1,
      borderRightColor: '#eee',
      shadowColor: '#000',
      shadowOffset: { width: 2, height: 0 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    drawerHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      backgroundColor: colors.primary,
      paddingHorizontal: 16,
      paddingVertical: 16,
    },
    drawerTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: '#fff',
    },
    filterSearchContainer: {
      padding: 12,
      borderBottomWidth: 1,
      borderBottomColor: '#eee',
      backgroundColor: '#fff',
    },
    searchInputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#f5f5f5',
      borderRadius: 20,
      paddingHorizontal: 12,
      height: 40,
      borderWidth: 1,
      borderColor: '#eee',
    },
    searchIcon: {
      marginRight: 8,
    },
    filterSearchInput: {
      flex: 1,
      fontSize: 14,
      color: '#333',
      paddingVertical: 8,
    },
    filterList: {
      flex: 1,
    },
    filterItem: {
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: '#eee',
      backgroundColor: '#fff',
    },
    filterItemContent: {
      flex: 1,
    },
    filterItemHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    filterItemActions: {
      flexDirection: 'row',
      gap: 8,
    },
    actionButton: {
      padding: 4,
      borderRadius: 4,
      backgroundColor: 'rgba(0, 0, 0, 0.05)',
    },
    filterItemText: {
      fontSize: 16,
      color: '#333',
      flex: 1,
      fontWeight: '400',
    },
    selectedFilterItem: {
      backgroundColor: '#f5f5f5',
    },
    selectedFilterItemText: {
      color: colors.primary,
      fontWeight: '500',
    },
    filterDetails: {
      marginTop: 8,
      paddingLeft: 8,
    },
    filterFieldText: {
      fontSize: 14,
      color: '#666',
      marginTop: 2,
    },
    accessibilityText: {
      fontSize: 12,
      color: '#666',
      marginTop: 8,
    },
    emptySearchContainer: {
      padding: 20,
      alignItems: 'center',
    },
    emptySearchText: {
      fontSize: 16,
      color: '#666',
      textAlign: 'center',
    },
    confirmOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    confirmBox: {
      backgroundColor: '#fff',
      borderRadius: 8,
      padding: 20,
      width: '80%',
      maxWidth: 400,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    confirmTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: '#333',
      marginBottom: 12,
    },
    confirmMessage: {
      fontSize: 16,
      color: '#666',
      marginBottom: 20,
    },
    confirmButtons: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      gap: 12,
    },
    confirmButton: {
      paddingVertical: 8,
      paddingHorizontal: 16,
      borderRadius: 4,
      minWidth: 80,
      alignItems: 'center',
    },
    cancelButton: {
      backgroundColor: '#f5f5f5',
    },
    deleteButton: {
      backgroundColor: '#FF4444',
    },
    cancelButtonText: {
      color: '#666',
      fontSize: 14,
      fontWeight: '500',
    },
    deleteButtonText: {
      color: '#fff',
      fontSize: 14,
      fontWeight: '500',
    },
  });
  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Appbar.Header style={styles.appbar} statusBarHeight={0}>
        <Appbar.BackAction color="#fff" onPress={() => router.back()} />
        <TouchableOpacity 
          style={styles.titleContainer}
          onPress={() => setIsDrawerOpen(!isDrawerOpen)}
        >
          <Text style={styles.title}>
            {selectedFilter?.name || "All products"}
          </Text>
          <MaterialCommunityIcons
            name={isDrawerOpen ? "chevron-up" : "chevron-down"}
            size={20}
            color="#fff"
            style={styles.dropdownIcon}
          />
        </TouchableOpacity>
        <View style={{ flex: 1 }} />
        <Appbar.Action 
          icon="plus"
          color="#fff" 
          onPress={() => {
            router.push({
              pathname: '/ui/product/AddProductPage',
              params: {}
            } as any)
          }}
        />
        <Appbar.Action 
          icon="filter-outline"
          color="#fff" 
          onPress={() => {
            router.push({
              pathname: '/ui/product/ProductFiltersPage',
              params: { callFrom: CALL_FROM.PRODUCT_LIST }
            } as any)
          }}
        />
        {/* <Appbar.Action 
          icon="sync"
          color="#fff" 
          onPress={onRefresh}
        /> */}
      </Appbar.Header>
      {renderSideNavigationView()}
      {renderDeleteConfirmation()}
      <View style={styles.content}>
        <View style={styles.searchContainer}>
          <TextInput
            style={styles.searchInput}
            placeholder="Search products..."
            value={searchQuery}
            onChangeText={handleSearch}
            placeholderTextColor="#999"
          />
        </View>
        {showFilters && (
          <View style={styles.filterContainer}>
            <Text style={styles.filterTitle}>Categories</Text>
            <View style={styles.chipContainer}>
              {getAllCategories().map((category) => (
                <Chip
                  key={category}
                  selected={selectedCategory === category}
                  onPress={() => handleCategorySelect(selectedCategory === category ? null : category)}
                  style={styles.categoryChip}
                >
                  {category}
                </Chip>
              ))}
            </View>
            <Text style={[styles.filterTitle, { marginTop: 16 }]}>Sub Categories</Text>
            <View style={styles.chipContainer}>
              {getAllSubCategories().map((subCategory) => (
                <Chip
                  key={subCategory}
                  selected={selectedSubCategory === subCategory}
                  onPress={() => handleSubCategorySelect(selectedSubCategory === subCategory ? null : subCategory)}
                  style={styles.subCategoryChip}
                >
                  {subCategory}
                </Chip>
              ))}
            </View>
          </View>
        )}
        {loading ? (
          <View style={styles.emptyContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        ) : filteredProducts.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No products found</Text>
          </View>
        ) : (
          <FlatList
            data={filteredProducts}
            renderItem={renderProductItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.productList}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={[colors.primary]}
              />
            }
          />
        )}
      </View>
    </SafeAreaView>
  );
} 