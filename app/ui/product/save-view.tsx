import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Platform,
  UIManager,
  ActivityIndicator,
  LayoutAnimation,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { ModelShareWithUsers, ModelShareWithUsersResponse } from '@/app/models/ModelShareWithUsers';
import { CALL_FROM, showSnackbar } from '../utils';
import { apiService } from '@/services/ApiService';
import { API_ENDPOINTS } from '@/config/api';


if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

const SHARE_OPTIONS = [
  { id: 'just_me', label: 'Just Me' },
  { id: 'everyone', label: 'Everyone' },
  { id: 'selected_users', label: 'Selected users, teams and territories' },
];

const ACCESSIBILITY_OPTIONS = [
  { id: 'View Only', label: 'Can view only' },
  { id: 'Public', label: 'Can view and edit' },
];

const DropdownSection = ({ 
  label, 
  value, 
  options, 
  onSelect, 
  expanded, 
  onToggle,
  selectedValues = [],
  isMultiSelect = false,
  showType = false,
}: {
  label: string;
  value: string;
  options: { id: string; label: string; type?: string }[];
  onSelect: (id: string) => void;
  expanded: boolean;
  onToggle: () => void;
  selectedValues?: string[];
  isMultiSelect?: boolean;
  showType?: boolean;
}) => {
  const getDisplayText = () => {
    if (isMultiSelect) {
      if (selectedValues.length === 0) return 'Select users';
      if (selectedValues.length === 1) {
        const selected = options.find(opt => opt.id === selectedValues[0]);
        return selected?.label || '';
      }
      return `${selectedValues.length} selected`;
    }
    const option = options.find(opt => opt.id === value);
    return option?.label || '';
  };

  React.useEffect(() => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
  }, [expanded]);

  return (
    <View style={styles.dropdownContainer}>
      <Text style={styles.sectionLabel}>{label}</Text>
      <View style={styles.dropdownWrapper}>
        <TouchableOpacity 
          style={[
            styles.dropdownTrigger,
            expanded && styles.dropdownTriggerExpanded
          ]}
          onPress={onToggle}
          activeOpacity={0.7}
        >
          <View style={styles.dropdownTriggerContent}>
            <Text style={[
              styles.dropdownTriggerText,
              expanded && styles.dropdownTriggerTextExpanded
            ]}>
              {getDisplayText()}
            </Text>
            <MaterialIcons 
              name={expanded ? "keyboard-arrow-up" : "keyboard-arrow-down"} 
              size={24} 
              color={expanded ? "#0F96BB" : "#666"} 
              style={styles.dropdownIcon}
            />
          </View>
        </TouchableOpacity>

        {expanded && (
          <ScrollView style={styles.optionsScrollView} nestedScrollEnabled>
            <View style={styles.optionsContainer}>
              {options.map((option, index) => {
                const isSelected = isMultiSelect 
                  ? selectedValues.includes(option.id)
                  : value === option.id;

                return (
                  <TouchableOpacity 
                    key={option.id}
                    style={[
                      styles.optionItem,
                      isSelected && styles.optionItemSelected,
                      index === options.length - 1 && styles.optionItemLast
                    ]}
                    onPress={() => {
                      onSelect(option.id);
                      if (!isMultiSelect) {
                        onToggle();
                      }
                    }}
                    activeOpacity={0.7}
                  >
                    {isMultiSelect && (
                      <View style={[styles.checkbox, isSelected && styles.checkboxChecked]}>
                        {isSelected && <MaterialIcons name="check" size={16} color="#fff" />}
                      </View>
                    )}
                    <View style={styles.optionContent}>
                      <Text style={[
                        styles.optionText,
                        isSelected && styles.optionTextSelected
                      ]}>
                        {option.label}
                      </Text>
                      {showType && option.type && (
                        <Text style={styles.optionType}>
                          {option.type === 'user' ? 'User' : 'Territory'}
                        </Text>
                      )}
                    </View>
                    {!isMultiSelect && isSelected && (
                      <MaterialIcons name="check" size={20} color="#0F96BB" />
                    )}
                  </TouchableOpacity>
                );
              })}
            </View>
          </ScrollView>
        )}
      </View>
    </View>
  );
};

export default function SaveView() {
  const router = useRouter();
  const params = useLocalSearchParams();

  const [title, setTitle] = useState('');
  const [showShareOptions, setShowShareOptions] = useState(false);
  const [showAccessibilityOptions, setShowAccessibilityOptions] = useState(false);
  const [showUsersList, setShowUsersList] = useState(false);
  const [selectedShareOption, setSelectedShareOption] = useState(SHARE_OPTIONS[0].id);
  const [selectedAccessibility, setSelectedAccessibility] = useState(ACCESSIBILITY_OPTIONS[0].id);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [preventSharing, setPreventSharing] = useState(false);
  const [filterData, setFilterData] = useState<any>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [usersList, setUsersList] = useState<ModelShareWithUsers | null>(null);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch users from API
  const fetchUsers = async () => {
    try {
      setIsLoadingUsers(true);
      setError(null);
      console.log('responseInSaveView'+ selectedUsers );
      const response = await apiService.get<ModelShareWithUsersResponse>(API_ENDPOINTS.SARE_WITH_USERS);
      console.log('responseInSaveView', JSON.stringify(response));
      if (response?.data) {
        const model = ModelShareWithUsers.fromResponse(response.data);
        if (model) {
          setUsersList(model);
          // If we have previously selected users, validate them against the new list
          if (selectedUsers.length > 0) {
            const validUserIds = model.users.map(u => u.id.toString());
            validUserIds.push(...model.territories.map(u => u.id.toString()));
            setSelectedUsers(selectedUsers.filter(id => validUserIds.includes(id)));
          }
        } else {
          showSnackbar(response.data.message || 'Failed to load share options');
        }
      }
    } catch (err) {
      setError('Failed to load users');
      console.error('Error fetching users:', err);
    } finally {
      setIsLoadingUsers(false);
    }
  };

  // Load initial values from navigation params
  useEffect(() => {
    if (!isInitialized && params) {
      try {
        if (params.filterData) {
          const parsedFilterData = JSON.parse(params.filterData as string);
          console.log('parsedFilterData', parsedFilterData);
          
          const updates: any = {
            filterData: parsedFilterData
          };

          if (parsedFilterData.name) {
            updates.title = parsedFilterData.name;
          }

          console.log('Filter values:', {
            share_with: parsedFilterData.share_with,
            accessibility: parsedFilterData.accessibility,
            users: parsedFilterData.users
          });
          
          if (parsedFilterData.share_with) {
            updates.selectedShareOption = parsedFilterData.share_with;
          }
          
          if (parsedFilterData.users && Array.isArray(parsedFilterData.users)) {
            updates.selectedUsers = parsedFilterData.users;
          }
          
          if (parsedFilterData.accessibility) {
            updates.selectedAccessibility = parsedFilterData.accessibility;
          }
          
          if (parsedFilterData.prevent_sharing !== undefined) {
            updates.preventSharing = parsedFilterData.prevent_sharing;
          }

          setTitle(updates.title || '');
          setSelectedShareOption(updates.selectedShareOption || SHARE_OPTIONS[0].id);
          setSelectedUsers(updates.selectedUsers || []);
          setSelectedAccessibility(updates.selectedAccessibility || ACCESSIBILITY_OPTIONS[0].id);
          setPreventSharing(updates.preventSharing || false);
          setFilterData(updates.filterData);
        }
      } catch (e) {
        console.error('Error parsing navigation params:', e);
      }
      setIsInitialized(true);
    }
  }, [isInitialized, params]);

  // Fetch users when share option changes to selected_users
  useEffect(() => {
    console.log('responseInSaveView', selectedShareOption);
    if (selectedShareOption === 'selected_users' ) {
      fetchUsers();
    }
  }, [selectedShareOption]);



  const getApplyEndPoint = (callFrom: string) => {
    if(callFrom == CALL_FROM.CONTACT_LIST){
      return API_ENDPOINTS.CONTACTS_FILTERS_UPDATE;
    }
    else if(callFrom == CALL_FROM.PRODUCT_LIST){   
      return API_ENDPOINTS.PRODUCT_FILTERS_UPDATE;
    }
    else if(callFrom == CALL_FROM.DEAL_LIST){
      return API_ENDPOINTS.DEAL_FILTERS_UPDATE;
    }
    else if(callFrom == CALL_FROM.ACCOUNT_LIST){
      return API_ENDPOINTS.ACCOUNT_FILTERS_UPDATE;
    }
  }

  const handleSave = async () => {
    try {
      if (!title.trim()) {
        showSnackbar('Please enter a title');
        return;
      }

      const data = {
        title: title,
        share_with: selectedShareOption,
        users: selectedShareOption === 'selected_users' ? selectedUsers : [],
        accessibility: selectedAccessibility,
        prevent_sharing: preventSharing,
        // Preserve other filter data fields
        ...(filterData && {
          filters: filterData.filters,
          id: filterData.id,
          created_at: filterData.created_at,
          updated_at: filterData.updated_at,
        }),
      };

      console.log('Saving data:', data);
      var endpoint=getApplyEndPoint(params.callFrom as string)
      const response = await apiService.post<{ status: boolean; data?: any; message?: string }>(
        endpoint || '', 
        data
      );
      console.log('Save filter response:', JSON.stringify(response));

      if (response?.data?.status) {
        showSnackbar(response?.data.message||'Filter saved successfully');
        router.back();
      } else {
        showSnackbar(response?.data?.message || 'Failed to save filter');
      }
    } catch (error) {
      console.error('Error saving filter:', error);
      showSnackbar('Failed to save filter. Please try again.');
    }
  };

  const getCombinedOptions = () => {
    if (!usersList) return [];
    
    const allOptions = [
      ...usersList.users.map(user => ({
        id: user.id.toString(),
        label: user.name,
        type: 'user'
      })),
      ...usersList.territories.map(territory => ({
        id: territory.id.toString(),
        label: territory.name,
        type: 'territory'
      }))
    ];
    
    // Sort by type and then by label
    return allOptions.sort((a, b) => {
      if (a.type === b.type) {
        return a.label.localeCompare(b.label);
      }
      return a.type === 'user' ? -1 : 1;
    });
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <MaterialIcons name="arrow-back" size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {params.isEdit === 'true' ? 'Edit Filter' : 'Save Filter'}
        </Text>
        <TouchableOpacity onPress={handleSave}>
          <MaterialIcons name="check" size={24} color="#000" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        <TextInput
          style={styles.titleInput}
          placeholder="Enter title"
          value={title}
          onChangeText={setTitle}
          placeholderTextColor="#666"
        />

        <DropdownSection
          label="Share with"
          value={selectedShareOption}
          options={SHARE_OPTIONS}
          onSelect={setSelectedShareOption}
          expanded={showShareOptions}
          onToggle={() => {
            setShowShareOptions(!showShareOptions);
            setShowAccessibilityOptions(false);
            setShowUsersList(false);
          }}
        />

        {selectedShareOption === 'selected_users' && !showShareOptions && (
          <>
            {isLoadingUsers ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color="#0F96BB" />
                <Text style={styles.loadingText}>Loading users...</Text>
              </View>
            ) : error ? (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{error}</Text>
                <TouchableOpacity onPress={fetchUsers} style={styles.retryButton}>
                  <Text style={styles.retryText}>Retry</Text>
                </TouchableOpacity>
              </View>
            ) : usersList && (
              <DropdownSection
                label="Select users/teams/territories"
                value=""
                options={getCombinedOptions()}
                onSelect={(userId) => {
                  if (selectedUsers.includes(userId)) {
                    setSelectedUsers(selectedUsers.filter(id => id !== userId));
                  } else {
                    setSelectedUsers([...selectedUsers, userId]);
                  }
                }}
                expanded={showUsersList}
                onToggle={() => {
                  setShowUsersList(!showUsersList);
                  setShowShareOptions(false);
                  setShowAccessibilityOptions(false);
                }}
                selectedValues={selectedUsers}
                isMultiSelect={true}
                showType={true}
              />
            )}
          </>
        )}

        <DropdownSection
          label="Accessibility"
          value={selectedAccessibility}
          options={ACCESSIBILITY_OPTIONS}
          onSelect={setSelectedAccessibility}
          expanded={showAccessibilityOptions}
          onToggle={() => {
            setShowAccessibilityOptions(!showAccessibilityOptions);
            setShowShareOptions(false);
            setShowUsersList(false);
          }}
        />

        <TouchableOpacity 
          style={styles.checkboxContainer}
          onPress={() => setPreventSharing(!preventSharing)}
        >
          <View style={[styles.checkbox, preventSharing && styles.checkboxChecked]}>
            {preventSharing && (
              <MaterialIcons name="check" size={16} color="#fff" />
            )}
          </View>
          <Text style={styles.checkboxLabel}>
            Prevent users from sharing the view to other people
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 20,
    color: '#000',
  },
  content: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  titleInput: {
    height: 48,
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    fontSize: 16,
    color: '#000',
    marginBottom: 16,
  },
  sectionLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    marginTop: 16,
    marginLeft: 16,
    fontWeight: '500',
  },
  listItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderColor: '#e0e0e0',
    marginHorizontal: 16,
    borderRadius: 8,
  },
  selectedItem: {
    backgroundColor: '#F5FBFD',
  },
  listItemText: {
    fontSize: 16,
    color: '#333',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 16,
    marginTop: 24,
    marginHorizontal: 16,
    borderRadius: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#666',
    borderRadius: 2,
    marginRight: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxChecked: {
    backgroundColor: '#0F96BB',
    borderColor: '#0F96BB',
  },
  checkboxLabel: {
    fontSize: 14,
    color: '#000',
    flex: 1,
  },
  dropdownContainer: {
    marginBottom: 16,
  },
  dropdownWrapper: {
    backgroundColor: '#fff',
    borderRadius: 8,
    overflow: 'hidden',
    marginHorizontal: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  dropdownTrigger: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
  },
  dropdownTriggerExpanded: {
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    borderColor: '#0F96BB',
  },
  dropdownTriggerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  dropdownTriggerText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  dropdownTriggerTextExpanded: {
    color: '#0F96BB',
  },
  dropdownIcon: {
    marginLeft: 8,
  },
  optionsContainer: {
    backgroundColor: '#fff',
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    borderWidth: 1,
    borderColor: '#0F96BB',
    borderTopWidth: 0,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  optionItemSelected: {
    backgroundColor: '#F5FBFD',
  },
  optionItemLast: {
    borderBottomWidth: 0,
  },
  optionContent: {
    flex: 1,
    marginRight: 8,
  },
  optionType: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  optionText: {
    fontSize: 16,
    color: '#333',
  },
  optionTextSelected: {
    color: '#0F96BB',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    backgroundColor: '#fff',
    marginHorizontal: 16,
    borderRadius: 8,
    marginTop: 8,
  },
  loadingText: {
    marginLeft: 8,
    color: '#666',
    fontSize: 14,
  },
  errorContainer: {
    padding: 16,
    backgroundColor: '#fff',
    marginHorizontal: 16,
    borderRadius: 8,
    marginTop: 8,
    alignItems: 'center',
  },
  errorText: {
    color: '#ff4444',
    fontSize: 14,
    marginBottom: 8,
  },
  retryButton: {
    padding: 8,
    backgroundColor: '#0F96BB',
    borderRadius: 4,
  },
  retryText: {
    color: '#fff',
    fontSize: 14,
  },
  optionsScrollView: {
    maxHeight: 300,
  },
}); 