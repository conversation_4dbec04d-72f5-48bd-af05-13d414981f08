import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
} from 'react-native';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>Provider, <PERSON><PERSON> } from 'react-native-paper';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { apiService } from '../../../services/ApiService';
import { API_ENDPOINTS } from '@/config/api';
import { showSnackbar } from '../utils';

interface Currency {
  id: number;
  currency: string;
  currency_code: string;
  decimal: string;
  is_default: number;
  active: number;
}

interface PricingType {
  pricing_type: string;
}

interface PriceEntry {
  id: string;
  currency: string;
  amount: string;
  setup_fee?: string;
  isExpanded?: boolean;
  errors?: {
    currency?: string;
    amount?: string;
  };
}

export default function AddPricePage() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const currencies: Currency[] = JSON.parse(params.currencies as string || '[]');
  const pricingTypes: PricingType[] = JSON.parse(params.pricingtypes as string || '[]');

  // Check if we're in edit mode
  const isEditMode = params.editMode === 'true';
  const editCurrencyCode = params.editCurrencyCode as string;
  const editAmount = params.editAmount as string;
  const editCurrency = params.editCurrency as string;

  const [pricingType, setPricingType] = useState(
    pricingTypes.length === 1 ? pricingTypes[0].pricing_type : 'One-time pricing'
  );
  const [subscriptionCycle, setSubscriptionCycle] = useState('Monthly');
  const [billingCycles, setBillingCycles] = useState('0');
  const [showSubscriptionMenu, setShowSubscriptionMenu] = useState(false);

  const [expandedId, setExpandedId] = useState<string | null>(null);

  // Initialize prices array - different for edit vs add mode
  const [prices, setPrices] = useState<PriceEntry[]>(() => {
    if (isEditMode) {
      return [{
        id: '1',
        currency: editCurrencyCode || '',
        amount: editAmount || '',
        setup_fee: '',
        isExpanded: true
      }];
    } else {
      return [{
        id: '1',
        currency: currencies.length === 1 ? currencies[0].currency_code : '',
        amount: '',
        setup_fee: '',
        isExpanded: true
      }];
    }
  });
  
  const [showPricingMenu, setShowPricingMenu] = useState(false);
  const [showCurrencyMenu, setShowCurrencyMenu] = useState<string | null>(null);
  const [menuAnchor, setMenuAnchor] = useState({ x: 0, y: 0 });
  const [errors, setErrors] = useState({
    billingCycles: '',
    subscriptionCycle: '',
  });

  const validateFields = () => {
    let isValid = true;
    const newErrors = {
      billingCycles: '',
      subscriptionCycle: '',
    };
    
    // Validate subscription fields if pricing type is subscription
    if (pricingType.toLowerCase() === 'subscription pricing') {
      if (!subscriptionCycle) {
        newErrors.subscriptionCycle = 'Subscription cycle is required';
        isValid = false;
      }
      if (!billingCycles) {
        newErrors.billingCycles = 'Number of billing cycles is required';
        isValid = false;
      }
    }

    // Validate prices
    const newPrices = prices.map(price => ({
      ...price,
      errors: {
        currency: !price.currency ? 'Currency is required' : '',
        amount: !price.amount ? 'Amount is required' : '',
      }
    }));

    const hasPriceErrors = newPrices.some(price => 
      price.errors?.currency || price.errors?.amount
    );

    setPrices(newPrices);
    setErrors(newErrors);

    return isValid && !hasPriceErrors;
  };

  const handleSave = async () => {
    if (!validateFields()) {
      showSnackbar('Please fill in all required fields');
      return;
    }
   
    try {
      const payload = {
        productid:params.productId,
        pricing_type: pricingType,
        ...(pricingType.toLowerCase() === 'subscription pricing' && {
          subscription_cycle: subscriptionCycle,
          no_of_billing_cycle: billingCycles
        }),
        currencies: prices.map(price => ({
          currency: price.currency,
          base_currency_amount: price.amount,
          ...(pricingType.toLowerCase() === 'subscription pricing' && {
            setup_fee: price.setup_fee || "0"
          })
        }))
      };

      await apiService.post(API_ENDPOINTS.ADD_PRICE, payload);
      showSnackbar(isEditMode ? 'Price updated successfully' : 'Prices added successfully');
      router.back();
    } catch (error) {
      console.error('Error adding prices:', error);
      showSnackbar(error instanceof Error ? error.message : 'Failed to add prices');
    }
  };

  const openMenu = (type: 'pricing' | 'currency' | 'subscription', priceId: string, event: any) => {
    if (type === 'pricing' && pricingTypes.length === 1) return;
    if (type === 'currency' && currencies.length === 1) return;

    const { pageX, pageY } = event.nativeEvent;
    setMenuAnchor({ x: pageX, y: pageY });
    
    switch (type) {
      case 'pricing':
        setShowPricingMenu(true);
        break;
      case 'currency':
        setShowCurrencyMenu(priceId);
        break;
      case 'subscription':
        setShowSubscriptionMenu(true);
        break;
    }
  };

  const handleCurrencySelect = (priceId: string, currencyCode: string) => {
    setPrices(current =>
      current.map(price =>
        price.id === priceId ? { ...price, currency: currencyCode } : price
      )
    );
    setShowCurrencyMenu(null);
  };

  const handleAmountChange = (priceId: string, value: string) => {
    setPrices(current =>
      current.map(price =>
        price.id === priceId ? { ...price, amount: value } : price
      )
    );
  };

  const handleSetupFeeChange = (priceId: string, value: string) => {
    setPrices(current =>
      current.map(price =>
        price.id === priceId ? { ...price, setup_fee: value } : price
      )
    );
  };

  const addNewPrice = () => {
    setPrices(current => [
      ...current.map(price => ({ ...price, isExpanded: false })),
      {
        id: (current.length + 1).toString(),
        currency: currencies.length === 1 ? currencies[0].currency_code : '',
        amount: '',
        setup_fee: '',
        isExpanded: true
      }
    ]);
  };

  const removePrice = (priceId: string) => {
    if (prices.length === 1) return; // Don't remove if it's the last item
    setPrices(current => current.filter(price => price.id !== priceId));
  };

  const toggleExpand = (priceId: string) => {
    setPrices(current =>
      current.map(price => ({
        ...price,
        isExpanded: price.id === priceId ? !price.isExpanded : false
      }))
    );
  };

  const renderPriceItem = (price: PriceEntry, index: number) => {
    const isExpanded = price.isExpanded;
    return (
      <View key={price.id} style={styles.priceSection}>
        <TouchableOpacity 
          style={styles.priceHeader}
          onPress={() => toggleExpand(price.id)}
        >
          <Text style={styles.priceTitle}>Price {index + 1}</Text>
          <View style={styles.headerActions}>
            <MaterialCommunityIcons 
              name={isExpanded ? "chevron-up" : "chevron-down"}
              size={24} 
              color="#666" 
            />
            {prices.length > 1 && !isEditMode && (
              <TouchableOpacity
                onPress={() => removePrice(price.id)}
                style={styles.deleteButton}
              >
                <MaterialCommunityIcons
                  name="delete-outline"
                  size={24}
                  color="#FF4444"
                />
              </TouchableOpacity>
            )}
          </View>
        </TouchableOpacity>

        {isExpanded && (
          <>
            <View style={styles.field}>
              <Text style={styles.label}>Currency</Text>
              <TouchableOpacity
                style={[
                  styles.dropdown,
                  (currencies.length === 1 || isEditMode) && styles.dropdownDisabled,
                  price.errors?.currency ? styles.inputError : null
                ]}
                onPress={(event) => openMenu('currency', price.id, event)}
                disabled={currencies.length === 1 || isEditMode}
              >
                <Text style={styles.dropdownText}>
                  {price.currency ? 
                    currencies.find(c => c.currency_code === price.currency)?.currency + 
                    ` (${price.currency})` : 
                    'Select currency'}
                </Text>
                {currencies.length > 1 && !isEditMode && (
                  <MaterialCommunityIcons
                    name="chevron-down"
                    size={24}
                    color="#666"
                  />
                )}
              </TouchableOpacity>
              {price.errors?.currency ? (
                <Text style={styles.errorText}>{price.errors.currency}</Text>
              ) : null}
              <Menu
                visible={showCurrencyMenu === price.id}
                onDismiss={() => setShowCurrencyMenu(null)}
                anchor={menuAnchor}
              >
                {currencies.map((currency) => (
                  <Menu.Item
                    key={currency.id}
                    onPress={() => handleCurrencySelect(price.id, currency.currency_code)}
                    title={`${currency.currency} (${currency.currency_code})`}
                  />
                ))}
              </Menu>
            </View>

            <View style={styles.field}>
              <Text style={styles.label}>Unit price *</Text>
              <TextInput
                style={[
                  styles.input,
                  price.errors?.amount ? styles.inputError : null
                ]}
                value={price.amount}
                onChangeText={(value) => handleAmountChange(price.id, value)}
                keyboardType="numeric"
                placeholder="Enter amount"
                placeholderTextColor="#999"
              />
              {price.errors?.amount ? (
                <Text style={styles.errorText}>{price.errors.amount}</Text>
              ) : null}
            </View>

            {pricingType.toLowerCase() === 'subscription pricing' && (
              <View style={styles.field}>
                <Text style={styles.label}>Setup fee</Text>
                <TextInput
                  style={styles.input}
                  value={price.setup_fee}
                  onChangeText={(value) => handleSetupFeeChange(price.id, value)}
                  keyboardType="numeric"
                  placeholder="Enter setup fee (optional)"
                  placeholderTextColor="#999"
                />
              </View>
            )}
          </>
        )}
      </View>
    );
  };

  const renderSubscriptionFields = () => {
    if (pricingType.toLowerCase() !== 'subscription pricing') return null;

    return (
      <>
        <View style={styles.section}>
          <Text style={styles.label}>Subscription cycle</Text>
          <TouchableOpacity
            style={[
              styles.dropdown,
              errors.subscriptionCycle ? styles.inputError : null
            ]}
            onPress={(event) => openMenu('subscription', '', event)}
          >
            <Text style={styles.dropdownText}>{subscriptionCycle}</Text>
            <MaterialCommunityIcons name="chevron-down" size={24} color="#666" />
          </TouchableOpacity>
          {errors.subscriptionCycle ? (
            <Text style={styles.errorText}>{errors.subscriptionCycle}</Text>
          ) : null}
          <Menu
            visible={showSubscriptionMenu}
            onDismiss={() => setShowSubscriptionMenu(false)}
            anchor={menuAnchor}
          >
            {['Monthly', 'Yearly', 'Weekly'].map((cycle) => (
              <Menu.Item
                key={cycle}
                onPress={() => {
                  setSubscriptionCycle(cycle);
                  setShowSubscriptionMenu(false);
                  setErrors(prev => ({ ...prev, subscriptionCycle: '' }));
                }}
                title={cycle}
              />
            ))}
          </Menu>
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>No. of billing cycles</Text>
          <TextInput
            style={[
              styles.input,
              errors.billingCycles ? styles.inputError : null
            ]}
            value={billingCycles}
            onChangeText={(value) => {
              setBillingCycles(value);
              setErrors(prev => ({ ...prev, billingCycles: '' }));
            }}
            keyboardType="numeric"
            placeholder="Enter number of billing cycles (0 for unlimited)"
            placeholderTextColor="#999"
          />
          {errors.billingCycles ? (
            <Text style={styles.errorText}>{errors.billingCycles}</Text>
          ) : null}
          <Text style={styles.helperText}>Enter 0 for unlimited billing cycles</Text>
        </View>
      </>
    );
  };

  return (
    <PaperProvider>
      <SafeAreaView style={styles.container} edges={['top']}>
        <Appbar.Header style={styles.appbar} statusBarHeight={0}>
          <Appbar.BackAction 
            color="#fff" 
            onPress={() => router.back()} 
          />
          <Appbar.Content
            title={isEditMode ? "Edit price" : "Add or edit prices"}
            titleStyle={{ color: '#fff' }}
          />
          <Appbar.Action 
            icon="check"
            color="#fff"
            onPress={handleSave}
          />
        </Appbar.Header>

        <ScrollView style={styles.content}>
          <View style={styles.section}>
            <Text style={styles.label}>Pricing type</Text>
            <TouchableOpacity
              style={[
                styles.dropdown,
                (pricingTypes.length === 1 || isEditMode) && styles.dropdownDisabled
              ]}
              onPress={(event) => openMenu('pricing', '', event)}
              disabled={pricingTypes.length === 1 || isEditMode}
            >
              <Text style={styles.dropdownText}>{pricingType}</Text>
              {pricingTypes.length > 1 && !isEditMode && (
                <MaterialCommunityIcons name="chevron-down" size={24} color="#666" />
              )}
            </TouchableOpacity>
            <Menu
              visible={showPricingMenu}
              onDismiss={() => setShowPricingMenu(false)}
              anchor={menuAnchor}
            >
              {pricingTypes.map((type, index) => (
                <Menu.Item
                  key={index}
                  onPress={() => {
                    setPricingType(type.pricing_type);
                    setShowPricingMenu(false);
                  }}
                  title={type.pricing_type}
                />
              ))}
            </Menu>
          </View>

          {renderSubscriptionFields()}

          <Text style={styles.sectionTitle}>Prices</Text>
          {prices.map((price, index) => renderPriceItem(price, index))}

          {currencies.length > 1 && !isEditMode && (
            <View style={styles.addButtonContainer}>
              <Button
                mode="outlined"
                onPress={addNewPrice}
                icon="plus"
                style={styles.addButton}
                labelStyle={styles.addButtonLabel}
              >
                Add More Prices
              </Button>
            </View>
          )}
        </ScrollView>
      </SafeAreaView>
    </PaperProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  appbar: {
    backgroundColor: '#0F96BB',
    elevation: 0,
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: '#fff',
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666',
    marginHorizontal: 16,
    marginBottom: 8,
  },
  priceSection: {
    backgroundColor: '#fff',
    padding: 16,
    marginBottom: 8,
  },
  priceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  priceTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  field: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 4,
    paddingHorizontal: 12,
    fontSize: 16,
    color: '#333',
    backgroundColor: '#fff',
  },
  dropdown: {
    height: 48,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 4,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#fff',
  },
  dropdownText: {
    fontSize: 16,
    color: '#333',
  },
  dropdownDisabled: {
    backgroundColor: '#f5f5f5',
    borderColor: '#ccc',
  },
  addButtonContainer: {
    padding: 16,
    paddingTop: 8,
  },
  addButton: {
    borderColor: '#0F96BB',
    borderRadius: 4,
  },
  addButtonLabel: {
    color: '#0F96BB',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deleteButton: {
    marginLeft: 8,
  },
  helperText: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
    fontStyle: 'italic',
  },
  errorText: {
    color: '#FF4444',
    fontSize: 12,
    marginTop: 4,
  },
  inputError: {
    borderColor: '#FF4444',
  },
}); 