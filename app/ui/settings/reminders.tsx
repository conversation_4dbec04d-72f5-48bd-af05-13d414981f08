import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { Appbar, Switch, RadioButton } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useColors } from '@/hooks/useThemeColor';

interface ReminderPreferences {
  taskReminders: boolean;
  meetingReminders: boolean;
  followUpReminders: boolean;
  dealReminders: boolean;
  reminderTime: '5min' | '10min' | '15min' | '30min' | '1hour';
  soundEnabled: boolean;
  vibrationEnabled: boolean;
}

export default function RemindersPage() {
  const router = useRouter();
  const colors = useColors();
  const [preferences, setPreferences] = useState<ReminderPreferences>({
    taskReminders: true,
    meetingReminders: true,
    followUpReminders: true,
    dealReminders: true,
    reminderTime: '15min',
    soundEnabled: true,
    vibrationEnabled: true,
  });

  const ToggleItem = ({ title, value, onToggle, description }: {
    title: string;
    value: boolean;
    onToggle: () => void;
    description?: string;
  }) => (
    <View style={styles.settingItem}>
      <View style={styles.settingContent}>
        <Text style={styles.settingText}>{title}</Text>
        {description && (
          <Text style={styles.settingDescription}>{description}</Text>
        )}
      </View>
      <Switch
        value={value}
        onValueChange={onToggle}
        color={colors.primary}
      />
    </View>
  );

  const reminderTimeOptions = [
    { label: '5 minutes before', value: '5min' },
    { label: '10 minutes before', value: '10min' },
    { label: '15 minutes before', value: '15min' },
    { label: '30 minutes before', value: '30min' },
    { label: '1 hour before', value: '1hour' },
  ];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#f5f5f5',
    },
    appbar: {
      backgroundColor: '#0F96BB',
      elevation: 0,
      height: 56,
    },
    content: {
      flex: 1,
    },
    section: {
      backgroundColor: '#fff',
      marginTop: 16,
    },
    sectionTitle: {
      fontSize: 14,
      color: '#666',
      padding: 16,
      paddingBottom: 8,
    },
    settingItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 16,
      backgroundColor: '#fff',
      borderBottomWidth: 1,
      borderBottomColor: '#eee',
    },
    settingContent: {
      flex: 1,
      marginRight: 16,
    },
    settingText: {
      fontSize: 16,
      color: '#333',
    },
    settingDescription: {
      fontSize: 14,
      color: '#666',
      marginTop: 4,
    },
    description: {
      fontSize: 14,
      color: '#666',
      padding: 16,
      lineHeight: 20,
    },
    radioItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 8,
      paddingHorizontal: 16,
    },
    radioLabel: {
      fontSize: 16,
      color: '#333',
      marginLeft: 8,
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Appbar.Header style={styles.appbar} statusBarHeight={0}>
        <Appbar.BackAction color="#fff" onPress={() => router.back()} />
        <Appbar.Content 
          title="Reminders" 
          titleStyle={{ color: '#fff', fontSize: 20, fontWeight: '600' }}
        />
      </Appbar.Header>

      <ScrollView style={styles.content}>
        <Text style={styles.description}>
          Configure your reminder preferences and notification settings.
        </Text>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>REMINDER TYPES</Text>
          <ToggleItem
            title="Task Reminders"
            value={preferences.taskReminders}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              taskReminders: !prev.taskReminders 
            }))}
          />
          <ToggleItem
            title="Meeting Reminders"
            value={preferences.meetingReminders}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              meetingReminders: !prev.meetingReminders 
            }))}
          />
          <ToggleItem
            title="Follow-up Reminders"
            value={preferences.followUpReminders}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              followUpReminders: !prev.followUpReminders 
            }))}
          />
          <ToggleItem
            title="Deal Reminders"
            value={preferences.dealReminders}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              dealReminders: !prev.dealReminders 
            }))}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>REMINDER TIME</Text>
          <RadioButton.Group
            onValueChange={value => setPreferences(prev => ({ 
              ...prev, 
              reminderTime: value as ReminderPreferences['reminderTime']
            }))}
            value={preferences.reminderTime}
          >
            {reminderTimeOptions.map((option) => (
              <View key={option.value} style={styles.settingItem}>
                <View style={styles.radioItem}>
                  <RadioButton value={option.value} color={colors.primary} />
                  <Text style={styles.radioLabel}>{option.label}</Text>
                </View>
              </View>
            ))}
          </RadioButton.Group>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>NOTIFICATION SETTINGS</Text>
          <ToggleItem
            title="Sound"
            value={preferences.soundEnabled}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              soundEnabled: !prev.soundEnabled 
            }))}
            description="Play sound for reminders"
          />
          <ToggleItem
            title="Vibration"
            value={preferences.vibrationEnabled}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              vibrationEnabled: !prev.vibrationEnabled 
            }))}
            description="Vibrate for reminders"
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
} 