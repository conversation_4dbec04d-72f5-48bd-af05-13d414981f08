import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Linking,
  Image,
} from 'react-native';
import { Appbar } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useColors } from '@/hooks/useThemeColor';

export default function AboutPage() {
  const router = useRouter();
  const colors = useColors();

  const MenuItem = ({ title, onPress, icon, showBorder = true }: {
    title: string;
    onPress: () => void;
    icon: string;
    showBorder?: boolean;
  }) => (
    <TouchableOpacity
      style={[styles.menuItem, showBorder && styles.menuItemBorder]}
      onPress={onPress}
    >
      <View style={styles.menuItemLeft}>
        <Ionicons name={icon as any} size={24} color="#666" style={styles.menuIcon} />
        <Text style={styles.menuItemText}>{title}</Text>
      </View>
      <Ionicons name="chevron-forward" size={24} color="#757575" />
    </TouchableOpacity>
  );

  const openLink = (url: string) => {
    Linking.openURL(url);
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#f5f5f5',
    },
    appbar: {
      backgroundColor: '#0F96BB',
      elevation: 0,
      height: 56,
    },
    content: {
      flex: 1,
    },
    header: {
      alignItems: 'center',
      padding: 32,
      backgroundColor: '#fff',
    },
    logo: {
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: '#fff',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 16,
      overflow: 'hidden',
    },
    appLogo: {
      width: '100%',
      height: '100%',
      resizeMode: 'contain',
    },
    appName: {
      fontSize: 24,
      fontWeight: '600',
      color: '#333',
      marginBottom: 8,
    },
    version: {
      fontSize: 16,
      color: '#666',
    },
    section: {
      backgroundColor: '#fff',
      marginTop: 16,
    },
    sectionTitle: {
      fontSize: 14,
      color: '#666',
      padding: 16,
      paddingBottom: 8,
    },
    menuItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 16,
      backgroundColor: '#fff',
    },
    menuItemBorder: {
      borderBottomWidth: 1,
      borderBottomColor: '#eee',
    },
    menuItemLeft: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    menuIcon: {
      marginRight: 16,
    },
    menuItemText: {
      fontSize: 16,
      color: '#333',
    },
    copyright: {
      padding: 16,
      alignItems: 'center',
    },
    copyrightText: {
      fontSize: 14,
      color: '#666',
      textAlign: 'center',
    },
    appInfo: {
      alignItems: 'center',
      padding: 32,
    },
    appIcon: {
      width: 80,
      height: 80,
      borderRadius: 40,
      marginBottom: 16,
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Appbar.Header style={styles.appbar} statusBarHeight={0}>
        <Appbar.BackAction color="#fff" onPress={() => router.back()} />
        <Appbar.Content 
          title="About Easy Daily Sales"
          titleStyle={{ color: '#fff', fontSize: 20, fontWeight: '600' }}
        />
      </Appbar.Header>

      <ScrollView style={styles.content}>
        <View style={styles.header}>
          <View style={styles.logo}>
            <Image 
              source={require('@/assets/images/android/icon.png')}
              style={styles.appLogo}
            />
          </View>
          <Text style={styles.appName}>Easy Daily Sales</Text>
          <Text style={styles.version}>Version 2.0.0</Text>
        </View>

        <View style={styles.section}>
          <MenuItem
            title="Rate us"
            icon="star-outline"
            onPress={() => openLink('https://www.easydaily.com')}
          />
          <MenuItem
            title="Privacy Policy"
            icon="shield-checkmark-outline"
            onPress={() => openLink('https://www.easydaily.com/privacy-policy')}
          />
          <MenuItem
            title="Terms of Service"
            icon="document-text-outline"
            onPress={() => openLink('https://www.easydaily.com/terms')}
          />
          <MenuItem
            title="Open Source Libraries"
            icon="code-slash-outline"
            onPress={() =>openLink('https://www.easydaily.com/how-it-works')}
                 //router.push('/ui/settings/licenses' as any)}
          />
          <MenuItem
            title="Help & Support"
            icon="help-circle-outline"
            onPress={() => openLink('https://www.easydaily.com/')}
            showBorder={false}
          />
        </View>

        <View style={styles.copyright}>
          <Text style={styles.copyrightText}>
            © 2024 EasyDaily Inc. All rights reserved.{'\n'}
            Made with ♥ in Coimbatore, India
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
} 