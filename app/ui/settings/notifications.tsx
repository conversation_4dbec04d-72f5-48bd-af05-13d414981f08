import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { Appbar, Switch } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useColors } from '@/hooks/useThemeColor';

export default function NotificationsPage() {
  const router = useRouter();
  const colors = useColors();
  const [settings, setSettings] = useState({
    newLeads: true,
    taskReminders: true,
    meetingReminders: true,
    emailNotifications: false,
    deals: true,
  });

  const NotificationItem = ({ title, value, onToggle }: { title: string, value: boolean, onToggle: () => void }) => (
    <View style={styles.notificationItem}>
      <Text style={styles.notificationText}>{title}</Text>
      <Switch
        value={value}
        onValueChange={onToggle}
        color={colors.primary}
      />
    </View>
  );

  const handleToggle = (key: keyof typeof settings) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#f5f5f5',
    },
    appbar: {
      backgroundColor: '#0F96BB',
      elevation: 0,
      height: 56,
    },
    content: {
      flex: 1,
    },
    section: {
      backgroundColor: '#fff',
      marginTop: 16,
    },
    notificationItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: '#eee',
    },
    notificationText: {
      fontSize: 16,
      color: '#333',
    },
    description: {
      fontSize: 14,
      color: '#666',
      padding: 16,
      lineHeight: 20,
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Appbar.Header style={styles.appbar} statusBarHeight={0}>
        <Appbar.BackAction color="#fff" onPress={() => router.back()} />
        <Appbar.Content 
          title="Notifications" 
          titleStyle={{ color: '#fff', fontSize: 20, fontWeight: '600' }}
        />
      </Appbar.Header>

      <ScrollView style={styles.content}>
        <Text style={styles.description}>
          Configure which notifications you want to see on your home screen.
          These settings only affect the notifications shown on the app's home screen.
        </Text>

        <View style={styles.section}>
          <NotificationItem
            title="New Leads"
            value={settings.newLeads}
            onToggle={() => handleToggle('newLeads')}
          />
          <NotificationItem
            title="Task Reminders"
            value={settings.taskReminders}
            onToggle={() => handleToggle('taskReminders')}
          />
          <NotificationItem
            title="Meeting Reminders"
            value={settings.meetingReminders}
            onToggle={() => handleToggle('meetingReminders')}
          />
          <NotificationItem
            title="Email Notifications"
            value={settings.emailNotifications}
            onToggle={() => handleToggle('emailNotifications')}
          />
          <NotificationItem
            title="Deals"
            value={settings.deals}
            onToggle={() => handleToggle('deals')}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
} 