import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { Appbar, Switch } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useColors } from '@/hooks/useThemeColor';

interface AlertPreferences {
  leadAlerts: boolean;
  dealAlerts: boolean;
  taskAlerts: boolean;
  meetingAlerts: boolean;
  teamAlerts: boolean;
  emailAlerts: boolean;
  pushNotifications: boolean;
  soundEnabled: boolean;
  vibrationEnabled: boolean;
}

export default function AlertsPage() {
  const router = useRouter();
  const colors = useColors();
  const [preferences, setPreferences] = useState<AlertPreferences>({
    leadAlerts: true,
    dealAlerts: true,
    taskAlerts: true,
    meetingAlerts: true,
    teamAlerts: true,
    emailAlerts: false,
    pushNotifications: true,
    soundEnabled: true,
    vibrationEnabled: true,
  });

  const ToggleItem = ({ title, value, onToggle, description }: {
    title: string;
    value: boolean;
    onToggle: () => void;
    description?: string;
  }) => (
    <View style={styles.settingItem}>
      <View style={styles.settingContent}>
        <Text style={styles.settingText}>{title}</Text>
        {description && (
          <Text style={styles.settingDescription}>{description}</Text>
        )}
      </View>
      <Switch
        value={value}
        onValueChange={onToggle}
        color={colors.primary}
      />
    </View>
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#f5f5f5',
    },
    appbar: {
      backgroundColor: '#0F96BB',
      elevation: 0,
      height: 56,
    },
    content: {
      flex: 1,
    },
    section: {
      backgroundColor: '#fff',
      marginTop: 16,
    },
    sectionTitle: {
      fontSize: 14,
      color: '#666',
      padding: 16,
      paddingBottom: 8,
    },
    settingItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 16,
      backgroundColor: '#fff',
      borderBottomWidth: 1,
      borderBottomColor: '#eee',
    },
    settingContent: {
      flex: 1,
      marginRight: 16,
    },
    settingText: {
      fontSize: 16,
      color: '#333',
    },
    settingDescription: {
      fontSize: 14,
      color: '#666',
      marginTop: 4,
    },
    description: {
      fontSize: 14,
      color: '#666',
      padding: 16,
      lineHeight: 20,
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Appbar.Header style={styles.appbar} statusBarHeight={0}>
        <Appbar.BackAction color="#fff" onPress={() => router.back()} />
        <Appbar.Content 
          title="Alerts" 
          titleStyle={{ color: '#fff', fontSize: 20, fontWeight: '600' }}
        />
      </Appbar.Header>

      <ScrollView style={styles.content}>
        <Text style={styles.description}>
          Configure which alerts you want to receive and how you want to be notified about them.
        </Text>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>ALERT TYPES</Text>
          <ToggleItem
            title="Lead Alerts"
            value={preferences.leadAlerts}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              leadAlerts: !prev.leadAlerts 
            }))}
            description="New leads and lead updates"
          />
          <ToggleItem
            title="Deal Alerts"
            value={preferences.dealAlerts}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              dealAlerts: !prev.dealAlerts 
            }))}
            description="Deal status changes and updates"
          />
          <ToggleItem
            title="Task Alerts"
            value={preferences.taskAlerts}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              taskAlerts: !prev.taskAlerts 
            }))}
            description="Task assignments and due dates"
          />
          <ToggleItem
            title="Meeting Alerts"
            value={preferences.meetingAlerts}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              meetingAlerts: !prev.meetingAlerts 
            }))}
            description="Meeting schedules and changes"
          />
          <ToggleItem
            title="Team Alerts"
            value={preferences.teamAlerts}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              teamAlerts: !prev.teamAlerts 
            }))}
            description="Team activity and updates"
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>NOTIFICATION METHODS</Text>
          <ToggleItem
            title="Push Notifications"
            value={preferences.pushNotifications}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              pushNotifications: !prev.pushNotifications 
            }))}
          />
          <ToggleItem
            title="Email Alerts"
            value={preferences.emailAlerts}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              emailAlerts: !prev.emailAlerts 
            }))}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>NOTIFICATION SETTINGS</Text>
          <ToggleItem
            title="Sound"
            value={preferences.soundEnabled}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              soundEnabled: !prev.soundEnabled 
            }))}
            description="Play sound for alerts"
          />
          <ToggleItem
            title="Vibration"
            value={preferences.vibrationEnabled}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              vibrationEnabled: !prev.vibrationEnabled 
            }))}
            description="Vibrate for alerts"
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
} 