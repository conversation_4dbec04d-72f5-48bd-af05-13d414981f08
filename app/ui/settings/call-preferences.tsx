import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { Appbar, RadioButton, Switch } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useColors } from '@/hooks/useThemeColor';

interface CallPreferences {
  defaultDialer: 'system' | 'in_app';
  autoRecordCalls: boolean;
  showCallNotes: boolean;
  callReminders: boolean;
}

export default function CallPreferencesPage() {
  const router = useRouter();
  const colors = useColors();
  const [preferences, setPreferences] = useState<CallPreferences>({
    defaultDialer: 'system',
    autoRecordCalls: true,
    showCallNotes: true,
    callReminders: true,
  });

  const ToggleItem = ({ title, value, onToggle }: {
    title: string;
    value: boolean;
    onToggle: () => void;
  }) => (
    <View style={styles.settingItem}>
      <Text style={styles.settingText}>{title}</Text>
      <Switch
        value={value}
        onValueChange={onToggle}
        color={colors.primary}
      />
    </View>
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#f5f5f5',
    },
    appbar: {
      backgroundColor: '#0F96BB',
      elevation: 0,
      height: 56,
    },
    content: {
      flex: 1,
    },
    section: {
      backgroundColor: '#fff',
      marginTop: 16,
    },
    sectionTitle: {
      fontSize: 14,
      color: '#666',
      padding: 16,
      paddingBottom: 8,
    },
    settingItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 16,
      backgroundColor: '#fff',
      borderBottomWidth: 1,
      borderBottomColor: '#eee',
    },
    settingText: {
      fontSize: 16,
      color: '#333',
    },
    radioItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 8,
      paddingHorizontal: 16,
    },
    radioLabel: {
      fontSize: 16,
      color: '#333',
      marginLeft: 8,
    },
    description: {
      fontSize: 14,
      color: '#666',
      padding: 16,
      lineHeight: 20,
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Appbar.Header style={styles.appbar} statusBarHeight={0}>
        <Appbar.BackAction color="#fff" onPress={() => router.back()} />
        <Appbar.Content 
          title="Call Preferences" 
          titleStyle={{ color: '#fff', fontSize: 20, fontWeight: '600' }}
        />
      </Appbar.Header>

      <ScrollView style={styles.content}>
        <Text style={styles.description}>
          Configure your calling preferences and options for a better calling experience.
        </Text>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>DEFAULT DIALER</Text>
          <RadioButton.Group
            onValueChange={value => setPreferences(prev => ({ ...prev, defaultDialer: value as 'system' | 'in_app' }))}
            value={preferences.defaultDialer}
          >
            <View style={styles.settingItem}>
              <View style={styles.radioItem}>
                <RadioButton value="system" color={colors.primary} />
                <Text style={styles.radioLabel}>System Dialer</Text>
              </View>
            </View>
            <View style={styles.settingItem}>
              <View style={styles.radioItem}>
                <RadioButton value="in_app" color={colors.primary} />
                <Text style={styles.radioLabel}>In-App Dialer</Text>
              </View>
            </View>
          </RadioButton.Group>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>CALL OPTIONS</Text>
          <ToggleItem
            title="Auto-record calls"
            value={preferences.autoRecordCalls}
            onToggle={() => setPreferences(prev => ({ ...prev, autoRecordCalls: !prev.autoRecordCalls }))}
          />
          <ToggleItem
            title="Show call notes"
            value={preferences.showCallNotes}
            onToggle={() => setPreferences(prev => ({ ...prev, showCallNotes: !prev.showCallNotes }))}
          />
          <ToggleItem
            title="Call reminders"
            value={preferences.callReminders}
            onToggle={() => setPreferences(prev => ({ ...prev, callReminders: !prev.callReminders }))}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
} 