import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { Appbar, Switch, ProgressBar } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useColors } from '@/hooks/useThemeColor';

interface OfflinePreferences {
  enableOfflineMode: boolean;
  syncContacts: boolean;
  syncDeals: boolean;
  syncTasks: boolean;
  syncNotes: boolean;
  autoSync: boolean;
}

export default function OfflinePreferencesPage() {
  const router = useRouter();
  const colors = useColors();
  const [preferences, setPreferences] = useState<OfflinePreferences>({
    enableOfflineMode: true,
    syncContacts: true,
    syncDeals: true,
    syncTasks: true,
    syncNotes: false,
    autoSync: true,
  });

  const [storageUsed, setStorageUsed] = useState({
    used: 156, // MB
    total: 500, // MB
  });

  const ToggleItem = ({ title, value, onToggle, description }: {
    title: string;
    value: boolean;
    onToggle: () => void;
    description?: string;
  }) => (
    <View style={styles.settingItem}>
      <View style={styles.settingContent}>
        <Text style={styles.settingText}>{title}</Text>
        {description && (
          <Text style={styles.settingDescription}>{description}</Text>
        )}
      </View>
      <Switch
        value={value}
        onValueChange={onToggle}
        color={colors.primary}
      />
    </View>
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#f5f5f5',
    },
    appbar: {
      backgroundColor: '#0F96BB',
      elevation: 0,
      height: 56,
    },
    content: {
      flex: 1,
    },
    section: {
      backgroundColor: '#fff',
      marginTop: 16,
    },
    sectionTitle: {
      fontSize: 14,
      color: '#666',
      padding: 16,
      paddingBottom: 8,
    },
    settingItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 16,
      backgroundColor: '#fff',
      borderBottomWidth: 1,
      borderBottomColor: '#eee',
    },
    settingContent: {
      flex: 1,
      marginRight: 16,
    },
    settingText: {
      fontSize: 16,
      color: '#333',
    },
    settingDescription: {
      fontSize: 14,
      color: '#666',
      marginTop: 4,
    },
    description: {
      fontSize: 14,
      color: '#666',
      padding: 16,
      lineHeight: 20,
    },
    storageSection: {
      padding: 16,
      backgroundColor: '#fff',
    },
    storageInfo: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 8,
    },
    storageText: {
      fontSize: 14,
      color: '#666',
    },
    progressBar: {
      height: 8,
      borderRadius: 4,
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Appbar.Header style={styles.appbar} statusBarHeight={0}>
        <Appbar.BackAction color="#fff" onPress={() => router.back()} />
        <Appbar.Content 
          title="Offline Preferences" 
          titleStyle={{ color: '#fff', fontSize: 20, fontWeight: '600' }}
        />
      </Appbar.Header>

      <ScrollView style={styles.content}>
        <Text style={styles.description}>
          Configure offline access settings and manage data synchronization for offline use.
        </Text>

        <View style={styles.section}>
          <ToggleItem
            title="Enable Offline Mode"
            value={preferences.enableOfflineMode}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              enableOfflineMode: !prev.enableOfflineMode 
            }))}
            description="Access your data without an internet connection"
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>SYNC OPTIONS</Text>
          <ToggleItem
            title="Sync Contacts"
            value={preferences.syncContacts}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              syncContacts: !prev.syncContacts 
            }))}
          />
          <ToggleItem
            title="Sync Deals"
            value={preferences.syncDeals}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              syncDeals: !prev.syncDeals 
            }))}
          />
          <ToggleItem
            title="Sync Tasks"
            value={preferences.syncTasks}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              syncTasks: !prev.syncTasks 
            }))}
          />
          <ToggleItem
            title="Sync Notes"
            value={preferences.syncNotes}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              syncNotes: !prev.syncNotes 
            }))}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>SYNC SETTINGS</Text>
          <ToggleItem
            title="Auto-sync when on Wi-Fi"
            value={preferences.autoSync}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              autoSync: !prev.autoSync 
            }))}
            description="Automatically sync data when connected to Wi-Fi"
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>STORAGE</Text>
          <View style={styles.storageSection}>
            <View style={styles.storageInfo}>
              <Text style={styles.storageText}>
                {storageUsed.used} MB of {storageUsed.total} MB used
              </Text>
              <Text style={styles.storageText}>
                {Math.round((storageUsed.used / storageUsed.total) * 100)}%
              </Text>
            </View>
            <ProgressBar
              progress={storageUsed.used / storageUsed.total}
              color={colors.primary}
              style={styles.progressBar}
            />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
} 