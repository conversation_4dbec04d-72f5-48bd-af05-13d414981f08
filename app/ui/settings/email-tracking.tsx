import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { Appbar, Switch } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useColors } from '@/hooks/useThemeColor';

interface EmailTrackingPreferences {
  emailOpens: boolean;
  linkClicks: boolean;
  attachmentDownloads: boolean;
  replyNotifications: boolean;
  forwardNotifications: boolean;
  pushNotifications: boolean;
  emailNotifications: boolean;
  desktopNotifications: boolean;
}

export default function EmailTrackingPage() {
  const router = useRouter();
  const colors = useColors();
  const [preferences, setPreferences] = useState<EmailTrackingPreferences>({
    emailOpens: true,
    linkClicks: true,
    attachmentDownloads: true,
    replyNotifications: true,
    forwardNotifications: true,
    pushNotifications: true,
    emailNotifications: false,
    desktopNotifications: true,
  });

  const ToggleItem = ({ title, value, onToggle, description }: {
    title: string;
    value: boolean;
    onToggle: () => void;
    description?: string;
  }) => (
    <View style={styles.settingItem}>
      <View style={styles.settingContent}>
        <Text style={styles.settingText}>{title}</Text>
        {description && (
          <Text style={styles.settingDescription}>{description}</Text>
        )}
      </View>
      <Switch
        value={value}
        onValueChange={onToggle}
        color={colors.primary}
      />
    </View>
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#f5f5f5',
    },
    appbar: {
      backgroundColor: '#0F96BB',
      elevation: 0,
      height: 56,
    },
    content: {
      flex: 1,
    },
    section: {
      backgroundColor: '#fff',
      marginTop: 16,
    },
    sectionTitle: {
      fontSize: 14,
      color: '#666',
      padding: 16,
      paddingBottom: 8,
    },
    settingItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 16,
      backgroundColor: '#fff',
      borderBottomWidth: 1,
      borderBottomColor: '#eee',
    },
    settingContent: {
      flex: 1,
      marginRight: 16,
    },
    settingText: {
      fontSize: 16,
      color: '#333',
    },
    settingDescription: {
      fontSize: 14,
      color: '#666',
      marginTop: 4,
    },
    description: {
      fontSize: 14,
      color: '#666',
      padding: 16,
      lineHeight: 20,
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Appbar.Header style={styles.appbar} statusBarHeight={0}>
        <Appbar.BackAction color="#fff" onPress={() => router.back()} />
        <Appbar.Content 
          title="Email Tracking" 
          titleStyle={{ color: '#fff', fontSize: 20, fontWeight: '600' }}
        />
      </Appbar.Header>

      <ScrollView style={styles.content}>
        <Text style={styles.description}>
          Configure which email activities you want to track and how you want to be notified about them.
        </Text>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>TRACK ACTIVITIES</Text>
          <ToggleItem
            title="Email Opens"
            value={preferences.emailOpens}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              emailOpens: !prev.emailOpens 
            }))}
            description="When recipients open your emails"
          />
          <ToggleItem
            title="Link Clicks"
            value={preferences.linkClicks}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              linkClicks: !prev.linkClicks 
            }))}
            description="When recipients click links in your emails"
          />
          <ToggleItem
            title="Attachment Downloads"
            value={preferences.attachmentDownloads}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              attachmentDownloads: !prev.attachmentDownloads 
            }))}
            description="When recipients download attachments"
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>EMAIL NOTIFICATIONS</Text>
          <ToggleItem
            title="Reply Notifications"
            value={preferences.replyNotifications}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              replyNotifications: !prev.replyNotifications 
            }))}
            description="When recipients reply to your emails"
          />
          <ToggleItem
            title="Forward Notifications"
            value={preferences.forwardNotifications}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              forwardNotifications: !prev.forwardNotifications 
            }))}
            description="When recipients forward your emails"
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>NOTIFICATION METHODS</Text>
          <ToggleItem
            title="Push Notifications"
            value={preferences.pushNotifications}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              pushNotifications: !prev.pushNotifications 
            }))}
          />
          <ToggleItem
            title="Email Notifications"
            value={preferences.emailNotifications}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              emailNotifications: !prev.emailNotifications 
            }))}
          />
          <ToggleItem
            title="Desktop Notifications"
            value={preferences.desktopNotifications}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              desktopNotifications: !prev.desktopNotifications 
            }))}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
} 