import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { Appbar, Switch } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useColors } from '@/hooks/useThemeColor';

interface AssignmentPreferences {
  newAssignments: boolean;
  reassignments: boolean;
  teamAssignments: boolean;
  dealAssignments: boolean;
  taskAssignments: boolean;
  emailNotifications: boolean;
  pushNotifications: boolean;
}

export default function AssignmentPage() {
  const router = useRouter();
  const colors = useColors();
  const [preferences, setPreferences] = useState<AssignmentPreferences>({
    newAssignments: true,
    reassignments: true,
    teamAssignments: true,
    dealAssignments: true,
    taskAssignments: true,
    emailNotifications: true,
    pushNotifications: true,
  });

  const ToggleItem = ({ title, value, onToggle, description }: {
    title: string;
    value: boolean;
    onToggle: () => void;
    description?: string;
  }) => (
    <View style={styles.settingItem}>
      <View style={styles.settingContent}>
        <Text style={styles.settingText}>{title}</Text>
        {description && (
          <Text style={styles.settingDescription}>{description}</Text>
        )}
      </View>
      <Switch
        value={value}
        onValueChange={onToggle}
        color={colors.primary}
      />
    </View>
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#f5f5f5',
    },
    appbar: {
      backgroundColor: '#0F96BB',
      elevation: 0,
      height: 56,
    },
    content: {
      flex: 1,
    },
    section: {
      backgroundColor: '#fff',
      marginTop: 16,
    },
    sectionTitle: {
      fontSize: 14,
      color: '#666',
      padding: 16,
      paddingBottom: 8,
    },
    settingItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 16,
      backgroundColor: '#fff',
      borderBottomWidth: 1,
      borderBottomColor: '#eee',
    },
    settingContent: {
      flex: 1,
      marginRight: 16,
    },
    settingText: {
      fontSize: 16,
      color: '#333',
    },
    settingDescription: {
      fontSize: 14,
      color: '#666',
      marginTop: 4,
    },
    description: {
      fontSize: 14,
      color: '#666',
      padding: 16,
      lineHeight: 20,
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Appbar.Header style={styles.appbar} statusBarHeight={0}>
        <Appbar.BackAction color="#fff" onPress={() => router.back()} />
        <Appbar.Content 
          title="Assignment Notifications" 
          titleStyle={{ color: '#fff', fontSize: 20, fontWeight: '600' }}
        />
      </Appbar.Header>

      <ScrollView style={styles.content}>
        <Text style={styles.description}>
          Configure which assignment notifications you want to receive and how you want to receive them.
        </Text>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>NOTIFY ME ABOUT</Text>
          <ToggleItem
            title="New Assignments"
            value={preferences.newAssignments}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              newAssignments: !prev.newAssignments 
            }))}
            description="When you are assigned new items"
          />
          <ToggleItem
            title="Reassignments"
            value={preferences.reassignments}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              reassignments: !prev.reassignments 
            }))}
            description="When items are reassigned to or from you"
          />
          <ToggleItem
            title="Team Assignments"
            value={preferences.teamAssignments}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              teamAssignments: !prev.teamAssignments 
            }))}
            description="When items are assigned to your team"
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>ASSIGNMENT TYPES</Text>
          <ToggleItem
            title="Deal Assignments"
            value={preferences.dealAssignments}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              dealAssignments: !prev.dealAssignments 
            }))}
          />
          <ToggleItem
            title="Task Assignments"
            value={preferences.taskAssignments}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              taskAssignments: !prev.taskAssignments 
            }))}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>NOTIFICATION METHODS</Text>
          <ToggleItem
            title="Email Notifications"
            value={preferences.emailNotifications}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              emailNotifications: !prev.emailNotifications 
            }))}
          />
          <ToggleItem
            title="Push Notifications"
            value={preferences.pushNotifications}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              pushNotifications: !prev.pushNotifications 
            }))}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
} 