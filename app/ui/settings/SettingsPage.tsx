import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { Appbar } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useColors } from '@/hooks/useThemeColor';
import { capitalizeWords } from '../utils';

import { useAuth } from '@/context/AuthContext';
import { authService } from '@/services/AuthService';

interface MenuItemProps {
  title: string;
  onPress: () => void;
  showBorder?: boolean;
}

interface SectionTitleProps {
  title: string;
}

export default function SettingsPage() {
  const router = useRouter();
  const colors = useColors();
  const { user, signOut } = useAuth();

  const handleLogout = async () => {
   // try {
      //await signOut();
      await authService.logout();
      // Navigate to login page after successful logout
      router.replace('/login');
    //} catch (error) {
      //console.error('Logout failed:', error);
      //Alert.alert(
        //"Error",
        //"Failed to logout. Please try again.",
        //[{ text: "OK" }]
      //);
    //}
  };

  const MenuItem = ({ title, onPress, showBorder = true }: MenuItemProps) => (
    <TouchableOpacity
      style={[styles.menuItem, showBorder && styles.menuItemBorder]}
      onPress={onPress}
    >
      <Text style={styles.menuItemText}>{title}</Text>
      <Ionicons name="chevron-forward" size={24} color="#757575" />
    </TouchableOpacity>
  );

  const SectionTitle = ({ title }: SectionTitleProps) => (
    <Text style={styles.sectionTitle}>{title}</Text>
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#f5f5f5',
    },
    appbar: {
      backgroundColor: '#0F96BB',
      elevation: 0,
      height: 56,
    },
    content: {
      flex: 1,
    },
    profileSection: {
      backgroundColor: '#fff',
      padding: 16,
      marginBottom: 16,
    },
    profileInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    profileLeft: {
      flex: 1,
    },
    avatar: {
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: '#0F96BB',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 8,
    },
    avatarText: {
      color: '#fff',
      fontSize: 24,
      fontWeight: '500',
    },
    userName: {
      fontSize: 18,
      fontWeight: '600',
      color: '#333',
      marginBottom: 4,
    },
    userEmail: {
      fontSize: 14,
      color: '#666',
      marginBottom: 12,
    },
    domainLabel: {
      fontSize: 12,
      color: '#666',
      marginBottom: 4,
    },
    domainText: {
      fontSize: 14,
      color: '#333',
    },
    editIcon: {
      padding: 8,
    },
    sectionTitle: {
      fontSize: 14,
      color: '#666',
      paddingHorizontal: 16,
      paddingVertical: 8,
      backgroundColor: '#f5f5f5',
    },
    menuItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: '#fff',
      paddingVertical: 16,
      paddingHorizontal: 16,
    },
    menuItemBorder: {
      borderBottomWidth: 1,
      borderBottomColor: '#eee',
    },
    menuItemText: {
      fontSize: 16,
      color: '#333',
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Appbar.Header style={styles.appbar} statusBarHeight={0}>
        <Appbar.BackAction color="#fff" onPress={() => router.back()} />
        <Appbar.Content 
          title="Settings" 
          titleStyle={{ color: '#fff', fontSize: 20, fontWeight: '600' }}
        />
      </Appbar.Header>

      <ScrollView style={styles.content}>
        <View style={styles.profileSection}>
          <View style={styles.profileInfo}>
            <View style={styles.profileLeft}>
              <View style={styles.avatar}>
                <Text style={styles.avatarText}>
                  {user?.username?.[0]?.toUpperCase() || 'U'}
                </Text>
              </View>
              <Text style={styles.userName}>{capitalizeWords(user?.username || null) || 'User Name'}</Text>
              <Text style={styles.userEmail}>{user?.email || '<EMAIL>'}</Text>
              <Text style={styles.domainLabel}>Mobile</Text>
              <Text style={styles.domainText}>{user?.mobile || 'Not provided'}</Text>
              <Text style={styles.domainLabel}>Role</Text>
              <Text style={styles.domainText}>{user?.role || 'Not provided'}</Text>
            </View>
            <TouchableOpacity 
              style={styles.editIcon}
              onPress={() => {
                Alert.alert(
                  "Logout",
                  "Are you sure you want to logout?",
                  [
                    {
                      text: "Cancel",
                      style: "cancel"
                    },
                    { 
                      text: "Logout", 
                      onPress: handleLogout,
                      style: "destructive"
                    }
                  ]
                );
              }}
            >
              <Ionicons name="log-out-outline" size={24} color="#0F96BB" />
            </TouchableOpacity>
          </View>
        </View>

          {/*renderUIforMoreOption()}*/}

        <SectionTitle title="ABOUT" />
        <MenuItem 
          title="About Easy Daily Sales app" 
          onPress={() => router.push('/ui/settings/about')}
          showBorder={false}
        />
      </ScrollView>
    </SafeAreaView>
  );

  const renderUIforMoreOption = () => {
    return (
      <View>
        <SectionTitle title="GENERAL" />
        <MenuItem 
          title="Notifications on home screen" 
          onPress={() => router.push('/ui/settings/notifications')}
        />
        <MenuItem 
          title="Call preferences" 
          onPress={() => router.push('/ui/settings/call-preferences')}
        />
        <MenuItem 
          title="Offline preferences" 
          onPress={() => router.push('/ui/settings/offline-preferences')}
          showBorder={false}
        />

        <SectionTitle title="PUSH NOTIFICATIONS" />
        <MenuItem 
          title="Assignment" 
          onPress={() => router.push('/ui/settings/assignment')}
        />
        <MenuItem 
          title="Reminders" 
          onPress={() => router.push('/ui/settings/reminders')}
        />
        <MenuItem 
          title="Email tracking" 
          onPress={() => router.push('/ui/settings/email-tracking')}
          showBorder={false}
        />

        <SectionTitle title="EMAILS" />
        <MenuItem 
          title="Alerts" 
          onPress={() => router.push('/ui/settings/alerts')}
        />
        <MenuItem 
          title="Daily digest" 
          onPress={() => router.push('/ui/settings/daily-digest')}
          showBorder={false}
        />
      </View>
    );
  };
} 