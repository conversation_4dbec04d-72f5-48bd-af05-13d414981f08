import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { Appbar, Switch, RadioButton } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useColors } from '@/hooks/useThemeColor';

interface DigestPreferences {
  enabled: boolean;
  leadSummary: boolean;
  dealSummary: boolean;
  taskSummary: boolean;
  meetingSummary: boolean;
  teamActivity: boolean;
  timePreference: '8am' | '9am' | '10am';
  weekendDigest: boolean;
}

export default function DailyDigestPage() {
  const router = useRouter();
  const colors = useColors();
  const [preferences, setPreferences] = useState<DigestPreferences>({
    enabled: true,
    leadSummary: true,
    dealSummary: true,
    taskSummary: true,
    meetingSummary: true,
    teamActivity: true,
    timePreference: '9am',
    weekendDigest: false,
  });

  const ToggleItem = ({ title, value, onToggle, description }: {
    title: string;
    value: boolean;
    onToggle: () => void;
    description?: string;
  }) => (
    <View style={styles.settingItem}>
      <View style={styles.settingContent}>
        <Text style={styles.settingText}>{title}</Text>
        {description && (
          <Text style={styles.settingDescription}>{description}</Text>
        )}
      </View>
      <Switch
        value={value}
        onValueChange={onToggle}
        color={colors.primary}
      />
    </View>
  );

  const timeOptions = [
    { label: '8:00 AM', value: '8am' },
    { label: '9:00 AM', value: '9am' },
    { label: '10:00 AM', value: '10am' },
  ];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#f5f5f5',
    },
    appbar: {
      backgroundColor: '#0F96BB',
      elevation: 0,
      height: 56,
    },
    content: {
      flex: 1,
    },
    section: {
      backgroundColor: '#fff',
      marginTop: 16,
    },
    sectionTitle: {
      fontSize: 14,
      color: '#666',
      padding: 16,
      paddingBottom: 8,
    },
    settingItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 16,
      backgroundColor: '#fff',
      borderBottomWidth: 1,
      borderBottomColor: '#eee',
    },
    settingContent: {
      flex: 1,
      marginRight: 16,
    },
    settingText: {
      fontSize: 16,
      color: '#333',
    },
    settingDescription: {
      fontSize: 14,
      color: '#666',
      marginTop: 4,
    },
    description: {
      fontSize: 14,
      color: '#666',
      padding: 16,
      lineHeight: 20,
    },
    radioItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 8,
      paddingHorizontal: 16,
    },
    radioLabel: {
      fontSize: 16,
      color: '#333',
      marginLeft: 8,
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Appbar.Header style={styles.appbar} statusBarHeight={0}>
        <Appbar.BackAction color="#fff" onPress={() => router.back()} />
        <Appbar.Content 
          title="Daily Digest" 
          titleStyle={{ color: '#fff', fontSize: 20, fontWeight: '600' }}
        />
      </Appbar.Header>

      <ScrollView style={styles.content}>
        <Text style={styles.description}>
          Configure your daily email digest preferences and what information you want to receive.
        </Text>

        <View style={styles.section}>
          <ToggleItem
            title="Enable Daily Digest"
            value={preferences.enabled}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              enabled: !prev.enabled 
            }))}
            description="Receive a daily summary of your activities"
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>INCLUDE IN DIGEST</Text>
          <ToggleItem
            title="Lead Summary"
            value={preferences.leadSummary}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              leadSummary: !prev.leadSummary 
            }))}
            description="Daily lead activity and updates"
          />
          <ToggleItem
            title="Deal Summary"
            value={preferences.dealSummary}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              dealSummary: !prev.dealSummary 
            }))}
            description="Deal progress and changes"
          />
          <ToggleItem
            title="Task Summary"
            value={preferences.taskSummary}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              taskSummary: !prev.taskSummary 
            }))}
            description="Completed and upcoming tasks"
          />
          <ToggleItem
            title="Meeting Summary"
            value={preferences.meetingSummary}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              meetingSummary: !prev.meetingSummary 
            }))}
            description="Today's meetings and schedules"
          />
          <ToggleItem
            title="Team Activity"
            value={preferences.teamActivity}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              teamActivity: !prev.teamActivity 
            }))}
            description="Team performance and updates"
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>DELIVERY TIME</Text>
          <RadioButton.Group
            onValueChange={value => setPreferences(prev => ({ 
              ...prev, 
              timePreference: value as DigestPreferences['timePreference']
            }))}
            value={preferences.timePreference}
          >
            {timeOptions.map((option) => (
              <View key={option.value} style={styles.settingItem}>
                <View style={styles.radioItem}>
                  <RadioButton value={option.value} color={colors.primary} />
                  <Text style={styles.radioLabel}>{option.label}</Text>
                </View>
              </View>
            ))}
          </RadioButton.Group>
        </View>

        <View style={styles.section}>
          <ToggleItem
            title="Weekend Digest"
            value={preferences.weekendDigest}
            onToggle={() => setPreferences(prev => ({ 
              ...prev, 
              weekendDigest: !prev.weekendDigest 
            }))}
            description="Receive digest emails on weekends"
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
} 