import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Platform,
  Alert,
  KeyboardAvoidingView,
  PermissionsAndroid,
  StatusBar,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { Appbar } from 'react-native-paper';
import AudioRecorderPlayer, { 
  AudioSourceAndroidType, 
  AudioEncoderAndroidType,
  AVEncodingOption,
  AVEncoderAudioQualityIOSType
} from 'react-native-audio-recorder-player';
import { useNavigation, useRoute } from '@react-navigation/native';
import RNFS from 'react-native-fs';
import { apiService } from '@/services/ApiService';
import { API_ENDPOINTS, API_BASE_URL } from '@/config/api';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { check, request, PERMISSIONS, RESULTS } from 'react-native-permissions';

interface RouteParams {
  id: string;
  name: string;
}

interface AudioRecordingState {
  isRecording: boolean;
  audioPath: string | null;
  recordingDuration: string;
  isPlaying: boolean;
}

interface AudioFile {
  uri: string;
  type: string;
  name: string;
}

const audioConfig = {
  ios: {
    AVSampleRateKeyIOS: 44100,
    AVNumberOfChannelsKeyIOS: 2,
    AVEncoderAudioQualityKeyIOS: AVEncoderAudioQualityIOSType.high,
    AVFormatIDKeyIOS: AVEncodingOption.aac,
    AVEncoderBitRateKeyIOS: 128000,
    AVLinearPCMBitDepthKeyIOS: 16,
    AVLinearPCMIsBigEndianKeyIOS: false,
    AVLinearPCMIsFloatKeyIOS: false
  },
  android: {
    AudioEncoderAndroid: AudioEncoderAndroidType.AAC,
    AudioSourceAndroid: AudioSourceAndroidType.MIC,
  }
};

const formatTime = (timeInSeconds: number, isCountdown = false) => {
  const minutes = Math.floor(timeInSeconds / 60);
  const seconds = Math.floor(timeInSeconds % 60);
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

const MAX_RECORDING_SECONDS = 119; // 1:59 in seconds

const AddNotesPage: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { id, name } = route.params as RouteParams;
  const [description, setDescription] = useState('');
  const [audioState, setAudioState] = useState<AudioRecordingState>({
    isRecording: false,
    audioPath: null,
    recordingDuration: '01:59',
    isPlaying: false,
  });

  const audioRecorderPlayer = React.useMemo(() => new AudioRecorderPlayer(), []);

  useEffect(() => {
    console.log('Deal Details:', { id, name });
    checkPermission();
    return () => {
      const cleanup = async () => {
        try {
          if (audioState.isRecording) {
            await audioRecorderPlayer.stopRecorder();
            audioRecorderPlayer.removeRecordBackListener();
          }
          if (audioState.isPlaying) {
            await audioRecorderPlayer.stopPlayer();
            audioRecorderPlayer.removePlayBackListener();
          }
        } catch (error) {
          console.error('Cleanup error:', error);
        }
      };
      cleanup();
    };
  }, []);

  const checkPermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const permissions = [PermissionsAndroid.PERMISSIONS.RECORD_AUDIO];
        
        // Only request storage permission on Android versions below 13 (API level 33)
        if (Platform.Version < 33) {
          permissions.push(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE);
        }
        
        const granted = await PermissionsAndroid.requestMultiple(permissions);
        const audioGranted = granted['android.permission.RECORD_AUDIO'] === PermissionsAndroid.RESULTS.GRANTED;
        
        // Only check storage permission if we requested it
        const storageGranted = Platform.Version >= 33 || 
          granted['android.permission.WRITE_EXTERNAL_STORAGE'] === PermissionsAndroid.RESULTS.GRANTED;
        
        console.log('audioGranted', audioGranted);
        console.log('storageGranted', storageGranted);
        
        if (!audioGranted || !storageGranted) {
          Alert.alert('Permission Required', 'Please grant microphone permission to record audio');
          return false;
        }
        return true;
      } catch (err) {
        console.error('Permission error:', err);
        return false;
      }
    } else if (Platform.OS === 'ios') {
      const micResult = await check(PERMISSIONS.IOS.MICROPHONE);
      if (micResult !== RESULTS.GRANTED) {
        const requestResult = await request(PERMISSIONS.IOS.MICROPHONE);
        return requestResult === RESULTS.GRANTED;
      }
      return true;
    }
    return true;
  };

  const startRecording = async () => {
    try {
      const hasPermission = await checkPermission();
      if (!hasPermission) {
        Alert.alert('Permission Denied', 'Microphone permission is required to record audio.');
        return;
      }

      // Clean up any existing recordings
      try {
        await audioRecorderPlayer.stopRecorder().catch(() => {});
        await audioRecorderPlayer.stopPlayer().catch(() => {});
      } catch (err) {
        console.log('Cleanup error (non-critical):', err);
      }

      const timestamp = Date.now();
      const directory = `${RNFS.TemporaryDirectoryPath}/recordings`;
      await RNFS.mkdir(directory).catch(() => {});
      const path = `${directory}/recording_${timestamp}.m4a`;

      console.log(`Starting recording on ${Platform.OS} at path:`, path);

      const iosPath = path.startsWith('file://') ? path : `file://${path}`;
      console.log('Full recording path:', iosPath);

      if (Platform.OS === 'ios') {
        await audioRecorderPlayer.startRecorder(
          iosPath,
          {
            AVFormatIDKeyIOS: AVEncodingOption.aac,
            AVEncoderAudioQualityKeyIOS: AVEncoderAudioQualityIOSType.high,
            AVEncoderBitRateKeyIOS: 128000,
            AVNumberOfChannelsKeyIOS: 2,
            AVSampleRateKeyIOS: 44100
          }
        );
      } else {
        await audioRecorderPlayer.startRecorder(
          path,
          {
            AudioEncoderAndroid: AudioEncoderAndroidType.AAC,
            AudioSourceAndroid: AudioSourceAndroidType.MIC
          }
        );
      }

      // Set up recording listener
      audioRecorderPlayer.addRecordBackListener((e) => {
        const current = Math.floor(e.currentPosition / 1000);
        const remaining = MAX_RECORDING_SECONDS - current;
        if (remaining <= 0) {
          stopRecording();
          return;
        }
        setAudioState(prev => ({
          ...prev,
          recordingDuration: formatTime(remaining, true)
        }));
      });

      // Update state after successful recording start
      setAudioState(prev => ({
        ...prev,
        isRecording: true,
        audioPath: path
      }));

      console.log('Recording started successfully');
    } catch (error) {
      console.error(`${Platform.OS} Recording Error:`, error);
      Alert.alert(
        'Recording Error', 
        `Failed to start recording: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  };

  const stopRecording = async () => {
    try {
      const result = await audioRecorderPlayer.stopRecorder();
      audioRecorderPlayer.removeRecordBackListener();
      console.log('Recording stopped, file at:', result);
      
      // Verify the file exists
      const exists = await RNFS.exists(result);
      console.log('File exists:', exists);
      
      if (!exists) {
        throw new Error('Recording file not found after stop');
      }

      setAudioState(prev => ({
        ...prev,
        isRecording: false,
        audioPath: result
      }));
    } catch (error) {
      console.error('Stop recording error:', error);
      Alert.alert('Error', 'Failed to stop recording');
      setAudioState(prev => ({
        ...prev,
        isRecording: false,
        audioPath: null
      }));
    }
  };

  const startPlaying = async () => {
    if (!audioState.audioPath) return;

    try {
      // Reset playback state
      setAudioState(prev => ({
        ...prev,
        isPlaying: false,
        recordingDuration: '00:00'
      }));

      // Stop any existing playback
      try {
        await audioRecorderPlayer.stopPlayer().catch(() => {});
        audioRecorderPlayer.removePlayBackListener();
      } catch (err) {
        console.log('Cleanup error (non-critical):', err);
      }

      console.log('Starting playback with path:', audioState.audioPath);
      
      // Add file:// prefix if needed for iOS
      const playPath = Platform.OS === 'ios' && !audioState.audioPath.startsWith('file://')
        ? `file://${audioState.audioPath}`
        : audioState.audioPath;
        
      await audioRecorderPlayer.startPlayer(playPath);
      
      audioRecorderPlayer.addPlayBackListener((e) => {
        const current = Math.floor(e.currentPosition / 1000);
        if (e.currentPosition >= e.duration) {
          stopPlaying();
          setAudioState(prev => ({
            ...prev,
            isPlaying: false,
            recordingDuration: '00:00'
          }));
          return;
        }
        setAudioState(prev => ({
          ...prev,
          isPlaying: true,
          recordingDuration: formatTime(current)
        }));
      });

      setAudioState(prev => ({
        ...prev,
        isPlaying: true
      }));
    } catch (error) {
      console.error('Playback error:', error);
      Alert.alert('Error', 'Failed to play recording');
    }
  };

  const stopPlaying = async () => {
    try {
      await audioRecorderPlayer.stopPlayer();
      audioRecorderPlayer.removePlayBackListener();
      setAudioState(prev => ({ 
        ...prev, 
        isPlaying: false,
        recordingDuration: '00:00'
      }));
    } catch (error) {
      console.error('Stop playback error:', error);
    }
  };

  const handleSubmit = async () => {
    try {
      if (!description.trim() && !audioState.audioPath) {
        Alert.alert('Error', 'Please enter a description or record a voice note');
        return;
      }
      
      const formData = new FormData();
      
      if (audioState.audioPath) {
        try {
          console.log('Processing audio file:', audioState.audioPath);
          const exists = await RNFS.exists(audioState.audioPath);
          if (!exists) {
            console.error('Audio file not found at path:', audioState.audioPath);
            throw new Error('Audio file not found');
          }
          
          const stats = await RNFS.stat(audioState.audioPath);
          console.log('File stats:', stats);
          
          // Properly format file URI based on platform
          const fileUri = Platform.OS === 'ios'
            ? (audioState.audioPath.startsWith('file://') 
                ? audioState.audioPath 
                : `file://${audioState.audioPath}`)
            : `file://${audioState.audioPath}`;
          
          const fileName = `recording_${Date.now()}.m4a`;
          
          console.log('Creating file object with:', {
            uri: fileUri,
            type: 'audio/x-m4a',
            name: fileName
          });
          
          const fileObject = {
            uri: fileUri,
            type: 'audio/x-m4a',
            name: fileName
          } as any;
          
          formData.append('file', fileObject);
        } catch (fileError: any) {
          console.error('File handling error:', fileError);
          throw new Error(`Failed to process audio file: ${fileError.message || 'Unknown error'}`);
        }
      }
      
      formData.append('releted_id', id);
      formData.append('releted_to', name);
      formData.append('note', description.trim() || '');

      const token = await AsyncStorage.getItem('auth_token');
      if (!token) {
        throw new Error('Authentication token not found');
      }
      
      const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.NOTES_ADD}`, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${token}`,
        },
        body: formData
      });

      console.log('Upload response status:', response.status);
      const responseData = await response.json();
      console.log('API response:', responseData);
      
      if (response.ok) {
        Alert.alert(
          'Success',
          'Note saved successfully',
          [
            {
              text: 'OK',
              onPress: () => {
                setDescription('');
                setAudioState({
                  isRecording: false,
                  audioPath: null,
                  recordingDuration: '01:59',
                  isPlaying: false,
                });
                navigation.goBack();
              }
            }
          ]
        );
      } else {
        throw new Error(responseData.message || 'Failed to save note');
      }
    } catch (error) {
      console.error('Error saving note:', error);
      const errorMessage = error instanceof Error 
        ? error.message 
        : 'Failed to save note. Please check your connection and try again.';
      Alert.alert('Error', errorMessage);
    }
  };

  const handleBack = () => {
    if (description.trim() || audioState.audioPath) {
      Alert.alert(
        'Discard Changes',
        'Are you sure you want to discard your changes?',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Discard', style: 'destructive', onPress: () => navigation.goBack() },
        ]
      );
    } else {
      navigation.goBack();
    }
  };

  return (
    <View style={styles.safeContainer}>
      <StatusBar 
        barStyle="light-content" 
        backgroundColor="#0F96BB"
      />
      <Appbar.Header style={styles.header}>
        <Appbar.BackAction onPress={handleBack} color="#FFFFFF" />
        <Appbar.Content 
          title="Add Notes" 
          titleStyle={styles.headerTitle}
        />
        {(!!description.trim() || !!audioState.audioPath) && (
          <Appbar.Action 
            icon="check"
            disabled={audioState.isRecording}
            onPress={handleSubmit}
            color={audioState.isRecording ? '#A0A0A0' : '#FFFFFF'}
          />
        )}
      </Appbar.Header>
      
      <View style={styles.container}>
        <KeyboardAvoidingView 
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'} 
          style={styles.content}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
        >
          <TextInput
            style={styles.input}
            placeholder="Enter description here"
            multiline
            value={description}
            onChangeText={setDescription}
          />
          <View style={styles.divider}>
            <View style={styles.dividerLine} />
            <Text style={styles.dividerText}>OR</Text>
            <View style={styles.dividerLine} />
          </View>
          <View style={styles.recorderContainer}>
            {audioState.isRecording ? (
              <View style={styles.recordingStatus}>
                <Text style={[styles.timerText, { color: audioState.recordingDuration === '00:00' ? '#FF3B30' : '#000' }]}>
                  {audioState.recordingDuration}
                </Text>
                <TouchableOpacity onPress={stopRecording} style={styles.recordButton}>
                  <MaterialIcons name="stop" size={32} color="#FF3B30" />
                </TouchableOpacity>
              </View>
            ) : audioState.audioPath ? (
              <View style={styles.recordingStatus}>
                <View style={styles.playbackControls}>
                  <TouchableOpacity
                    onPress={audioState.isPlaying ? stopPlaying : startPlaying}
                    style={styles.playButton}
                  >
                    <MaterialIcons
                      name={audioState.isPlaying ? "pause" : "play-arrow"}
                      size={32}
                      color="#007AFF"
                    />
                    <Text style={styles.playButtonText}>
                      {audioState.isPlaying ? "Pause" : "Play Recording"}
                    </Text>
                  </TouchableOpacity>
                  <Text style={styles.playbackTime}>
                    {audioState.isPlaying ? audioState.recordingDuration : '00:00'}
                  </Text>
                </View>
                <TouchableOpacity
                  onPress={() => setAudioState({
                    isRecording: false,
                    audioPath: null,
                    recordingDuration: '01:59',
                    isPlaying: false,
                  })}
                  style={[styles.recordButton, styles.newRecordingButton]}
                >
                  <View style={styles.newRecordingContent}>
                    <MaterialIcons name="mic" size={24} color="#007AFF" />
                    <Text style={styles.newRecordingText}>New Recording</Text>
                  </View>
                </TouchableOpacity>
              </View>
            ) : (
              <TouchableOpacity onPress={startRecording} style={styles.recordButton}>
                <MaterialIcons name="mic" size={32} color="#007AFF" />
                <Text style={styles.recordText}>Tap to record voice notes</Text>
              </TouchableOpacity>
            )}
          </View>
        </KeyboardAvoidingView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  safeContainer: {
    flex: 1,
    backgroundColor: '#0F96BB',
  },
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    backgroundColor: '#0F96BB',
    elevation: Platform.OS === 'android' ? 4 : 0,
    shadowColor: Platform.OS === 'ios' ? '#000000' : 'transparent',
    shadowOffset: { 
      width: 0, 
      height: Platform.OS === 'ios' ? 2 : 0 
    },
    shadowOpacity: Platform.OS === 'ios' ? 0.25 : 0,
    shadowRadius: Platform.OS === 'ios' ? 3.84 : 0,
    height: Platform.OS === 'android' ? 56 : 44,
  },
  headerTitle: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  content: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  input: {
    flex: 1,
    padding: 16,
    fontSize: 16,
    textAlignVertical: 'top',
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#E5E5E5',
  },
  dividerText: {
    paddingHorizontal: 16,
    color: '#8E8E93',
    fontSize: 14,
  },
  recorderContainer: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 150,
  },
  recordingStatus: {
    alignItems: 'center',
  },
  timerText: {
    fontSize: 48,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  recordButton: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  recordText: {
    marginTop: 8,
    color: '#007AFF',
    fontSize: 14,
  },
  newRecordingButton: {
    marginTop: 16,
    backgroundColor: '#F0F0F0',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    width: 'auto',
  },
  newRecordingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  newRecordingText: {
    color: '#007AFF',
    fontSize: 14,
    marginLeft: 8,
    fontWeight: '500',
  },
  playbackControls: {
    alignItems: 'center',
    marginBottom: 16,
  },
  playButton: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
  },
  playButtonText: {
    color: '#007AFF',
    fontSize: 14,
    marginTop: 4,
  },
  playbackTime: {
    fontSize: 18,
    color: '#007AFF',
    marginTop: 8,
  },
});

export default AddNotesPage;
