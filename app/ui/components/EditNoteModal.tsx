import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Modal,
  Alert,
  Platform,
  KeyboardAvoidingView,
  ActivityIndicator,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Note } from '../../models/notesListModel';
import { apiService } from '../../../services/ApiService';
import { API_ENDPOINTS } from '../../../config/api';
import { showSnackbar } from '../utils';

interface EditNoteModalProps {
  visible: boolean;
  note: Note | null;
  onClose: () => void;
  onSave: (updatedNote: Note) => void;
}

interface UpdateNoteResponse {
  status: number;
  message: string;
  note: number;
}

const EditNoteModal: React.FC<EditNoteModalProps> = ({
  visible,
  note,
  onClose,
  onSave,
}) => {
  const [noteText, setNoteText] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (note) {
      setNoteText(note.notes || '');
    }
  }, [note]);

  const handleSave = async () => {
    if (!note) return;

    if (!noteText.trim()) {
      Alert.alert('Error', 'Note text cannot be empty');
      return;
    }

    try {
      setLoading(true);

      const updateData = {
        notes: noteText.trim(),
        belongsto_id: note.belongsto_id.toString(),
        belongsto: note.belongsto,
      };

      const response = await apiService.put<UpdateNoteResponse>(
        `${API_ENDPOINTS.UPDATE_NOTE}/${note.id}`,
        updateData
      );

      if (response.status === 200) {
        // Create updated note object
        const updatedNote = new Note({
          ...note.toJSON(),
          notes: noteText.trim(),
          updated_at: new Date().toISOString(),
        });

        showSnackbar('Note updated successfully');
        onSave(updatedNote);
        onClose();
      } else {
        throw new Error(response.data?.message || 'Failed to update note');
      }
    } catch (error: any) {
      console.error('Error updating note:', error);
      Alert.alert(
        'Error',
        error.message || 'Failed to update note. Please try again.'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (noteText !== (note?.notes || '')) {
      Alert.alert(
        'Discard Changes',
        'Are you sure you want to discard your changes?',
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Discard', 
            style: 'destructive', 
            onPress: () => {
              setNoteText(note?.notes || '');
              onClose();
            }
          },
        ]
      );
    } else {
      onClose();
    }
  };

  if (!note) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={handleClose} style={styles.headerButton}>
            <MaterialIcons name="close" size={24} color="#007AFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Edit Note</Text>
          <TouchableOpacity 
            onPress={handleSave} 
            style={[styles.headerButton, loading && styles.disabledButton]}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator size="small" color="#007AFF" />
            ) : (
              <Text style={styles.saveButtonText}>Save</Text>
            )}
          </TouchableOpacity>
        </View>

        <KeyboardAvoidingView 
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'} 
          style={styles.content}
        >
          <View style={styles.noteInfo}>
            <Text style={styles.noteDate}>
              Created: {note.formatNoteTime('long')}
            </Text>
          </View>

          <TextInput
            style={styles.textInput}
            value={noteText}
            onChangeText={setNoteText}
            placeholder="Enter your note here..."
            multiline
            textAlignVertical="top"
            autoFocus
            editable={!loading}
          />
        </KeyboardAvoidingView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
    backgroundColor: '#F8F8F8',
  },
  headerButton: {
    padding: 8,
    minWidth: 60,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
  },
  disabledButton: {
    opacity: 0.5,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  noteInfo: {
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  noteDate: {
    fontSize: 14,
    color: '#8E8E93',
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    lineHeight: 24,
    color: '#000000',
    padding: 0,
  },
});

export default EditNoteModal;
