import React, { useEffect, useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Platform,
  StatusBar,
  Modal,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useFocusEffect } from '@react-navigation/native';
import { Note, NotesResponse } from '../../models/notesListModel';
import { apiService } from '../../../services/ApiService';
import { API_ENDPOINTS, UPLOADS_BASE_URL } from '../../../config/api';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';
import { Appbar } from 'react-native-paper';
import { readUserScope, showSnackbar } from '../utils';
import EditNoteModal from '../components/EditNoteModal';

const audioPlayer = new AudioRecorderPlayer();

interface RouteParams {
  id: string;
  type: string;
}

// Update with all possible screens in your app
type RootStackParamList = {
  AddNotesPage: { id: string; name: string };
  'ui/helpers/AddNotesPage': { id: string; name: string };
  'add-notes': { id: string; name: string };
  'notes/add': { id: string; name: string };
  // ... other screens
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

const NotesListScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute();
  const { id, type } = route.params as RouteParams;
  const [notes, setNotes] = useState<Note[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [playingNoteId, setPlayingNoteId] = useState<number | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [selectedNote, setSelectedNote] = useState<Note | null>(null);
  const [readMoreModalVisible, setReadMoreModalVisible] = useState(false);
  const [readMoreNote, setReadMoreNote] = useState<Note | null>(null);

const [accessScope, setAccessScope] = useState<any>(null);
const [canEdit, setCanEdit] = useState(false);
const [canDelete, setCanDelete] = useState(false);

  useEffect(() => {
    const fetchAccessScope = async () => {
      const scope = await readUserScope();
      setAccessScope(scope);
    };
    fetchAccessScope();
  }, []);

  // Refresh edit/delete access based on accessScope
  const refreshAccessScope = () => {
    setCanEdit(accessScope?.notes?.edit === true || accessScope?.notes?.edit === 'global');
    setCanDelete(accessScope?.notes?.delete === true || accessScope?.notes?.delete === 'global');
  };

  useEffect(() => {
    if (accessScope) {
      refreshAccessScope();
    }
  }, [accessScope]);

  useEffect(() => {
    return () => {
      // Cleanup audio player when component unmounts
      stopPlaying();
    };
  }, []);

  useFocusEffect(
    useCallback(() => {
      console.log('Notes list screen focused, refreshing notes...');
      fetchNotes();
    }, [id, type])
  );

  const fetchNotes = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.get<NotesResponse>(
        `notes/${id}/${type}`
      );
      console.log("NotestLog====>"+response);
      const notesList = Note.fromResponse(response.data);
      setNotes(notesList);
    } catch (err: any) {
      const message = err.message || 'An error occurred while fetching notes';
      setError(message);
      Alert.alert('Error', message);
    } finally {
      setLoading(false);
    }
  };

  const startPlaying = async (note: Note) => {
    try {
      // Stop any currently playing audio
      if (isPlaying) {
        await stopPlaying();
      }


     const voiceNoteUrl =UPLOADS_BASE_URL+note.voicenote;
      if (!voiceNoteUrl) {
        Alert.alert('Error', 'Voice note URL not found');
        return;
      }
      console.log("voiceNoteUrl====>"+voiceNoteUrl);
      await audioPlayer.startPlayer(voiceNoteUrl);
      setPlayingNoteId(note.id);
      setIsPlaying(true);

      // Add playback listener
      audioPlayer.addPlayBackListener((e) => {
        // Check if we're near the end of the audio (within 1 second)
        if (Math.abs(e.currentPosition - e.duration) < 1000 || e.currentPosition >= e.duration) {
          audioPlayer.stopPlayer().then(() => {
            audioPlayer.removePlayBackListener();
            setPlayingNoteId(null);
            setIsPlaying(false);
          });
          return;
        }
      });
    } catch (error) {
     // console.error('Playback error:', error);
      Alert.alert('Error', 'Failed to play voice note');
      stopPlaying();
    }
  };

  const stopPlaying = async () => {
    try {
      if (isPlaying) {
        await audioPlayer.stopPlayer();
        audioPlayer.removePlayBackListener();
      }
    } catch (error) {
      console.error('Stop playback error:', error);
    } finally {
      setPlayingNoteId(null);
      setIsPlaying(false);
    }
  };

  const deleteNote = async (noteId: number) => {
    try {
      const response = await apiService.delete(`${API_ENDPOINTS.DELETE_NOTE}/${noteId}`);

      if (response.status === 200 || response.status === 204) {
        setNotes(prevNotes => prevNotes.filter(note => note.id !== noteId));
        showSnackbar('Note deleted successfully');
      } else {
        throw new Error(response.data?.message || 'Failed to delete note');
      }
    } catch (error: any) {
      console.error('Error deleting note:', error);
      Alert.alert('Error', error.message || 'Failed to delete note');
    }
  };

  const handleEditNote = (note: Note) => {
    setSelectedNote(note);
    setEditModalVisible(true);
  };

  const handleSaveNote = (updatedNote: Note) => {
    setNotes(prevNotes =>
      prevNotes.map(note =>
        note.id === updatedNote.id ? updatedNote : note
      )
    );
  };

  const handleDeleteNote = (note: Note) => {
    Alert.alert(
      'Delete Note',
      'Are you sure you want to delete this note?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => deleteNote(note.id)
        },
      ]
    );
  };

  // Check if text needs "read more" (more than 2 lines)
  const needsReadMore = (text: string) => {
    if (!text) return false;
    // Show read more for text longer than 50 characters to ensure it shows up
    return text.length > 50;
  };

  // Handle read more
  const handleReadMore = (note: Note) => {
    setReadMoreNote(note);
    setReadMoreModalVisible(true);
  };

  const renderNoteItem = ({ item }: { item: Note }) => (
    <View style={styles.noteItem}>
      <TouchableOpacity
        style={styles.noteContent}
        onPress={() => {
          if (item.hasVoiceNote()) {
            if (playingNoteId === item.id && isPlaying) {
              stopPlaying();
            } else {
              startPlaying(item);
            }
          }
        }}
        activeOpacity={0.7}
      >
        {/* Note Header */}
        <View style={styles.noteHeader}>
          <Text style={styles.noteDate}>{item.formatNoteTime('short')}</Text>
          {item.hasVoiceNote() && (
            <View style={styles.voiceIndicator}>
              <MaterialIcons
                name={playingNoteId === item.id && isPlaying ? "pause" : "play-arrow"}
                size={16}
                color="#007AFF"
              />
              <Text style={styles.voiceText}>Voice</Text>
            </View>
          )}
        </View>

        {/* Note Text */}
        {item.notes ? (
          <View>
            {item.hasVoiceNote() && (
              <View style={styles.voiceNoteRow}>
                <MaterialIcons name="graphic-eq" size={16} color="#007AFF" />
                <Text style={styles.voiceLabel}>Voice Note</Text>
              </View>
            )}
            <Text style={styles.noteText} numberOfLines={2}>
              {item.notes}
            </Text>
            {needsReadMore(item.notes) && (
              <TouchableOpacity
                style={styles.readMoreButton}
                onPress={() => handleReadMore(item)}
              >
                <Text style={styles.readMoreText}>read more</Text>
              </TouchableOpacity>
            )}
          </View>
        ) : item.hasVoiceNote() ? (
          <View style={styles.voiceNoteRow}>
            <MaterialIcons name="graphic-eq" size={16} color="#007AFF" />
            <Text style={styles.voiceOnlyText}>Voice Note</Text>
          </View>
        ) : null}
      </TouchableOpacity>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        {item.isTextOnly() && canEdit && (
          <TouchableOpacity
            style={styles.editButton}
            onPress={() => handleEditNote(item)}
          >
            <MaterialIcons name="edit" size={14} color="#666" />
          </TouchableOpacity>
        )}

        {canDelete && (
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() => handleDeleteNote(item)}
          >
            <MaterialIcons name="delete" size={14} color="#FF3B30" />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  // Add a function to handle navigation
  const handleAddNotes = () => {
    try {
      // Try different navigation patterns
      navigation.navigate('ui/helpers/AddNotesPage', { id, name: type });
    } catch (error) {
      console.error('Navigation error:', error);
      // Fallback navigation
      Alert.alert('Navigation Error', 'Could not navigate to Add Notes screen. Please check your navigation setup.');
    }
  };

  const handleBack = () => {
    navigation.goBack();
  };

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="light-content"
        backgroundColor="#0F96BB"
      />
      <Appbar.Header style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <MaterialIcons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Appbar.Content
          title="Notes"
          titleStyle={styles.headerTitle}
        />
        {accessScope?.notes?.create === true && (
          <TouchableOpacity onPress={handleAddNotes} style={styles.plusButton}>
            <MaterialIcons name="add" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        )}
      </Appbar.Header>

      <View style={styles.content}>
        {loading ? (
          <View style={styles.centerContainer}>
            <ActivityIndicator size="large" color="#007AFF" />
            <Text style={styles.loadingText}>Loading notes...</Text>
          </View>
        ) : error ? (
          <View style={styles.centerContainer}>
            <MaterialIcons name="error-outline" size={64} color="#FF3B30" />
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={fetchNotes}
            >
              <MaterialIcons name="refresh" size={20} color="#FFFFFF" />
              <Text style={styles.retryText}>Try Again</Text>
            </TouchableOpacity>
          </View>
        ) : notes.length === 0 ? (
          <View style={styles.centerContainer}>
            <View style={styles.emptyIconContainer}>
              <MaterialIcons name="note-add" size={80} color="#C7C7CC" />
            </View>
            <Text style={styles.emptyTitle}>No notes yet</Text>
            <Text style={styles.emptySubtitle}>
              Tap the + button to add your first note
            </Text>
          </View>
        ) : (
          <FlatList
            data={notes}
            renderItem={renderNoteItem}
            keyExtractor={(item) => item.id.toString()}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
            ItemSeparatorComponent={() => <View style={{ height: 4 }} />}
          />
        )}
      </View>

      {/* Edit Note Modal */}
      <EditNoteModal
        visible={editModalVisible}
        note={selectedNote}
        onClose={() => {
          setEditModalVisible(false);
          setSelectedNote(null);
        }}
        onSave={handleSaveNote}
      />

      {/* Read More Modal */}
      <Modal
        visible={readMoreModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setReadMoreModalVisible(false)}
      >
        <View style={styles.readMoreModal}>
          <View style={styles.readMoreHeader}>
            <Text style={styles.readMoreTitle}>Note Details</Text>
            <TouchableOpacity
              onPress={() => setReadMoreModalVisible(false)}
              style={styles.closeButton}
            >
              <MaterialIcons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          {readMoreNote && (
            <View style={styles.readMoreContent}>
              <View style={styles.readMoreNoteInfo}>
                <Text style={styles.readMoreDate}>
                  {readMoreNote.formatNoteTime('long')}
                </Text>
                {readMoreNote.hasVoiceNote() && (
                  <View style={styles.voiceNoteRow}>
                    <MaterialIcons name="graphic-eq" size={16} color="#007AFF" />
                    <Text style={styles.voiceLabel}>Voice Note</Text>
                  </View>
                )}
              </View>

              <Text style={styles.readMoreNoteText}>
                {readMoreNote.notes}
              </Text>
            </View>
          )}
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0F96BB',
  },
  content: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    backgroundColor: '#0F96BB',
    elevation: Platform.OS === 'android' ? 4 : 0,
    shadowColor: Platform.OS === 'ios' ? '#000000' : 'transparent',
    shadowOffset: {
      width: 0,
      height: Platform.OS === 'ios' ? 2 : 0
    },
    shadowOpacity: Platform.OS === 'ios' ? 0.25 : 0,
    shadowRadius: Platform.OS === 'ios' ? 3.84 : 0,
    height: Platform.OS === 'android' ? 56 : 44,
  },
  headerTitle: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  backButton: {
    padding: 8,
    marginLeft: 8,
  },
  plusButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyIconContainer: {
    marginBottom: 24,
    opacity: 0.6,
  },
  loadingText: {
    fontSize: 16,
    color: '#8E8E93',
    marginTop: 16,
    fontWeight: '500',
  },
  listContainer: {
    padding: 16,
  },
  noteItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    marginBottom: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  noteContent: {
    flex: 1,
    marginRight: 4,
  },
  noteHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  noteDate: {
    fontSize: 12,
    color: '#999',
    fontWeight: '500',
  },
  voiceIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0F8FF',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  voiceText: {
    fontSize: 11,
    color: '#007AFF',
    marginLeft: 4,
    fontWeight: '500',
  },
  noteText: {
    fontSize: 15,
    color: '#333',
    lineHeight: 20,
  },
  voiceNoteRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  voiceLabel: {
    fontSize: 12,
    color: '#007AFF',
    fontWeight: '500',
    marginLeft: 6,
  },
  voiceOnlyText: {
    fontSize: 15,
    color: '#007AFF',
    fontStyle: 'italic',
    marginLeft: 6,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 6,
    alignSelf: 'flex-start',
    paddingTop: 0,
    marginTop: -2,
  },
  errorText: {
    fontSize: 16,
    color: '#FF3B30',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: '#007AFF',
    borderRadius: 25,
    marginTop: 24,
    ...Platform.select({
      ios: {
        shadowColor: '#007AFF',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  retryText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
    marginLeft: 8,
  },
  emptyText: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
  },
  emptyTitle: {
    fontSize: 22,
    fontWeight: '600',
    color: '#1C1C1E',
    textAlign: 'center',
    marginBottom: 12,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
    lineHeight: 22,
    maxWidth: 280,
  },
  editButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  deleteButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  readMoreButton: {
    alignSelf: 'flex-start',
    marginTop: 6,
    paddingVertical: 2,
    paddingHorizontal: 4,
  },
  readMoreText: {
    fontSize: 13,
    color: '#007AFF',
    fontWeight: '600',
    textDecorationLine: 'underline',
  },
  readMoreModal: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  readMoreHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
    backgroundColor: '#F8F9FA',
  },
  readMoreTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: '#F0F0F0',
  },
  readMoreContent: {
    flex: 1,
    padding: 20,
  },
  readMoreNoteInfo: {
    marginBottom: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  readMoreDate: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  readMoreNoteText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
  },
});

export default NotesListScreen;