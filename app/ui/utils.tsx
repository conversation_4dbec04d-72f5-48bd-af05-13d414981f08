import Toast from 'react-native-toast-message';
import { format, parse } from 'date-fns';
import { Alert, Modal, Platform, View, TouchableWithoutFeedback, TouchableOpacity, Text, StyleSheet } from 'react-native';
import Snackbar from 'react-native-snackbar';
import { router } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { USER_STORAGE_ALWAYS, SALES_ACTIVITIES_STORAGE_KEY, RECORDS_STORAGE_KEY, USER_STORAGE_KEY } from '@/services/AuthService';
import React, { createContext, useContext, useState, useEffect, useMemo } from 'react';
import { Ionicons } from '@expo/vector-icons';
import { useColors } from '@/hooks/useThemeColor';
import { Linking } from 'react-native';

export const logAPIRespose = (id:string,message: any) => {
  console.log('OkHttp===='+id,message);
}
export const log = (id:string,message: any) => {
  console.log('loged===='+id,message);
}

export type IconType = keyof typeof Ionicons.glyphMap;

export interface Activity {
  id: string | number;
  activity_name: string;
  active: number;
  activity_icon?: IconType;
  navigate?: () => void;
  [key: string]: any;
}

const renderMenuItemIcon = (styles:any,menuItems: [Activity],showMoreOptions:boolean,setShowMoreOptions: (show: boolean) => void) => {
  return <Modal
        visible={showMoreOptions}
        transparent
        animationType="slide"
        onRequestClose={() => setShowMoreOptions(false)}
      >
        <TouchableWithoutFeedback onPress={() => setShowMoreOptions(false)}>
          <View style={styles.modalOverlay}>
            <TouchableWithoutFeedback>
              <View style={styles.bottomSheet}>
                <View style={styles.bottomSheetHeader}>
                  <Text style={styles.bottomSheetTitle}>More Options</Text>
                  <TouchableOpacity 
                    onPress={() => setShowMoreOptions(false)}
                    style={styles.closeButton}
                  >
                    <Ionicons name="close" size={24} color="#666" />
                  </TouchableOpacity>
                </View>
                <View style={styles.menuItems}>
                  {menuItems.map((item) => (
                    <TouchableOpacity
                      key={item.id}
                      style={styles.menuItem}
                      onPress={() => handleMenuItemPress(item.id)}
                    >
                      <Ionicons name={item.activity_icon as any} size={24} color={colors.text.primary} />
                      <Text style={styles.menuItemText}>{item.label}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
  
}

interface AppDataContextType {
  recordTypes: Activity[];
  salesActivities: Activity[];
  setRecordTypes: (types: Activity[]) => void;
  setSalesActivities: (activities: Activity[]) => void;
  updateRecordTypes: (types: Activity[]) => Promise<void>;
  updateSalesActivities: (activities: Activity[]) => Promise<void>;
}

const AppDataContext = createContext<AppDataContextType | undefined>(undefined);

export function AppDataProvider({ children }: { children: React.ReactNode }) {
  const [recordTypes, setRecordTypes] = useState<Activity[]>([]);
  const [salesActivities, setSalesActivities] = useState<Activity[]>([]);

  const updateRecordTypes = async (types: Activity[]) => {
    try {
      await AsyncStorage.setItem(RECORDS_STORAGE_KEY, JSON.stringify(types));
      console.log("updateRecordTypes: "+JSON.stringify(types));
      setRecordTypes(types);
    } catch (error) {
      console.error('Error saving record types:', error);
    }
  };

  const updateSalesActivities = async (activities: Activity[]) => {
    try {
      await AsyncStorage.setItem(SALES_ACTIVITIES_STORAGE_KEY, JSON.stringify(activities));
      console.log("updateActivitiesTypes: "+JSON.stringify(activities));
      setSalesActivities(activities);
    } catch (error) {
      console.error('Error saving sales activities:', error);
    }
  };

  useEffect(() => {
    const loadStoredData = async () => {
      try {
        const storedRecords = await AsyncStorage.getItem(RECORDS_STORAGE_KEY);
        const storedActivities = await AsyncStorage.getItem(SALES_ACTIVITIES_STORAGE_KEY);

        if (storedRecords) {
          setRecordTypes(JSON.parse(storedRecords));
        }
        if (storedActivities) {
          setSalesActivities(JSON.parse(storedActivities));
        }
      } catch (error) {
        console.error('Error loading stored data:', error);
      }
    };

    loadStoredData();
  }, []);

  return (
    <AppDataContext.Provider value={{
      recordTypes,
      salesActivities,
      setRecordTypes,
      setSalesActivities,
      updateRecordTypes,
      updateSalesActivities
    }}>
      {children}
    </AppDataContext.Provider>
  );
}

export function useAppData() {
  const context = useContext(AppDataContext);
  if (context === undefined) {
    throw new Error('useAppData must be used within an AppDataProvider');
  }
  return context;
}

// Export these for backward compatibility, but they should be deprecated
export const RECORD_TYPES: Activity[] = [];
export const SALES_ACTIVITIES: Activity[] = [];

export const DATE_FORMAT = {
  SYSTEM_FORMAT:'yyyy-MM-dd\'T\'HH:mm:ss.SSSxxx',
  DATE_TIME_FORMAT: 'yyyy-MM-dd hh:mm a',
  DATE_FORMAT: 'yyyy-MM-dd',
  DATE_FORMAT_NOT_USED: 'dd/MM/yyyy',
  TIME_FORMAT: 'hh:mm a',
};  
export const handleAddNote = (accountId:string, name:string) => {
  //Alert.alert('Add Note', 'This would navigate to add a note');
  router.push({
    pathname: '/ui/helpers/AddNotesPage',
    params: { id: accountId , name: name}
   })
};
/**
 * Displays a notification for errors.
 * @param {string} message - The message to be displayed.
 */
export const showSnackbar = (message: string) => {
  if (Platform.OS === 'web') {
    console.log('Error: ' + message);
    return;
  }
  else if(Platform.OS === 'android'){
    Snackbar.show({
      text: message,
      //backgroundColor: '#E63946', // Red color for errors
    })
    return
  }
  // Use Toast instead of Snackbar
  Toast.show({
    type: 'error',
    text1: 'Error',
    text2: message,
    position: 'bottom',
    visibilityTime: 4000,
  });
};


//You can use this to pass the call from to the next screen identify the call from
export const  CALL_FROM = {
  PRODUCT_LIST: 'PRODUCT_LIST',
  DEAL_LIST: 'DEAL_LIST',
  ACCOUNT_LIST: 'ACCOUNT_LIST',
  CONTACT_LIST: 'CONTACT_LIST',
  TASK_LIST: 'TASK_LIST',
};
/**
 * Displays a toast notification for success.
 * @param {string} message - The message to be displayed.
 */
export const showToast = (message: string) => {
  if (Platform.OS === 'web') {
    alert(message);
    return;
  }
  
  Toast.show({
    type: 'success',
    text1: 'Success',
    text2: message,
    position: 'bottom',
    visibilityTime: 4000,
  });
};

export const validateForEditAndDeleteActs = async (id: string, scope: string,ownerId:string): Promise<boolean> => {
  console.log("validateForEditAndDeleteActs:id " + id + " scope: " + scope + " ownerId: " + ownerId);
  
  if (scope === 'global') {
    console.log("validateForEditAndDeleteActs: global ");
    return true;
  }
  else if (scope === 'territory') {
    console.log("validateForEditAndDeleteActs: territory ");
  if(id==='null'||id==='undefined'||id===''){
    return false;
  }
    const userData = await AsyncStorage.getItem('user_data_always');
    var userDataJson = JSON.parse(userData!);
    var territories = userDataJson.user.territories;
    return territories.some((t:any) => t.id === parseInt(id));
  }
  else if (scope === 'owned') {
    console.log("validateForEditAndDeleteActs: owned ");
    if(ownerId==='null'||ownerId==='undefined'||ownerId===''){
    return false;
    }
    const userData = await AsyncStorage.getItem(USER_STORAGE_KEY);
    var userDataJson = JSON.parse(userData!);
    return parseInt(ownerId) === parseInt(userDataJson.id)|| ownerId === userDataJson.username;
  }
  else {
    console.log("validateForEditAndDeleteActs: else ");
    return false;
  }
}

export const callCreateContactPage=async  (contactDetails:any,initialContact:any)=>{
  console.log("callCreateContactPage: " + JSON.stringify(contactDetails) + " " + JSON.stringify(initialContact));
  const scope = await readUserScope();
  if(contactDetails===''&&initialContact===''){
     router.push('/contact' as any);
  }
  else if(scope.contact.create){
    router.push({
              pathname: '/contact' as any,
              params: { contact: JSON.stringify(contactDetails?.contacts), id: initialContact.id }
            });
  }
  else{
    showSnackbar('You do not have permission to create a contact');
  }
}

export const checkAllAdd=async ()=>{
  const accessScope = await readUserScope();
  console.log("accessScopeOneAction: " + JSON.stringify(accessScope));
  if (accessScope?.contact?.create === true && accessScope?.task?.create === true &&
    accessScope?.appointment?.create === true && accessScope?.notes?.create === true) { 
    return true;
    }
    else{
      return false;
    }
}

export const readUserScope=async():Promise<any>=>{
  const roleInfo=await AsyncStorage.getItem(USER_STORAGE_ALWAYS);
  const parsedUser = JSON.parse(roleInfo!);
  const accessScope = parsedUser.user.access_scope;
  console.log("accessScope: " + JSON.stringify(accessScope));
  return accessScope;
}

export const updateUserScope=async(scope:any):Promise<any>=>{
  const roleInfo=await AsyncStorage.getItem(USER_STORAGE_ALWAYS);
  const parsedUser = JSON.parse(roleInfo!);
  parsedUser.user.access_scope=scope;
  await AsyncStorage.setItem(USER_STORAGE_ALWAYS, JSON.stringify(parsedUser));
  console.log("updateUserScope: " + JSON.stringify(scope));
  return scope;
}

// Utility function for date conversion
export const convertDate = (input: string, inputFormat: string, outputFormat: string): string => {
  try {
    //console.log('Input:', input, 'InputFormat:', inputFormat, 'OutputFormat:', outputFormat);
    
    // Parse the input string to a Date object
    const parsedDate = parse(input, inputFormat, new Date());
    
    // Format the date to desired output format
    return format(parsedDate, outputFormat);
  } catch (error) {
    console.error('Date conversion error:', error);
    return input; // Return original input in case of error
  }
};

/**
 * Capitalizes the first letter of each word in a string
 * @param str - The string to capitalize
 * @returns The capitalized string
 */
export const capitalizeWords = (str: string | null): string => {
  if (!str) return '';
  
  // Split by spaces and handle multiple spaces
  return str
    .trim()
    .split(/\s+/)
    .map(word => {
      // Handle empty strings
      if (word.length === 0) return '';
      // Handle special cases like "pvt.", "ltd.", etc.
      const lowerWord = word.toLowerCase();
      if (['pvt', 'ltd', 'llc', 'llp', 'inc'].includes(lowerWord)) {
        return lowerWord.toUpperCase();
      }
      // Default case: capitalize first letter
      return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    })
    .join(' ');
};

/**
 * Gets the first letter capitalized from a string
 * @param str - The string to get initial from
 * @param fallback - Optional fallback character if string is empty (defaults to 'U')
 * @returns The capitalized first letter
 */
export const getInitial = (str: string | null, fallback: string = 'U'): string => {
  if (!str || str.length === 0) return fallback.toUpperCase();
  // Get first non-space character
  const firstChar = str.trim().charAt(0);
  return firstChar ? firstChar.toUpperCase() : fallback.toUpperCase();
};

/**
 * Formats a name by capitalizing words and handling special cases
 * @param firstName - The first name
 * @param lastName - The last name (optional)
 * @returns The formatted full name
 */
export const formatName = (firstName: string | null, lastName: string | null): string => {
  const first = capitalizeWords(firstName || '');
  const last = capitalizeWords(lastName || '');
  return [first, last].filter(Boolean).join(' ') || 'Unknown';
};

/**
 * Generates a consistent color for a contact avatar based on their name
 * @param name - The name to generate a color for
 * @returns A hex color string
 */
export const getAvatarColor = (name: string | null): string => {
  if (!name) return '#0F96BB'; // Default color if no name

  // Google Contacts-like colors
  const colors = [
    '#F28B82', // Red
    '#FBBC04', // Yellow
    '#FFF475', // Light Yellow
    '#CCFF90', // Light Green
    '#A7FFEB', // Teal
    '#CBF0F8', // Light Blue
    '#AECBFA', // Blue
    '#D7AEFB', // Purple
    '#FDCFE8', // Pink
    '#E6C9A8', // Brown
    '#E8EAED', // Gray
  ];

  // Generate a consistent index based on the name
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  // Convert hash to positive index
  const index = Math.abs(hash) % colors.length;
  return colors[index];
};

/**
 * Determines whether to use white or black text based on background color luminance.
 * @param hexColor - The background color in hex format (e.g., '#RRGGBB').
 * @returns '#FFFFFF' for white text or '#000000' for black text.
 */
export const getContrastingTextColor = (hexColor: string): string => {
  // Remove '#' if it exists
  const cleanHex = hexColor.replace(/^#/, '');

  // Parse r, g, b values
  const bigint = parseInt(cleanHex, 16);
  const r = (bigint >> 16) & 255;
  const g = (bigint >> 8) & 255;
  const b = bigint & 255;

  // Calculate luminance (per WCAG 2.0) - a common standard
  const luminance = (0.2126 * sRGBtoLinear(r) + 0.7152 * sRGBtoLinear(g) + 0.0722 * sRGBtoLinear(b));

  // Use white text if luminance is below a threshold (0.179 is a common value for WCAG AA)
  return luminance > 0.179 ? '#000000' : '#FFFFFF';
};

// Helper function to convert sRGB to Linear RGB
const sRGBtoLinear = (c: number): number => {
  const s = c / 255.0;
  return s <= 0.04045 ? s / 12.92 : Math.pow((s + 0.055) / 1.055, 2.4);
};

export interface MoreOptionsModalProps {
  visible: boolean;
  onClose: () => void;
  recordTypes?: Activity[];
  salesActivities?: Activity[];
  onActivityPress?: (activity: Activity) => void;
}

export const MoreOptionsModal: React.FC<MoreOptionsModalProps> = ({
  visible,
  onClose,
  recordTypes = [],
  salesActivities = [],
  onActivityPress
}) => {
  const colors = useColors();
  const styles = useMemo(() => createMoreOptionsStyles(colors), [colors]);

  const handleActivityPress = (activity: Activity) => {
    if (onActivityPress) {
      onActivityPress(activity);
    } else {
      const navigateFn = activity?.navigate;
      if (navigateFn && typeof navigateFn === 'function') {
        navigateFn();
      } else if (activity?.activity_name) {
        const activityName = activity.activity_name.toLowerCase();
        switch (activityName) {
          case 'task':
            router.push({ pathname: '/TaskListScreen' as any });
            break;
          case 'meeting':
            router.push({ pathname: '/meetings/createMeeting' as any });
            break;
          case 'contact':
            router.push({ pathname: '/contacts' as any });
            break;
          case 'account':
            router.push({ pathname: '/distributors' as any });
            break;
          case 'deal':
            router.push({ pathname: '/deals' as any });
            break;
          case 'product':
            router.push({ pathname: '/ui/product/ProductListPage' as any });
            break;
          case 'quotation':
            router.push({ pathname: '/DealsQuoteList' as any });
            break;
          default:
            Alert.alert('Coming Soon', `${activity.activity_name} creation will be available soon!`);
        }
      }
    }
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <TouchableOpacity 
        style={styles.overlay}
        activeOpacity={1}
        onPress={onClose}
      >
        <View style={styles.popup}>
          <Text style={styles.popupTitle}>Select Record Type</Text>
          
          {recordTypes.length > 0 && (
            <View style={styles.popupSection}>
              {recordTypes.map((type: Activity) => {
                const iconName: IconType = type.activity_icon || 'document-outline';
                return (
                  <TouchableOpacity
                    key={type.id}
                    style={styles.popupItem}
                    onPress={() => handleActivityPress(type)}
                  >
                    <View style={[styles.popupIcon, { backgroundColor: colors.background.tertiary }]}>
                      <Ionicons 
                        name={iconName}
                        size={20} 
                        color={colors.text.primary} 
                      />
                    </View>
                    <Text style={styles.popupItemText}>{type.activity_name}</Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          )}

          {salesActivities.length > 0 && (
            <View style={styles.popupSection}>
              {salesActivities.map((activity) => (
                <TouchableOpacity 
                  key={activity.id} 
                  style={styles.popupItem}
                  onPress={() => handleActivityPress(activity)}
                >
                  <View style={[styles.popupIcon, { backgroundColor: colors.background.tertiary }]}>
                    <Ionicons 
                      name={activity.activity_icon as IconType || 'document-outline'} 
                      size={20} 
                      color={colors.text.primary} 
                    />
                  </View>
                  <Text style={styles.popupItemText}>{activity.activity_name}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

const createMoreOptionsStyles = (colors: ReturnType<typeof useColors>) => StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  popup: {
    backgroundColor: colors.background.primary,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 16,
    paddingBottom: Platform.OS === 'ios' ? 30 : 20,
    maxHeight: '70%',
  },
  popupTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text.primary,
    paddingHorizontal: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.tertiary,
  },
  popupSection: {
    marginTop: 16,
  },
  popupItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  popupIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  popupItemText: {
    fontSize: 16,
    color: colors.text.primary,
  },
});

/**
 * Handles sending an email using the device's default email client
 * @param {string} email - The recipient's email address
 * @param {string} subject - The email subject
 * @param {string} body - The email body
 * @param {string[]} cc - Optional array of CC email addresses
 * @param {string[]} bcc - Optional array of BCC email addresses
 */
export const handleSendEmail = async (
  email: string,
  subject: string = '',
  body: string = '',
  cc: string[] = [],
  bcc: string[] = []
) => {
  try {
    // Construct the mailto URL with all parameters
    let mailtoUrl = `mailto:${encodeURIComponent(email)}`;
    
    // Add subject if provided
    if (subject) {
      mailtoUrl += `?subject=${encodeURIComponent(subject)}`;
    }
    
    // Add body if provided
    if (body) {
      mailtoUrl += `${subject ? '&' : '?'}body=${encodeURIComponent(body)}`;
    }
    
    // Add CC if provided
    if (cc.length > 0) {
      mailtoUrl += `${mailtoUrl.includes('?') ? '&' : '?'}cc=${encodeURIComponent(cc.join(','))}`;
    }
    
    // Add BCC if provided
    if (bcc.length > 0) {
      mailtoUrl += `${mailtoUrl.includes('?') ? '&' : '?'}bcc=${encodeURIComponent(bcc.join(','))}`;
    }

    // Check if the device can handle the mailto URL
    const canOpen = await Linking.canOpenURL(mailtoUrl);
    
    if (canOpen) {
      await Linking.openURL(mailtoUrl);
    } else {
      showSnackbar('No email client found on your device');
    }
  } catch (error) {
    console.error('Error sending email:', error);
    showSnackbar('Could not open email client. Please try again.');
  }
};