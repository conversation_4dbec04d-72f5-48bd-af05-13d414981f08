// This file is cloned from CreateDeals.tsx to serve as the EditDeal page for editing deals.
// The next step will be to add logic to fetch deal details by ID and populate the form for editing.

import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  ScrollView,
  ActivityIndicator,
  Alert,
  Platform,
  KeyboardAvoidingView,
  Modal,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { DealField, DealFieldsResponse, DealFieldChoice } from './models/DealField';
import { useColors } from '@/hooks/useThemeColor';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useAuth } from '../context/AuthContext';
import { Item } from 'react-native-paper/lib/typescript/components/Drawer/Drawer';
import { showSnackbar } from './ui/utils';

type DealFormData = { [key: string]: string | number | null };

const EditDeal = () => {
  const params = useLocalSearchParams();
  const dealId = params.dealId as string;
  const dealDetails = params.dealDetails;
  const [fields, setFields] = useState<DealField[]>([]);
  const [formData, setFormData] = useState<DealFormData>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [currentDropdownField, setCurrentDropdownField] = useState<DealField | null>(null);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [currentDateField, setCurrentDateField] = useState<string>('');
  const [tempDate, setTempDate] = useState(new Date());
  const [dateError, setDateError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  const router = useRouter();
  const colors = useColors();
  const { user } = useAuth();
  console.log('dealDetails',JSON.stringify(dealDetails));
  // Validate date
  const isDateValid = (selectedDate: Date): boolean => {
    const now = new Date();
    now.setHours(0, 0, 0, 0);
    const selected = new Date(selectedDate);
    selected.setHours(0, 0, 0, 0);
    return selected >= now;
  };

  // Fetch deal fields and deal details
  useEffect(() => {
    const fetchAll = async () => {
      try {
        setLoading(true);
        setError(null);
        // Fetch fields
        const fieldsRes = await apiService.get<DealFieldsResponse>(API_ENDPOINTS.DEAL_FIELDS);
        if (!fieldsRes.data || !fieldsRes.data.dealFields) throw new Error('Failed to fetch deal fields');
        const sortedFields = [...fieldsRes.data.dealFields].sort((a, b) => {
          if (a.order === null) return 1;
          if (b.order === null) return -1;
          return a.order - b.order;
        });
        setFields(fieldsRes.data.dealFields);

        // Fetch deal details
        const dealRes = await apiService.get(`${API_ENDPOINTS.DEALS}/${dealId}`);
        
        
        // Extract deal data from response
        const dealData = dealRes.data?.deals || dealRes.data?.data?.deals || dealRes.data?.data;
        if (!dealData) throw new Error('Failed to fetch deal details');        // Initialize form data with deal details
        const initialFormData: DealFormData = {};
        sortedFields.forEach(field => {
          const fieldName = field.field_name;
          const isDropdownOrLookup = ['dropdown', 'lookup'].includes(field.field_type.toLowerCase());
          const info = dealData.find((item: any) => item.field_name === fieldName);
          
          if (isDropdownOrLookup) {
            // Handle dropdown/lookup fields
            if (info) {
              const fieldValue = info.field_value;
              if (fieldValue) {
                const selectedChoice = info.choices?.find((choice: any) => 
                  choice.id?.toString() === fieldValue.toString()
                );
                if (selectedChoice) {
                  // Store both ID and display value
                  initialFormData[`${fieldName}_id`] = selectedChoice.id;
                  initialFormData[fieldName] = selectedChoice.custom_option;
                  if (field.lookup_type === 'users' || fieldName.toLowerCase().includes('owner')) {
                    initialFormData[`${fieldName}_display`] = selectedChoice.custom_option;
                  }
                }
              }
            }
          } else {            // Handle regular fields
            if (info) {
              const value = info.field_value;
              initialFormData[fieldName] = value === undefined || value === '' ? null : value;
            }
          }
        });
        console.log('Initial form data:', JSON.stringify(initialFormData, null, 2));
        setFormData(initialFormData);
      } catch (err) {
        console.error('Error fetching deal data:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    if (dealId) fetchAll();
  }, [dealId]);

  // Input, dropdown, date handlers (same as CreateDeals)
  const handleInputChange = (fieldName: string, value: string) => {
    setFormData(prevData => ({ 
      ...prevData, 
      [fieldName]: value === '' ? null : value 
    }));
  };
  const handleDropdownSelect = (field: DealField, choice: DealFieldChoice) => {
    const isOwnerField = field.field_name.toLowerCase().includes('owner') ||
      field.field_label.toLowerCase().includes('owner') ||
      field.lookup_type === 'users';
    if (isOwnerField) {
      setFormData(prevData => ({
        ...prevData,
        [`${field.field_name}_id`]: choice.id,
        [field.field_name]: choice.custom_option,
        [`${field.field_name}_display`]: choice.custom_option
      }));
    } else {
      setFormData(prevData => ({
        ...prevData,
        [`${field.field_name}_id`]: choice.id,
        [field.field_name]: choice.custom_option
      }));
    }
    setShowDropdown(false);
  };
  const onDateChange = (event: any, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      setShowDatePicker(false);
      if (event.type !== 'dismissed' && selectedDate) {
        if (isDateValid(selectedDate)) {
          handleInputChange(currentDateField, selectedDate.toISOString().split('T')[0]);
          setDateError(null);
        } else {
          setDateError('Please select a future date');
          Alert.alert('Invalid Date', 'Please select a future date');
        }
      }
    } else {
      if (selectedDate) setTempDate(selectedDate);
    }
  };
  const handleDateConfirm = () => {
    if (isDateValid(tempDate)) {
      handleInputChange(currentDateField, tempDate.toISOString().split('T')[0]);
      setShowDatePicker(false);
      setDateError(null);
    } else {
      setDateError('Please select a future date');
      Alert.alert('Invalid Date', 'Please select a future date');
    }
  };

  // Submit logic for editing
  const handleSubmit = async () => {
    try {
      const requiredFields = fields.filter(field => field.required === 'on');
      const missingFields = requiredFields.filter(field => !formData[field.field_name]);
      if (missingFields.length > 0) {
        Alert.alert('Validation Error', `Please fill in the following required fields: ${missingFields.map(f => f.field_label).join(', ')}`);
        return;
      }      setSubmitting(true);      const deal = fields.map(field => {
        const isDropdownOrLookup = ['dropdown', 'lookup'].includes(field.field_type.toLowerCase());
        let fieldValue;
        
        if (isDropdownOrLookup) {
          // For dropdown/lookup fields, use the ID value
          fieldValue = formData[`${field.field_name}_id`] || null;
        } else {
          // For other fields, handle empty strings and undefined
          const value = formData[field.field_name];
          fieldValue = value === '' || value === undefined ? null : value;
        }

        return {
          id: field.id,
          field_name: field.field_name,
          field_label: field.field_label,
          field_type: field.field_type,
          field_value: fieldValue,
          required: field.required,
          active: field.active,
          lookup_type: field.lookup_type
        };
      });

      const requestData = { deal };
      console.log('Full Update Deal Request Body:', JSON.stringify(requestData, null, 2));
      const response = await apiService.put(`${API_ENDPOINTS.DEALS}/${dealId}`, requestData);
        if (response.status === 200) {
        console.log('API Response:', JSON.stringify(response.data, null, 2));
        const successMessage = response.data?.message || 'Deal updated successfully';
        showSnackbar(successMessage);
        router.back();
      } else {
        const errorMessage = response.data?.message || 'Failed to update deal';
        showSnackbar(errorMessage);
        throw new Error(errorMessage);
      }
    } catch (err) {
      console.error('Error updating deal:', err);
      showSnackbar('Failed to update deal. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const renderDropdownOptions = () => {
    if (!showDropdown || !currentDropdownField) return null;

    const choices = currentDropdownField.choices || [];
    const filteredChoices = searchQuery
      ? choices.filter(choice =>
          choice.custom_option.toLowerCase().includes(searchQuery.toLowerCase())
        )
      : choices;

    return (
      <Modal
        visible={showDropdown}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowDropdown(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.dropdownContainer}>
            <View style={styles.dropdownHeader}>
              <Text style={styles.dropdownTitle}>
                Select {currentDropdownField.field_label}
              </Text>
              <TouchableOpacity
                onPress={() => setShowDropdown(false)}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>
            <TextInput
              style={styles.searchInput}
              placeholder="Search..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoCapitalize="none"
            />
            <FlatList
              data={filteredChoices}
              keyExtractor={(item) => item.id.toString()}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.dropdownOption}
                  onPress={() => handleDropdownSelect(currentDropdownField, item)}
                >
                  <Text style={styles.dropdownOptionText}>{item.custom_option}</Text>
                </TouchableOpacity>
              )}
              style={styles.dropdownList}
            />
          </View>
        </View>
      </Modal>
    );
  };

  const renderDatePicker = () => {
    if (!showDatePicker) return null;

    if (Platform.OS === 'ios') {
      return (
        <Modal
          visible={showDatePicker}
          transparent={true}
          animationType="slide"
        >
          <View style={styles.modalOverlay}>
            <View style={styles.datePickerContainer}>
              <View style={styles.datePickerHeader}>
                <TouchableOpacity
                  onPress={() => setShowDatePicker(false)}
                  style={styles.datePickerCancelButton}
                >
                  <Text style={styles.datePickerButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={handleDateConfirm}
                  style={styles.datePickerConfirmButton}
                >
                  <Text style={[styles.datePickerButtonText, { color: '#0F96BB' }]}>Done</Text>
                </TouchableOpacity>
              </View>
              <DateTimePicker
                value={tempDate}
                mode="date"
                display="spinner"
                onChange={onDateChange}
                minimumDate={new Date()}
              />
            </View>
          </View>
        </Modal>
      );
    }

    return (
      <DateTimePicker
        value={tempDate}
        mode="date"
        display="default"
        onChange={onDateChange}
        minimumDate={new Date()}
      />
    );
  };

  const renderFormField = (field: DealField) => {

    console.log('fieldValuesdkjhsdjk',formData[field.field_name]);
    const value = formData[field.field_name] || '';
    const isRequired = field.required === 'on';
    const isDropdownOrLookup = ['dropdown', 'lookup'].includes(field.field_type.toLowerCase());
    const isDate = field.field_type.toLowerCase() === 'date';

    return (
      <View key={field.field_name} style={styles.fieldContainer}>
        <Text style={styles.fieldLabel}>
          {field.field_label}
          {isRequired && <Text style={styles.requiredStar}> *</Text>}
        </Text>
        
        {isDropdownOrLookup ? (
          <TouchableOpacity
            style={styles.dropdownField}
            onPress={() => {
              setCurrentDropdownField(field);
              setShowDropdown(true);
              setSearchQuery('');
            }}
          >
            <Text style={[styles.dropdownFieldText, !value && styles.placeholderText]}>
              {value || `Select ${field.field_label}`}
            </Text>
            <Ionicons name="chevron-down" size={20} color="#666" />
          </TouchableOpacity>
        ) : isDate ? (
          <TouchableOpacity
            style={styles.dateField}
            onPress={() => {
              setCurrentDateField(field.field_name);
              setShowDatePicker(true);
              if (value) {
                setTempDate(new Date(value));
              }
            }}
          >
            <Text style={[styles.dateFieldText, !value && styles.placeholderText]}>
              {value || `Select ${field.field_label}`}
            </Text>
            <Ionicons name="calendar-outline" size={20} color="#666" />
          </TouchableOpacity>
        ) : (
          <TextInput
            style={styles.input}
            value={value === null ? '' : value.toString()}
            onChangeText={(text) => handleInputChange(field.field_name, text)}
            placeholder={`Enter ${field.field_label}`}
            placeholderTextColor="#999"
            multiline={field.field_type.toLowerCase() === 'textarea'}
            numberOfLines={field.field_type.toLowerCase() === 'textarea' ? 4 : 1}
          />
        )}
        {isDate && dateError && field.field_name === currentDateField && (
          <Text style={styles.formErrorText}>{dateError}</Text>
        )}
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0F96BB" />
        <Text style={styles.loadingText}>Loading Opportunity details...</Text>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => router.replace({ pathname: '/dealDetails', params: { dealId } })}>
          <Text style={styles.retryButtonText}>Back</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <Stack.Screen options={{ headerShown: false }} />
      <KeyboardAvoidingView 
        style={{ flex: 1 }} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton} 
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Edit Deal</Text>
          <View style={{ width: 40 }} />
        </View>

        <View style={styles.mainContainer}>
          <ScrollView 
            style={styles.scrollView}
            contentContainerStyle={styles.contentContainer}
            keyboardShouldPersistTaps="handled"
          >
            {fields.map(field => { 
              if(field.active === 1){ 
                return renderFormField(field)
              }
            })}
          </ScrollView>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.submitButton, submitting && styles.submittingButton]}
              onPress={handleSubmit}
              disabled={submitting}
            >
              {submitting ? (
                <ActivityIndicator color="#fff" size="small" />
              ) : (
                <Text style={styles.submitButtonText}>Update Deal</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>

        {renderDropdownOptions()}
        {renderDatePicker()}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#0F96BB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#0F96BB',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  mainContainer: {
    flex: 1,
    backgroundColor: '#F5F5F5',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 100,
  },
  fieldContainer: {
    marginBottom: 16,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  requiredStar: {
    color: '#FF3B30',
  },
  input: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#333',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  dropdownField: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  dropdownFieldText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  dateField: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  dateFieldText: {
    fontSize: 16,
    color: '#333',
  },
  placeholderText: {
    color: '#999',
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  submitButton: {
    backgroundColor: '#0F96BB',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
  },
  submittingButton: {
    opacity: 0.7,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  dropdownContainer: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  dropdownHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  dropdownTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  closeButton: {
    padding: 4,
  },
  searchInput: {
    backgroundColor: '#F5F5F5',
    margin: 16,
    padding: 12,
    borderRadius: 8,
    fontSize: 16,
  },
  dropdownList: {
    maxHeight: 400,
  },
  dropdownOption: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  dropdownOptionText: {
    fontSize: 16,
    color: '#333',
  },
  datePickerContainer: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  datePickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  datePickerCancelButton: {
    padding: 8,
  },
  datePickerConfirmButton: {
    padding: 8,
  },
  datePickerButtonText: {
    fontSize: 16,
    color: '#666',
  },
  errorText: {
    fontSize: 16,
    color: '#FF3B30',
    marginBottom: 16,
    textAlign: 'center',
  },
  formErrorText: {
    color: '#FF3B30',
    fontSize: 12,
    marginTop: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#FFFFFF',
  },
  retryButton: {
    backgroundColor: '#0F96BB',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default EditDeal;