import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  ScrollView,
  ActivityIndicator,
  Alert,
  Platform,
  KeyboardAvoidingView,
  Modal,
  FlatList,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { QuoteField, QuoteFieldsResponse, QuoteFormData } from './models/QuoteField';
import DateTimePicker from '@react-native-community/datetimepicker';
import { showSnackbar } from './ui/utils';
import { DynamicFormField } from './dynamic_fields/DynamicFormField';
import { FIELD_TYPES } from './models/contact_fields_model';

const STATUS_BAR_CONFIG = {
  barStyle: "light-content" as const,
  backgroundColor: "#0F96BB",
  translucent: Platform.OS === 'android'
};

// Using a default export to expose the component as a screen
export default function DealsAddQuote() {
  const [fields, setFields] = useState<QuoteField[]>([]);
  const [formData, setFormData] = useState<QuoteFormData>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [currentDropdownField, setCurrentDropdownField] = useState<QuoteField | null>(null);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [currentDateField, setCurrentDateField] = useState<string>('');
  const [tempDate, setTempDate] = useState(new Date());
  const [searchQuery, setSearchQuery] = useState('');
  const [invalidFields, setInvalidFields] = useState<string[]>([]);
  const [showAllFields, setShowAllFields] = useState(false); // Toggle for showing quick_add "off" fields
  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false);

  const router = useRouter();
  const params = useLocalSearchParams();
  const dealId = params.dealId as string;
  const insets = useSafeAreaInsets();

  // Group fields by quotationgroups_id for better organization
  const groupedFields = React.useMemo(() => {
    const groups: { [key: number]: QuoteField[] } = {};
    
    fields.forEach(field => {
      if (!groups[field.quotationgroups_id]) {
        groups[field.quotationgroups_id] = [];
      }
      groups[field.quotationgroups_id].push(field);
    });
    
    return groups;
  }, [fields]);

  // Fetch quote fields on component mount
  useEffect(() => {
    fetchQuoteFields();
  }, []);

  // Pre-fill deal_id if it's passed in params
  useEffect(() => {
    if (dealId && fields.length > 0) {
      const dealField = fields.find(field => field.field_name === 'deal_id');
      if (dealField) {
        // Set the deal_id in form data
        setFormData(prev => ({
          ...prev,
          'deal_id': dealId
        }));

        // If the field is a lookup or dropdown and doesn't have choices loaded
        if ((dealField.field_type.toLowerCase() === 'lookup' || 
             dealField.field_type.toLowerCase() === 'dropdown') && 
            (!dealField.choices || dealField.choices.length === 0)) {
          // Fetch choices if not available
          fetchDealChoices(dealField);
        }
      }
    }
  }, [dealId, fields]);

  // Fetch quote fields from API
  const fetchQuoteFields = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('Fetching quote fields...');
      const response = await apiService.get<QuoteFieldsResponse>(API_ENDPOINTS.QUOTATION_FIELDS);
      
      if (!response.data || !response.data.response) {
        throw new Error('Failed to fetch quote fields');
      }

      console.log(`Fetched ${response.data.response.length} quote fields`);
      
      // Filter out inactive fields
      const activeFields = response.data.response.filter(field => field.active === 1);
      setFields(activeFields);
      
      // Initialize form data
      const initialFormData: QuoteFormData = {};
      activeFields.forEach(field => {
        // Pre-fill with default values if available
        initialFormData[field.field_name] = field.field_value || null;
      });
      
      setFormData(initialFormData);
    } catch (err) {
      console.error('Error fetching quote fields:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Handle text input changes
  const handleInputChange = (fieldName: string, value: string) => {
    setFormData(prevData => ({
      ...prevData,
      [fieldName]: value
    }));
    
    // If this field was marked as invalid and now has a value, remove it from invalidFields
    if (invalidFields.includes(fieldName) && value) {
      setInvalidFields(prev => prev.filter(field => field !== fieldName));
    }
  };

  // Handle dropdown selection
  const handleDropdownSelect = (field: QuoteField, choice: any) => {
    setFormData(prevData => ({
      ...prevData,
      [field.field_name]: choice.id.toString()
    }));
    setShowDropdown(false);
  };

  // Handle date selection
  const onDateChange = (event: any, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      // On Android, the picker closes immediately after selection
      setShowDatePicker(false);
      
      // Only update if a date was actually selected (not cancelled)
      if (event.type !== 'dismissed' && selectedDate) {
        handleInputChange(currentDateField, selectedDate.toISOString().split('T')[0]);
      }
    } else {
      // On iOS, just update the temp date (user will press Done to confirm)
      if (selectedDate) {
        setTempDate(selectedDate);
      }
    }
  };

  const handleDateConfirm = () => {
    handleInputChange(currentDateField, tempDate.toISOString().split('T')[0]);
    setShowDatePicker(false);
  };

  // Function to get quick_add "on" fields
  const getQuickAddFields = () => {
    return fields
      .filter(field => field.active === 1 && field.quick_add === "on")
      .sort((a, b) => (a.order || 0) - (b.order || 0));
  };

  // Function to get quick_add "off" fields
  const getQuickAddOffFields = () => {
    return fields
      .filter(field => field.active === 1 && field.quick_add === "off")
      .sort((a, b) => (a.order || 0) - (b.order || 0));
  };

  // Function to render the toggle for showing all fields
  const renderFieldToggle = () => {
    const quickAddOffFields = getQuickAddOffFields();

    if (quickAddOffFields.length === 0) {
      return null; // Don't show toggle if there are no quick_add "off" fields
    }

    return (
      <View style={styles.toggleContainer}>
        <Text style={styles.toggleLabel}>Show all fields</Text>
        <TouchableOpacity
          style={[styles.toggleButton, showAllFields && styles.toggleButtonActive]}
          onPress={() => setShowAllFields(!showAllFields)}
        >
          <View style={[styles.toggleSlider, showAllFields && styles.toggleSliderActive]} />
        </TouchableOpacity>
      </View>
    );
  };

  const validateForm = () => {
    const errors: string[] = [];

    // Get fields to validate based on current visibility
    const fieldsToValidate = showAllFields
      ? fields.filter(field => field.active === 1)
      : fields.filter(field => field.active === 1 && field.quick_add === "on");

    // Always include deal_id in validation regardless of visibility
    const dealIdField = fields.find(field => field.field_name === 'deal_id');
    if (dealIdField && !fieldsToValidate.includes(dealIdField)) {
      fieldsToValidate.push(dealIdField);
    }

    fieldsToValidate.forEach(field => {
      if ((field.required === 'on' || field.field_name === 'deal_id') &&
          (!formData[field.field_name] || formData[field.field_name].toString().trim() === '')) {
        errors.push(`${field.field_label} is required`);
      }
    });
    return errors;
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      // Set hasAttemptedSubmit to true to show validation errors
      setHasAttemptedSubmit(true);

      const errors = validateForm();
      if (errors.length > 0) {
        // Set the invalid fields to show red borders
        const invalidFieldNames = errors.map(error => {
          const field = fields.find(f => `${f.field_label} is required` === error);
          return field ? field.field_name : '';
        }).filter(name => name);
        setInvalidFields(invalidFieldNames);
        Alert.alert('Validation Error', errors[0]);
        return;
      }

      // Clear invalid fields if validation passes
      setInvalidFields([]);
      setSubmitting(true);

      // Prepare data for submission - format according to API requirements
      const requestData = fields
        .filter(field => field.active === 1)
        .map(field => {
          let value = formData[field.field_name] || null;

          // For dropdown and lookup fields, we need to handle the value differently
          if ((field.field_type === FIELD_TYPES.DROPDOWN || field.field_type === FIELD_TYPES.LOOKUP) && value) {
            const selectedChoice = field.choices?.find(c => c.id.toString() === value);
            value = selectedChoice ? selectedChoice.id.toString() : value; // Pass the ID for API
          }

          return {
            id: field.id,
            custom_field: field.custom_field,
            field_label: field.field_label,
            field_name: field.field_name,
            field_type: field.field_type,
            required: field.required,
            field_value: value
          };
        });

      console.log('Submitting quote data:', requestData);

      const response = await apiService.post(API_ENDPOINTS.CREATE_QUOTATION, requestData);
      
      console.log('API Response:', JSON.stringify(response.data, null, 2));

      if (response.data?.status === true) {
        // Show success message
        showSnackbar('Quote created successfully');
        
        // Navigate back to the deal details
        if (dealId) {
          router.back();
        } else if (response.data?.response?.deal_id) {
          router.push({
            pathname: 'dealDetails' as any,
            params: { dealId: response.data.response.deal_id.toString() }
          });
        } else {
          router.back(); // Fallback to going back if no deal_id
        }
        return;
      }
      
      // If we get here, it means there was an error in the response
      throw new Error(response.data?.message || 'Failed to create quote');
      
    } catch (err) {
      console.error('Error creating quote:', err);
      Alert.alert(
        'Error',
        'Failed to create quote. Please try again.'
      );
    } finally {
      setSubmitting(false);
    }
  };

  // Filtered choices for dropdown/lookup fields
  const filteredChoices = React.useMemo(() => {
    if (!currentDropdownField?.choices) return [];
    if (!searchQuery) return currentDropdownField.choices;
    
    const isLookup = currentDropdownField.field_type.toLowerCase() === 'lookup';
    const searchProperty = isLookup ? 'value' : 'custom_option';
    
    return currentDropdownField.choices.filter(choice => {
      const searchValue = choice[searchProperty as keyof typeof choice];
      if (typeof searchValue === 'string') {
        return searchValue.toLowerCase().includes(searchQuery.toLowerCase());
      }
      return false;
    });
  }, [currentDropdownField?.choices, searchQuery]);

  // Render dropdown options
  const renderDropdownOptions = () => {
    if (!currentDropdownField) return null;
    
    const isLookup = currentDropdownField.field_type.toLowerCase() === 'lookup';
    const displayProperty = isLookup ? 'value' : 'custom_option';
    
    return (
      <Modal
        visible={showDropdown}
        transparent={true}
        animationType="slide"
        onRequestClose={() => {
          setShowDropdown(false);
          setSearchQuery('');
        }}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.dropdownContainer}>
            <View style={styles.dropdownHeader}>
              <Text style={styles.dropdownTitle}>{currentDropdownField.field_label}</Text>
              <TouchableOpacity 
                onPress={() => {
                  setShowDropdown(false);
                  setSearchQuery('');
                }}
                hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
              >
                <Ionicons name="close" size={24} color="#000" />
              </TouchableOpacity>
            </View>
            
            <View style={styles.searchContainer}>
              <Ionicons name="search" size={20} color="#888" />
              <TextInput
                style={styles.searchInput}
                placeholder="Search..."
                value={searchQuery}
                onChangeText={setSearchQuery}
                autoFocus={true}
              />
            </View>
            
            <FlatList
              data={filteredChoices}
              keyExtractor={(item) => item.id.toString()}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.dropdownItem}
                  onPress={() => handleDropdownSelect(currentDropdownField, item)}
                >
                  <Text style={styles.dropdownItemText}>
                    {item[displayProperty as keyof typeof item] as string}
                  </Text>
                </TouchableOpacity>
              )}
              ListEmptyComponent={
                <View style={styles.emptyList}>
                  <Text style={styles.emptyListText}>No options found</Text>
                </View>
              }
            />
          </View>
        </SafeAreaView>
      </Modal>
    );
  };

  // Render date picker
  const renderDatePicker = () => {
    if (!showDatePicker) return null;
    
    if (Platform.OS === 'ios') {
      return (
        <Modal
          visible={showDatePicker}
          transparent={true}
          animationType="slide"
        >
          <SafeAreaView style={styles.modalContainer}>
            <View style={styles.datePickerContainer}>
              <View style={styles.datePickerHeader}>
                <TouchableOpacity 
                  onPress={() => setShowDatePicker(false)}
                  hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
                >
                  <Text style={styles.cancelButton}>Cancel</Text>
                </TouchableOpacity>
                <Text style={styles.datePickerTitle}>Select Date</Text>
                <TouchableOpacity 
                  onPress={handleDateConfirm}
                  hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
                >
                  <Text style={styles.doneButton}>Done</Text>
                </TouchableOpacity>
              </View>
              <DateTimePicker
                value={tempDate}
                mode="date"
                display="spinner"
                onChange={onDateChange}
                style={styles.datePicker}
              />
            </View>
          </SafeAreaView>
        </Modal>
      );
    }
    
    // For Android, we use the native date picker
    return (
      <DateTimePicker
        value={tempDate}
        mode="date"
        display="default"
        onChange={onDateChange}
      />
    );
  };

  // Add a function to get display value for a field
  const getFieldDisplayValue = (field: QuoteField, value: string | number | null) => {
    if (!value) return '';
    
    if (field.field_type.toLowerCase() === 'lookup' || field.field_type.toLowerCase() === 'dropdown') {
      const selectedChoice = field.choices.find(c => c.id.toString() === value.toString());
      if (selectedChoice) {
        return field.field_type.toLowerCase() === 'lookup' 
          ? selectedChoice.value 
          : selectedChoice.custom_option;
      }
    }
    return value.toString();
  };

  // Add function to fetch deal choices if needed
  const fetchDealChoices = async (dealField: QuoteField) => {
    try {
      const response = await apiService.get(`${API_ENDPOINTS.DEALS}`);
      if (response.data?.response) {
        // Update the field choices
        const updatedFields = fields.map(field => {
          if (field.id === dealField.id) {
            return {
              ...field,
              choices: response.data.response.map((deal: any) => ({
                id: deal.id,
                value: deal.deal_title || 'Untitled Deal',  // Use deal_title as primary value
                custom_option: deal.deal_title || 'Untitled Deal'  // Use deal_title for display
              }))
            };
          }
          return field;
        });
        setFields(updatedFields);
      }
    } catch (error) {
      console.error('Error fetching deal choices:', error);
    }
  };

  // Update the renderFormField function to properly show default values
  const renderFormField = (field: QuoteField) => {
    // Skip rendering inactive fields
    if (field.active !== 1) return null;
    
    const fieldValue = formData[field.field_name];
    const isRequired = field.required === 'on' || field.field_name === 'deal_id';
    const isReadOnly = field.read_only === 'on';
    const showRedBorder = isRequired && invalidFields.includes(field.field_name);
    
    let fieldContent;
    
    switch (field.field_type.toLowerCase()) {
      case 'text field':
      case 'number':
        fieldContent = (
          <TextInput
            style={[
              styles.input,
              isReadOnly && styles.readOnlyInput,
              showRedBorder && styles.errorInput
            ]}
            placeholder={field.placeholder || `Enter ${field.field_label.toLowerCase()}`}
            value={fieldValue?.toString() || ''}
            onChangeText={(text) => handleInputChange(field.field_name, text)}
            editable={!isReadOnly}
            keyboardType={field.field_type.toLowerCase() === 'number' ? 'numeric' : 'default'}
          />
        );
        break;
        
      case 'dropdown':
      case 'lookup':
        // Get display value for dropdown/lookup
        let displayValue = 'Click to select';
        
        if (fieldValue) {
          displayValue = getFieldDisplayValue(field, fieldValue) || 'Click to select';
        }
        
        fieldContent = (
          <TouchableOpacity
            style={[
              styles.dropdownField,
              isReadOnly && styles.readOnlyInput,
              showRedBorder && styles.errorInput
            ]}
            onPress={() => {
              if (!isReadOnly) {
                setCurrentDropdownField(field);
                setShowDropdown(true);
                setSearchQuery('');
              }
            }}
            disabled={isReadOnly}
          >
            <Text style={[
              styles.dropdownText,
              !fieldValue && styles.placeholderText
            ]}>
              {displayValue}
            </Text>
            <Ionicons name="chevron-down" size={20} color="#888" />
          </TouchableOpacity>
        );
        break;
        
      case 'datepicker':
        fieldContent = (
          <TouchableOpacity
            style={[
              styles.dropdownField,
              isReadOnly && styles.readOnlyInput,
              showRedBorder && styles.errorInput
            ]}
            onPress={() => {
              if (!isReadOnly) {
                setCurrentDateField(field.field_name);
                setTempDate(fieldValue ? new Date(fieldValue as string) : new Date());
                setShowDatePicker(true);
              }
            }}
            disabled={isReadOnly}
          >
            <Text style={[
              styles.dropdownText,
              !fieldValue && styles.placeholderText
            ]}>
              {fieldValue || field.placeholder || 'Pick a date'}
            </Text>
            <Ionicons name="calendar-outline" size={20} color="#888" />
          </TouchableOpacity>
        );
        break;
        
      default:
        fieldContent = (
          <TextInput
            style={[
              styles.input,
              showRedBorder && styles.errorInput
            ]}
            placeholder={field.placeholder || `Enter ${field.field_label.toLowerCase()}`}
            value={fieldValue?.toString() || ''}
            onChangeText={(text) => handleInputChange(field.field_name, text)}
          />
        );
    }
    
    return (
      <View key={field.id} style={styles.fieldContainer}>
        <View style={styles.labelContainer}>
          <Text style={styles.fieldLabel}>{field.field_label}</Text>
          {isRequired && <Text style={styles.requiredStar}>*</Text>}
        </View>
        {fieldContent}
      </View>
    );
  };
  
  // Function to get quick_add "on" fields from a group
  const getQuickAddFieldsFromGroup = (groupFields: QuoteField[]) => {
    return groupFields
      .filter(field => field.active === 1 && field.quick_add === "on")
      .sort((a, b) => (a.order || 0) - (b.order || 0));
  };

  // Function to get quick_add "off" fields from a group
  const getQuickAddOffFieldsFromGroup = (groupFields: QuoteField[]) => {
    return groupFields
      .filter(field => field.active === 1 && field.quick_add === "off")
      .sort((a, b) => (a.order || 0) - (b.order || 0));
  };

  // Render group of fields
  const renderFieldGroup = (groupId: number, groupFields: QuoteField[]) => {
    // Get group name based on groupId
    let groupName = 'Basic information';

    switch (groupId) {
      case 2:
        groupName = 'Basic information';
        break;
      case 3:
        groupName = 'Shipping information';
        break;
      case 4:
        groupName = 'Billing information';
        break;
      case 5:
        groupName = 'Additional information';
        break;
      default:
        groupName = `Group ${groupId}`;
    }

    const quickAddOnFields = getQuickAddFieldsFromGroup(groupFields);
    const quickAddOffFields = getQuickAddOffFieldsFromGroup(groupFields);

    // Only render the group if it has visible fields
    const hasVisibleFields = quickAddOnFields.length > 0 || (showAllFields && quickAddOffFields.length > 0);

    if (!hasVisibleFields) {
      return null;
    }

    return (
      <View key={groupId} style={styles.sectionContainer}>
        <Text style={styles.sectionHeader}>{groupName}</Text>

        {/* Render quick_add "on" fields first */}
        {quickAddOnFields.map(field => (
          <DynamicFormField
            key={field.id}
            field={field}
            value={formData[field.field_name]?.toString() || ''}
            onChangeText={(value) => handleInputChange(field.field_name, value)}
            showRequired={hasAttemptedSubmit}
            showAllFields={showAllFields}
          />
        ))}

        {/* Render quick_add "off" fields if toggle is enabled */}
        {showAllFields && quickAddOffFields.map(field => (
          <DynamicFormField
            key={field.id}
            field={field}
            value={formData[field.field_name]?.toString() || ''}
            onChangeText={(value) => handleInputChange(field.field_name, value)}
            showRequired={hasAttemptedSubmit}
            showAllFields={showAllFields}
          />
        ))}
      </View>
    );
  };

  const handleBack = () => {
    router.back();
  };

  // Loading state UI
  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { paddingTop: insets.top }]}>
        <StatusBar {...STATUS_BAR_CONFIG} />
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <View style={styles.headerContent}>
            <Text style={styles.headerTitleText}>Add Quote</Text>
          </View>
          <View style={styles.headerRight} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0F96BB" />
          <Text style={styles.loadingText}>Loading quote fields...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Error state UI
  if (error) {
    return (
      <SafeAreaView style={[styles.container, { paddingTop: insets.top }]}>
        <StatusBar {...STATUS_BAR_CONFIG} />
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Ionicons name="arrow-back" size={28} color="#FFFFFF" />
          </TouchableOpacity>
          <View style={styles.headerContent}>
            <Text style={styles.headerTitleText}>Add Quote</Text>
          </View>
          <View style={styles.headerRight} />
        </View>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={fetchQuoteFields}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { paddingTop: insets.top }]}>
      <StatusBar {...STATUS_BAR_CONFIG} />
      <Stack.Screen options={{ headerShown: false }} />

      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Ionicons name={Platform.OS === 'ios' ? 'chevron-back' : 'arrow-back'}  size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitleText}>Add Quote</Text>
        </View>
        <TouchableOpacity 
          onPress={handleSubmit} 
          disabled={submitting}
          style={styles.headerRight}
        >
          <Ionicons name="checkmark" size={28} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
      >
        <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
          {/* Render toggle for showing all fields */}
          {renderFieldToggle()}

          {/* Render grouped fields */}
          {Object.entries(groupedFields).map(([groupIdStr, groupFields]) => {
            const groupId = parseInt(groupIdStr, 10);
            return renderFieldGroup(groupId, groupFields);
          })}

          {/* Add padding at the bottom for the fixed submit button */}
          <View style={{ height: 80 }} />
        </ScrollView>

        {/* Fixed Submit Button at Bottom */}
        <View style={styles.fixedSubmitButtonContainer}>
          <TouchableOpacity
            style={styles.submitButton}
            onPress={handleSubmit}
            disabled={submitting}
          >
            {submitting ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <Text style={styles.submitButtonText}>Create Quote</Text>
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
      
      {/* Render dropdowns and date pickers */}
      {renderDropdownOptions()}
      {renderDatePicker()}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#0F96BB',
    height: Platform.OS === 'ios' ? 44 : 56,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
    zIndex: 1000,
    paddingHorizontal: Platform.OS === 'ios' ? 8 : 0,
  },
  backButton: {
    width: Platform.OS === 'ios' ? 44 : 56,
    height: Platform.OS === 'ios' ? 44 : 56,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: Platform.OS === 'ios' ? -8 : 0,
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: Platform.OS === 'ios' ? 4 : 16,
  },
  headerRight: {
    width: Platform.OS === 'ios' ? 44 : 56,
    height: Platform.OS === 'ios' ? 44 : 56,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Platform.OS === 'ios' ? -8 : 0,
  },
  headerTitleText: {
    color: '#FFFFFF',
    fontSize: Platform.OS === 'ios' ? 17 : 20,
    fontWeight: Platform.OS === 'ios' ? '600' : '500',
    textAlign: 'center',
  },
  contentContainer: {
    paddingBottom: 30,
  },
  sectionContainer: {
    backgroundColor: 'white',
    borderRadius: 8,
    marginHorizontal: 16,
    marginTop: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionHeader: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0F96BB',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  fieldContainer: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  fieldLabel: {
    fontSize: 14,
    color: '#444',
  },
  requiredStar: {
    color: 'red',
    fontSize: 14,
    marginLeft: 2,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  readOnlyInput: {
    backgroundColor: '#f5f5f5',
    borderColor: '#e0e0e0',
  },
  dropdownField: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    backgroundColor: '#fff',
  },
  dropdownText: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  placeholderText: {
    color: '#aaa',
  },
  submitButton: {
    backgroundColor: '#0F96BB',
    borderRadius: 8,
    paddingVertical: 14,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  dropdownContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    maxHeight: '80%',
  },
  dropdownHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  dropdownTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    margin: 16,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
  },
  dropdownItem: {
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  dropdownItemText: {
    fontSize: 16,
    color: '#333',
  },
  emptyList: {
    padding: 20,
    alignItems: 'center',
  },
  emptyListText: {
    fontSize: 16,
    color: '#888',
  },
  datePickerContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  datePickerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  datePickerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  cancelButton: {
    fontSize: 16,
    color: '#888',
  },
  doneButton: {
    fontSize: 16,
    color: '#0F96BB',
    fontWeight: '600',
  },
  datePicker: {
    height: 200,
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#888',
  },
  errorContainer: {
    flex: 1,
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#d32f2f',
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#0F96BB',
    borderRadius: 8,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  fixedSubmitButtonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  errorInput: {
    borderColor: '#ff0000',
    borderWidth: 1,
  },
  toggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginHorizontal: 16,
    marginTop: 16,
    backgroundColor: 'white',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  toggleLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  toggleButton: {
    width: 50,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#ddd',
    justifyContent: 'center',
    paddingHorizontal: 2,
  },
  toggleButtonActive: {
    backgroundColor: '#0F96BB',
  },
  toggleSlider: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  toggleSliderActive: {
    transform: [{ translateX: 22 }],
  },
});