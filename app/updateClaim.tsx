import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  Platform,
  StatusBar,
  Alert,
  ScrollView,
  Image,
  Modal,
} from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';

// Status bar height for Android
const STATUSBAR_HEIGHT = Platform.OS === 'android' ? StatusBar.currentHeight || 0 : 0;

interface TransportMode {
  mode: string;
  rate: number;
}

export default function UpdateClaimScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const activityId = params.activityId as string;
  
  const [transportModes, setTransportModes] = useState<TransportMode[]>([]);
  const [selectedMode, setSelectedMode] = useState(params.currentMode as string || '');
  const [amount, setAmount] = useState(params.currentAmount as string || '0');
  const [remarks, setRemarks] = useState(params.currentRemarks as string || '');
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);

  useEffect(() => {
    // Parse transport modes from params
    try {
      const modesData = JSON.parse(params.transportModes as string || '[]');
      setTransportModes(modesData);
      
      // If no current mode is selected, set the first mode as default
      if (!params.currentMode && modesData.length > 0) {
        setSelectedMode(modesData[0].mode);
      }
    } catch (err) {
      console.error('Error parsing transport modes:', err);
      setTransportModes([]);
    }
  }, [params.transportModes]);

  // Get rate for selected mode
  const getSelectedModeRate = () => {
    const selectedTransportMode = transportModes.find(mode => mode.mode === selectedMode);
    return selectedTransportMode?.rate || 0;
  };

  const handleBack = () => {
    router.back();
  };

  const requestPermissions = async () => {
    if (Platform.OS !== 'web') {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Sorry, we need camera roll permissions to make this work!');
        return false;
      }
      return true;
    }
    return true;
  };

  const pickImage = async (useCamera: boolean) => {
    try {
      if (!await requestPermissions()) return;

      let result;
      if (useCamera) {
        // Request camera permission first
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Sorry, we need camera permissions to make this work!');
          return;
        }
        result = await ImagePicker.launchCameraAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsEditing: false,
          quality: 0.5,
        });
      } else {
        result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsEditing: false,
          quality: 0.5,
          allowsMultipleSelection: true,
          selectionLimit: 5 - selectedImages.length,
        });
      }

      if (!result.canceled) {
        if (useCamera) {
          setSelectedImages([...selectedImages, result.assets[0].uri]);
        } else {
          const newImages = result.assets.map(asset => asset.uri);
          setSelectedImages([...selectedImages, ...newImages]);
        }
      }
    } catch (err) {
      console.error('Error picking image:', err);
      Alert.alert('Error', 'Failed to pick image');
    }
  };

  const removeImage = (index: number) => {
    const newImages = [...selectedImages];
    newImages.splice(index, 1);
    setSelectedImages(newImages);
  };

  const showImagePickerOptions = () => {
    Alert.alert(
      'Select Image',
      'Choose an option',
      [
        {
          text: 'Take Photo',
          onPress: () => pickImage(true),
        },
        {
          text: 'Choose from Gallery',
          onPress: () => pickImage(false),
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ],
      { cancelable: true }
    );
  };

  const handleUpdate = async () => {
    if (!selectedMode) {
      Alert.alert('Error', 'Please select a transport mode');
      return;
    }

    // Only require images if they should be shown (not for bike or car)
    if (shouldShowImageUpload() && selectedImages.length === 0) {
      Alert.alert('Error', 'Please select at least one image');
      return;
    }

    try {
      // Create form data
      const formData = new FormData();
      formData.append('transport_mode', selectedMode);
      
      // Add amount if it's not bike or car
      if (shouldShowAmountField() && amount) {
        formData.append('claim_amount', amount);
      } else if (selectedMode.toLowerCase() === 'public transport' && !amount) {
        Alert.alert('Error', 'Amount is required for Public Transport');
        return;
      }

      // Add remarks if provided
      if (remarks) {
        formData.append('remarks', remarks);
      }

      // Add all images (only if images should be shown)
      if (shouldShowImageUpload()) {
        selectedImages.forEach((imageUri, index) => {
          const imageUriParts = imageUri.split('.');
          const imageFileType = imageUriParts[imageUriParts.length - 1];

          formData.append(`claim_image_${index}`, {
            uri: imageUri,
            name: `claim_image_${index}.${imageFileType}`,
            type: `image/${imageFileType}`
          } as any);
        });
      }

      // Make API call
      const response = await apiService.put(
        `conveyance/${activityId}/edit`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      if (response.data.status) {
        Alert.alert('Success', response.data.message, [
          { 
            text: 'OK', 
            onPress: () => {
              router.back();
              // You might want to refresh the previous screen's data here
            }
          }
        ]);
      } else {
        Alert.alert('Error', response.data.message || 'Failed to update claim');
      }
    } catch (error: any) {
      console.error('Error updating claim:', error);
      Alert.alert(
        'Error',
        error.response?.data?.message || 'Failed to update claim. Please try again.'
      );
    }
  };

  const toggleModal = () => {
    setIsModalVisible(!isModalVisible);
  };

  // Function to check if amount field should be shown
  const shouldShowAmountField = () => {
    const mode = selectedMode.toLowerCase();
    return mode !== 'bike' && mode !== 'car';
  };

  // Function to check if image upload should be shown
  const shouldShowImageUpload = () => {
    const mode = selectedMode.toLowerCase();
    return mode !== 'bike' && mode !== 'car';
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor="#0F96BB" barStyle="light-content" />
      <View style={styles.statusBarPlaceholder} />
      <Stack.Screen options={{ headerShown: false }} />
      
      {/* Custom Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Ionicons name={Platform.OS === 'ios' ? 'chevron-back' : 'arrow-back'}  size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Update Claim</Text>
        <View style={{ width: 30 }} />
      </View>

      <ScrollView style={styles.content}>
        {/* Mode of Transport */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Mode of Transport*</Text>
          <TouchableOpacity 
            style={styles.dropdownButton} 
            onPress={toggleModal}
          >
            <Text style={[
              styles.dropdownButtonText, 
              !selectedMode && styles.dropdownButtonPlaceholder
            ]}>
              {selectedMode || 'Select transport mode'}
            </Text>
            <Ionicons name="chevron-down" size={24} color="#666" />
          </TouchableOpacity>
          {selectedMode && (
            <Text style={styles.rateText}>
              Rate: ₹{getSelectedModeRate()}/km
            </Text>
          )}
        </View>

        {/* Amount Claimable - Only show if not bike or car */}
        {shouldShowAmountField() && (
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Amount Claimable</Text>
            <TextInput
              style={styles.textInput}
              value={amount}
              onChangeText={setAmount}
              keyboardType="numeric"
              placeholder="Enter amount"
            />
          </View>
        )}

        {/* Remarks */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Remarks</Text>
          <TextInput
            style={[styles.textInput, styles.remarksInput]}
            value={remarks}
            onChangeText={setRemarks}
            placeholder="Enter remarks"
            multiline
            numberOfLines={4}
          />
        </View>

        {/* Image Upload - Only show if not bike or car */}
        {shouldShowImageUpload() && (
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Upload Images*</Text>
            <View style={styles.imageGrid}>
              {selectedImages.map((image, index) => (
                <View key={index} style={styles.imageContainer}>
                  <View style={styles.imagePreview}>
                    <TouchableOpacity
                      style={styles.removeButton}
                      onPress={() => removeImage(index)}
                    >
                      <Ionicons name="close-circle" size={24} color="red" />
                    </TouchableOpacity>
                    <Image
                      source={{ uri: image }}
                      style={styles.previewImage}
                      resizeMode="cover"
                    />
                  </View>
                </View>
              ))}
              {selectedImages.length < 5 && (
                <View style={styles.imageContainer}>
                  <TouchableOpacity
                    style={styles.addImageButton}
                    onPress={showImagePickerOptions}
                  >
                    <Ionicons name="add-circle" size={40} color="#0F96BB" />
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </View>
        )}

        {/* Update Button */}
        <TouchableOpacity 
          style={styles.updateButton} 
          onPress={handleUpdate}
        >
          <Text style={styles.updateButtonText}>Update</Text>
        </TouchableOpacity>

        {/* Modal for transport mode selection */}
        <Modal
          visible={isModalVisible}
          transparent={true}
          animationType="slide"
          onRequestClose={toggleModal}
        >
          <TouchableOpacity 
            style={styles.modalOverlay} 
            activeOpacity={1} 
            onPress={toggleModal}
          >
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Select Mode of Transport</Text>
                <TouchableOpacity onPress={toggleModal}>
                  <Ionicons name="close" size={24} color="#333" />
                </TouchableOpacity>
              </View>
              {transportModes.map((mode) => (
                <TouchableOpacity
                  key={mode.mode}
                  style={[
                    styles.modeOption,
                    selectedMode === mode.mode && styles.selectedModeOption
                  ]}
                  onPress={() => {
                    setSelectedMode(mode.mode);
                    toggleModal();
                  }}
                >
                  <Text style={[
                    styles.modeOptionText,
                    selectedMode === mode.mode && styles.selectedModeOptionText
                  ]}>
                    {mode.mode}
                  </Text>
                  <Text style={[
                    styles.modeRateText,
                    selectedMode === mode.mode && styles.selectedModeOptionText
                  ]}>
                    ₹{mode.rate}/km
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </TouchableOpacity>
        </Modal>

      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  statusBarPlaceholder: {
    height: STATUSBAR_HEIGHT,
    backgroundColor: '#0F96BB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#0F96BB',
    paddingVertical: 4,
    paddingHorizontal: 15,
    height: Platform.OS === 'android' ? 44 : 36,
  },
  backButton: {
    padding: 2,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    color: '#333',
    marginBottom: 8,
    fontWeight: '500',
  },
  textInput: {
    backgroundColor: 'white',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 12,
    fontSize: 16,
  },
  remarksInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 12,
  },
  uploadButtonText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#666',
  },
  imageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
  },
  imageContainer: {
    width: 60,
    height: 60,
  },
  imagePreview: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#ddd',
    position: 'relative',
  },
  previewImage: {
    width: '100%',
    height: '100%',
  },
  removeButton: {
    position: 'absolute',
    top: -3,
    right: -3,
    zIndex: 1,
    backgroundColor: 'white',
    borderRadius: 12,
  },
  updateButton: {
    backgroundColor: '#0F96BB',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 30,
  },
  updateButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  rateText: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
    fontStyle: 'italic',
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'white',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 12,
    height: 50,
  },
  dropdownButtonText: {
    fontSize: 16,
    color: '#333',
  },
  dropdownButtonPlaceholder: {
    color: '#999',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 16,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  selectedModeOption: {
    backgroundColor: '#0F96BB',
  },
  modeOptionText: {
    fontSize: 16,
    color: '#333',
  },
  modeRateText: {
    fontSize: 14,
    color: '#666',
  },
  selectedModeOptionText: {
    color: 'white',
  },
  addImageButton: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
  },
}); 