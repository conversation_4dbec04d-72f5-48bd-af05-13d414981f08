import { Redirect } from 'expo-router';
import { useAuth } from '../context/AuthContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useEffect, useState } from 'react';
import { setupNotifications } from '../utils/firebaseNotifications';
import { initializeFirebase, isFirebaseReady } from '../utils/firebaseConfig';

// Define the token storage key - must match what's in AuthService
const TOKEN_STORAGE_KEY = 'auth_token';
const USER_STORAGE_KEY = 'user_data';

export default function Index() {
  const { user, isLoading } = useAuth();
  const [directAuthCheck, setDirectAuthCheck] = useState({
    isChecked: false,
    isAuthenticated: false
  });

  // Additional direct check for token to ensure we're not missing authentication
  useEffect(() => {
    const checkAuthDirectly = async () => {
      try {
        // Check both token and user data
        const token = await AsyncStorage.getItem(TOKEN_STORAGE_KEY);
        const userData = await AsyncStorage.getItem(USER_STORAGE_KEY);

        console.log('📱 Direct storage check - Auth state:', {
          hasToken: !!token,
          hasUserData: !!userData,
          token: token ? token.substring(0, 10) + '...' : 'null'
        });

        // If we have both token and user data, consider the user authenticated
        if (token && userData) {
          setDirectAuthCheck({
            isChecked: true,
            isAuthenticated: true
          });
        } else {
          setDirectAuthCheck({
            isChecked: true,
            isAuthenticated: false
          });
        }
      } catch (error) {
        console.error('Error checking auth directly:', error);
        setDirectAuthCheck({
          isChecked: true,
          isAuthenticated: false
        });
      }
    };

    checkAuthDirectly();
  }, []);

  useEffect(() => {
    const initializeNotifications = async () => {
      try {
        // Ensure Firebase is initialized first
        if (!isFirebaseReady()) {
          console.log('Initializing Firebase before notifications setup');
          await initializeFirebase();
        }

        // Add a small delay to ensure Firebase has fully initialized
        await new Promise(resolve => setTimeout(resolve, 500));

        // Now set up notifications
        console.log('Setting up notifications');
        await setupNotifications();
        console.log('Notifications setup completed successfully');
      } catch (error) {
        console.error('Failed to initialize notifications:', error);
        
        // Try one more time with a delay in case of race conditions
        try {
          console.log('Retrying notification setup after a delay...');
          await new Promise(resolve => setTimeout(resolve, 1500));
          
          await initializeFirebase();
          await setupNotifications();
          console.log('Notifications setup succeeded on second attempt');
        } catch (retryError) {
          console.error('Failed to initialize notifications after retry:', retryError);
        }
      }
    };

    initializeNotifications();
  }, []);

  // Log both auth methods
  console.log('📱 App index - Auth state comparison:', {
    contextAuth: { isAuthenticated: !!user, isLoading },
    directStorageAuth: directAuthCheck
  });

  // If direct check shows user is authenticated, go to tabs
  if (directAuthCheck.isChecked && directAuthCheck.isAuthenticated) {
    console.log('🔑 Index: User is authenticated (direct check) - Redirecting to tabs');
    return <Redirect href={{ pathname: "/(tabs)" as any }} />;
  }

  // If still loading or not checked directly yet, wait
  if (isLoading || !directAuthCheck.isChecked) {
    console.log('⏳ Index: Still checking authentication status');
    return null; // Show nothing while checking
  }

  // If user context indicates authenticated, go to tabs
  if (user) {
    console.log('🔑 Index: User is authenticated (context) - Redirecting to tabs');
    return <Redirect href={{ pathname: "/(tabs)" as any }} />;
  }

  // Otherwise go to login
  console.log('🔒 Index: User is not authenticated - Redirecting to login page');
  return <Redirect href={{ pathname: "/login" as any }} />;
}