/**
 * Attendance Toggle Demo Page
 * Demonstrates the attendance toggle behavior with different statuses
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Switch,
  ActivityIndicator,
  Alert
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useColors } from '../hooks/useThemeColor';
import { attendanceStorageService } from '../services/AttendanceStorageService';
import { convertDate, DATE_FORMAT } from './ui/utils';

export default function AttendanceToggleDemoScreen() {
  const [currentStatus, setCurrentStatus] = useState<string>('Pending');
  const [isLoading, setIsLoading] = useState(false);
  const colors = useColors();
  const router = useRouter();

  // Helper function to get current attendance status
  const getCurrentAttendanceStatus = () => {
    return currentStatus;
  };

  // Mock toggle function for demo
  const mockToggleAttendance = async (value: boolean) => {
    setIsLoading(true);
    
    // Simulate API call delay
    setTimeout(() => {
      const newStatus = value ? 'Inprogress' : 'Completed';
      setCurrentStatus(newStatus);
      setIsLoading(false);
      
      Alert.alert(
        'Status Changed',
        `Attendance status changed to: ${newStatus}`,
        [{ text: 'OK' }]
      );
    }, 1000);
  };

  // Function to change status for testing
  const changeStatus = async (newStatus: string) => {
    try {
      setCurrentStatus(newStatus);
      
      // Also update in storage for consistency
      const today = convertDate(new Date().toISOString(), DATE_FORMAT.SYSTEM_FORMAT, DATE_FORMAT.DATE_FORMAT);
      await attendanceStorageService.updateAttendanceStatus(today, newStatus, {
        checkin_time: newStatus === 'Inprogress' ? '09:00 AM' : null,
        checkout_time: newStatus === 'Completed' ? '05:30 PM' : null,
        location: 'Demo Location'
      });
      
      Alert.alert('Success', `Status changed to ${newStatus}`);
    } catch (error) {
      console.error('Error changing status:', error);
      Alert.alert('Error', 'Failed to change status');
    }
  };

  const statusOptions = [
    { label: 'Pending', value: 'Pending', color: '#FF9500' },
    { label: 'In Progress', value: 'Inprogress', color: '#4CD964' },
    { label: 'Completed', value: 'Completed', color: '#007AFF' }
  ];

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <Stack.Screen 
        options={{ 
          title: 'Attendance Toggle Demo',
          headerShown: true,
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: 'white',
          headerTitleStyle: { fontWeight: 'bold' }
        }} 
      />
      
      <ScrollView style={styles.scrollView}>
        {/* Current Status Display */}
        <View style={[styles.card, { backgroundColor: colors.background.secondary }]}>
          <Text style={[styles.cardTitle, { color: colors.text.primary }]}>
            Current Attendance Status
          </Text>
          <View style={styles.statusDisplay}>
            <View style={[styles.statusBadge, { backgroundColor: statusOptions.find(s => s.value === currentStatus)?.color || '#999' }]}>
              <Text style={styles.statusBadgeText}>{currentStatus}</Text>
            </View>
          </View>
        </View>

        {/* Attendance Toggle Demo */}
        <View style={[styles.card, { backgroundColor: colors.background.secondary }]}>
          <Text style={[styles.cardTitle, { color: colors.text.primary }]}>
            Attendance Toggle (Home Page Style)
          </Text>
          <Text style={[styles.cardSubtitle, { color: colors.text.secondary }]}>
            This shows how the toggle appears in the home page header
          </Text>
          
          {/* Mock Home Page Header */}
          <View style={styles.mockHeader}>
            <Text style={styles.mockHeaderTitle}>Sales CRM</Text>
            <View style={styles.mockHeaderRight}>
              <TouchableOpacity style={styles.mockMenuButton}>
                <View style={[styles.attendanceContainer, { backgroundColor: getCurrentAttendanceStatus() === "Inprogress" ? 'rgba(255, 255, 255, 0.2)' : 'rgba(255, 255, 255, 0.1)' }]}>
                  {/* Only show text if status is not Pending */}
                  {getCurrentAttendanceStatus() !== "Pending" && (
                    <Text style={[styles.attendanceLabel, { color: '#ffffff' }]}>
                      {getCurrentAttendanceStatus() === "Inprogress" ? "In" : "Out"}
                    </Text>
                  )}
                  {isLoading ? (
                    <ActivityIndicator size="small" color="#ffffff" style={styles.attendanceLoader} />
                  ) : (
                    <Switch
                      value={getCurrentAttendanceStatus() === "Inprogress"}
                      onValueChange={mockToggleAttendance}
                      trackColor={{ false: 'rgba(255, 255, 255, 0.3)', true: '#4CD964' }}
                      thumbColor={'#ffffff'}
                      ios_backgroundColor={'rgba(255, 255, 255, 0.3)'}
                      style={[styles.attendanceSwitch, { transform: [{ scaleX: 0.7 }, { scaleY: 0.7 }] }]}
                      disabled={isLoading}
                    />
                  )}
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Status Change Controls */}
        <View style={[styles.card, { backgroundColor: colors.background.secondary }]}>
          <Text style={[styles.cardTitle, { color: colors.text.primary }]}>
            Change Status (For Testing)
          </Text>
          <Text style={[styles.cardSubtitle, { color: colors.text.secondary }]}>
            Try different statuses to see how the toggle behaves
          </Text>
          
          <View style={styles.statusButtons}>
            {statusOptions.map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.statusButton,
                  { 
                    backgroundColor: option.color,
                    opacity: currentStatus === option.value ? 1 : 0.7
                  }
                ]}
                onPress={() => changeStatus(option.value)}
              >
                <Text style={styles.statusButtonText}>{option.label}</Text>
                {currentStatus === option.value && (
                  <Ionicons name="checkmark" size={16} color="white" />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Behavior Explanation */}
        <View style={[styles.card, { backgroundColor: colors.background.secondary }]}>
          <Text style={[styles.cardTitle, { color: colors.text.primary }]}>
            Toggle Behavior Explanation
          </Text>
          
          <View style={styles.behaviorList}>
            <View style={styles.behaviorItem}>
              <View style={[styles.behaviorIcon, { backgroundColor: '#FF9500' }]}>
                <Text style={styles.behaviorIconText}>P</Text>
              </View>
              <View style={styles.behaviorContent}>
                <Text style={[styles.behaviorTitle, { color: colors.text.primary }]}>
                  Pending Status
                </Text>
                <Text style={[styles.behaviorDescription, { color: colors.text.secondary }]}>
                  • "Out" text is hidden{'\n'}
                  • Only toggle switch is visible{'\n'}
                  • User must check in first
                </Text>
              </View>
            </View>

            <View style={styles.behaviorItem}>
              <View style={[styles.behaviorIcon, { backgroundColor: '#4CD964' }]}>
                <Text style={styles.behaviorIconText}>I</Text>
              </View>
              <View style={styles.behaviorContent}>
                <Text style={[styles.behaviorTitle, { color: colors.text.primary }]}>
                  In Progress Status
                </Text>
                <Text style={[styles.behaviorDescription, { color: colors.text.secondary }]}>
                  • "In" text is visible{'\n'}
                  • Toggle switch is ON{'\n'}
                  • User can check out
                </Text>
              </View>
            </View>

            <View style={styles.behaviorItem}>
              <View style={[styles.behaviorIcon, { backgroundColor: '#007AFF' }]}>
                <Text style={styles.behaviorIconText}>C</Text>
              </View>
              <View style={styles.behaviorContent}>
                <Text style={[styles.behaviorTitle, { color: colors.text.primary }]}>
                  Completed Status
                </Text>
                <Text style={[styles.behaviorDescription, { color: colors.text.secondary }]}>
                  • "Out" text is visible{'\n'}
                  • Toggle switch is OFF{'\n'}
                  • Attendance is closed for the day
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Navigation */}
        <View style={[styles.card, { backgroundColor: colors.background.secondary }]}>
          <Text style={[styles.cardTitle, { color: colors.text.primary }]}>
            Navigation
          </Text>
          <TouchableOpacity
            style={[styles.navButton, { backgroundColor: colors.primary }]}
            onPress={() => router.push('/(tabs)/' as any)}
          >
            <Ionicons name="home-outline" size={20} color="white" />
            <Text style={styles.navButtonText}>Go to Home Page</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  card: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  cardSubtitle: {
    fontSize: 14,
    marginBottom: 16,
    lineHeight: 20,
  },
  statusDisplay: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  statusBadge: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  statusBadgeText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  mockHeader: {
    backgroundColor: '#0F96BB',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
  },
  mockHeaderTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  mockHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  mockMenuButton: {
    padding: 0,
  },
  attendanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    minWidth: 80,
    justifyContent: 'center',
  },
  attendanceLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginRight: 8,
  },
  attendanceLoader: {
    marginRight: 8,
  },
  attendanceSwitch: {
    // Transform applied inline
  },
  statusButtons: {
    gap: 12,
  },
  statusButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  statusButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  behaviorList: {
    gap: 16,
  },
  behaviorItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
  },
  behaviorIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  behaviorIconText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  behaviorContent: {
    flex: 1,
  },
  behaviorTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  behaviorDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
  },
  navButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
});
