import React, { useState, useMemo, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  SafeAreaView,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter } from 'expo-router';
import { useColors } from '@/hooks/useThemeColor';
import { Contact, ContactsResponse } from './models/Contact';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';
import { getAvatarColor, getContrastingTextColor } from './ui/utils';

export default function ContactsScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [filteredContacts, setFilteredContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const router = useRouter();
  const colors = useColors();
  const styles = useMemo(() => createStyles(colors), [colors]);

  useEffect(() => {
    fetchContacts();
  }, []);

  const formatDaysAgo = (dateStr: string): string => {
    // First, ensure we have a valid date string
    if (!dateStr) return 'New';
    
    try {
      // Parse the date string - handle different formats
      const date = new Date(dateStr);
      
      // Check if date is valid
      if (isNaN(date.getTime())) {
        console.error('Invalid date format:', dateStr);
        return 'New';
      }
      
      const now = new Date();
      
      // Calculate difference in milliseconds
      const diffTime = Math.abs(now.getTime() - date.getTime());
      
      // Convert to days
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
      
      // Format based on time difference
      if (diffDays === 0) {
        // If it's today, we can be more specific with hours
        const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
        if (diffHours === 0) {
          const diffMinutes = Math.floor(diffTime / (1000 * 60));
          return diffMinutes <= 1 ? 'Just now' : `${diffMinutes} minutes ago`;
        }
        return diffHours === 1 ? '1 hour ago' : `${diffHours} hours ago`;
      }
      
      if (diffDays === 1) return 'Yesterday';
      if (diffDays < 7) return `${diffDays} days ago`;
      if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
      
      // Calculate months difference more accurately
      const monthDiff = (now.getFullYear() - date.getFullYear()) * 12 + (now.getMonth() - date.getMonth());
      if (monthDiff < 12) return monthDiff === 1 ? '1 month ago' : `${monthDiff} months ago`;
      
      // Calculate years difference
      const yearDiff = Math.floor(monthDiff / 12);
      return yearDiff === 1 ? '1 year ago' : `${yearDiff} years ago`;
    } catch (error) {
      //console.error('Error formatting date:', error);
      return 'New';
    }
  };

  const fetchContacts = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiService.get<ContactsResponse>(API_ENDPOINTS.CONTACTS);
      
      if (response.data?.contacts) {
        console.log(`Received ${response.data.contacts.length} contacts, sorting now...`);
        
        // Create a copy of the contacts array to avoid mutating the original
        const contactsCopy = [...response.data.contacts];
        
        // Sort contacts by created_at date (newest first)
        // If created_at is null, place at the end (not the beginning)
        const sortedContacts = contactsCopy.sort((a, b) => {
          // If either date is null/undefined, handle accordingly
          if (!a.created_at && !b.created_at) return 0; // Both null, no change
          if (!a.created_at) return 1; // a is null, move to end
          if (!b.created_at) return -1; // b is null, move to end
          
          // Both have dates, sort newest first (descending order)
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        });
        
        // Log the first few sorted contacts to verify
        console.log('First 5 contacts after sorting (newest first):');
        sortedContacts.slice(0, Math.min(5, sortedContacts.length)).forEach((contact, index) => {
          console.log(`${index + 1}. ${contact.first_name} ${contact.last_name || ''}: ${contact.created_at || 'NULL'}`);
        });
        
        setContacts(sortedContacts);
        setFilteredContacts(sortedContacts);
        console.log('Contacts sorted successfully: newest first');
      } else {
        throw new Error('No contacts data received');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch contacts');
      console.error('Error fetching contacts:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    if (text) {
      // Filter contacts based on search query
      const filtered = contacts.filter(
        contact => 
          `${contact.first_name} ${contact.last_name || ''}`.toLowerCase().includes(text.toLowerCase()) ||
          contact.emails?.toLowerCase().includes(text.toLowerCase()) ||
          contact.job_title?.toLowerCase().includes(text.toLowerCase())
      );
      
      // Maintain the same sorting order as the original list
      setFilteredContacts(filtered);
    } else {
      // If search is cleared, show all contacts in their sorted order
      setFilteredContacts(contacts);
    }
  };

  const handleContactPress = (contactId: string) => {
    router.push({
      pathname: '/contactDetail',
      params: { id: contactId }
    });
  };

  const handleAddNew = () => {
    router.push('/contact');
  };

  const getInitials = (contact: Contact) => {
    const firstInitial = contact.first_name?.[0] || '';
    const lastInitial = contact.last_name?.[0] || '';
    return (firstInitial + lastInitial).toUpperCase();
  };

  const renderContactItem = ({ item }: { item: Contact }) => {
    const isNew = !item.created_at;
    const fullName = `${item.first_name} ${item.last_name || ''}`;
    const avatarBgColor = getAvatarColor(fullName);
    const textColor = getContrastingTextColor(avatarBgColor);
    return (
      <TouchableOpacity 
        style={[styles.contactItemContainer, isNew && styles.newContactItem]}
        onPress={() => handleContactPress(item.id.toString())}
      >
        <View style={[styles.contactInitials, { backgroundColor: avatarBgColor }]}>
          <Text style={[styles.initialsText, { color: textColor }]}>{getInitials(item)}</Text>
          {isNew && (
            <View style={styles.newBadge}>
              <Text style={styles.newBadgeText}>NEW</Text>
            </View>
          )}
        </View>
        <View style={styles.contactDetails}>
          <Text style={styles.contactName}>{fullName}</Text>
          <Text style={styles.contactCompany}>{item.job_title || 'No Company'}</Text>
          <Text style={styles.contactEmail}>{item.emails || 'No Email'}</Text>
        </View>
        <View style={styles.contactRightSection}>
          <Text style={[styles.timeAgo, isNew && styles.newTimeAgo]}>{formatDaysAgo(item.created_at)}</Text>
          <Text style={[styles.statusText, item.subscription_status === 'lead' ? styles.leadStatus : styles.customerStatus]}>
            {item.subscription_status || 'Lead'}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchContacts}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          title: 'Contacts',
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: colors.text.inverse,
          headerTitleStyle: {
            fontWeight: 'bold',
          },
          headerShown: true,
          headerRight: () => (
            <TouchableOpacity onPress={handleAddNew} style={styles.addButton}>
              <Ionicons name="add" size={24} color={colors.text.inverse} />
            </TouchableOpacity>
          ),
        }}
      />

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color={colors.text.tertiary} style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search contacts..."
          value={searchQuery}
          onChangeText={handleSearch}
          placeholderTextColor={colors.text.tertiary}
        />
        {searchQuery ? (
          <TouchableOpacity onPress={() => handleSearch('')}>
            <Ionicons name="close-circle" size={20} color={colors.text.tertiary} />
          </TouchableOpacity>
        ) : null}
      </View>

      {/* Contacts List */}
      <FlatList
        data={filteredContacts}
        keyExtractor={item => item.id.toString()}
        renderItem={renderContactItem}
        contentContainerStyle={styles.listContainer}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
    </SafeAreaView>
  );
}

const createStyles = (colors: ReturnType<typeof useColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.secondary,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: 'red',
    fontSize: 16,
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: colors.text.inverse,
    fontSize: 16,
    fontWeight: '500',
  },
  addButton: {
    marginRight: 15,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.primary,
    margin: 15,
    paddingHorizontal: 15,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.input.border,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 50,
    fontSize: 16,
    color: colors.text.primary,
  },
  listContainer: {
    paddingBottom: 20,
  },
  contactItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.primary,
    paddingVertical: 15,
    paddingHorizontal: 15,
  },
  newContactItem: {
    backgroundColor: 'rgba(255, 193, 7, 0.05)', // Light yellow background for new contacts
  },
  contactInitials: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
    position: 'relative', // For positioning the new badge
  },
  initialsText: {
    color: colors.text.inverse,
    fontSize: 20,
    fontWeight: 'bold',
  },
  newBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: colors.status.warning,
    paddingHorizontal: 5,
    paddingVertical: 2,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: colors.background.primary,
  },
  newBadgeText: {
    color: colors.background.primary,
    fontSize: 8,
    fontWeight: 'bold',
  },
  contactDetails: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text.primary,
    marginBottom: 3,
  },
  contactCompany: {
    fontSize: 14,
    color: colors.text.secondary,
    marginBottom: 3,
  },
  contactEmail: {
    fontSize: 14,
    color: colors.text.tertiary,
  },
  contactRightSection: {
    marginLeft: 10,
    alignItems: 'flex-end',
  },
  timeAgo: {
    fontSize: 12,
    color: colors.text.tertiary,
    marginBottom: 5,
    textAlign: 'right',
  },
  newTimeAgo: {
    color: colors.status.warning,
    fontWeight: '500',
  },
  statusText: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 15,
    fontSize: 12,
    fontWeight: '500',
  },
  leadStatus: {
    backgroundColor: colors.status.warning,
    color: colors.background.primary,
  },
  customerStatus: {
    backgroundColor: colors.status.success,
    color: colors.background.primary,
  },
  separator: {
    height: 1,
    backgroundColor: colors.input.border,
  },
});

