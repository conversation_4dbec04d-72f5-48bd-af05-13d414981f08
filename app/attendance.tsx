import React, { useState, useEffect, useCallback } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  TouchableOpacity, 
  FlatList,
  Modal,
  SafeAreaView,
  ActivityIndicator,
  RefreshControl,
  Platform,
  ScrollView,
  Pressable,
  StatusBar
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter } from 'expo-router';
import { Attendance, AttendanceResponse, formatAttendanceDate, formatAttendanceTime } from './models/Attendance';
import { apiService } from '../services/ApiService';
import { API_ENDPOINTS } from '../config/api';

export default function AttendanceScreen() {
  const [isFilterVisible, setFilterVisible] = useState(false);
  const [isMonthYearVisible, setMonthYearVisible] = useState(false);
  const [filterType, setFilterType] = useState('monthly');
  
  // For month/year selection, maintain separate temp state that only applies when "Apply" is clicked
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1); // Current month (1-12)
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear()); // Current year
  const [tempMonth, setTempMonth] = useState(selectedMonth);
  const [tempYear, setTempYear] = useState(selectedYear);
  
  const [attendanceData, setAttendanceData] = useState<Attendance[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filterPeriod, setFilterPeriod] = useState<string | null>(null);
  const router = useRouter();

  // Generate arrays for month and year selection - Define outside of render for stability
  const months = [
    { value: 1, label: 'January' },
    { value: 2, label: 'February' },
    { value: 3, label: 'March' },
    { value: 4, label: 'April' },
    { value: 5, label: 'May' },
    { value: 6, label: 'June' },
    { value: 7, label: 'July' },
    { value: 8, label: 'August' },
    { value: 9, label: 'September' },
    { value: 10, label: 'October' },
    { value: 11, label: 'November' },
    { value: 12, label: 'December' }
  ];
  
  // Generate years (only current year and previous 2 years)
  const currentYear = new Date().getFullYear();
  const years = [currentYear - 2, currentYear - 1, currentYear];

  // Fetch attendance data with filter parameters - Wrapped in useCallback to avoid recreation
  const fetchAttendanceData = useCallback(async (showLoader = true) => {
    try {
      if (showLoader) setLoading(true);
      setError(null);
      
      // Construct the endpoint URL with filter parameters
      let endpoint = `${API_ENDPOINTS.ATTENDANCE_HISTORY}?filter=${filterType}`;
      
      // Add month and year parameters for monthly filter
      if (filterType === 'monthly') {
        endpoint += `&month=${selectedMonth}&year=${selectedYear}`;
      }
      
      console.log('Fetching attendance history with endpoint:', endpoint);
      const response = await apiService.get<AttendanceResponse>(endpoint);
      console.log('Attendance API Response:', response);
      
      if (response.data?.data?.attendance_data) {
        setAttendanceData(response.data.data.attendance_data);
        // Set filter period from response if available
        if (response.data.data.period) {
          setFilterPeriod(response.data.data.period);
        }
      } else {
        throw new Error('No attendance data received');
      }
    } catch (err) {
      console.error('Error fetching attendance:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch attendance data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [filterType, selectedMonth, selectedYear]);

  // Initial data fetch
  useEffect(() => {
    fetchAttendanceData();
  }, [fetchAttendanceData]);

  const handleRefresh = () => {
    setRefreshing(true);
    fetchAttendanceData(false);
  };

  const handleBack = () => {
    router.back();
  };

  const handleFilter = () => {
    setFilterVisible(true);
  };

  const handleFilterSelect = (type: string) => {
    setFilterType(type);
    setFilterVisible(false);
    
    // Show month/year picker for monthly filter
    if (type === 'monthly') {
      // Reset temp values to current selections
      setTempMonth(selectedMonth);
      setTempYear(selectedYear);
      
      // Use setTimeout to ensure the first modal is fully closed before opening the second
      setTimeout(() => {
        setMonthYearVisible(true);
      }, 300);
    }
  };

  // Handle month selection (updates temporary state only)
  const handleMonthSelect = useCallback((month: number) => {
    // If selected year is current year, validate month selection
    if (tempYear === currentYear && month > (new Date().getMonth() + 1)) {
      // Don't allow future months for current year
      return;
    }
    
    // Update temp month
    setTempMonth(month);
  }, [tempYear, currentYear]);

  // Handle year selection (updates temporary state only)
  const handleYearSelect = useCallback((year: number) => {
    // Update temp year
    setTempYear(year);
    
    // If changing to current year, validate the currently selected month
    if (year === currentYear && tempMonth > (new Date().getMonth() + 1)) {
      // Reset to current month if previously selected month is in the future
      setTempMonth(new Date().getMonth() + 1);
    }
  }, [tempMonth, currentYear]);

  // Apply month/year filter - this is the only place where we update the actual state
  const applyMonthYearFilter = () => {
    // Apply temp values to actual state
    setSelectedMonth(tempMonth);
    setSelectedYear(tempYear);
    // Close the modal
    setMonthYearVisible(false);
    // Data will be fetched automatically through the useEffect
  };

  // Open month/year modal with current selections
  const openMonthYearModal = () => {
    // Reset temp values to match current selections
    setTempMonth(selectedMonth);
    setTempYear(selectedYear);
    setMonthYearVisible(true);
  };

  // Get filter display text
  const getFilterDisplayText = useCallback(() => {
    if (filterPeriod) {
      return filterPeriod;
    }
    
    if (filterType === 'today') {
      return 'Today';
    } else if (filterType === 'weekly') {
      return 'This Week';
    } else {
      const monthName = months.find(m => m.value === selectedMonth)?.label;
      return `${monthName} ${selectedYear}`;
    }
  }, [filterPeriod, filterType, selectedMonth, selectedYear, months]);

  const getTimeSpent = (record: Attendance): string => {
    if (record.time_spent) {
      return record.time_spent;
    }
    
    // If time_spent is null but we have check_in and check_out times, we could calculate it
    if (record.check_in_time && record.check_out_time) {
      try {
        const checkIn = new Date(record.check_in_time);
        const checkOut = new Date(record.check_out_time);
        
        // Calculate time difference in milliseconds
        const diffMs = checkOut.getTime() - checkIn.getTime();
        
        // Convert to hours and minutes
        const hours = Math.floor(diffMs / (1000 * 60 * 60));
        const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
        
        return `${hours}h ${minutes}m`;
      } catch (error) {
        console.error('Error calculating time spent:', error);
      }
    }
    
    return '-';
  };

  const TableHeader = () => (
    <View style={styles.tableHeader}>
      <Text style={[styles.headerCell, { flex: 0.5 }]}>S.No</Text>
      <Text style={styles.headerCell}>Date</Text>
      <Text style={styles.headerCell}>InTime</Text>
      <Text style={styles.headerCell}>OutTime</Text>
      <Text style={styles.headerCell}>Time spent</Text>
    </View>
  );

  const renderItem = ({ item, index }: { item: Attendance; index: number }) => (
    <View style={styles.tableRow}>
      <Text style={[styles.tableCell, { flex: 0.5 }]}>{index + 1}</Text>
      <Text style={styles.tableCell}>{formatAttendanceDate(item.date)}</Text>
      <Text style={styles.tableCell}>{formatAttendanceTime(item.check_in_time)}</Text>
      <Text style={styles.tableCell}>{formatAttendanceTime(item.check_out_time)}</Text>
      <Text style={styles.tableCell}>{getTimeSpent(item)}</Text>
    </View>
  );

  // Loading state
  if (loading && !refreshing) {
    return (
      <SafeAreaView style={[styles.container, styles.loadingContainer]}>
        <ActivityIndicator size="large" color="#0F96BB" />
        <Text style={styles.loadingText}>Loading attendance data...</Text>
      </SafeAreaView>
    );
  }

  // Error state
  if (error) {
    return (
      <SafeAreaView style={[styles.container, styles.errorContainer]}>
        <Ionicons name="alert-circle-outline" size={50} color="red" />
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => fetchAttendanceData()}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0 }]}>
      <StatusBar backgroundColor="#0F96BB" barStyle="light-content" />
      <Stack.Screen 
        options={{ 
          headerShown: false,
        }} 
      />
      
      {/* Custom Header */}
      <View style={[styles.header, Platform.OS === 'android' && styles.androidHeader]}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Attendance History</Text>
        <TouchableOpacity onPress={handleFilter} style={styles.filterButton}>
          <Ionicons name="funnel-outline" size={24} color="white" />
        </TouchableOpacity>
      </View>
      
      {/* Current Filter Display */}
      <View style={styles.filterIndicator}>
        <Text style={styles.filterLabel}>Showing:</Text>
        <View style={styles.filterPill}>
          <Text style={styles.filterPillText}>{getFilterDisplayText()}</Text>
          <TouchableOpacity onPress={handleFilter}>
            <Ionicons name="chevron-down" size={16} color="#0F96BB" />
          </TouchableOpacity>
        </View>
      </View>
      
      {/* Table */}
      {attendanceData.length > 0 ? (
        <FlatList
          data={attendanceData}
          renderItem={renderItem}
          keyExtractor={item => item.id.toString()}
          ListHeaderComponent={TableHeader}
          stickyHeaderIndices={[0]}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={['#0F96BB']}
            />
          }
        />
      ) : (
        !loading && (
          <ScrollView
            style={styles.emptyStateWrapper}
            contentContainerStyle={styles.emptyScrollContent}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={['#0F96BB']}
              />
            }
          >
            <View style={styles.emptyContainer}>
              <Ionicons name="calendar-outline" size={64} color="#ccc" />
              <Text style={styles.emptyText}>No attendance records found</Text>
              <Text style={styles.emptySubtext}>
                {filterType === 'today' && 'No attendance data for today'}
                {filterType === 'weekly' && 'No attendance data for this week'}
                {filterType === 'monthly' && `No attendance data for ${months.find(m => m.value === selectedMonth)?.label} ${selectedYear}`}
                {!filterType && 'Try adjusting your filter settings'}
              </Text>
              <TouchableOpacity
                style={styles.refreshButton}
                onPress={handleRefresh}
              >
                <Ionicons name="refresh-outline" size={20} color="#0F96BB" />
                <Text style={styles.refreshButtonText}>Refresh</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        )
      )}
      
      {/* Filter Modal */}
      <Modal
        visible={isFilterVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setFilterVisible(false)}
      >
        <Pressable style={styles.modalOverlay} onPress={() => setFilterVisible(false)}>
          <Pressable style={styles.modalContent} onPress={e => e.stopPropagation()}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Filter</Text>
              <TouchableOpacity 
                onPress={() => setFilterVisible(false)}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color="black" />
              </TouchableOpacity>
            </View>
            
            <TouchableOpacity 
              style={[styles.filterOption, filterType === 'today' && styles.activeFilterOption]}
              onPress={() => handleFilterSelect('today')}
            >
              <Text style={[styles.filterOptionText, filterType === 'today' && styles.activeFilterOptionText]}>Today</Text>
              {filterType === 'today' && (
                <Ionicons name="checkmark" size={20} color="#0F96BB" />
              )}
            </TouchableOpacity>
            
            <View style={styles.divider} />
            
            <TouchableOpacity 
              style={[styles.filterOption, filterType === 'weekly' && styles.activeFilterOption]}
              onPress={() => handleFilterSelect('weekly')}
            >
              <Text style={[styles.filterOptionText, filterType === 'weekly' && styles.activeFilterOptionText]}>Weekly</Text>
              {filterType === 'weekly' && (
                <Ionicons name="checkmark" size={20} color="#0F96BB" />
              )}
            </TouchableOpacity>
            
            <View style={styles.divider} />
            
            <TouchableOpacity 
              style={[styles.filterOption, filterType === 'monthly' && styles.activeFilterOption]}
              onPress={() => handleFilterSelect('monthly')}
            >
              <Text style={[styles.filterOptionText, filterType === 'monthly' && styles.activeFilterOptionText]}>Monthly</Text>
              {filterType === 'monthly' && (
                <Ionicons name="checkmark" size={20} color="#0F96BB" />
              )}
            </TouchableOpacity>
          </Pressable>
        </Pressable>
      </Modal>

      {/* Month/Year Selection Modal */}
      <Modal
        visible={isMonthYearVisible}
        transparent
        animationType="slide"
        onRequestClose={() => {}}
      >
        <Pressable style={styles.modalOverlay} onPress={e => e.stopPropagation()}>
          <Pressable style={styles.modalContent} onPress={e => e.stopPropagation()}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Month & Year</Text>
              <TouchableOpacity 
                onPress={() => setMonthYearVisible(false)}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color="black" />
              </TouchableOpacity>
            </View>

            <View style={styles.calendarContainer}>
              {/* Month Selection */}
              <Text style={styles.pickerLabel}>Month</Text>
              {tempYear === currentYear && (
                <Text style={styles.pickerHelperText}>
                  Only months up to {months[new Date().getMonth()].label} are available for {currentYear}
                </Text>
              )}
              <ScrollView 
                horizontal 
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.pickerScrollContent}
              >
                {months.map((month) => {
                  // Determine if this month should be disabled (future month in current year)
                  const isDisabled = tempYear === currentYear && month.value > (new Date().getMonth() + 1);
                  
                  return (
                    <TouchableOpacity
                      key={month.value}
                      style={[
                        styles.pickerItem,
                        tempMonth === month.value && styles.pickerItemSelected,
                        isDisabled && styles.pickerItemDisabled
                      ]}
                      onPress={() => !isDisabled && handleMonthSelect(month.value)}
                      disabled={isDisabled}
                      activeOpacity={0.7}
                    >
                      <Text 
                        style={[
                          styles.pickerItemText,
                          tempMonth === month.value && styles.pickerItemTextSelected,
                          isDisabled && styles.pickerItemTextDisabled
                        ]}
                      >
                        {month.label}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
              </ScrollView>

              {/* Year Selection */}
              <Text style={[styles.pickerLabel, {marginTop: 20}]}>Year</Text>
              <ScrollView 
                horizontal 
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.pickerScrollContent}
              >
                {years.map((year) => (
                  <TouchableOpacity
                    key={year}
                    style={[
                      styles.pickerItem,
                      tempYear === year && styles.pickerItemSelected
                    ]}
                    onPress={() => handleYearSelect(year)}
                    activeOpacity={0.7}
                  >
                    <Text 
                      style={[
                        styles.pickerItemText,
                        tempYear === year && styles.pickerItemTextSelected
                      ]}
                    >
                      {year}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            <TouchableOpacity 
              style={styles.applyButton}
              onPress={applyMonthYearFilter}
            >
              <Text style={styles.applyButtonText}>Apply</Text>
            </TouchableOpacity>
          </Pressable>
        </Pressable>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#0F96BB',
  },
  errorContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginTop: 10,
    fontSize: 16,
    color: 'red',
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#0F96BB',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
  },
  emptyStateWrapper: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  emptyScrollContent: {
    flexGrow: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 20,
    minHeight: 400, // Ensure minimum height for proper centering
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#555',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#888',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  refreshButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f0f8ff',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#0F96BB',
    gap: 8,
  },
  refreshButtonText: {
    fontSize: 16,
    color: '#0F96BB',
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#0F96BB',
    paddingVertical: 15,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#0F8AAA',
  },
  androidHeader: {
    paddingTop: 10,
    elevation: 4, // Add elevation for shadow effect on Android
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  filterButton: {
    padding: 5,
  },
  filterIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f7f7f7',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
  },
  filterLabel: {
    fontSize: 14,
    color: '#666',
    marginRight: 8,
  },
  filterPill: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e6f7fb',
    paddingHorizontal: 12,
    paddingVertical: 5,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#cce7f0',
  },
  filterPillText: {
    fontSize: 14,
    color: '#0F96BB',
    fontWeight: '500',
    marginRight: 4,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#f0f0f0',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
    paddingVertical: 12,
  },
  headerCell: {
    flex: 1,
    fontWeight: 'bold',
    textAlign: 'center',
    color: '#555',
    fontSize: 14,
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
    paddingVertical: 12,
    backgroundColor: 'white',
  },
  tableCell: {
    flex: 1,
    textAlign: 'center',
    color: '#333',
    fontSize: 13,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: '#f0f8ff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 5,
  },
  filterOption: {
    paddingVertical: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  activeFilterOption: {
    backgroundColor: 'rgba(15, 150, 187, 0.1)',
    borderRadius: 8,
    paddingHorizontal: 10,
  },
  filterOptionText: {
    fontSize: 16,
    color: '#333',
  },
  activeFilterOptionText: {
    color: '#0F96BB',
    fontWeight: '500',
  },
  divider: {
    height: 1,
    backgroundColor: '#e1e1e1',
  },
  calendarContainer: {
    marginVertical: 15,
  },
  pickerLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  pickerHelperText: {
    fontSize: 12,
    color: '#777',
    marginBottom: 8,
    fontStyle: 'italic',
  },
  pickerScrollContent: {
    paddingBottom: 10,
  },
  pickerItem: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 8,
    backgroundColor: '#e6e6e6',
  },
  pickerItemSelected: {
    backgroundColor: '#0F96BB',
  },
  pickerItemDisabled: {
    backgroundColor: '#f0f0f0',
    opacity: 0.5,
  },
  pickerItemText: {
    fontSize: 14,
    color: '#333',
  },
  pickerItemTextSelected: {
    color: 'white',
    fontWeight: 'bold',
  },
  pickerItemTextDisabled: {
    color: '#999',
  },
  applyButton: {
    backgroundColor: '#0F96BB',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginTop: 15,
  },
  applyButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
}); 