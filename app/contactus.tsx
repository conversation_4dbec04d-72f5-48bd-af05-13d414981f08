import React, { useState, useMemo, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ActivityIndicator,
  Linking,
  Dimensions
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useColors } from '@/hooks/useThemeColor';
import { StatusBar } from 'expo-status-bar';

export default function ContactUsScreen() {
  const colors = useColors();
  const router = useRouter();
  const styles = useMemo(() => createStyles(colors), [colors]);
  
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({
    name: '',
    email: '',
    message: ''
  });
  const [screenWidth, setScreenWidth] = useState(Dimensions.get('window').width);

  // Handle screen dimension changes for responsive layout
  useEffect(() => {
    const dimensionsHandler = ({ window }: { window: { width: number, height: number } }) => {
      setScreenWidth(window.width);
    };
    
    const subscription = Dimensions.addEventListener('change', dimensionsHandler);
    
    return () => subscription.remove();
  }, []);

  // Get platform-specific styles and behaviors
  const isWeb = Platform.OS === 'web';
  const isIOS = Platform.OS === 'ios';
  const isAndroid = Platform.OS === 'android';

  const validateForm = () => {
    let valid = true;
    const newErrors = { name: '', email: '', message: '' };
    
    // Validate name
    if (!name.trim()) {
      newErrors.name = 'Please enter your name';
      valid = false;
    }
    
    // Validate email
    if (!email.trim()) {
      newErrors.email = 'Please enter your email';
      valid = false;
    } else if (!/^\S+@\S+\.\S+$/.test(email)) {
      newErrors.email = 'Please enter a valid email address';
      valid = false;
    }
    
    // Validate message
    if (!message.trim()) {
      newErrors.message = 'Please enter your message';
      valid = false;
    }
    
    setErrors(newErrors);
    
    // Show alert dialog on mobile platforms
    if (!valid && !isWeb) {
      const firstError = newErrors.name || newErrors.email || newErrors.message;
      Alert.alert('Error', firstError);
    }
    
    return valid;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;
    
    setIsSubmitting(true);
    
    // Simulate API call - replace with actual API call
    setTimeout(() => {
      setIsSubmitting(false);
      
      if (isWeb) {
        // For web, show a different success indication
        setName('');
        setEmail('');
        setMessage('');
        // Could implement a toast notification or inline success message for web
      } else {
        // For mobile, use Alert
        Alert.alert(
          'Thank You!',
          'Your message has been sent. We\'ll get back to you soon.',
          [
            {
              text: 'OK',
              onPress: () => {
                setName('');
                setEmail('');
                setMessage('');
              }
            }
          ]
        );
      }
    }, 1500);
  };

  const handleCall = () => {
    const phoneNumber = isIOS ? 'tel:+1234567890' : 'tel:+1234567890';
    Linking.canOpenURL(phoneNumber)
      .then(supported => {
        if (supported) {
          Linking.openURL(phoneNumber);
        } else {
          console.log('Phone call not supported');
          // Show alternative contact method
        }
      })
      .catch(err => console.error('Error opening phone app:', err));
  };

  const handleEmail = () => {
    const emailUrl = 'mailto:<EMAIL>';
    Linking.canOpenURL(emailUrl)
      .then(supported => {
        if (supported) {
          Linking.openURL(emailUrl);
        } else {
          console.log('Email not supported');
          // Show alternative contact method
        }
      })
      .catch(err => console.error('Error opening email app:', err));
  };

  const handleLocation = () => {
    const mapUrl = Platform.select({
      ios: 'maps:0,0?q=123+Business+Street,+City,+Country',
      android: 'geo:0,0?q=123+Business+Street,+City,+Country',
      default: 'https://maps.google.com/?q=123+Business+Street,+City,+Country'
    });
    
    Linking.canOpenURL(mapUrl)
      .then(supported => {
        if (supported) {
          Linking.openURL(mapUrl);
        } else {
          Linking.openURL('https://maps.google.com/?q=123+Business+Street,+City,+Country');
        }
      })
      .catch(err => console.error('Error opening maps:', err));
  };

  // Render different components based on platform
  const ContentWrapper = isWeb ? View : KeyboardAvoidingView;
  const contentWrapperProps = isWeb ? {} : {
    behavior: Platform.OS === 'ios' ? 'padding' as const : 'height' as const,
    style: styles.keyboardAvoidingView,
  };

  return (
    <>
      <StatusBar style="light" />
      
      <Stack.Screen options={{ headerShown: false }} />
      
      <SafeAreaView style={styles.container} edges={['right', 'left', 'top']}>
        <View style={[styles.customHeader, { backgroundColor: '#0F96BB' }]}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => router.back()}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            accessibilityRole="button"
            accessibilityLabel="Go back"
          >
            <Ionicons 
              name={Platform.OS === 'ios' ? 'chevron-back' : 'arrow-back'} 
              size={30} 
              color="white" 
            />
          </TouchableOpacity>
          
          <Text style={styles.headerTitle}>Contact Us</Text>
          
          <View style={styles.emptyRightHeader} />
        </View>
        
        <ContentWrapper {...contentWrapperProps}>
          <View style={styles.contentContainer}>
            <ScrollView 
              style={styles.scrollView} 
              contentContainerStyle={[
                styles.scrollContent,
                isWeb && screenWidth > 768 && styles.webScrollContent
              ]}
              showsVerticalScrollIndicator={false}
            >
              <View style={[
                styles.contactInfoSection,
                isWeb && screenWidth > 768 && styles.webContactSection
              ]}>
                <Text style={styles.sectionTitle}>Get in Touch</Text>
                
                <TouchableOpacity 
                  style={styles.contactMethod} 
                  onPress={handleCall}
                  accessibilityRole="button"
                  accessibilityLabel="Call our phone number"
                >
                  <View style={styles.iconContainer}>
                    <Ionicons name="call-outline" size={24} color="#0F96BB" />
                  </View>
                  <View style={styles.contactDetails}>
                    <Text style={styles.contactMethodTitle}>Phone</Text>
                    <Text style={styles.contactMethodValue}>+1 (234) 567-890</Text>
                  </View>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={styles.contactMethod} 
                  onPress={handleEmail}
                  accessibilityRole="button"
                  accessibilityLabel="Email us"
                >
                  <View style={styles.iconContainer}>
                    <Ionicons name="mail-outline" size={24} color="#0F96BB" />
                  </View>
                  <View style={styles.contactDetails}>
                    <Text style={styles.contactMethodTitle}>Email</Text>
                    <Text style={styles.contactMethodValue}><EMAIL></Text>
                  </View>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={styles.contactMethod} 
                  onPress={handleLocation}
                  accessibilityRole="button"
                  accessibilityLabel="View our location"
                >
                  <View style={styles.iconContainer}>
                    <Ionicons name="location-outline" size={24} color="#0F96BB" />
                  </View>
                  <View style={styles.contactDetails}>
                    <Text style={styles.contactMethodTitle}>Address</Text>
                    <Text style={styles.contactMethodValue}>123 Business Street, City, Country</Text>
                  </View>
                </TouchableOpacity>
              </View>
              
              <View style={[
                styles.formSection,
                isWeb && screenWidth > 768 && styles.webFormSection
              ]}>
                <Text style={styles.sectionTitle}>Send us a Message</Text>
                
                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>Name</Text>
                  <TextInput
                    style={[styles.input, errors.name ? styles.inputError : null]}
                    value={name}
                    onChangeText={setName}
                    placeholder="Enter your name"
                    placeholderTextColor={colors.input.placeholder}
                    accessibilityLabel="Name input"
                  />
                  {errors.name && isWeb && <Text style={styles.errorText}>{errors.name}</Text>}
                </View>
                
                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>Email</Text>
                  <TextInput
                    style={[styles.input, errors.email ? styles.inputError : null]}
                    value={email}
                    onChangeText={setEmail}
                    placeholder="Enter your email"
                    placeholderTextColor={colors.input.placeholder}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    accessibilityLabel="Email input"
                  />
                  {errors.email && isWeb && <Text style={styles.errorText}>{errors.email}</Text>}
                </View>
                
                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>Message</Text>
                  <TextInput
                    style={[styles.input, styles.messageInput, errors.message ? styles.inputError : null]}
                    value={message}
                    onChangeText={setMessage}
                    placeholder="How can we help you?"
                    placeholderTextColor={colors.input.placeholder}
                    multiline
                    numberOfLines={5}
                    textAlignVertical="top"
                    accessibilityLabel="Message input"
                  />
                  {errors.message && isWeb && <Text style={styles.errorText}>{errors.message}</Text>}
                </View>
                
                {/* Only add bottom padding on mobile */}
                {!isWeb && <View style={styles.bottomPadding} />}
              </View>

              {/* For web, include submit button in the scroll view for larger screens */}
              {isWeb && screenWidth > 768 && (
                <View style={styles.webSubmitButtonContainer}>
                  <TouchableOpacity 
                    style={[styles.submitButton, { backgroundColor: '#0F96BB' }]} 
                    onPress={handleSubmit}
                    disabled={isSubmitting}
                    accessibilityRole="button"
                    accessibilityLabel="Submit your message"
                  >
                    {isSubmitting ? (
                      <ActivityIndicator size="small" color="#fff" />
                    ) : (
                      <Text style={styles.submitButtonText}>Submit</Text>
                    )}
                  </TouchableOpacity>
                </View>
              )}
            </ScrollView>
            
            {/* Fixed button at bottom for mobile or small screens */}
            {(!isWeb || screenWidth <= 768) && (
              <View style={styles.fixedButtonContainer}>
                <TouchableOpacity 
                  style={[styles.submitButton, { backgroundColor: '#0F96BB' }]} 
                  onPress={handleSubmit}
                  disabled={isSubmitting}
                  accessibilityRole="button"
                  accessibilityLabel="Submit your message"
                >
                  {isSubmitting ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <Text style={styles.submitButtonText}>Submit</Text>
                  )}
                </TouchableOpacity>
              </View>
            )}
          </View>
        </ContentWrapper>
      </SafeAreaView>
    </>
  );
}

const createStyles = (colors: ReturnType<typeof useColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  contentContainer: {
    flex: 1,
    position: 'relative',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 40,
  },
  webScrollContent: {
    maxWidth: 800,
    alignSelf: 'center',
    width: '100%',
    paddingBottom: 60,
  },
  customHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 10,
    paddingLeft: 5,
    paddingRight: 15,
    height: 56,
  },
  backButton: {
    padding: 10,
    marginLeft: 0,
    justifyContent: 'center',
    alignItems: 'center',
    width: 44,
    height: 44,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
    flex: 1,
    textAlign: 'center',
    marginRight: 30, // Offset to compensate for the back button and center the title
  },
  emptyRightHeader: {
    width: 30, // Same width as the back button icon for balance
  },
  contactInfoSection: {
    marginBottom: 30,
    backgroundColor: colors.background.secondary,
    padding: 20,
    borderRadius: 10,
    shadowColor: colors.text.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  webContactSection: {
    marginBottom: 30,
  },
  formSection: {
    backgroundColor: colors.background.secondary,
    padding: 20,
    borderRadius: 10,
    shadowColor: colors.text.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  webFormSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: colors.text.primary,
    marginBottom: 20,
  },
  contactMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.background.tertiary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  contactDetails: {
    flex: 1,
  },
  contactMethodTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text.primary,
    marginBottom: 2,
  },
  contactMethodValue: {
    fontSize: 14,
    color: colors.text.secondary,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text.primary,
    marginBottom: 8,
  },
  input: {
    backgroundColor: colors.background.primary,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: colors.text.primary,
    borderWidth: 1,
    borderColor: colors.background.tertiary,
    ...(Platform.OS === 'web' ? { outlineStyle: 'none' } : {}),
  },
  inputError: {
    borderColor: 'red',
  },
  errorText: {
    color: 'red',
    fontSize: 12,
    marginTop: 4,
  },
  messageInput: {
    height: 120,
    paddingTop: 12,
    textAlignVertical: 'top',
  },
  bottomPadding: {
    height: 80,
  },
  fixedButtonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.background.primary,
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: colors.background.tertiary,
    shadowColor: colors.text.primary,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  webSubmitButtonContainer: {
    marginTop: 10,
    marginBottom: 30,
  },
  submitButton: {
    backgroundColor: '#0F96BB',
    borderRadius: 8,
    paddingVertical: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },
  submitButtonText: {
    color: colors.text.inverse,
    fontSize: 16,
    fontWeight: 'bold',
  },
}); 