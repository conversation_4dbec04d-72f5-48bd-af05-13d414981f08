import React, { useState, useMemo, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, ActivityIndicator, Platform, StatusBar as RNStatusBar } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useColors } from '@/hooks/useThemeColor';
import { Notification, NotificationResponse, formatNotificationTime, isNotificationRead, getNotificationTypeIcon } from '@/app/models/NotificationModel';
import { apiService } from '@/services/ApiService';
import { API_ENDPOINTS } from '@/config/api';
import { showSnackbar } from '@/app/ui/utils';
import { Appbar } from 'react-native-paper';

// App bar and status bar color
const APP_BAR_COLOR = "#0F96BB";

export default function NotificationsScreen() {
  const router = useRouter();
  const colors = useColors();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const styles = useMemo(() => createStyles(colors), [colors]);

  // Log navigation for debugging
  useEffect(() => {
    console.log('Notifications screen mounted');
  }, []);

  // Fetch notifications
  const fetchNotifications = async (isRefreshing = false) => {
    try {
      console.log('Fetching notifications from API');
      if (!isRefreshing) {
        setLoading(true);
      }

      // Use the dynamic auth token from ApiService
      // The ApiService automatically adds the auth token from AsyncStorage
      const response = await apiService.get<NotificationResponse>(API_ENDPOINTS.NOTIFICATIONS);

      if (response.data?.status === 200 && response.data.success) {
        console.log(`Received ${response.data.data.notifications.length} notifications`);
        setNotifications(response.data.data.notifications);
      } else {
        console.error('API returned error:', response.data?.message);
        showSnackbar('Failed to fetch notifications');
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
      showSnackbar('Error loading notifications. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Handle pull-to-refresh
  const handleRefresh = () => {
    setRefreshing(true);
    fetchNotifications(true);
  };

  useEffect(() => {
    fetchNotifications();
  }, []);

  // Handle notification item press
  const handleNotificationPress = async (notification: Notification) => {
    try {
      console.log('Handling notification press:', notification);

      // First, mark the notification as read if it's not already read
      if (!isNotificationRead(notification.read_status)) {
        console.log(`Marking notification ${notification.id} as read`);
        try {
          // Mark notification as read in the API using dynamic auth token
          // Send the read_status payload as required by the API
          const endpoint = API_ENDPOINTS.MARK_NOTIFICATION_READ.replace(':id', notification.id.toString());
          console.log(`Making PUT request to: ${endpoint}`);
          console.log('Request payload:', { read_status: 1 });

          const response = await apiService.put(endpoint, {
            read_status: 1
          });

          console.log('Mark as read response status:', response.status);
          console.log('Mark as read response data:', response.data);

          // Update local state only if the API call was successful
          // Check both HTTP status and API response status
          if (response.status === 200 || response.data?.status === 200) {
            setNotifications(prevNotifications =>
              prevNotifications.map(item =>
                item.id === notification.id
                  ? { ...item, read_status: 1 }
                  : item
              )
            );
            console.log(`Successfully marked notification ${notification.id} as read`);
          } else {
            console.warn('API response indicates failure:', {
              httpStatus: response.status,
              apiStatus: response.data?.status,
              message: response.data?.message
            });
            showSnackbar('Failed to update notification status');
          }
        } catch (markError) {
          console.error('Error marking notification as read:', markError);
          // Show a snackbar for the error but continue with navigation
          showSnackbar('Failed to mark notification as read');
        }
      }

      // Navigate based on notification targetable_type and targetable_id
      if (notification.targetable_type && notification.targetable_id) {
        const targetType = notification.targetable_type.toLowerCase();
        const targetId = notification.targetable_id;

        console.log(`Navigating to ${targetType} with ID ${targetId}`);

        switch (targetType) {
          case 'tasks':
            router.push({ pathname: '/tasks/[id]', params: { id: targetId } } as any);
            break;
          case 'meetings':
            router.push({ pathname: '/meetings/detailedMeeting', params: { data: JSON.stringify(targetId) } } as any);
            break;
          case 'deals':
            router.push({ pathname: '/dealDetails', params: { dealId: targetId } } as any);
            break;
          case 'contacts':
            router.push({ pathname: '/contactDetail', params: { id: targetId } } as any);
            break;
          case 'sales_accounts':
          case 'accounts':
            router.push({ pathname: '/accountDetail', params: { id: targetId } } as any);
            break;
          default:
            console.log(`No navigation handler for type: ${targetType}`);
            showSnackbar(`Cannot open ${targetType} notification`);
        }
      } else {
        console.log('Notification has no target to navigate to');
        showSnackbar('This notification has no associated content to display');
      }
    } catch (error) {
      console.error('Error handling notification:', error);
      showSnackbar('Error processing notification');
    }
  };

  // Handle back button press
  const handleBackPress = () => {
    router.back();
  };

  // Render a notification item
  const renderNotificationItem = ({ item }: { item: Notification }) => {
    const isRead = isNotificationRead(item.read_status);
    const formattedType = item.targetable_type
      ? item.targetable_type.charAt(0).toUpperCase() + item.targetable_type.slice(1).replace('_', ' ')
      : 'Notification';

    return (
      <TouchableOpacity
        style={[
          styles.notificationItem,
          !isRead && styles.unreadNotification
        ]}
        onPress={() => handleNotificationPress(item)}
        activeOpacity={0.7}
      >
        <View style={[styles.iconContainer, { backgroundColor: getIconBackgroundColor(item.targetable_type) }]}>
          <Ionicons
            name={getNotificationTypeIcon(item.targetable_type) as any}
            size={24}
            color={colors.text.inverse}
          />
        </View>
        <View style={styles.notificationContent}>
          <View style={styles.notificationHeader}>
            <Text style={[
              styles.notificationTitle,
              !isRead && styles.unreadText
            ]}>
              {formattedType}
            </Text>
            <Text style={styles.notificationTime}>
              {formatNotificationTime(item.updated_at || item.trigger_time || item.trigger_date)}
            </Text>
          </View>
          <Text
            style={[
              styles.notificationMessage,
              !isRead && styles.unreadText
            ]}
            numberOfLines={2}
          >
            {item.message}
          </Text>
        </View>
        {!isRead && <View style={styles.unreadDot} />}
      </TouchableOpacity>
    );
  };

  // Get icon background color based on notification type
  const getIconBackgroundColor = (targetableType: string | null) => {
    if (!targetableType) return colors.accent;

    switch (targetableType.toLowerCase()) {
      case 'tasks':
        return colors.status.warning;
      case 'meetings':
        return colors.primary;
      case 'deals':
        return colors.status.success;
      case 'contacts':
        return colors.status.info;
      case 'sales_accounts':
      case 'accounts':
        return colors.status.info;
      case 'support':
        return colors.status.info;
      case 'system':
        return colors.text.secondary;
      default:
        return colors.accent;
    }
  };

  // Prepare UI

  if (loading) {
    return (
      <SafeAreaView
        style={styles.container}
        edges={Platform.OS === 'ios' ? ['top', 'left', 'right'] : ['left', 'right']}
      >
        {Platform.OS === 'ios' ? (
          <StatusBar style="light" />
        ) : (
          <RNStatusBar
            backgroundColor={APP_BAR_COLOR}
            barStyle="light-content"
            translucent={true}
          />
        )}

        {/* App Bar */}
        <Appbar.Header style={styles.appbar} statusBarHeight={Platform.OS === 'ios' ? 0 : RNStatusBar.currentHeight}>
          <Appbar.Action
            icon="arrow-left"
            onPress={handleBackPress}
            color="#FFFFFF"
          />
          <Appbar.Content
            title="Notifications"
            titleStyle={styles.appbarTitle}
          />
        </Appbar.Header>

        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      style={styles.container}
      edges={Platform.OS === 'ios' ? ['top', 'left', 'right'] : ['left', 'right']}
    >
      {Platform.OS === 'ios' ? (
        <StatusBar style="light" />
      ) : (
        <RNStatusBar
          backgroundColor={APP_BAR_COLOR}
          barStyle="light-content"
          translucent={true}
        />
      )}

      {/* App Bar */}
      <Appbar.Header style={styles.appbar} statusBarHeight={Platform.OS === 'ios' ? 0 : RNStatusBar.currentHeight}>
        <Appbar.Action
          icon="arrow-left"
          onPress={handleBackPress}
          color="#FFFFFF"
        />
        <Appbar.Content
          title="Notifications"
          titleStyle={styles.appbarTitle}
        />
      </Appbar.Header>

      {/* Scrollable Content */}
      <View style={styles.scrollContent}>
        {/* Notifications List */}
        {notifications.length > 0 ? (
          <FlatList
            data={notifications}
            renderItem={renderNotificationItem}
            keyExtractor={item => item.id.toString()}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
            refreshing={refreshing}
            onRefresh={handleRefresh}
          />
        ) : (
          <View style={styles.emptyContainer}>
            <Ionicons name="notifications-off-outline" size={80} color={colors.text.disabled} />
            <Text style={styles.emptyText}>No notifications yet</Text>
          </View>
        )}
      </View>
    </SafeAreaView>
  );
}

const createStyles = (colors: ReturnType<typeof useColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Platform.OS === 'ios' ? APP_BAR_COLOR : '#FFFFFF',
  },
  appbar: {
    backgroundColor: APP_BAR_COLOR,
    elevation: Platform.OS === 'android' ? 4 : 0,
    shadowColor: Platform.OS === 'ios' ? '#000' : 'transparent',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    height: Platform.OS === 'ios' ? 44 : 56,
  },
  appbarTitle: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  scrollContent: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    marginTop: Platform.OS === 'ios' ? 0 : 0,
  },
  backButton: {
    padding: 4,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  markAllButton: {
    padding: 4,
  },
  markAllText: {
    fontSize: 14,
    color: 'white',
    fontWeight: '500',
  },
  listContainer: {
    paddingHorizontal: 0,
    paddingTop: 0,
    paddingBottom: 16,
  },
  notificationItem: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 0,
    marginVertical: 0,
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    position: 'relative',
  },
  unreadNotification: {
    backgroundColor: 'rgba(240, 245, 250, 1)', // Light blue background for unread
  },
  unreadText: {
    fontWeight: '700', // Make unread text bolder
    color: colors.text.primary,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  notificationContent: {
    flex: 1,
    marginRight: 24, // Add space for unread dot
  },
  notificationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text.primary,
    flex: 1,
    marginRight: 8,
  },
  notificationTime: {
    fontSize: 12,
    color: colors.text.tertiary,
    marginTop: 2,
  },
  notificationMessage: {
    fontSize: 14,
    color: colors.text.secondary,
    lineHeight: 20,
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: APP_BAR_COLOR,
    position: 'absolute',
    top: 16,
    right: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: colors.text.secondary,
    marginTop: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    marginTop: 0, // Remove any gap between header and loading indicator
  },
});