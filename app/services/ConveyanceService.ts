import { apiService } from '../../services/ApiService';
import { API_ENDPOINTS, API_BASE_URL } from '../../config/api';
import { ConveyanceResponse, ConveyanceRequest, EnhancedConveyanceResponse } from '../models/Conveyance';

/**
 * Service for handling conveyance-related API calls
 */
class ConveyanceService {
  /**
   * Fetch conveyance history list based on filter type, month and year
   * @param filterType Filter type (today, weekly, monthly)
   * @param month Month in string format (e.g., "03") - Required for monthly filter
   * @param year Year in string format (e.g., "2025") - Required for monthly filter
   * @returns Promise with conveyance history data
   */
  static async getConveyanceHistory(filterType: string = 'monthly', month?: string, year?: string): Promise<EnhancedConveyanceResponse> {
    try {
      const requestData: ConveyanceRequest = {};
      
      // Set the appropriate request parameters based on filter type
      if (filterType.toLowerCase() === 'today') {
        requestData.filter = 'today';
      } else if (filterType.toLowerCase() === 'weekly') {
        requestData.filter = 'weekly';
      } else if (filterType.toLowerCase() === 'monthly') {
        requestData.filter = 'monthly';
        requestData.month = month;
        requestData.year = year;
      }
      
      console.log('Fetching conveyance history with filter:', filterType, requestData);
      
      const response = await apiService.post<EnhancedConveyanceResponse>(
        API_ENDPOINTS.CONVEYANCE_LIST,
        requestData
      );
      
      console.log('Conveyance API response status:', response.status);
      
      if (response.status !== 200) {
        throw new Error(`API Error: ${response.status}`);
      }
      
      return response.data;
    } catch (error) {
      console.error('Conveyance API Error:', error);
      throw error;
    }
  }
  
  /**
   * Test the API connection with the auth token
   * This can be called from a screen to verify connectivity
   */
  static async testApiConnection(): Promise<boolean> {
    try {
      const testData: ConveyanceRequest = {
        filter: 'monthly',
        month: "03",
        year: "2025"
      };
      
      // Use the API service to make the request which automatically handles the auth token
      const response = await apiService.post(
        API_ENDPOINTS.CONVEYANCE_LIST,
        testData
      );
      
      console.log('API Test Response:', {
        status: response.status,
        ok: response.status >= 200 && response.status < 300,
        data: response.data
      });
      
      return response.status >= 200 && response.status < 300;
    } catch (error) {
      console.error('API Test Error:', error);
      return false;
    }
  }
}

export default ConveyanceService; 