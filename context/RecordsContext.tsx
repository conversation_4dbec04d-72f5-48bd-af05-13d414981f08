import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { RECORDS_STORAGE_KEY, SALES_ACTIVITIES_STORAGE_KEY } from '../services/AuthService';

export interface Activity {
  id: string;
  activity_name: string;
  activity_icon?: string;
  icon?: string;
}

interface RecordsContextType {
  recordTypes: Activity[];
  salesActivities: Activity[];
  setRecordTypes: (types: Activity[]) => void;
  setSalesActivities: (activities: Activity[]) => void;
  updateRecordTypes: (types: Activity[]) => Promise<void>;
  updateSalesActivities: (activities: Activity[]) => Promise<void>;
}

const RecordsContext = createContext<RecordsContextType | undefined>(undefined);

export const RecordsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [recordTypes, setRecordTypes] = useState<Activity[]>([]);
  const [salesActivities, setSalesActivities] = useState<Activity[]>([]);

  const updateRecordTypes = async (types: Activity[]) => {
    try {
      await AsyncStorage.setItem(RECORDS_STORAGE_KEY, JSON.stringify(types));
      console.log("updateRecordTypes: " + JSON.stringify(types));
      setRecordTypes(types);
    } catch (error) {
      console.error('Error saving record types:', error);
    }
  };

  const updateSalesActivities = async (activities: Activity[]) => {
    try {
      await AsyncStorage.setItem(SALES_ACTIVITIES_STORAGE_KEY, JSON.stringify(activities));
      console.log("updateActivitiesTypes: " + JSON.stringify(activities));
      setSalesActivities(activities);
    } catch (error) {
      console.error('Error saving sales activities:', error);
    }
  };

  useEffect(() => {
    const loadStoredData = async () => {
      try {
        const storedRecords = await AsyncStorage.getItem(RECORDS_STORAGE_KEY);
        const storedActivities = await AsyncStorage.getItem(SALES_ACTIVITIES_STORAGE_KEY);

        if (storedRecords) {
          setRecordTypes(JSON.parse(storedRecords));
        }
        if (storedActivities) {
          setSalesActivities(JSON.parse(storedActivities));
        }
      } catch (error) {
        console.error('Error loading stored data:', error);
      }
    };

    loadStoredData();
  }, []);

  return (
    <RecordsContext.Provider value={{
      recordTypes,
      salesActivities,
      setRecordTypes,
      setSalesActivities,
      updateRecordTypes,
      updateSalesActivities
    }}>
      {children}
    </RecordsContext.Provider>
  );
};

export const useRecords = () => {
  const context = useContext(RecordsContext);
  if (context === undefined) {
    throw new Error('useRecords must be used within a RecordsProvider');
  }
  return context;
}; 