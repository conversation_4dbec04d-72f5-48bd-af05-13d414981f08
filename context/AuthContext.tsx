import React, { createContext, useContext, useEffect, useState } from 'react';
import { router } from 'expo-router';
import { User } from '../models/User';
import { authService } from '../services/AuthService';

type AuthContextType = {
  user: User | null;
  isLoading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for stored user data on app start
    const loadUser = async () => {
      console.log('🔄 App initialization: Loading user from storage...');
      setIsLoading(true); // Ensure loading state is true at the start
      try {
        const loadedUser = await authService.loadUserFromStorage();
        if (loadedUser) {
          console.log('✅ App initialization: User found in storage, logging in automatically');
          setUser(loadedUser);
        } else {
          console.log('❌ App initialization: No user found in storage, showing login screen');
          setUser(null);
        }
      } catch (error) {
        console.error('❌ Failed to load user from storage:', error);
        setUser(null);
      } finally {
        // Use a small delay to ensure state updates are processed
        setTimeout(() => {
          setIsLoading(false);
          console.log('✅ App initialization complete, isLoading set to false');
        }, 500); // Increased delay to ensure state updates properly
      }
    };

    loadUser();
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      console.log('🔄 Starting sign-in process for email:', email);
      setIsLoading(true);
      
      // Call the auth service to login
      console.log('📲 Calling auth service login method');
      const user = await authService.login({ email, password });
      
      // Update state with the user
      console.log('👤 Setting user state with logged in user');
      setUser(user);
      
      // Use setTimeout to delay navigation until the next tick of the event loop
      // This gives the root layout component time to mount properly
      console.log('⏱️ Scheduling navigation after layout mount');
      setTimeout(() => {
        try {
          console.log('🧭 Navigating to tabs route');
          router.replace('/(tabs)');
        } catch (navError) {
          console.error('❌ Navigation error:', navError);
        }
      }, 500); // Increased delay to ensure layout mounting
    } catch (error) {
      console.error('❌ Sign-in process failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
      console.log('✅ Sign-in process completed (success or failure)');
    }
  };

  const signOut = async () => {
    try {
      setIsLoading(true);
      
      // First clear the user state to prevent unwanted side-effects
      setUser(null);
      
      // Then call the auth service to logout
      await authService.logout();
      
      // Use setTimeout to delay navigation until the next tick of the event loop
      // This gives the root layout component time to mount properly
      console.log('⏱️ Scheduling navigation after logout');
      setTimeout(() => {
        try {
          console.log('🧭 Navigating to login page');
          router.replace('/login');
        } catch (navError) {
          console.error('❌ Navigation error:', navError);
        }
      }, 500); // Increased delay to ensure layout mounting
    } catch (error) {
      console.error('Logout failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthContext.Provider value={{ user, isLoading, signIn, signOut }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 