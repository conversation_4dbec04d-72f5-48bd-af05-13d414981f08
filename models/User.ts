/**
 * User Model
 * Represents a user in the system based on the login API response
 */

export interface UserData {
  token: string;
  id: number;
  username: string;
  email: string;
  mobile: string;
  role: number;
  fcm_token?: string;
  avatar?: string;
}

export interface LoginResponse {
  status: number;
  message: string;
  user: UserData;
}

export interface LoginCredentials {
  email: string;
  password: string;
  fcm_token?: string;
}

export class User {
  public token: string;
  public id: number;
  public username: string;
  public email: string;
  public mobile: string;
  public role: number;
  public avatar?: string;

  constructor(userData: UserData) {
    this.token = userData.token;
    this.id = userData.id;
    this.username = userData.username;
    this.email = userData.email;
    this.mobile = userData.mobile;
    this.role = userData.role;
    this.avatar = userData.avatar;
  }

  /**
   * Check if this user has admin role
   */
  public isAdmin(): boolean {
    return this.role === 1;
  }

  /**
   * Create a plain object representation of the user (for storage)
   */
  public toJSON() {
    return {
      token: this.token,
      id: this.id,
      username: this.username,
      email: this.email,
      mobile: this.mobile,
      role: this.role,
      avatar: this.avatar,
    };
  }

  /**
   * Create a User instance from stored JSON data
   */
  public static fromJSON(json: any): User {
    return new User({
      token: json.token,
      id: json.id,
      username: json.username,
      email: json.email,
      mobile: json.mobile || '',
      role: json.role,
      avatar: json.avatar,
    });
  }
} 