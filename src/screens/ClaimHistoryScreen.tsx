import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, Modal, Pressable, ActivityIndicator, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { Calendar, DateData } from 'react-native-calendars';
import TextTicker from 'react-native-text-ticker';
import { getClaimHistory } from '../services/api';
import { TokenStorage } from '../utils/tokenStorage';
import { Colors } from '../common/colors';
import { formatDateDDMM, formatDateDDMMDay } from '../utils/date';
import { UpdateClaimModal } from '../components/conveyance-details/UpdateClaimModal';

const { width } = Dimensions.get('window');

// String constants for status
const STATUS_STRINGS = {
  PENDING: 'Pending',
  COMPLETED: 'Completed',
  REJECTED: 'Rejected',
  APPROVED: 'Approved',
  WAITING_FOR_FINANCE_APPROVAL: 'Waiting for Finance Approval',
  WAITING_FOR_MANAGER_APPROVAL: 'Waiting for Manager Approval',
  FINANCE_REJECTED: 'Finance Rejected',
  MANAGER_REJECTED: 'Manager Rejected',
};

const renderRow = (item: any, index: number, type: string) => {
  const { statusText, statusColor } = getStatusInfo(item.claim_status, type);

  return (
    <View style={styles.row} key={item.id || index}>
      <View style={[styles.cell, styles.serialNoColumn]}>
        <Text style={styles.cellText} numberOfLines={1}>{index + 1}</Text>
      </View>
      <View style={[styles.cell, styles.taskColumn]}>
        <TextTicker
          style={styles.cellText}
          duration={8000}
          loop
          bounce={false}
          repeatSpacer={50}
          marqueeDelay={1000}
          scrollSpeed={50}
          useNativeDriver
        >
          {item.task || '-'}
        </TextTicker>
      </View>
      <View style={[styles.cell, styles.dateColumn]}>
        <Text style={styles.cellText} numberOfLines={1} ellipsizeMode="tail">{formatDateDDMMDay(item.start_date)}</Text>
      </View>
      <View style={styles.cell}>
        <Text style={styles.cellText} numberOfLines={1} ellipsizeMode="tail">{item.distance_travelled ? `${item.distance_travelled} km` : '-'}</Text>
      </View>
      <View style={styles.cell}>
        <TextTicker
          style={[styles.cellText, { color: statusColor }]}
          duration={8000}
          loop
          bounce={false}
          repeatSpacer={50}
          marqueeDelay={1000}
          scrollSpeed={50}
          useNativeDriver
        >
          {statusText}
        </TextTicker>
      </View>
      {type === STATUS_STRINGS.REJECTED && (
        <TouchableOpacity
          style={styles.editBtn}
          onPress={() => setEditModal({ visible: true, remark: item.claim_approved_remarks || '', claimId: item.id })}
        >
          <MaterialCommunityIcons name="pencil-outline" size={16} color={Colors.primary} />
        </TouchableOpacity>
      )}
    </View>
  );
}; 