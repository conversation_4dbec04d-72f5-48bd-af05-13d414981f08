import messaging from '@react-native-firebase/messaging';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';

const FCM_TOKEN_KEY = 'fcm_token';

export const requestUserPermission = async () => {
  const authStatus = await messaging().requestPermission();
  const enabled =
    authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
    authStatus === messaging.AuthorizationStatus.PROVISIONAL;

  if (enabled) {
    console.log('Authorization status:', authStatus);
  }

  return enabled;
};

export const getFCMToken = async () => {
  try {
    const existingToken = await AsyncStorage.getItem(FCM_TOKEN_KEY);
    
    if (existingToken) return existingToken;

    const fcmToken = await messaging().getToken();
    if (fcmToken) {
      console.log('FCM Token:', fcmToken);
      await AsyncStorage.setItem(FCM_TOKEN_KEY, fcmToken);
      return fcmToken;
    }
  } catch (error) {
    console.error('Error getting FCM token:', error);
    return null;
  }
};

export const notificationListener = async () => {
  messaging().onNotificationOpenedApp(remoteMessage => {
    console.log(
      'Notification caused app to open from background state:',
      remoteMessage.notification,
    );
  });

  // Check whether an initial notification is available
  messaging()
    .getInitialNotification()
    .then(remoteMessage => {
      if (remoteMessage) {
        console.log(
          'Notification caused app to open from quit state:',
          remoteMessage.notification,
        );
      }
    });

  // Foreground message handling
  messaging().onMessage(async remoteMessage => {
    console.log('Received foreground message:', remoteMessage);
    
    if (Platform.OS === 'ios') {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: remoteMessage.notification?.title || '',
          body: remoteMessage.notification?.body || '',
          data: remoteMessage.data,
        },
        trigger: null,
      });
    }
  });
};

export const setupNotifications = async () => {
  const hasPermission = await requestUserPermission();
  if (hasPermission) {
    await getFCMToken();
    await notificationListener();
  }
  return hasPermission;
}; 